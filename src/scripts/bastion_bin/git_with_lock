#!/bin/bash

# This script acts as a light-weight wrapper around git.
# It verifies that the user has correctly locked the bastion before running
# any git operation that can potentially change the repository state (such as a checkout).

set -eo pipefail

# we don't want to restrict git operations outside nirvana monorepo so a quick check:
if [[ ! "$PWD"/ =~ ^$HOME/nirvana/ ]]; then
    exec git "$@"
fi

# list of git operations that are unconditionally allowed for convenience,
# these are light-weight read-only operations that are safe to run without a lock
allowlist=("status" "diff" "log" "show" "fetch")

if [[ $# -eq 0 ]]; then
    exec git
fi

for cmd in "${allowlist[@]}"; do
    if [[ "$1" == "$cmd" ]]; then
        exec git "$@"
    fi
done

# if we are here it means the first argument is not in the allowlist
# so we need to check if the bastion is locked

lock_info_file="$HOME/nirvana_flock.info"

if [[ -z "$NIRVANA_EMAIL" ]]; then
    echo "Error: NIRVANA_EMAIL environment variable is not set"
    exit 1
fi

if [[ -z "$BASTION_LOCK" || ! -f "$lock_info_file" ]]; then
    echo "Error: Bastion not locked properly. Use lock_bastion to acquire a lock before running git operations"
    exit 1
fi

# Read the first line which contains USER:PID in machine-friendly format
first_line=$(head -n 1 "$lock_info_file")

# Parse USER:PID from the first line using regex parsing
if [[ "$first_line" =~ ^([^:]+):([0-9]+)$ ]]; then
    lock_user="${BASH_REMATCH[1]}"
    lock_pid="${BASH_REMATCH[2]}"
else
    echo "Internal error: Invalid lock file format"
    exit 1
fi

# Validation 1: Check if the current user matches the lock user
if [[ "$NIRVANA_EMAIL" != "$lock_user" ]]; then
    cat <<EOF
Error: Currently bastion is locked by $lock_user, but you are $NIRVANA_EMAIL.

Lock details:
$(tail -n +2 "$lock_info_file")

To forcefully unlock the bastion, run force_unlock_bastion
EOF
    exit 1
fi

# Validation 2: Check if the current BASTION_LOCK matches the lock PID
if [[ "$BASTION_LOCK" != "$lock_pid" ]]; then
    echo "Error: Lock PID mismatch. Expected $lock_pid, but BASTION_LOCK is $BASTION_LOCK"
    exit 1
fi

# Validation 3: Check if the PID is still running
if [[ ! -e "/proc/$lock_pid" ]]; then
    echo "Error: Lock PID $lock_pid is no longer running"
    echo "The lock may have been released unexpectedly. Please acquire a new lock with lock_bastion"
    exit 1
fi

# All validations passed, proceed with git command
exec git "$@"
