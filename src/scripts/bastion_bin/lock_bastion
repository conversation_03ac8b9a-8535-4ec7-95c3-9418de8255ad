#!/bin/bash

# Exit on error, and pipe failures
set -eo pipefail

# fail if NIRVANA_EMAIL is not set
if [[ -z "$NIRVANA_EMAIL" ]]; then
    echo "Error: NIRVANA_EMAIL environment variable is not set"
    exit 1
fi

# Get current timestamp and reason
timestamp=$(date '+%Y-%m-%d %H:%M:%S')
if [[ -z "$1" ]]; then
    echo -n "Please provide a reason for locking the bastion: "
    read -r reason
    if [[ -z "$reason" ]]; then
        reason="no reason provided"
    fi
else
    reason="$1"
fi

(
    # here -n denotes non-blocking mode,
    # so flock will return immediately with non-zero exit code if the lock is already held
    if ! flock -n 200; then
        cat <<EOF
Error: Lock is already held.
$( [[ -f "$HOME/nirvana_flock.info" ]] && tail -n +2 "$HOME/nirvana_flock.info" )

To forcefully unlock the bastion, run force_unlock_bastion
EOF
        exit 1
    fi

    # Write detailed information to a separate info file
    # First line is machine-friendly format: USER:PID
    # Rest of the lines are human-readable format
    cat > "$HOME/nirvana_flock.info" << EOF
$NIRVANA_EMAIL:$$
    User = $NIRVANA_EMAIL
    PID = $$
    Reason = $reason
    Timestamp = $timestamp
EOF

    cat << EOF

Lock acquired successfully.
You are now inside a sub-shell with the lock acquired.
To start another subshell with the same lock, run:
    BASTION_LOCK=$$ bash

Instructions to release the lock:
  type exit to release the lock (this will exit the sub-shell and you will return back to your original shell)
  alternatively, lock will be automatically released when your SSH session ends

EOF

    export BASTION_LOCK=$$
    exec bash
) 200>"$HOME/nirvana_flock"