load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "equipments",
    srcs = [
        "deps.go",
        "equipments.go",
        "fx.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/underwriting_panels/equipments",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/underwriting_panels/base_panel",
        "//nirvana/nonfleet/underwriting_panels/enums",
        "//nirvana/nonfleet/underwriting_panels/packages",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_fx//:fx",
    ],
)
