package equipments

import (
	"context"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/packages"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"

	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"

	"github.com/cockroachdb/errors"

	nrb_app2 "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/enums"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

type EquipmentsPanel[T application.AppInfo] struct {
	deps Deps[T]
}

var _ base_panel.Panel[application.AppInfo] = &packages.PackagesPanel[application.AppInfo]{}

func NewEquipmentsPanel[T application.AppInfo](deps Deps[T]) *EquipmentsPanel[T] {
	return &EquipmentsPanel[T]{deps: deps}
}

func (e *EquipmentsPanel[T]) SetIsReviewed(ctx context.Context, appReviewID string, isReviewed bool) error {
	if err := e.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review nrb_app2.ApplicationReview) (nrb_app2.ApplicationReview, error) {
			err := review.SetPanelReview(nrb_app2.EquipmentPanelType, isReviewed)
			if err != nil {
				return review, errors.Wrapf(err, "failed to set panel review")
			}

			return review, nil
		},
	); err != nil {
		return errors.Wrapf(err, "unable to update application review %v", appReviewID)
	}
	return nil
}

func (e *EquipmentsPanel[T]) IsReviewed(input *base_panel.PanelInput[T]) bool {
	return input.ApplicationReview.IsPanelReviewed(nrb_app2.EquipmentPanelType)
}

func (*EquipmentsPanel[T]) PanelKey() enums.FlagPanel {
	return enums.Equipments
}

func (*EquipmentsPanel[T]) EquipmentSummary(input *base_panel.PanelInput[T]) []oapi_uw.EquipmentSummary {
	return input.Application.Info.GetEquipmentInfo().GetEquipmentSummary()
}

func (*EquipmentsPanel[T]) GetActualPUCount(input *base_panel.PanelInput[T]) int {
	return input.Application.Info.GetActualPowerUnitCount()
}

func (*EquipmentsPanel[T]) EquipmentDetails(input *base_panel.PanelInput[T], prettifyEnums bool) []oapi_uw.EquipmentUnit {
	return input.Application.Info.GetEquipmentInfo().GetEquipmentDetails(prettifyEnums)
}
