package packages

import (
	"context"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/type_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	nf_db_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/utils"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/enums"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
	nonfleet_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	appreviewutils "nirvanatech.com/nirvana/underwriting/app_review/utils"
	"nirvanatech.com/nirvana/underwriting/app_review/widgets/global"
)

type PackagesPanel[T application.AppInfo] struct {
	deps Deps[T]
}

var _ base_panel.Panel[application.AppInfo] = (*PackagesPanel[application.AppInfo])(nil)

func NewPackagesPanel[T application.AppInfo](deps Deps[T]) *PackagesPanel[T] {
	return &PackagesPanel[T]{deps: deps}
}

func (p *PackagesPanel[T]) SetIsReviewed(ctx context.Context, appReviewID string, isReviewed bool) error {
	if err := p.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review nf_app_review.ApplicationReview) (nf_app_review.ApplicationReview, error) {
			err := review.SetPanelReview(nf_app_review.PackagesPanelType, isReviewed)
			if err != nil {
				return review, errors.Wrapf(err, "failed to set panel review")
			}
			return review, nil
		},
	); err != nil {
		return errors.Wrapf(err, "unable to update application review %v", appReviewID)
	}
	return nil
}

func (p *PackagesPanel[T]) IsReviewed(input *base_panel.PanelInput[T]) bool {
	return input.ApplicationReview.IsPanelReviewed(nf_app_review.PackagesPanelType)
}

func (p *PackagesPanel[T]) PanelKey() enums.FlagPanel {
	return enums.Packages
}

func (p *PackagesPanel[T]) GetPackages(
	ctx context.Context,
	input *base_panel.PanelInput[T],
) (*nonfleet_uw.ApplicationReviewPackages, error) {
	if input == nil || input.Application == nil ||
		input.Application.QuoteSubmissionID == uuid.Nil {
		return nil, errors.New("couldn't get quote submission id")
	}

	if input.ApplicationReview == nil {
		return nil, errors.New("couldn't get application review")
	}

	var (
		uwPackage *nonfleet_uw.PackageDetails = nil
		status    *nonfleet.JobRunStatus      = nil
	)
	indSubID := input.Application.QuoteSubmissionID

	subObj, err := p.deps.AppWrapper.GetSubmissionById(ctx, indSubID)
	if err != nil {
		return nil, errors.Wrapf(err, "couldn't fetch submission: %s", indSubID)
	}

	originalIndication, err := p.deps.AppWrapper.GetIndOptionById(ctx, subObj.SelectedIndicationID)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't fetch indication")
	}
	originalPackage := utils.GeneratePackageDetails(originalIndication)

	reviewSubID := input.ApplicationReview.GetSubmissionID()
	if reviewSubID != indSubID {
		// Fetch UW Submission and update package data
		uwSubObj, err := p.deps.AppWrapper.GetSubmissionById(ctx, reviewSubID)
		if err != nil {
			return nil, errors.Wrapf(err, "couldn't fetch submission: %s", reviewSubID)
		}

		if uwSubObj.JobRunInfo == nil {
			status = pointer_utils.ToPointer(nonfleet.JobRunStatusFailure)
		} else {
			jobRun, err := p.deps.Jobber.GetJobRun(ctx, *uwSubObj.JobRunInfo)
			if err != nil {
				return nil, errors.Wrapf(err, "couldn't fetch job info: %#v", *uwSubObj.JobRunInfo)
			}
			currStatus := global.GetClientJobRunStatus(jobRun.Status)
			status = pointer_utils.ToPointer(currStatus)
			if currStatus == nonfleet.JobRunStatusSuccess {
				indToSelect, err := p.deps.AppWrapper.GetIndOptionByPackageType(ctx, *uwSubObj, originalIndication.GetPackage())
				if err != nil {
					return nil, errors.Wrapf(err, "couldn't fetch package indication option: %s", uwSubObj.SelectedIndicationID)
				}

				indID, err := uuid.Parse(indToSelect.GetID())
				if err != nil {
					return nil, errors.Wrapf(err, "couldn't parse indID: %s", indToSelect.GetID())
				}

				err = p.deps.AppWrapper.SetSelectedIndIDOnSub(ctx, uwSubObj.ID, indID)
				if err != nil {
					return nil, errors.Wrapf(err, "couldn't set indID: %s to sub: %s", indID, uwSubObj.ID)
				}

				uwPackage = pointer_utils.ToPointer(utils.GeneratePackageDetails(indToSelect))
			}
		}
	}

	override := input.ApplicationReview.GetOverrides()
	var ancillaryCovs []uw.AncillaryCoverage
	for _, coverage := range override.AncillaryCoverages {
		ancillaryCovs = append(ancillaryCovs, coverage)
	}
	ancillaryCoverages, err := uw.BindAncillaryCoverageFromDbToRest(ancillaryCovs)
	if err != nil {
		return nil, errors.Wrapf(err, "couldn't bind ancillary coverages to rest")
	}

	var aggregateCreditLimitsByCoverage *oapi_uw.ApplicationReviewAggregateCreditLimitsByCoverage
	if input.Application.ProgramType == policy_enums.ProgramTypeNonFleetAdmitted {
		ac := GetAggregateCreditLimitsByCoverage(input.Application.Info.GetState(), input.Application.Info.GetCoverageInfo())
		aggregateCreditLimitsByCoverage = &ac
	}

	return &nonfleet_uw.ApplicationReviewPackages{
		IsReviewed:                      p.IsReviewed(input),
		Metadata:                        subObj.Info.GetQuoteMetadata(),
		Options:                         subObj.Info.GetQuoteOptions(),
		OriginalPackage:                 &originalPackage,
		Status:                          status,
		UpdatedPackage:                  uwPackage,
		AncillaryCoverages:              ancillaryCoverages,
		AlPercentage:                    pointer_utils.Int(type_utils.GetValueOrDefault(override.ALPercent, 0)),
		ApdPercentage:                   pointer_utils.Int(type_utils.GetValueOrDefault(override.APDPercent, 0)),
		GlPercentage:                    pointer_utils.Int(type_utils.GetValueOrDefault(override.GLPercent, 0)),
		MtcPercentage:                   pointer_utils.Int(type_utils.GetValueOrDefault(override.MTCPercent, 0)),
		SafetyCredits:                   pointer_utils.Int(type_utils.GetValueOrDefault(override.SafetyCredit, 0)),
		PaymentPlan:                     pointer_utils.ToPointer(getPaymentPlanToOapi(override.PaymentPlan)),
		IsAPDMTCDeductibleCombined:      pointer_utils.ToPointer(subObj.Info.IsAPDMTCDeductibleCombined()),
		AggregateCreditLimitsByCoverage: aggregateCreditLimitsByCoverage,
	}, nil
}

func GetAggregateCreditLimitsByCoverage(
	state us_states.USState,
	covInfo *application.CoverageInfo,
) oapi_uw.ApplicationReviewAggregateCreditLimitsByCoverage {
	aggregateCreditLimitsByCoverage := oapi_uw.ApplicationReviewAggregateCreditLimitsByCoverage{}
	// Note: isNonAdmitted is always set to false since Non-Fleet does not currently support non-admitted programs.
	if covInfo.ContainsCoverageInPrimaryCoveragesAndIsRequired(app_enums.CoverageAutoLiability) {
		minLimit, maxLimit := appreviewutils.GetCreditLimits(state, app_enums.CoverageAutoLiability, false)
		aggregateCreditLimitsByCoverage.AutoLiability = &oapi_uw.ApplicationReviewAggregateCreditLimitsByCoverageData{
			Maximum: &maxLimit,
			Minimum: &minLimit,
		}
	}

	if covInfo.ContainsCoverageInPrimaryCoveragesAndIsRequired(app_enums.CoverageAutoPhysicalDamage) {
		minLimit, maxLimit := appreviewutils.GetCreditLimits(state, app_enums.CoverageAutoPhysicalDamage, false)
		aggregateCreditLimitsByCoverage.AutoPhysicalDamage = &oapi_uw.ApplicationReviewAggregateCreditLimitsByCoverageData{
			Maximum: &maxLimit,
			Minimum: &minLimit,
		}
	}

	if covInfo.ContainsCoverageInPrimaryCoveragesAndIsRequired(app_enums.CoverageMotorTruckCargo) {
		minLimit, maxLimit := appreviewutils.GetCreditLimits(state, app_enums.CoverageMotorTruckCargo, false)
		aggregateCreditLimitsByCoverage.MotorTruckCargo = &oapi_uw.ApplicationReviewAggregateCreditLimitsByCoverageData{
			Maximum: &maxLimit,
			Minimum: &minLimit,
		}
	}
	return aggregateCreditLimitsByCoverage
}

func (p *PackagesPanel[T]) UpdatePackages(
	ctx context.Context,
	appReviewID string,
	alPercent *int,
	apdPercent *int,
	mtcPercent *int,
	glPercent *int,
	ancillaryCoverages *oapi_common.AncillaryCoverages,
	paymentPlan *nonfleet.PaymentPlan,
) error {
	err := p.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review nf_app_review.ApplicationReview) (nf_app_review.ApplicationReview, error) {
			err := review.UpdateOverrides(
				func(override *nf_app_review.Overrides) (nf_app_review.Overrides, error) {
					if ancillaryCoverages != nil {
						formAncillaryCoverages, err := uw.BindAncillaryCoverages(*ancillaryCoverages)
						if err != nil {
							return *override, errors.Wrapf(err, "couldn't bind ancillary coverages")
						}
						updatedAncillaryCoverages, err := uw.UpdateAncillaryCoverages(review.GetOverrides().AncillaryCoverages, formAncillaryCoverages, nil)
						if err != nil {
							return *override, errors.Wrapf(err, "couldn't update ancillary coverages")
						}
						override.AncillaryCoverages = updatedAncillaryCoverages
					}

					if mtcPercent != nil {
						override.MTCPercent = mtcPercent
					}

					if apdPercent != nil {
						override.APDPercent = apdPercent
					}

					if alPercent != nil {
						override.ALPercent = alPercent
					}

					if glPercent != nil {
						override.GLPercent = glPercent
					}

					if paymentPlan != nil {
						override.PaymentPlan = getPaymentPlanFromOapi(paymentPlan)
					}
					return *override, nil
				})

			return review, err
		})
	if err != nil {
		return errors.Wrapf(err, "couldn't update packages for app review: %s", appReviewID)
	}
	return nil
}

func getPaymentPlanFromOapi(plan *nonfleet.PaymentPlan) *nf_db_enums.PaymentPlan {
	switch *plan {
	case nonfleet.PaymentPlanInstallmentWithEFT:
		return pointer_utils.ToPointer(nf_db_enums.PaymentPlanInstallmentWithEFT)
	case nonfleet.PaymentPlanPaidInFull:
		return pointer_utils.ToPointer(nf_db_enums.PaymentPlanPaidInFull)
	default:
		return pointer_utils.ToPointer(nf_db_enums.PaymentPlanInstallmentWithEFT)
	}
}

func getPaymentPlanToOapi(plan *nf_db_enums.PaymentPlan) nonfleet.PaymentPlan {
	if plan == nil {
		return nonfleet.PaymentPlanInstallmentWithEFT
	}

	switch *plan {
	case nf_db_enums.PaymentPlanInstallmentWithEFT:
		return nonfleet.PaymentPlanInstallmentWithEFT
	case nf_db_enums.PaymentPlanPaidInFull:
		return nonfleet.PaymentPlanPaidInFull
	default:
		return nonfleet.PaymentPlanInstallmentWithEFT
	}
}
