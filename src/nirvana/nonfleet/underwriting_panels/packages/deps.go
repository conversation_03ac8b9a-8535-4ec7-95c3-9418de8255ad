package packages

import (
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
)

type Deps[T nf_app.AppInfo] struct {
	fx.In

	AppReviewWrapper nf_app_review.Wrapper
	AppWrapper       nf_app.Wrapper[T]
	Jobber           quoting_jobber.Client
}
