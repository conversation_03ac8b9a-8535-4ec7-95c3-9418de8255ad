package driver

import (
	"context"
	"strings"
	"time"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	openapi_types "github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/infra/authz"
	nf_driver "nirvanatech.com/nirvana/nonfleet/driver"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/enums"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
	"nirvanatech.com/nirvana/rating/mvr"
)

type DriversPanel[T application.AppInfo] struct {
	deps Deps[T]
}

var _ base_panel.Panel[application.AppInfo] = &DriversPanel[application.AppInfo]{}

func NewDriversPanel[T application.AppInfo](deps Deps[T]) *DriversPanel[T] {
	return &DriversPanel[T]{deps: deps}
}

func (dp *DriversPanel[T]) SetIsReviewed(ctx context.Context, appReviewID string, isReviewed bool) error {
	if err := dp.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review app_review.ApplicationReview) (app_review.ApplicationReview, error) {
			err := review.SetPanelReview(app_review.DriverPanelType, isReviewed)
			if err != nil {
				return review, errors.Wrapf(err, "failed to set panel review")
			}
			return review, nil
		},
	); err != nil {
		return errors.Wrapf(err, "unable to update application review %v", appReviewID)
	}
	return nil
}

func (dp *DriversPanel[T]) IsReviewed(input *base_panel.PanelInput[T]) bool {
	return input.ApplicationReview.IsPanelReviewed(app_review.DriverPanelType)
}

func (dp *DriversPanel[T]) PanelKey() enums.FlagPanel {
	return enums.Drivers
}

func (dp *DriversPanel[T]) IsMVRPulled(input *base_panel.PanelInput[T]) bool {
	return input.ApplicationReview.IsMVRPulled()
}

type MVRDetails struct {
	CdlClass        *string
	CdlNumber       string
	CdlStatus       *string
	ExpirationDate  *openapi_types.Date
	IssueDate       *openapi_types.Date
	MvrPullError    *string
	MvrPullStatus   *oapi_common.MVRPullStatus
	ViolationCount  int
	ViolationPoints int
	Violations      *[]oapi_common.DriverViolation
	StreetAddr      string
	CityStateZip    string
}

func GetMVRDetails(
	ctx context.Context,
	driversBasicDetails []application.DriverBasicDetails,
	effectiveDate time.Time,
	programType policy_enums.ProgramType,
	usState us_states.USState,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) []MVRDetails {
	var retval []MVRDetails

	var drivers []admitted_app.DriverDetails
	for _, d := range driversBasicDetails {
		drivers = append(drivers, admitted_app.DriverDetails{
			DriverBasicDetails: d,
		})
	}

	mvrData, _, _ := mvr.GetMovingViolationCountsForDrivers(
		ctx,
		drivers,
		effectiveDate,
		programType,
		usState,
		fetcherClientFactory,
	)

	for idx := range driversBasicDetails {
		var mvrDetails MVRDetails
		var mvrToUse *mvr.DriverViolationRecord
		for _, mvrD := range mvrData {
			if strings.EqualFold(mvrD.LicenseNumber, driversBasicDetails[idx].LicenseNumber) {
				mvrToUse = pointer_utils.ToPointer(mvrD)
			}
		}

		if mvrToUse != nil {
			if mvrToUse.ErrorVal != nil {
				mvrDetails.CdlNumber = mvrToUse.LicenseNumber
				mvrDetails.MvrPullStatus = pointer_utils.ToPointer(oapi_common.MVRPullStatusError)
				mvrDetails.MvrPullError = pointer_utils.ToPointer(mvrToUse.ErrorVal.Error())
			} else {
				mvrDetails.MvrPullStatus = pointer_utils.ToPointer(oapi_common.MVRPullStatusSuccess)
				mvrDetails.MvrPullError = nil
				if mvrToUse.DateIssued != nil {
					mvrDetails.IssueDate = pointer_utils.ToPointer(openapi_types.Date{Time: *mvrToUse.DateIssued})
				}
				if mvrToUse.DateExpiry != nil {
					mvrDetails.ExpirationDate = pointer_utils.ToPointer(openapi_types.Date{Time: *mvrToUse.DateExpiry})
				}
				mvrDetails.CdlStatus = pointer_utils.ToPointer(mvrToUse.LicenseStatus)
				mvrDetails.CdlClass = pointer_utils.ToPointer(mvrToUse.LicenseClass)
				mvrDetails.CdlNumber = mvrToUse.LicenseNumber
				mvrDetails.StreetAddr = mvrToUse.StreetAddr
				mvrDetails.CityStateZip = mvrToUse.CityStateZip
				var vs []oapi_common.DriverViolation
				for _, v := range mvrToUse.Violations {
					var date *openapi_types.Date
					if v.GenericViolationData.ViolationDate != nil {
						date = &openapi_types.Date{Time: v.GenericViolationData.ViolationDate.AsTime()}
					}

					isMovingViolation := v.NFViolationData.IsMovingViolation()
					points := v.GetViolationPoints(programType)
					vs = append(vs, oapi_common.DriverViolation{
						AtFaultViolation:                     pointer_utils.Bool(v.NFViolationData.IsAtFaultViolation()),
						Code:                                 pointer_utils.String(v.GenericViolationData.AssignedViolationCode),
						Description:                          pointer_utils.String(v.NFViolationData.GetDescription()),
						Date:                                 date,
						MovingViolation:                      pointer_utils.Bool(isMovingViolation),
						Points:                               pointer_utils.Int(points),
						RecklessDrivingMobileDeviceViolation: pointer_utils.Bool(v.NFViolationData.IsRecklessDrivingMobileDeviceViolation()),
						SevereViolation:                      pointer_utils.Bool(v.NFViolationData.IsSevereViolation()),
						Type:                                 pointer_utils.String(v.NFViolationData.GetViolationTypeString()),
						IsExpired:                            pointer_utils.Bool(v.PointsExpired),
					})
				}
				mvrDetails.Violations = &vs
				mvrDetails.ViolationCount = len(vs)
				mvrDetails.ViolationPoints = mvrToUse.GetDriverScore()
			}
		}

		// Append the mvr details to the return value
		// If the MVR details are not present, we append with empty values for mvrDetails
		retval = append(retval, mvrDetails)
	}

	return retval
}

func (dp *DriversPanel[T]) Drivers(
	ctx context.Context,
	input *base_panel.PanelInput[T],
	fetcherClientFactory data_fetching.FetcherClientFactory,
	// This is a temporary flag added for backward compatibility till we migrate all the MVRs to the drivers info column
	fetchFromAppReviewDriversInfo bool,
) ([]oapi_uw.ApplicationReviewDriverRecord, error) {
	var drivers []oapi_uw.ApplicationReviewDriverRecord
	var mvrDetails []MVRDetails

	var driverBasicDetails []application.DriverBasicDetails
	for _, d := range input.Submission.Info.GetDriverDetails() {
		driverBasicDetails = append(driverBasicDetails, d.DriverBasicDetails)
	}

	// Fetch MVR details only if the MVR pulled flag is set by UW
	if input.ApplicationReview.IsMVRPulled() {
		mvrDetails = GetMVRDetails(
			ctx,
			driverBasicDetails,
			input.ApplicationReview.GetEffectiveDate(),
			input.Application.ProgramType,
			input.Submission.Info.GetState(),
			fetcherClientFactory,
		)
	}

	ownerDetails := input.Submission.Info.GetBusinessOwnerDetails()
	ownerDOB, err := time_utils.DateFromString(ownerDetails.DateOfBirth)
	if err != nil {
		return nil, err
	}
	isOwnerDriverOnPolicy := ownerDetails.DriverOnPolicy != nil && *ownerDetails.DriverOnPolicy

	for i, d := range input.Submission.Info.GetDriverDetails() {
		driverInfo := oapi_uw.ApplicationReviewDriverRecord{
			CdlNumber:    d.LicenseNumber,
			DateOfBirth:  openapi_types.Date{Time: d.DateOfBirth},
			DateOfHire:   openapi_types.Date{Time: d.DateOfHire},
			DlNumber:     d.LicenseNumber,
			Name:         strings.TrimSpace(d.FirstName) + " " + strings.TrimSpace(d.LastName),
			State:        d.LicenseState,
			YearsOfExp:   pointer_utils.Int(d.YearsOfExp),
			IsOutOfState: pointer_utils.ToPointer(d.IsOutOfState),
		}
		overrides := input.ApplicationReview.GetOverrides()
		if overrides.ExcludedDrivers != nil &&
			overrides.ExcludedDrivers[d.LicenseNumber] {
			driverInfo.IsExcluded = true
		}
		// We check both if the map is nil and if the driver is in overrides, if not we use the default
		if overrides.OutOfStateDrivers != nil {
			if _, ok := overrides.OutOfStateDrivers[d.LicenseNumber]; ok {
				driverInfo.IsOutOfState = pointer_utils.ToPointer(overrides.OutOfStateDrivers[d.LicenseNumber])
			}
		}

		driverInfo.IsOwner = nf_driver.IsOwnerAndDriverSame(
			ownerDetails.FirstName,
			ownerDetails.LastName,
			ownerDOB,
			d.FirstName,
			d.LastName,
			time_utils.DateFromTime(d.DateOfBirth)) && isOwnerDriverOnPolicy

		// Doing index matching here to get the MVR details for the driver
		// This doesn't cause any issues as the drivers are in the same order
		if mvrDetails != nil && len(mvrDetails) > i {
			// TODO: REMOVE THIS FROM HERE AS THIS IS CONVERSION OF OAPI FROM DB
			// SHOULD BE AT THE HANDLER LEVEL OR WE SHOULD CREATE A NEW BUSINESS LOGIC BASED OBJECT
			driverInfo.MvrPullStatus = mvrDetails[i].MvrPullStatus
			driverInfo.MvrPullError = mvrDetails[i].MvrPullError
			driverInfo.IssueDate = mvrDetails[i].IssueDate
			driverInfo.ExpirationDate = mvrDetails[i].ExpirationDate
			driverInfo.CdlStatus = mvrDetails[i].CdlStatus
			driverInfo.CdlClass = mvrDetails[i].CdlClass
			driverInfo.Violations = mvrDetails[i].Violations
			driverInfo.ViolationCount = pointer_utils.Int(mvrDetails[i].ViolationCount)
			driverInfo.ViolationPoints = pointer_utils.Int(mvrDetails[i].ViolationPoints)
			driverInfo.AddressLine1 = mvrDetails[i].StreetAddr
			driverInfo.AddressLine2 = mvrDetails[i].CityStateZip
		}

		if fetchFromAppReviewDriversInfo {
			vios, vc, vp, err := ConvertViolationsToOAPI(d.LicenseNumber, input.ApplicationReview.GetDriversInfo())
			if err != nil {
				return nil, errors.Wrap(err, "unable to convert violations to OAPI")
			}
			if vios != nil {
				driverInfo.Violations = &vios
			}
			if vc != -1 && input.ApplicationReview.IsMVRPulled() {
				driverInfo.ViolationCount = pointer_utils.Int(vc)
			}
			if vp != -1 && input.ApplicationReview.IsMVRPulled() {
				driverInfo.ViolationPoints = pointer_utils.Int(vp)
			}
		}
		drivers = append(drivers, driverInfo)
	}
	return drivers, nil
}

func (dp *DriversPanel[T]) UpdateDriver(
	ctx context.Context,
	input *base_panel.PanelInput[T],
	dlNumber string,
	driverUpdateForm *oapi_uw.UpdateApplicationReviewDriverRecordForm,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) error {
	driversInfo := input.ApplicationReview.GetDriversInfo()
	if driversInfo != nil {
		for licenseNumber, d := range driversInfo.Drivers {
			if strings.EqualFold(licenseNumber, dlNumber) {
				d.IsExcluded = driverUpdateForm.IsExcluded
				d.IsOutOfState = driverUpdateForm.IsOutOfState
				driversInfo.Drivers[licenseNumber] = d
			}
		}
	}

	if err := dp.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		input.ApplicationReview.GetID().String(),
		func(appReview app_review.ApplicationReview) (app_review.ApplicationReview, error) {
			appReview.UpdateDriversInfo(driversInfo)
			err := appReview.UpdateOverrides(
				func(override *app_review.Overrides) (app_review.Overrides, error) {
					if override.ExcludedDrivers == nil {
						override.ExcludedDrivers = make(map[string]bool)
					}
					override.ExcludedDrivers[dlNumber] = driverUpdateForm.IsExcluded

					if override.OutOfStateDrivers == nil {
						override.OutOfStateDrivers = make(map[string]bool)
					}
					override.OutOfStateDrivers[dlNumber] = driverUpdateForm.IsOutOfState
					return *override, nil
				})
			if err != nil {
				return nil, err
			}
			return appReview, nil
		},
	); err != nil {
		return errors.Wrap(err, "unable to update application")
	}

	// If the driver is excluded, we want to recalculate the
	// moving violations and update the application review overrides
	if input.ApplicationReview.IsMVRPulled() {
		appReview, err := dp.deps.AppReviewWrapper.GetAppReviewByID(ctx, input.ApplicationReview.GetID().String())
		if err != nil {
			return errors.Wrap(err, "unable to get application review")
		}
		// Fetch app review again to get the latest overrides
		input.ApplicationReview = appReview
		err = dp.SetPullMVRAndUpdateOverrides(ctx, input, fetcherClientFactory)
		if err != nil {
			return errors.Wrap(err, "unable to re-pull mvr")
		}
	}
	return nil
}

func (dp *DriversPanel[T]) SetPullMVRAndUpdateOverrides(
	ctx context.Context,
	input *base_panel.PanelInput[T],
	fetcherClientFactory data_fetching.FetcherClientFactory,
) error {
	overrides := input.ApplicationReview.GetOverrides()
	var err error
	var updatedDriverRecords []driverRecord
	if input.Application.ProgramType == policy_enums.ProgramTypeNonFleetAdmitted {
		var driverBasicDetails []application.DriverBasicDetails
		for _, d := range input.Submission.Info.GetDriverDetails() {
			driverBasicDetails = append(driverBasicDetails, d.DriverBasicDetails)
		}

		_, driverRecords, err := GetDriverVioOverridesAndInfo(
			ctx,
			input.ApplicationReview.GetEffectiveDate(),
			filterDrivers(driverBasicDetails, overrides.ExcludedDrivers),
			input.Application.ProgramType,
			input.Application.Info.GetState(),
			fetcherClientFactory,
		)
		if err != nil {
			return errors.Wrap(err, "failed to get driver violation overrides")
		}

		for licenseNumber, d := range input.ApplicationReview.GetDriversInfo().Drivers {
			var vios []ViolationRecord
			for _, dr := range driverRecords {
				if strings.EqualFold(licenseNumber, dr.licenseNumber) {
					vios = dr.violations
				}
			}
			updatedDriverRecords = append(updatedDriverRecords, driverRecord{
				licenseNumber:      licenseNumber,
				violations:         vios,
				isDriverExcluded:   d.IsExcluded,
				isDriverOutOfState: d.IsOutOfState,
			})
		}
	}

	appReviewDriversInfo := input.ApplicationReview.GetDriversInfo()
	updatedDriverRecords = getUniqueDriverRecords(appReviewDriversInfo, updatedDriverRecords, false)
	appReviewDriversInfo, err = updateAppReviewDriversInfo(
		input.ApplicationReview.GetEffectiveDate(),
		appReviewDriversInfo,
		updatedDriverRecords,
	)
	if err != nil {
		return errors.Wrap(err, "failed to get updated drivers info")
	}

	updatedDriverOverrides, err := getDriverOverridesV2(*appReviewDriversInfo)
	if err != nil {
		log.Error(ctx, "failed to get driver violation overrides",
			log.String("app_review_id", input.ApplicationReview.GetID().String()),
			log.Err(err))
		return errors.Wrap(err,
			"failed to get driver violation overrides")
	}

	err = updateDriverInfoAndOverridesInDB(
		ctx,
		dp.deps.AppReviewWrapper,
		input.ApplicationReview.GetID(),
		*updatedDriverOverrides,
		appReviewDriversInfo,
		true,
	)
	if err != nil {
		return errors.Wrapf(err, "unable to update app review %s", input.ApplicationReview.GetID())
	}
	return nil
}

type DriverViolationOverrides struct {
	ViolationPoints       *map[string]int64
	CountViolationClassA  *int64
	CountViolationClassB  *int64
	CountViolationClassC  *int64
	CountViolationClassD  *int64
	CountViolationClassE  *int64
	CountViolationClassF  *int64
	CountViolationClassN  *int64
	CountViolationClassNA *int64
	DriverViolations      *[]admitted_app.DriverViolation
}

func filterDrivers(
	drivers []application.DriverBasicDetails,
	excludedDrivers map[string]bool,
) []application.DriverBasicDetails {
	var retval []application.DriverBasicDetails
	for _, d := range drivers {
		if excludedDrivers != nil && excludedDrivers[d.LicenseNumber] {
			continue
		} else {
			retval = append(retval, d)
		}
	}
	return retval
}

func GetDriverVioOverridesAndInfo(
	ctx context.Context,
	effectiveDate time.Time,
	driversBasicDetails []application.DriverBasicDetails,
	programType policy_enums.ProgramType,
	usState us_states.USState,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) (DriverViolationOverrides, []driverRecord, error) {
	var retval DriverViolationOverrides

	var driverRecords []driverRecord
	var drivers []admitted_app.DriverDetails
	for _, d := range driversBasicDetails {
		drivers = append(drivers, admitted_app.DriverDetails{
			DriverBasicDetails: d,
		})
	}
	mvrViolations, reports, errs := mvr.GetMovingViolationCountsForDrivers(
		ctx,
		drivers,
		effectiveDate,
		programType,
		usState,
		fetcherClientFactory,
	)

	for _, d := range mvrViolations {
		var driverDetails admitted_app.DriverDetails
		for _, driverDetail := range drivers {
			if strings.EqualFold(d.LicenseNumber, driverDetail.LicenseNumber) {
				driverDetails = driverDetail
				break
			}
		}

		var vios []ViolationRecord
		for _, v := range d.Violations {
			vios = append(vios, ViolationRecord{
				Code:            v.GenericViolationData.AssignedViolationCode,
				Date:            v.GenericViolationData.ViolationDate.AsTime(),
				IsManuallyAdded: false,
			})
		}
		driverRecords = append(driverRecords, driverRecord{
			licenseNumber: driverDetails.LicenseNumber,
			violations:    vios,
		})
	}

	pointsPerDriver := calculateViolationPointsForDrivers(
		mvrViolations,
		driversBasicDetails,
	)
	retval.ViolationPoints = &pointsPerDriver

	aggregatePGRVios, err := mvr.GetAggregatePGRCompanyViolations(
		ctx,
		driversBasicDetails,
		reports,
		errs,
		effectiveDate,
		programType,
	)
	if err != nil {
		return retval, driverRecords, errors.Wrap(err, "Couldn't get prg violations")
	}

	violationClasses := []string{"A", "B", "C", "D", "E", "F", "N", "NA"}
	for _, class := range violationClasses {
		if v, ok := aggregatePGRVios[class]; ok {
			violations := int64(v)
			switch class {
			case "A":
				retval.CountViolationClassA = &violations
			case "B":
				retval.CountViolationClassB = &violations
			case "C":
				retval.CountViolationClassC = &violations
			case "D":
				retval.CountViolationClassD = &violations
			case "E":
				retval.CountViolationClassE = &violations
			case "F":
				retval.CountViolationClassF = &violations
			case "N":
				retval.CountViolationClassN = &violations
			case "NA":
				retval.CountViolationClassNA = &violations
			}
		}
	}

	pgrViolationsPerDriver, err := mvr.GetPGRCompanyViolationsPerDriver(
		ctx,
		driversBasicDetails,
		reports,
		errs,
		effectiveDate,
		programType,
	)
	if err != nil {
		return retval, driverRecords, errors.Wrap(err, "Couldn't get pgr violations per driver")
	}

	driverVios := make([]admitted_app.DriverViolation, 0)
	for _, d := range driversBasicDetails {
		classCounts := make(map[string]int64)
		for licenseNumber, pgrVio := range pgrViolationsPerDriver {
			if strings.EqualFold(licenseNumber, d.LicenseNumber) {
				classCounts = pgrVio.ClassCounts
				break
			}
		}
		points := pointsPerDriver[d.LicenseNumber]
		driverVios = append(driverVios, admitted_app.DriverViolation{
			LicenseNumber:   d.LicenseNumber,
			ViolationPoints: points,
			ClassCounts:     classCounts,
		})
	}

	retval.DriverViolations = pointer_utils.ToPointer(driverVios)

	return retval, driverRecords, nil
}

func calculateViolationPointsForDrivers(
	mvrViolations []mvr.DriverViolationRecord,
	driverRecords []application.DriverBasicDetails,
) map[string]int64 {
	points := make(map[string]int64)

	for i, d := range driverRecords {
		if mvrViolations != nil {
			if len(mvrViolations) < i {
				break
			}

			var mvrViolationRecordToUse mvr.DriverViolationRecord
			for _, violation := range mvrViolations {
				if strings.EqualFold(violation.LicenseNumber, d.LicenseNumber) {
					mvrViolationRecordToUse = violation
					break
				}
			}

			// GetDriverScore method accounts for the additional points logic.
			// If we have a driver with multiple violations of the same code, we don't
			// simply add the points, we add the points for the first violation and then
			// add the additional points for the rest of the violations. This helps in penalizing
			// the driver more for repeated violations.
			totalPoints := mvrViolationRecordToUse.GetDriverScore()
			points[d.LicenseNumber] = int64(totalPoints)
		}
	}

	return points
}

func RepullMVRAndPersistInOverrides(
	ctx context.Context,
	admittedAppWrapper application.Wrapper[*admitted_app.AdmittedApp],
	nfApplicationReviewWrapper app_review.Wrapper,
	ffClient feature_flag_lib.Client,
	appReview app_review.ApplicationReview,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) error {
	// If UW hasn't pressed the pull MVR button, don't pull MVR
	if !appReview.IsMVRPulled() {
		return nil
	}

	// TODO: Remove this Feature Flag check once we migrate to the
	// new logic of giving UW flexibility to override the violations
	flagEnabled, err := ffClient.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(authz.UserFromContext(ctx)),
		feature_flag_lib.FeatureMVROverrides,
		false,
	)
	if err != nil {
		// Log the error and continue, we don't want to block the process
		log.Warn(ctx, "Unable to check flag", log.Err(err))
	}

	// If flag is enabled, don't pull MVR & directly use the previously persisted overrides
	if flagEnabled {
		return nil
	}

	appObj, err := admittedAppWrapper.GetAppById(ctx, appReview.GetApplicationID())
	if err != nil {
		return errors.Wrapf(err, "unable to get app %s", appReview.GetApplicationID())
	}

	var driverBasicDetails []application.DriverBasicDetails
	for _, d := range appObj.Info.GetDriverDetails() {
		driverBasicDetails = append(driverBasicDetails, d.DriverBasicDetails)
	}

	// Re-pull MVR & set the overrides in app review
	_, driverRecords, err := GetDriverVioOverridesAndInfo(
		ctx,
		appReview.GetEffectiveDate(),
		filterDrivers(driverBasicDetails, appReview.GetOverrides().ExcludedDrivers),
		appObj.ProgramType,
		appObj.Info.GetState(),
		fetcherClientFactory,
	)
	if err != nil {
		return errors.Wrapf(err, "failed to get driver violation overrides")
	}

	var updatedDriverRecords []driverRecord
	for licenseNumber, d := range appReview.GetDriversInfo().Drivers {
		var vios []ViolationRecord
		for _, dr := range driverRecords {
			if strings.EqualFold(licenseNumber, dr.licenseNumber) {
				vios = dr.violations
			}
		}
		updatedDriverRecords = append(updatedDriverRecords, driverRecord{
			licenseNumber:      licenseNumber,
			violations:         vios,
			isDriverExcluded:   d.IsExcluded,
			isDriverOutOfState: d.IsOutOfState,
		})
	}

	appReviewDriversInfo := appReview.GetDriversInfo()
	updatedDriverRecords = getUniqueDriverRecords(appReviewDriversInfo, updatedDriverRecords, false)

	appReviewDriversInfo, err = updateAppReviewDriversInfo(
		appReview.GetEffectiveDate(),
		appReviewDriversInfo,
		updatedDriverRecords,
	)
	if err != nil {
		return errors.Wrap(err, "failed to get updated drivers info")
	}

	updatedDriverOverrides, err := getDriverOverridesV2(*appReviewDriversInfo)
	if err != nil {
		log.Error(ctx, "failed to get driver violation overrides",
			log.String("app_review_id", appReview.GetID().String()),
			log.Err(err))
		return errors.Wrap(err,
			"failed to get driver violation overrides")
	}

	err = updateDriverInfoAndOverridesInDB(
		ctx,
		nfApplicationReviewWrapper,
		appReview.GetID(),
		*updatedDriverOverrides,
		appReviewDriversInfo,
		true,
	)
	if err != nil {
		return errors.Wrapf(err, "unable to update app review %s", appReview.GetID())
	}
	return nil
}

func getDriverOverridesV2(
	appReviewDriversInfo app_review.DriversInfo,
) (*DriverViolationOverrides, error) {
	// calculate overrides
	aggPGRClassMap, err := GetAggPGRClassViolationMap(appReviewDriversInfo)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get agg pgr class violation map")
	}

	var driverViolations []admitted_app.DriverViolation
	violationPointsOverrides := make(map[string]int64)
	for licenseNumber, d := range appReviewDriversInfo.Drivers {
		if d.IsExcluded {
			continue
		}
		totalViolPoints := 0
		for _, v := range d.Violations {
			totalViolPoints += v.ActualPoints
		}

		driverViolations = append(driverViolations, admitted_app.DriverViolation{
			LicenseNumber:   licenseNumber,
			ViolationPoints: int64(totalViolPoints),
			ClassCounts:     d.ViolationClassCounts,
		})
		violationPointsOverrides[licenseNumber] = int64(totalViolPoints)
	}

	return &DriverViolationOverrides{
		DriverViolations:      &driverViolations,
		ViolationPoints:       &violationPointsOverrides,
		CountViolationClassA:  pointer_utils.ToPointer(aggPGRClassMap["A"]),
		CountViolationClassB:  pointer_utils.ToPointer(aggPGRClassMap["B"]),
		CountViolationClassC:  pointer_utils.ToPointer(aggPGRClassMap["C"]),
		CountViolationClassD:  pointer_utils.ToPointer(aggPGRClassMap["D"]),
		CountViolationClassE:  pointer_utils.ToPointer(aggPGRClassMap["E"]),
		CountViolationClassF:  pointer_utils.ToPointer(aggPGRClassMap["F"]),
		CountViolationClassN:  pointer_utils.ToPointer(aggPGRClassMap["N"]),
		CountViolationClassNA: pointer_utils.ToPointer(aggPGRClassMap["NA"]),
	}, nil
}

func updateDriverInfoAndOverridesInDB(
	ctx context.Context,
	appReviewWrapper app_review.Wrapper,
	appReviewId uuid.UUID,
	driverViolationsOverrides DriverViolationOverrides,
	appReviewDriversInfo *app_review.DriversInfo,
	shouldSetMVRPulled bool,
) error {
	err := appReviewWrapper.UpdateAppReview(
		ctx,
		appReviewId.String(),
		func(review app_review.ApplicationReview) (app_review.ApplicationReview, error) {
			if shouldSetMVRPulled {
				review.SetMVRPulled(true)
			}
			review.UpdateDriversInfo(appReviewDriversInfo)
			err := review.UpdateOverrides(
				func(override *app_review.Overrides) (app_review.Overrides, error) {
					override.DriverViolations = driverViolationsOverrides.DriverViolations
					override.ViolationPoints = driverViolationsOverrides.ViolationPoints
					override.CountViolationClassA = driverViolationsOverrides.CountViolationClassA
					override.CountViolationClassB = driverViolationsOverrides.CountViolationClassB
					override.CountViolationClassC = driverViolationsOverrides.CountViolationClassC
					override.CountViolationClassD = driverViolationsOverrides.CountViolationClassD
					override.CountViolationClassE = driverViolationsOverrides.CountViolationClassE
					override.CountViolationClassF = driverViolationsOverrides.CountViolationClassF
					override.CountViolationClassN = driverViolationsOverrides.CountViolationClassN
					override.CountViolationClassNA = driverViolationsOverrides.CountViolationClassNA
					return *override, nil
				})
			if err != nil {
				return nil, errors.Wrapf(err, "unable to update app review %s", appReviewId)
			}
			return review, nil
		})
	if err != nil {
		return errors.Wrapf(err, "unable to update app review %s", appReviewId)
	}
	return nil
}

func updateAppReviewDriversInfo(
	effectiveDate time.Time,
	originalAppReviewDriversInfo *app_review.DriversInfo,
	driverRecords []driverRecord,
) (*app_review.DriversInfo, error) {
	if originalAppReviewDriversInfo == nil || originalAppReviewDriversInfo.Drivers == nil {
		drivers := make(map[string]app_review.Driver)
		originalAppReviewDriversInfo = &app_review.DriversInfo{
			Drivers: drivers,
		}
	}
	for _, d := range driverRecords {
		violations, err := GetApplicationReviewViolations(effectiveDate, d.violations)
		if err != nil {
			return originalAppReviewDriversInfo, errors.Wrap(err, "unable to get violations")
		}

		// calculate driver total violation points and count
		classCountsMap, err := GetPGRClassViolationMap(d.isDriverExcluded, violations)
		if err != nil {
			return originalAppReviewDriversInfo, errors.Wrap(err, "failed to get pgr class violation map")
		}

		if driverInfo, exists := originalAppReviewDriversInfo.Drivers[d.licenseNumber]; exists {
			// If driver is already existing, update the information
			driverInfo.IsExcluded = d.isDriverExcluded
			driverInfo.IsOutOfState = d.isDriverOutOfState
			driverInfo.Violations = violations
			driverInfo.ViolationClassCounts = classCountsMap

			originalAppReviewDriversInfo.Drivers[d.licenseNumber] = driverInfo
		} else {
			// If driver is not present, add a new entry
			originalAppReviewDriversInfo.Drivers[d.licenseNumber] = app_review.Driver{
				IsExcluded:           d.isDriverExcluded,
				IsOutOfState:         d.isDriverOutOfState,
				Violations:           violations,
				ViolationClassCounts: classCountsMap,
			}
		}
	}
	return originalAppReviewDriversInfo, nil
}

type driverRecord struct {
	licenseNumber      string
	violations         []ViolationRecord
	isDriverExcluded   bool
	isDriverOutOfState bool
}

func (dp *DriversPanel[T]) UpdateDriverV2(
	ctx context.Context,
	appReviewId uuid.UUID,
	driverLicenseNumber string,
	isDriverExcluded bool,
	isDriverOutOfState bool,
	violationsRecords []ViolationRecord,
) error {
	appReview, err := dp.deps.AppReviewWrapper.GetAppReviewByID(ctx, appReviewId.String())
	if err != nil {
		return errors.Wrap(err, "unable to get application review")
	}

	appObj, err := dp.deps.AdmittedWrapper.GetAppById(ctx, appReview.GetApplicationID())
	if err != nil {
		return errors.Wrap(err, "unable to get application review")
	}

	driversInfo := appReview.GetDriversInfo()
	if driversInfo != nil {
		for licenseNumber, d := range driversInfo.Drivers {
			if strings.EqualFold(licenseNumber, driverLicenseNumber) {
				d.IsExcluded = isDriverExcluded
				d.IsOutOfState = isDriverOutOfState
				driversInfo.Drivers[licenseNumber] = d
			}
		}
	}

	if err := dp.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReview.GetID().String(),
		func(appReview app_review.ApplicationReview) (app_review.ApplicationReview, error) {
			appReview.UpdateDriversInfo(driversInfo)
			err := appReview.UpdateOverrides(
				func(override *app_review.Overrides) (app_review.Overrides, error) {
					if override.ExcludedDrivers == nil {
						override.ExcludedDrivers = make(map[string]bool)
					}
					override.ExcludedDrivers[driverLicenseNumber] = isDriverExcluded

					if override.OutOfStateDrivers == nil {
						override.OutOfStateDrivers = make(map[string]bool)
					}
					override.OutOfStateDrivers[driverLicenseNumber] = isDriverOutOfState
					return *override, nil
				})
			if err != nil {
				return nil, err
			}
			return appReview, nil
		},
	); err != nil {
		log.Error(ctx, "unable to update application review",
			log.String("app_review_id", appReview.GetID().String()),
			log.Err(err))
		return errors.Wrap(err, "unable to update application review")
	}

	appReview, err = dp.deps.AppReviewWrapper.GetAppReviewByID(ctx, appReviewId.String())
	if err != nil {
		return errors.Wrap(err, "unable to get application review")
	}

	var driverRecords []driverRecord
	// RePull MVR if MVR is already pulled
	admittedDrivers := getAdmittedAppDriverDetails(appObj.Info.GetDriverDetails())
	if appReview.IsMVRPulled() {
		driverMVRRecords, err := getDriverRecordsFromMVR(
			ctx,
			dp.deps.FetcherClientFactory,
			appReview.GetEffectiveDate(),
			appObj.Info.GetState(),
			filterAdmittedAppDrivers(admittedDrivers, appReview.GetOverrides().ExcludedDrivers))
		if err != nil {
			// We silently fail this to allow the underwriter to add violations manually
			log.Error(ctx, "failed to get driver records from MVR", log.Err(err))
		}
		driverRecords = append(driverRecords, driverMVRRecords...)
	}

	var dr *driverRecord
	for _, d := range driverRecords {
		if strings.EqualFold(d.licenseNumber, driverLicenseNumber) {
			dr = &d
		}
	}

	if dr == nil {
		driverRecords = append(driverRecords, driverRecord{
			licenseNumber:      driverLicenseNumber,
			violations:         violationsRecords,
			isDriverExcluded:   isDriverExcluded,
			isDriverOutOfState: isDriverOutOfState,
		})
	} else {
		violationsRecords = append(violationsRecords, dr.violations...)
		dr.isDriverExcluded = isDriverExcluded
		dr.isDriverOutOfState = isDriverOutOfState
		dr.violations = violationsRecords
	}

	driverRecords = getUniqueDriverRecords(appReview.GetDriversInfo(), driverRecords, true)

	appReviewDriversInfo := appReview.GetDriversInfo()

	appReviewDriversInfo, err = updateAppReviewDriversInfo(appReview.GetEffectiveDate(), appReviewDriversInfo, driverRecords)
	if err != nil {
		return errors.Wrap(err, "failed to get drivers info")
	}

	driverViolationsOverrides, err := getDriverOverridesV2(
		*appReviewDriversInfo)
	if err != nil {
		return errors.Wrap(err, "failed to get overrides")
	}

	err = updateDriverInfoAndOverridesInDB(
		ctx,
		dp.deps.AppReviewWrapper,
		appReview.GetID(),
		*driverViolationsOverrides,
		appReviewDriversInfo,
		false)
	if err != nil {
		return errors.Wrap(err, "failed to update overrides")
	}
	return nil
}

func getDriverRecordsFromMVR(
	ctx context.Context,
	fetcherCle data_fetching.FetcherClientFactory,
	effectiveDate time.Time,
	usState us_states.USState,
	driverDetails []admitted_app.DriverDetails,
) ([]driverRecord, error) {
	mvrViolations, _, errs := mvr.GetMovingViolationCountsForDrivers(
		ctx,
		driverDetails,
		effectiveDate,
		policy_enums.ProgramTypeNonFleetAdmitted,
		usState,
		fetcherCle,
	)
	if errs != nil {
		return nil, errors.Wrap(errs[0], "failed to get mvr violations")
	}
	var driverRecords []driverRecord
	for i, d := range driverDetails {
		if mvrViolations != nil {
			if len(mvrViolations) < i {
				break
			}

			var violationRecords []ViolationRecord
			for _, violation := range mvrViolations {
				if strings.EqualFold(violation.LicenseNumber, d.LicenseNumber) {
					for _, v := range violation.Violations {
						violationRecords = append(violationRecords, ViolationRecord{
							Code:            v.NFViolationData.GetViolationCode(),
							Date:            v.GenericViolationData.ViolationDate.AsTime(),
							IsManuallyAdded: false,
							IsExpired:       v.PointsExpired,
						})
					}
				}
			}

			driverRecords = append(driverRecords, driverRecord{
				licenseNumber:      d.LicenseNumber,
				violations:         violationRecords,
				isDriverExcluded:   !d.IsIncluded,
				isDriverOutOfState: d.IsOutOfState,
			})
		}
	}
	return driverRecords, nil
}
