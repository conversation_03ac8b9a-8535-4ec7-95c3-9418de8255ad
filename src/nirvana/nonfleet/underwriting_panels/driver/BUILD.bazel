load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "driver",
    srcs = [
        "deps.go",
        "driver.go",
        "fx.go",
        "utils.go",
        "violation.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/driver",
        "//nirvana/nonfleet/underwriting_panels/base_panel",
        "//nirvana/nonfleet/underwriting_panels/enums",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/rating/mvr",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "driver_test",
    srcs = [
        "driver_test.go",
        "violation_test.go",
    ],
    embed = [":driver"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/infra/fx/testloader",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
        "@tools_gotest//assert",
    ],
)
