package driver

import (
	"go.uber.org/fx"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	admitted "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

type Deps[T nf_app.AppInfo] struct {
	fx.In

	AppReviewWrapper     nf_app_review.Wrapper
	FetcherClientFactory data_fetching.FetcherClientFactory
	AdmittedWrapper      nf_app.Wrapper[*admitted.AdmittedApp]
}
