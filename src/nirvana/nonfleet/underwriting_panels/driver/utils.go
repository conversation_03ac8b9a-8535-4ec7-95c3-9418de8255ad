package driver

import (
	"strings"

	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
)

func getAdmittedAppDriverDetails(drivers []application.DriverDetails) []admitted_app.DriverDetails {
	var driverDetails []admitted_app.DriverDetails
	for _, d := range drivers {
		driverDetails = append(driverDetails, admitted_app.DriverDetails{
			DriverBasicDetails: d.DriverBasicDetails,
			YearsOfExp:         d.YearsOfExp,
			IsIncluded:         d.IsIncluded,
			IsOutOfState:       d.IsOutOfState,
		})
	}
	return driverDetails
}

func filterAdmittedAppDrivers(
	drivers []admitted_app.DriverDetails,
	excludedDrivers map[string]bool,
) []admitted_app.DriverDetails {
	var retval []admitted_app.DriverDetails
	for _, d := range drivers {
		if excludedDrivers != nil && excludedDrivers[d.LicenseNumber] {
			continue
		} else {
			retval = append(retval, d)
		}
	}
	return retval
}

// getUniqueDriverRecords returns the unique driver records to be
// added to the application review drivers info column
func getUniqueDriverRecords(
	driversInfo *app_review.DriversInfo,
	drivers []driverRecord,
	isManuallyAdded bool,
) []driverRecord {
	uniqueDriverRecords := make(map[string]driverRecord)

	// Add all driver records that we want to add from the input slice to the map
	for _, d := range drivers {
		uniqueDriverRecords[d.licenseNumber] = d
	}

	// If driversInfo or its Drivers map is nil, return the original drivers slice
	if driversInfo == nil || driversInfo.Drivers == nil {
		return drivers
	}

	// Add or merge driver records from driversInfo
	for licenseNumber, d := range driversInfo.Drivers {
		var existingViolations []ViolationRecord
		for _, v := range d.Violations {
			existingViolations = append(existingViolations, ViolationRecord{
				Code:            v.Code,
				Date:            v.Date,
				IsManuallyAdded: v.IsManuallyAdded,
				CreatedBy:       v.CreatedBy,
			})
		}

		found := false
		for l := range uniqueDriverRecords {
			// if the driver from the drivers info column is already in the unique driver records, merge the violations
			// We only want to merge the violations if the violation they differ in isManuallyAdded flag
			if strings.EqualFold(l, licenseNumber) {
				found = true
				driverToBeAdded := uniqueDriverRecords[licenseNumber]
				var vios []ViolationRecord
				for _, v := range existingViolations {
					if v.IsManuallyAdded != isManuallyAdded {
						vios = append(vios, v)
					}
				}
				driverToBeAdded.violations = append(driverToBeAdded.violations, vios...)
				uniqueDriverRecords[licenseNumber] = driverToBeAdded
			}
		}

		if !found {
			// Add the driver from drivers info to the unique driver records
			uniqueDriverRecords[licenseNumber] = driverRecord{
				licenseNumber:      licenseNumber,
				isDriverExcluded:   d.IsExcluded,
				isDriverOutOfState: d.IsOutOfState,
				violations:         existingViolations,
			}
		}
	}

	// Convert map to slice
	retval := make([]driverRecord, 0, len(uniqueDriverRecords))
	for _, d := range uniqueDriverRecords {
		retval = append(retval, d)
	}

	return retval
}
