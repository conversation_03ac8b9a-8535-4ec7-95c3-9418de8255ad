package losses

import (
	"go.uber.org/fx"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/underwriting/app_review/widgets/safety/scorev2"
	"nirvanatech.com/nirvana/underwriting/common"
)

type Deps[T nf_app.AppInfo] struct {
	fx.In
	AppReviewWrapper         nf_app_review.Wrapper
	UWSafetyFetcher          *common.UWSafetyFetcher
	SafetyScoreWidgetFactory scorev2.SafetyScoreWidgetFactory
}
