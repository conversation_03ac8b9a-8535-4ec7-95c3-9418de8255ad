load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "losses",
    srcs = [
        "deps.go",
        "fx.go",
        "losses.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/underwriting_panels/losses",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/underwriting_panels/base_panel",
        "//nirvana/nonfleet/underwriting_panels/enums",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/underwriting/app_review/widgets/safety/scorev2",
        "//nirvana/underwriting/common",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_fx//:fx",
    ],
)
