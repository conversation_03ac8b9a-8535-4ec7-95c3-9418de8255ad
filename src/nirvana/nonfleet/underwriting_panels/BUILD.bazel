load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "underwriting_panels",
    srcs = ["admitted_panels_manager.go"],
    importpath = "nirvanatech.com/nirvana/nonfleet/underwriting_panels",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/nonfleet/underwriting_panels/base_panel",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/nonfleet/underwriting_panels/equipments",
        "//nirvana/nonfleet/underwriting_panels/losses",
        "//nirvana/nonfleet/underwriting_panels/operations",
        "//nirvana/nonfleet/underwriting_panels/packages",
        "//nirvana/nonfleet/underwriting_panels/safety",
        "//nirvana/nonfleet/underwriting_panels/summary",
        "@org_uber_go_fx//:fx",
    ],
)
