package base_panel

import (
	"context"

	"github.com/cockroachdb/errors"

	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
)

type PanelInput[T nf_app.AppInfo] struct {
	// todo: remove Application and use Submission everywhere
	Application       *nf_app.Application[T]
	ApplicationReview application_review.ApplicationReview
	Submission        *nf_app.Submission[T]
}

type BasePanel[T nf_app.AppInfo] struct {
	deps Deps[T]
}

func NewBasePanel[T nf_app.AppInfo](deps Deps[T]) *BasePanel[T] {
	return &BasePanel[T]{deps: deps}
}

func (b *BasePanel[T]) GetPanelInput(ctx context.Context, appReviewID string) (*PanelInput[T], error) {
	appReview, err := b.deps.AppReviewWrapper.GetAppReviewByID(ctx, appReviewID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get app review with id: %s", appReview)
	}

	appObj, err := b.deps.AppWrapper.GetAppById(ctx, appReview.GetApplicationID())
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get app with id: %s", appReview.GetApplicationID())
	}

	submissionObj, err := b.deps.AppWrapper.GetSubmissionById(ctx, appReview.GetSubmissionID())
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get Submission with id: %s", appReview.GetSubmissionID())
	}

	return &PanelInput[T]{
		Application:       appObj,
		ApplicationReview: appReview,
		Submission:        submissionObj,
	}, nil
}
