load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "base_panel",
    srcs = [
        "base_panel.go",
        "deps.go",
        "fx.go",
        "interface.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/underwriting_panels/enums",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_fx//:fx",
    ],
)
