package summary

import (
	"go.uber.org/fx"
	non_fleet_experiment "nirvanatech.com/nirvana/application/experiments/non_fleet"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/underwriting/scheduler"
)

type Deps[T nf_app.AppInfo] struct {
	fx.In

	AppReviewWrapper       nf_app_review.Wrapper
	AppWrapper             nf_app.Wrapper[T]
	FetcherClientFactory   data_fetching.FetcherClientFactory
	ProcessorClientFactory data_processing.ProcessorClientFactory
	AuthWrapper            auth.DataWrapper
	AgencyWrapper          agency.DataWrapper
	ExperimentManager      *non_fleet_experiment.Manager
	UWScheduler            scheduler.UwScheduler
}
