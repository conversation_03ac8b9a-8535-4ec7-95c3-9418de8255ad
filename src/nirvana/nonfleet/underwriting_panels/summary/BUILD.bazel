load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "summary",
    srcs = [
        "deps.go",
        "fx.go",
        "summary.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/underwriting_panels/summary",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/application/experiments/non_fleet",
        "//nirvana/application/experiments/non_fleet/pre_telematics_quote",
        "//nirvana/common-go/application-util",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/experiments/models",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/underwriting_panels/base_panel",
        "//nirvana/nonfleet/underwriting_panels/enums",
        "//nirvana/openapi-specs/components/nonfleet",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/openapi-specs/components/underwriting",
        "//nirvana/underwriting/scheduler",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@org_uber_go_fx//:fx",
    ],
)
