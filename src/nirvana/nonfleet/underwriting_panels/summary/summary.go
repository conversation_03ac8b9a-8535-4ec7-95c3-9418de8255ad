package summary

import (
	"context"
	"fmt"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"

	"nirvanatech.com/nirvana/common-go/log"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	oapi_types "github.com/oapi-codegen/runtime/types"
	"nirvanatech.com/nirvana/application/experiments/non_fleet/pre_telematics_quote"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"

	applicationutil "nirvanatech.com/nirvana/common-go/application-util"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	nf_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	experiments_models "nirvanatech.com/nirvana/experiments/models"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/enums"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
	nonfleet_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	"nirvanatech.com/nirvana/underwriting/scheduler"
)

type SummaryPanel[T application.AppInfo] struct {
	deps Deps[T]
}

var _ base_panel.Panel[application.AppInfo] = &SummaryPanel[application.AppInfo]{}

func NewSummaryPanel[T application.AppInfo](deps Deps[T]) *SummaryPanel[T] {
	return &SummaryPanel[T]{deps: deps}
}

func (s *SummaryPanel[T]) SetIsReviewed(ctx context.Context, appReviewID string, isReviewed bool) error {
	return nil
}

func (s *SummaryPanel[T]) IsReviewed(input *base_panel.PanelInput[T]) bool {
	return false
}

func (s *SummaryPanel[T]) PanelKey() enums.FlagPanel {
	return enums.Packages
}

func getAppReviewStatus(appReviewState nf_app_review.AppReviewState) nonfleet_uw.ApplicationReviewStatus {
	switch appReviewState {
	case nf_app_review.AppReviewStatePending:
		return nonfleet_uw.UnderUWReview
	case nf_app_review.AppReviewStateApproved:
		return nonfleet_uw.Approved
	case nf_app_review.AppReviewStateDeclined:
		return nonfleet_uw.Declined
	case nf_app_review.AppReviewStateStale:
		return nonfleet_uw.Stale
	case nf_app_review.AppReviewStateClosed:
		return nonfleet_uw.Closed
	case nf_app_review.AppReviewStateReferral:
		return nonfleet_uw.UnderReferralReview
	default:
		return nonfleet_uw.Unhandled
	}
}

func getAppStatus(appState nf_enums.AppState) nonfleet.AppState {
	switch appState {
	case nf_enums.AppStateIncomplete:
		return nonfleet.AppStateIncomplete
	case nf_enums.AppStateComplete:
		return nonfleet.AppStateComplete
	case nf_enums.AppStateQuoteGenerating:
		return nonfleet.AppStateQuoteGenerating
	case nf_enums.AppStateQuoteGenerated:
		return nonfleet.AppStateQuoteGenerated
	case nf_enums.AppStateUnderUWReview:
		return nonfleet.AppStateUnderUWReview
	case nf_enums.AppStateUnderReferralReview:
		return nonfleet.AppStateUnderReferralReview
	case nf_enums.AppStateApproved:
		return nonfleet.AppStateApproved
	case nf_enums.AppStateDeclined:
		return nonfleet.AppStateDeclined
	case nf_enums.AppStatePolicyCreated:
		return nonfleet.AppStatePolicyCreated
	case nf_enums.AppStateClosed:
		return nonfleet.AppStateClosed
	case nf_enums.AppStatePanic:
		return nonfleet.AppStatePanic
	case nf_enums.AppStateBindableQuoteGenerated:
		return nonfleet.AppStateBindableQuoteGenerated
	default:
		return nonfleet.AppStateIncomplete
	}
}

func (s *SummaryPanel[T]) Summary(
	ctx context.Context,
	input *base_panel.PanelInput[T],
	programType policy_enums.ProgramType,
) (*nonfleet_uw.ApplicationReviewSummary, error) {
	if input == nil || input.Application == nil {
		return nil, errors.New("application was nil")
	}

	producer, err := s.deps.AuthWrapper.FetchUserInfo(ctx, input.Application.ProducerID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch producer info for app %s", input.Application.ID.String())
	}

	var uwFullName string
	if input.ApplicationReview.GetUnderwriterID() != uuid.Nil {
		uw, err := s.deps.AuthWrapper.FetchUserInfo(ctx, input.ApplicationReview.GetUnderwriterID())
		if err != nil {
			return nil, errors.Wrapf(err, "failed to fetch underwriter info for app review %s",
				input.ApplicationReview.GetUnderwriterID())
		}
		uwFullName = uw.FullName()
	}

	var phoneNumber string
	if producer.PhoneNumber != nil {
		phoneNumber = *producer.PhoneNumber
	}

	agency, err := s.deps.AgencyWrapper.FetchAgency(ctx, input.Application.AgencyID)
	if err != nil {
		return nil, err
	}

	var tspConnHandleID *string
	if input.Application.Info.GetTSPConnHandleID() != nil {
		tspConnHandleID = pointer_utils.String(input.Application.Info.GetTSPConnHandleID().String())
	}

	tspName := pointer_utils.String("")
	if input.Application.Info.GetTSPName() != nil {
		tspName = pointer_utils.String(input.Application.Info.GetTSPName().String())
	}

	bizOwnerAddress := input.Application.Info.GetAddress()
	mailingAddress := input.Application.Info.GetMailingAddress()

	var panelReviewStatuses []nonfleet_uw.PanelReviewStatus
	panels := []nonfleet_uw.ApplicationReviewFlagPanel{
		nonfleet_uw.Coverages,
		nonfleet_uw.Drivers,
		nonfleet_uw.Equipments,
		nonfleet_uw.Losses,
		nonfleet_uw.Operations,
		nonfleet_uw.Safety,
	}

	for _, panel := range panels {
		panelReviewStatuses = append(panelReviewStatuses, nonfleet_uw.PanelReviewStatus{
			Panel:      panel,
			IsReviewed: input.ApplicationReview.IsPanelReviewed(getPanel(panel)),
		})
	}

	assignees, err := s.GetAssignees(ctx, input)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get assignees")
	}

	var preTelematicsQuoteState nonfleet.PreTelematicsQuoteState
	if input.Application.PreTelematicsQuoteState != nil {
		preTelematicsQuoteState = admitted_app.ConvertToPreTelematicsQuoteStateRest(*input.Application.PreTelematicsQuoteState)
	}

	// Compute duplicate applications via helper.
	duplicateApplications, err := s.getDuplicateApplications(ctx, input)
	if err != nil {
		return nil, err
	}

	// Determine if safety credit override should be allowed, If the application is not part of the Pre-Telematics
	// experiment, we check if any of the duplicate applications are tagged as FlexQuote.
	allowSafetyCreditOverrideDueToPreTelematics := false

	preTelematicsQuoteApplicabilities, err := s.deps.ExperimentManager.GetLatestExperimentApplicabilities(
		ctx,
		pre_telematics_quote.PreQuoteTelematicsV1ExperimentId,
		[]uuid.UUID{input.Application.ID},
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get experiment applicabilities")
	}

	// Determine whether the current application is part of the Pre-Telematics experiment
	// by checking if at least one applicability record has Applied == true. The API may
	// return records with Applied == false when the subject was evaluated but not applied.
	// use slice_utils.Find to check for any applicability where Applied == true
	isPartOfPreTelematics := slice_utils.Find(preTelematicsQuoteApplicabilities, func(ap experiments_models.ExperimentApplicability) bool {
		return ap.Applied
	}) != nil

	// If the application is *not* part of the Pre-Telematics experiment, and at least one
	// duplicate application *is* (tagged as FlexQuote), then allow the safety-credit override.
	if !isPartOfPreTelematics {
		allowSafetyCreditOverrideDueToPreTelematics = slice_utils.Find(duplicateApplications, func(dup nonfleet.DuplicateApplications) bool {
			return slice_utils.Contains(dup.Tags, nonfleet.FlexQuote)
		}) != nil
	}

	return &nonfleet_uw.ApplicationReviewSummary{
		FmcsaAddress: &nonfleet.Address{
			City:   mailingAddress.City,
			State:  mailingAddress.State,
			Street: mailingAddress.Street,
			Zip:    mailingAddress.ZipCode,
		},
		Address: nonfleet.Address{
			City:   bizOwnerAddress.City,
			State:  bizOwnerAddress.State,
			Street: bizOwnerAddress.Street,
			Zip:    bizOwnerAddress.ZipCode,
		},
		AgencyName:              agency.Name,
		AgentEmail:              producer.Email,
		AgentPhoneNumber:        phoneNumber,
		ApplicationID:           input.Application.ID.String(),
		CompanyName:             input.Application.Info.GetCompanyName(),
		DotNumber:               int(input.Application.Info.GetDot()),
		EffectiveDate:           oapi_types.Date{Time: input.ApplicationReview.GetEffectiveDate()},
		NumberOfPUs:             input.Application.Info.GetPowerUnitCount(),
		ProducerName:            fmt.Sprintf("%s %s", producer.FirstName, producer.LastName),
		ProgramType:             nonfleet.ProgramType(programType.String()),
		ApplicationShortID:      string(input.Application.ShortID),
		State:                   getAppReviewStatus(input.ApplicationReview.GetAppState()),
		AppState:                pointer_utils.ToPointer(getAppStatus(input.Application.State)),
		TspConnectionHandleId:   tspConnHandleID,
		TspName:                 tspName,
		UnderwriterName:         uwFullName,
		PanelReviewStatuses:     panelReviewStatuses,
		Assignees:               *assignees,
		CreatedAt:               pointer_utils.Time(input.ApplicationReview.GetCreatedAt()),
		PreTelematicsQuoteState: pointer_utils.ToPointer(preTelematicsQuoteState),
		DuplicateApplications:   duplicateApplications,
		AllowSafetyCreditOverrideDueToPreTelematics: allowSafetyCreditOverrideDueToPreTelematics,
	}, nil
}

// getDuplicateApplications identifies duplicate applications (same DOT, effective date within ±1 month) and tags
// them based on experiment applicability (e.g., FlexQuote).
func (s *SummaryPanel[T]) getDuplicateApplications(
	ctx context.Context,
	input *base_panel.PanelInput[T],
) ([]nonfleet.DuplicateApplications, error) {
	// Fetch all applications having the same DOT number as the original application.
	otherApps, err := s.deps.AppWrapper.GetAllApplicationsByDotNumber(ctx, input.Application.Info.GetDot())
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch applications by DOT number")
	}

	otherAppIds := slice_utils.Map(otherApps, func(app application.Application[application.AppInfo]) interface{} {
		return app.ID
	})

	originalEffectiveDate := input.ApplicationReview.GetEffectiveDate()

	// Filter applications within ±1 month of the original effective date.
	filteredAppIDs := make([]uuid.UUID, 0)
	filteredApps := make([]application.Application[application.AppInfo], 0) // to retain app objects
	// Determine whether the current application belongs to a test agency.
	agency, err := s.deps.AgencyWrapper.FetchAgency(ctx, input.Application.AgencyID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get agency %s", input.Application.AgencyID)
	}

	// Fetch reviews for these applications.
	reviews, err := s.deps.AppReviewWrapper.GetAllApplicationReviews(
		ctx,
		nf_app_review.AppIDIn(otherAppIds),
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch application reviews")
	}

	// Build a quick-lookup map of application IDs that have at least one review.
	applicationsWithReviews := make(map[uuid.UUID]bool, len(reviews))
	for _, rev := range reviews {
		applicationsWithReviews[rev.GetApplicationID()] = true
	}
	inputIsTestAgency := agency.IsTestAgency

	for _, app := range otherApps {

		// Skip if this application does not have any corresponding review
		// i.e. the application is not yet submitted for review.
		if _, ok := applicationsWithReviews[app.ID]; !ok {
			continue
		}

		if app.ID == input.Application.ID {
			continue
		}
		if app.EffectiveDate.Before(originalEffectiveDate.AddDate(0, -1, 0)) ||
			app.EffectiveDate.After(originalEffectiveDate.AddDate(0, 1, 0)) {
			continue
		}

		// Ensure we only compare against apps that belong to the same "test" class (test vs non-test) as the current app.
		agency, err := s.deps.AgencyWrapper.FetchAgency(ctx, app.AgencyID)
		if err != nil {
			log.Warn(ctx, "failed to fetch agency while fetching duplicates in summary panel", log.Stringer("agency_id", app.AgencyID))
			continue // skip if we can't fetch the agency
		}
		// If the current app is a test agency and the other app is not, skip.
		if inputIsTestAgency != agency.IsTestAgency {
			continue
		}

		filteredAppIDs = append(filteredAppIDs, app.ID)
		filteredApps = append(filteredApps, app)
	}

	// If no potential duplicates, return early.
	if len(filteredAppIDs) == 0 {
		return []nonfleet.DuplicateApplications{}, nil
	}

	// Fetch experiment applicabilities for these filtered applications.
	preTelematicsQuoteApplicabilities, err := s.deps.ExperimentManager.GetLatestExperimentApplicabilities(
		ctx,
		pre_telematics_quote.PreQuoteTelematicsV1ExperimentId,
		filteredAppIDs,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get experiment applicabilities")
	}

	preTelematicsQuoteApplicabilityMap := make(map[string]bool)
	for _, ap := range preTelematicsQuoteApplicabilities {
		if ap.Applied {
			preTelematicsQuoteApplicabilityMap[ap.SubjectId] = true
		}
	}

	// Build the duplicate-application payload.
	duplicates := make([]nonfleet.DuplicateApplications, 0, len(filteredApps))
	for _, app := range filteredApps {
		var tags []nonfleet.DuplicateApplicationTag
		if preTelematicsQuoteApplicabilityMap[app.ID.String()] {
			tags = append(tags, nonfleet.FlexQuote)
		}

		duplicates = append(duplicates, nonfleet.DuplicateApplications{
			ShortId: string(app.ShortID),
			Tags:    tags,
		})
	}

	return duplicates, nil
}

func (s *SummaryPanel[T]) GetAssignees(
	ctx context.Context,
	input *base_panel.PanelInput[T],
) (*oapi_uw.ApplicationReviewAssignees, error) {
	appReview := input.ApplicationReview
	app := input.Application
	var currentUW oapi_uw.ApplicationReviewUser
	if input.ApplicationReview.GetUnderwriterID() != uuid.Nil {
		underwriter, err := s.deps.AuthWrapper.FetchAuthzUser(ctx, appReview.GetUnderwriterID())
		if err != nil {
			return nil, errors.Wrapf(err, "unable to fetch underwriter %s", input.ApplicationReview.GetUnderwriterID())
		}

		currentUW = oapi_uw.ApplicationReviewUser{
			Email:   oapi_types.Email(underwriter.Email),
			Id:      underwriter.ID.String(),
			Name:    underwriter.FullName(),
			IconUrl: applicationutil.GetUnderwriterIconURL(underwriter.Email),
		}
	}

	availableUnderwriters, err := s.deps.UWScheduler.GetAllAvailableUnderwriters(ctx,
		scheduler.UWAssignmentParams{
			AppID:         appReview.GetApplicationID().String(),
			AgencyID:      app.AgencyID,
			DotNumber:     app.Info.GetDot(),
			EffectiveDate: appReview.GetEffectiveDate(),
			ProgramType:   policy_enums.ProgramTypeNonFleetAdmitted,
		})
	if err != nil {
		return nil, errors.Wrap(err, "unable to fetch all underwriters")
	}

	underwriterOptions := make([]oapi_uw.ApplicationReviewUser, 0)
	for _, u := range availableUnderwriters {
		underwriterOptions = append(underwriterOptions, oapi_uw.ApplicationReviewUser{
			Email:   oapi_types.Email(u.Email),
			Id:      u.ID.String(),
			Name:    u.FullName(),
			IconUrl: applicationutil.GetUnderwriterIconURL(u.Email),
		})
	}

	return &oapi_uw.ApplicationReviewAssignees{
		Underwriter: &oapi_uw.ApplicationReviewAssignee{
			Current: &currentUW,
			Options: &underwriterOptions,
		},
	}, nil
}

func (s *SummaryPanel[T]) UpdateAssignee(
	ctx context.Context,
	input *base_panel.PanelInput[T],
	underwriterIDStr *string,
) (*authz.User, error) {
	// If the current UW is the same as the new UW, then return
	if underwriterIDStr != nil && *underwriterIDStr == input.ApplicationReview.GetUnderwriterID().String() {
		uw, err := s.deps.AuthWrapper.FetchAuthzUser(ctx, input.ApplicationReview.GetUnderwriterID())
		if err != nil {
			return nil, errors.Wrapf(err, "unable to fetch underwriter %s", input.ApplicationReview.GetUnderwriterID())
		}
		return uw, nil
	}

	underwriterID, err := checkIfUserIsUW(ctx, underwriterIDStr, s.deps.AuthWrapper)
	if err != nil {
		return nil, errors.Wrapf(err, "the user %v is not a valid UW", underwriterIDStr)
	}

	// Update the underwriterID in the application review
	err = s.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		input.ApplicationReview.GetID().String(),
		appReviewUWUpdateFn(underwriterID),
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to update app review %s", input.ApplicationReview.GetID().String())
	}

	// Update the underwriterID in the application
	err = s.deps.AppWrapper.UpdateApp(ctx, input.Application.ID, appUWUpdateFn[T](underwriterID))
	if err != nil {
		return nil, errors.Wrapf(err, "failed to update app %s", input.Application.ID.String())
	}

	// Update the underwriterID in the current submission
	err = s.deps.AppWrapper.UpdateSubmission(
		ctx,
		input.ApplicationReview.GetSubmissionID(),
		submissionUWUpdateFn[T](underwriterID),
	)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to update submission %s",
			input.ApplicationReview.GetSubmissionID().
				String(),
		)
	}

	uw, err := s.deps.AuthWrapper.FetchAuthzUser(ctx, underwriterID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to fetch underwriter %s", underwriterID)
	}
	return uw, nil
}

func checkIfUserIsUW(
	ctx context.Context,
	underwriterIDStr *string,
	authWrapper auth.DataWrapper,
) (uuid.UUID, error) {
	if underwriterIDStr == nil {
		return uuid.Nil, errors.New("underwriterID was nil")
	}

	underwriterID, err := uuid.Parse(*underwriterIDStr)
	if err != nil {
		return uuid.Nil, errors.Wrapf(err, "failed to parse underwriterID %s", *underwriterIDStr)
	}

	roles, err := authWrapper.FetchNirvanaRoles(ctx, underwriterID)
	if err != nil {
		return uuid.Nil, errors.Wrapf(err, "failed to fetch authz roles for id %s", underwriterID.String())
	}

	isUnderwriter := false
	for _, role := range roles {
		if role.Group == authz.SeniorUnderwriterRole {
			isUnderwriter = true
			break
		}
	}

	if !isUnderwriter {
		return uuid.Nil, errors.New("user is not an underwriter")
	}

	return underwriterID, nil
}

func appReviewUWUpdateFn(underwriterID uuid.UUID) nf_app_review.AppReviewUpdateFn {
	return func(review nf_app_review.ApplicationReview) (nf_app_review.ApplicationReview, error) {
		review.SetUnderwriterID(underwriterID)
		return review, nil
	}
}

func appUWUpdateFn[T application.AppInfo](underwriterID uuid.UUID) application.AppUpdateFn[T] {
	return func(app application.Application[T]) (application.Application[T], error) {
		app.UnderwriterID = underwriterID
		return app, nil
	}
}

func submissionUWUpdateFn[T application.AppInfo](underwriterID uuid.UUID) application.SubmissionUpdateFn[T] {
	return func(submission application.Submission[T]) (*application.Submission[T], error) {
		submission.UnderwriterID = underwriterID
		return &submission, nil
	}
}

func getPanel(panel nonfleet_uw.ApplicationReviewFlagPanel) nf_app_review.PanelType {
	switch panel {
	case nonfleet_uw.Coverages:
		return nf_app_review.PackagesPanelType
	case nonfleet_uw.Drivers:
		return nf_app_review.DriverPanelType
	case nonfleet_uw.Equipments:
		return nf_app_review.EquipmentPanelType
	case nonfleet_uw.Losses:
		return nf_app_review.LossesPanelType
	case nonfleet_uw.Operations:
		return nf_app_review.OperationsPanelType
	case nonfleet_uw.Safety:
		return nf_app_review.SafetyPanelType
	default:
		return nf_app_review.PackagesPanelType
	}
}
