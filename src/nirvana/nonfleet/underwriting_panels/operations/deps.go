package operations

import (
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/common-go/crypto_utils"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
)

type Deps[T nf_app.AppInfo] struct {
	fx.In

	AppReviewWrapper                nf_app_review.Wrapper
	AppWrapper                      nf_app.Wrapper[T]
	FetcherClientFactory            data_fetching.FetcherClientFactory
	ProcessorClientFactory          data_processing.ProcessorClientFactory
	WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
	ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
	CryptoClient                    *crypto_utils.Client
}
