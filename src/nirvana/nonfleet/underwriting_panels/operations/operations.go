package operations

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"

	"nirvanatech.com/nirvana/nonfleet/application"

	"github.com/cockroachdb/errors"
	openapi_types "github.com/oapi-codegen/runtime/types"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/insurance_carriers_utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	admitted_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/common"
	nf_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management"
	"nirvanatech.com/nirvana/nonfleet/calculators"
	"nirvanatech.com/nirvana/nonfleet/model"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/enums"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
	"nirvanatech.com/nirvana/rating/data_fetching/lni_fetching"
	"nirvanatech.com/nirvana/rating/data_processing/lni_processing"
	scraper_enums "nirvanatech.com/nirvana/servers/quote_scraper/enums"
)

const (
	USDOTScoreComputedFieldName = "usdotScoreGrp"
)

type OperationsPanel[T nf_app.AppInfo] struct {
	deps Deps[T]
}

// var _ underwriting_panels.Panel[nf_app.AppInfo] = &OperationsPanel[nf_app.AppInfo]{}
func NewOperationsPanel[T nf_app.AppInfo](deps Deps[T]) *OperationsPanel[T] {
	return &OperationsPanel[T]{deps: deps}
}

func (o *OperationsPanel[T]) SetIsReviewed(ctx context.Context, appReviewID string, isReviewed bool) error {
	if err := o.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review nf_app_review.ApplicationReview) (nf_app_review.ApplicationReview, error) {
			err := review.SetPanelReview(nf_app_review.OperationsPanelType, isReviewed)
			if err != nil {
				return review, errors.Wrapf(err, "failed to set panel review")
			}
			return review, nil
		},
	); err != nil {
		return errors.Wrapf(err, "unable to update application review %v", appReviewID)
	}
	return nil
}

func (o *OperationsPanel[T]) IsReviewed(input *base_panel.PanelInput[T]) bool {
	return input.ApplicationReview.IsPanelReviewed(nf_app_review.OperationsPanelType)
}

func (*OperationsPanel[T]) PanelKey() enums.FlagPanel {
	return enums.Operations
}

func (o *OperationsPanel[T]) YearsInBusiness(ctx context.Context, input *base_panel.PanelInput[T]) oapi_uw.OperationsYearsInBusiness {
	fetcherClient, closerF, err := o.deps.FetcherClientFactory()
	if err != nil {
		log.Error(ctx, "failed to create data fetcher", log.Err(err))
		return oapi_uw.OperationsYearsInBusiness{Years: 2}
	}
	defer func() { _ = closerF() }()

	processorClient, closerP, err := o.deps.ProcessorClientFactory(fetcherClient)
	if err != nil {
		log.Error(ctx, "failed to create data processor", log.Err(err))
		return oapi_uw.OperationsYearsInBusiness{Years: 2}
	}
	defer func() { _ = closerP() }()

	dotNumber := input.Application.Info.GetDot()
	effectiveDate := input.ApplicationReview.GetEffectiveDate()

	yearsInBusiness, err := processorClient.GetYearsInBusinessFromInsuranceHistoryV1(
		ctx,
		&data_processing.InsuranceHistoryYearsInBusinessRequestV1{
			FetcherSpec: &data_processing.InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests_{
				FetcherRequests: &data_processing.InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests{
					InsuranceHistoryRequest: &data_fetching.BIPDInsuranceHistoryRequestV1{
						DotNumber: dotNumber,
					},
					ActiveOrPendingInsuranceRequest: &data_fetching.BIPDActiveOrPendingInsuranceRequestV1{
						DotNumber: dotNumber,
					},
				},
			},
			EffectiveDate: timestamppb.New(effectiveDate),
		},
	)
	if err != nil {
		// In case of error, we return 2 years as default
		// We flag this to the UW as well
		return oapi_uw.OperationsYearsInBusiness{Years: 2}
	}
	numYears, fraction := math.Modf(yearsInBusiness.Years)
	return oapi_uw.OperationsYearsInBusiness{Years: int(numYears), Months: int(fraction * 12.0)}
}

func (o *OperationsPanel[T]) AdditionalInformation(
	ctx context.Context,
	input *base_panel.PanelInput[T],
) (*oapi_uw.OperationsAdditionalInformation, error) {
	additionalInformation := input.Application.Info.GetOperationsAdditionalInformation()
	overrides := input.ApplicationReview.GetOverrides()
	if overrides.BizOwnerCreditScore != nil {
		// If the UW has provided a credit score override, we use that
		additionalInformation.AdmittedAdditionalInformation.BizOwnerCreditScore = oapi_uw.BusinessOwnerCreditScore(
			"CreditScore" + (*overrides.BizOwnerCreditScore).String(),
		)
	}
	if overrides.USDotScore != nil {
		// If the UW has provided a USDOT score override, we use that
		additionalInformation.AdmittedAdditionalInformation.UsDotScore = oapi_uw.USDotScore(
			"USDotScore" + (*overrides.USDotScore).String(),
		)
	}

	scraperInfo := input.ApplicationReview.GetScraperInfo()
	if scraperInfo != nil &&
		scraperInfo.CreditScoreInfo.Status == scraper_enums.ScrapingStatusSuccess &&
		scraperInfo.CreditScore != nil {
		// This block is for the case when we have credit score from the scraper
		additionalInformation.AdmittedAdditionalInformation.RecommendedBizOwnerCreditScore = pointer_utils.ToPointer(oapi_uw.BusinessOwnerCreditScore("CreditScore" + scraperInfo.CreditScore.String()))

		additionalInformation.AdmittedAdditionalInformation.RecommendedBizOwnerCreditScoreFactor = pointer_utils.ToPointer(o.getCreditScoreRecommendationFactor(input, scraperInfo))
	}

	// We always want to have the ability to refresh USDOT score
	// Except when we have USDOT score from the scraper
	additionalInformation.AdmittedAdditionalInformation.EnableUSDOTRefresh = true

	if scraperInfo != nil &&
		scraperInfo.USDOTScoreInfo.Status == scraper_enums.ScrapingStatusSuccess &&
		scraperInfo.USDOTScore != nil {
		// This block is for the case when we have USDOT score from the scraper
		additionalInformation.AdmittedAdditionalInformation.RecommendedUSDotScore = pointer_utils.ToPointer(oapi_uw.USDotScore("USDotScore" + scraperInfo.USDOTScore.String()))

		additionalInformation.AdmittedAdditionalInformation.RecommendedUSDotScoreFactor = pointer_utils.Float32(calculators.USDOTFactors[*scraperInfo.USDOTScore])

		additionalInformation.AdmittedAdditionalInformation.EnableUSDOTRefresh = false
	} else {
		// This else block is for the case when we don't have USDOT score from the scraper
		rmlUSDOTScore, rmlUSDOTScoreFactor, err := o.getRMLUSDotScoreAndFactor(ctx, input)
		// We don't want to do anything if we don't have USDOT score from RML
		// We just send `RecommendedUSDotScore` & `RecommendedUSDotScoreFactor` as nil
		if err == nil {
			additionalInformation.AdmittedAdditionalInformation.RecommendedUSDotScore = pointer_utils.ToPointer(
				oapi_uw.USDotScore("USDotScore" + rmlUSDOTScore.String()))

			additionalInformation.AdmittedAdditionalInformation.RecommendedUSDotScoreFactor = rmlUSDOTScoreFactor
		}
	}

	fetcherClient, closer, err := o.deps.FetcherClientFactory()
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create data fetcher")
	}
	defer func() { _ = closer() }()

	additionalInformation.AdmittedAdditionalInformation.IsExistingPgrCustomer = checkIfProgressiveCustomer(
		ctx,
		fetcherClient,
		input.Application.Info.GetDot(),
	)

	additionalInformation.AdmittedAdditionalInformation.IsNewCreditScoreApp = false
	if model.IsNirvanaCreditModel(input.Submission.Info.GetModelPinConfigInfo().RateML.Version) {
		additionalInformation.AdmittedAdditionalInformation.IsNewCreditScoreApp = true
		indicationOption, err := o.deps.AppWrapper.GetIndOptionById(ctx, input.Submission.SelectedIndicationID)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get indication option by id")
		}

		lnCreditScore := indicationOption.GetCreditScore()
		if lnCreditScore != nil {
			additionalInformation.AdmittedAdditionalInformation.LnCreditScore = pointer_utils.ToPointer(oapi_uw.BusinessOwnerCreditScore("CreditScore" +
				indicationOption.GetCreditScore().String()))
		}

		lnNoHitFlag, err := o.getLNCreditNoHitFlag(ctx, input)
		if err != nil {
			log.Error(ctx, "failed to get ln credit no hit flag",
				log.String("application_id", input.Application.ID.String()),
				log.Err(err))
			additionalInformation.AdmittedAdditionalInformation.HasNewCreditError = pointer_utils.ToPointer(true)
		}
		additionalInformation.AdmittedAdditionalInformation.LnNoHit = lnNoHitFlag
	}

	return &additionalInformation, nil
}

func (o *OperationsPanel[T]) getRMLUSDotScoreAndFactor(
	ctx context.Context,
	input *base_panel.PanelInput[T],
) (*nf_enums.USDotScore, *float32, error) {
	var rmlUSDOTScore nf_enums.USDotScore
	var rmlUSDOTScoreFactor float32
	var err error

	// 1. Get Indication Submission Obj (Note: QuoteSubmissionID is the same as IndicationSubmissionID)
	indicationSubObj, err := o.deps.AppWrapper.GetSubmissionById(ctx, input.Application.QuoteSubmissionID)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to get indication submission by id")
	}
	// 2. Get Indication Option Obj
	indOpt, err := o.deps.AppWrapper.GetIndOptionById(ctx, indicationSubObj.SelectedIndicationID)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to get indication option by id")
	}

	// 3. Get USDOT Score from Indication Option
	if rmlUSDOTScoreStr, ok := indOpt.GetCompanyComputation().ComputedFields[USDOTScoreComputedFieldName]; ok {
		rmlUSDOTScore, err = nf_enums.USDotScoreString(strings.ToUpper(rmlUSDOTScoreStr.(string)))
		if err != nil {
			return nil, nil, errors.Wrapf(err, "failed to parse USDOT score from indication option")
		}

		rmlUSDOTScoreFactor = calculators.USDOTFactors[rmlUSDOTScore]
	}
	return &rmlUSDOTScore, &rmlUSDOTScoreFactor, nil
}

func (o *OperationsPanel[T]) getCreditScoreRecommendationFactor(
	input *base_panel.PanelInput[T],
	scraperInfo *common.ScraperInfo,
) []oapi_uw.RecommendationFactor {
	// To get the recommendation factor, we need to calculate the puCount
	// We cap the puCount to 10 as the recommendation factors are only available for 1-10 PUs
	puCount := input.Application.Info.GetActualPowerUnitCount()
	if input.Application.Info.GetActualPowerUnitCount() > 10 {
		// Capping puCount to 10
		puCount = 10
	}
	if input.Application.Info.GetActualPowerUnitCount() == 0 {
		// Setting puCount to 1 if it's 0
		puCount = 1
	}

	recommendationFactor := calculators.CreditRecommendationFactors[calculators.CreditRecommendationFactorKey{
		PUCount:     puCount,
		CreditScore: *scraperInfo.CreditScore,
	}]

	// We need to send recommendation factor per coverage specifically for CreditScore
	var recommendationFactors []oapi_uw.RecommendationFactor
	for _, cov := range input.Application.Info.GetRequiredCoverages() {
		if cov.CoverageType == app_enums.CoverageAutoLiability {
			recommendationFactors = append(recommendationFactors, oapi_uw.RecommendationFactor{
				CoverageType: oapi_common.CoverageAutoLiability,
				Value:        pointer_utils.ToPointer(recommendationFactor.ALFactor),
			})
		}
		if cov.CoverageType == app_enums.CoverageAutoPhysicalDamage {
			recommendationFactors = append(recommendationFactors, oapi_uw.RecommendationFactor{
				CoverageType: oapi_common.CoverageAutoPhysicalDamage,
				Value:        pointer_utils.ToPointer(recommendationFactor.APDFactor),
			})
		}
		if cov.CoverageType == app_enums.CoverageMotorTruckCargo {
			recommendationFactors = append(recommendationFactors, oapi_uw.RecommendationFactor{
				CoverageType: oapi_common.CoverageMotorTruckCargo,
				Value:        pointer_utils.ToPointer(recommendationFactor.MTCFactor),
			})
		}
	}
	return recommendationFactors
}

func checkIfProgressiveCustomer(
	ctx context.Context,
	fetcherClient data_fetching.FetcherClient,
	dot int64,
) bool {
	latestRecord, err := GetLatestInsuranceRecord(ctx, fetcherClient, dot)
	if err != nil {
		return false
	}
	if slice_utils.Contains(
		insurance_carriers_utils.ProgressiveSubsidiaries,
		strings.ToUpper(latestRecord.InsuranceCompanyName),
	) && latestRecord.CancelEffectiveDate == nil {
		return true
	}

	return false
}

func GetLatestInsuranceRecord(
	ctx context.Context,
	fetcherClient data_fetching.FetcherClient,
	dot int64,
) (*data_fetching.InsuranceRecordV1, error) {
	request := data_fetching.BIPDActiveOrPendingInsuranceRequestV1{DotNumber: dot}
	insurance, err := fetcherClient.GetBIPDActiveOrPendingInsuranceV1(ctx, &request)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get LNI records for DOT %v", dot)
	}
	if len(insurance.GetRecords()) == 0 {
		return nil, errors.New("no insurance records found")
	}
	currentInsurance := insurance.GetRecords()[0]

	return currentInsurance, nil
}

func (o *OperationsPanel[T]) OperatingCommodities(input *base_panel.PanelInput[T]) []oapi_uw.OperationsCommodity {
	return input.Application.Info.GetOperationsCommodities()
}

func (o *OperationsPanel[T]) InsuranceHistory(
	ctx context.Context,
	input *base_panel.PanelInput[T],
) []oapi_uw.OperationsFleetHistoryInsuranceHistoryItem {
	var insuranceHistory []oapi_uw.OperationsFleetHistoryInsuranceHistoryItem

	fetcherClient, closer, err := o.deps.FetcherClientFactory()
	if err != nil {
		log.Error(ctx, "Failed to create data fetcher", log.Err(err))
		return nil
	}
	defer func() { _ = closer() }()

	records, err := lni_fetching.FetchBIPDInsuranceRecordsV1(ctx, fetcherClient, input.Application.Info.GetDot())
	if err != nil {
		// In case of error, we return empty insurance history
		// We flag the same to UW as well
		return insuranceHistory
	}

	for _, record := range records {
		st := openapi_types.Date{}
		var et *openapi_types.Date
		if record.EffectiveDate != nil {
			st.Time = record.EffectiveDate.AsTime()
		}
		if record.CancelEffectiveDate != nil {
			et = &openapi_types.Date{Time: record.CancelEffectiveDate.AsTime()}
		}
		insuranceHistory = append(
			insuranceHistory,
			oapi_uw.OperationsFleetHistoryInsuranceHistoryItem{
				Carrier:       record.InsuranceCompanyName,
				InsuranceType: record.InsuranceType,
				StartDate:     st,
				EndDate:       et,
			},
		)
	}
	return insuranceHistory
}

func (o *OperationsPanel[T]) UpdateBizOwnerCreditScore(
	ctx context.Context, appReviewID string, bizOwnerCreditScore admitted_enums.CreditScore,
) error {
	return o.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review nf_app_review.ApplicationReview) (nf_app_review.ApplicationReview, error) {
			err := review.UpdateOverrides(
				func(override *nf_app_review.Overrides) (nf_app_review.Overrides, error) {
					override.BizOwnerCreditScore = pointer_utils.ToPointer(bizOwnerCreditScore)
					return *override, nil
				})

			return review, err
		})
}

func (o *OperationsPanel[T]) UpdateUSDotScore(
	ctx context.Context, appReviewID string, usDotScore nf_enums.USDotScore,
) error {
	return o.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review nf_app_review.ApplicationReview) (nf_app_review.ApplicationReview, error) {
			err := review.UpdateOverrides(
				func(override *nf_app_review.Overrides) (nf_app_review.Overrides, error) {
					override.USDotScore = pointer_utils.ToPointer(usDotScore)
					return *override, nil
				},
			)
			return review, err
		},
	)
}

func (o *OperationsPanel[T]) UpdateEffectiveDate(
	ctx context.Context, appReviewID string, effDate time.Time,
) error {
	return o.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review nf_app_review.ApplicationReview) (nf_app_review.ApplicationReview, error) {
			err := review.UpdateOverrides(
				func(override *nf_app_review.Overrides) (nf_app_review.Overrides, error) {
					override.EffectiveDate = pointer_utils.ToPointer(effDate)
					override.EffectiveDateTo = pointer_utils.ToPointer(effDate.AddDate(1, 0, 0))
					return *override, nil
				},
			)
			return review, err
		},
	)
}

func (o *OperationsPanel[T]) LNIData(ctx context.Context, input *base_panel.PanelInput[T]) *oapi_uw.LNIData {
	dot := input.Application.Info.GetDot()
	effDate := input.ApplicationReview.GetEffectiveDate()
	fetcherClient, closerF, err := o.deps.FetcherClientFactory()
	if err != nil {
		log.Error(ctx, "Failed to create data fetcher", log.Err(err))
		return nil
	}
	defer func() { _ = closerF() }()

	processorClient, closerP, err := o.deps.ProcessorClientFactory(fetcherClient)
	if err != nil {
		log.Error(ctx, "Failed to create data processor", log.Err(err))
		return nil

	}
	defer func() { _ = closerP() }()

	// We are suppressing errors here as they are already being flagged to UW using rules

	yib, err := lni_processing.GetYearsInBusinessFromAuthorityHistoryV1(
		ctx,
		processorClient,
		dot,
		effDate,
	)
	if err != nil {
		yib = 0
	}

	continuousCoverageYears, _ := lni_processing.GetBIPDContinuousCoverageYearsV1(
		ctx,
		processorClient,
		dot,
		effDate,
	)

	currentCarrierYears, _ := lni_processing.GetBIPDCurrentCarrierContinuousCoverageYearsV1(
		ctx,
		fetcherClient,
		processorClient,
		dot,
		effDate,
	)
	return &oapi_uw.LNIData{
		ContinuousCoverageYears: pointer_utils.ToPointer(int64(math.Floor(continuousCoverageYears))),
		CurrentCarrierYears:     pointer_utils.ToPointer(int64(math.Floor(currentCarrierYears))),
		MonthsInBusiness:        pointer_utils.ToPointer(int64(math.Floor(yib * time_utils.NumberOfMonthsInAYear))),
		YearsInBusiness:         pointer_utils.ToPointer(int64(math.Floor(yib))),
	}
}

func (o *OperationsPanel[T]) Operations(
	ctx context.Context,
	input *base_panel.PanelInput[T],
) (*oapi_uw.ApplicationReviewOperation, error) {
	additionInfo, err := o.AdditionalInformation(ctx, input)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get additional information")
	}
	return &oapi_uw.ApplicationReviewOperation{
		IsReviewed:                   o.IsReviewed(input),
		YearsInBusiness:              o.YearsInBusiness(ctx, input),
		AdditionalInformation:        *additionInfo,
		Commodities:                  o.OperatingCommodities(input),
		FleetHistoryInsuranceHistory: o.InsuranceHistory(ctx, input),
		TerminalLocation:             input.Application.Info.GetTerminalLocationOAPI(),
		BusinessOwner:                input.Application.Info.GetBusinessOwnerDetails(),
		LniData:                      o.LNIData(ctx, input),
	}, nil
}

func (o *OperationsPanel[T]) getLNCreditNoHitFlag(
	ctx context.Context,
	input *base_panel.PanelInput[T],
) (*bool, error) {
	if input.Submission.DataContextId == nil {
		log.Info(ctx, "No context id found for application",
			log.String("application_id", input.Application.ID.String()))
		return nil, nil //nolint:nilnil
	}

	interceptors := interceptors_management.NewWritableStoreFirstInterceptors(
		o.deps.ReadFromStoreInterceptorFactory,
		o.deps.WriteToStoreInterceptorFactory,
		*input.Submission.DataContextId,
	)
	fetcherClient, closerF, err := o.deps.FetcherClientFactory(interceptors...)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create data fetcher")
	}
	defer func() { _ = closerF() }()

	processorClient, closerP, err := o.deps.ProcessorClientFactory(fetcherClient)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get processor client")
	}
	defer func() { _ = closerP() }()

	businessOwnerDetails := input.Submission.Info.GetBusinessOwnerDetails()
	dob, err := time_utils.DateFromString(businessOwnerDetails.DateOfBirth)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to parse date of birth")
	}

	usState, err := us_states.StrToUSState(businessOwnerDetails.Address.State)
	if err != nil {
		return nil, errors.Wrap(err, "Invalid US state")
	}

	ownerAddress := &data_fetching.AddressV1{
		Street: businessOwnerDetails.Address.Street,
		City:   businessOwnerDetails.Address.City,
		State:  usState.ToCode(),
		Zip:    businessOwnerDetails.Address.Zip,
	}

	nationalCreditFileRequest := &data_fetching.NationalCreditFileRequestV1{
		Dob:           timestamppb.New(dob.ToTime()),
		FirstName:     businessOwnerDetails.FirstName,
		LastName:      businessOwnerDetails.LastName,
		MiddleName:    "",
		Address:       ownerAddress,
		Staleness:     0,
		ApplicationID: input.Application.ID.String(),
	}

	creditFeaturesRequest := &data_processing.NFCreditFeaturesRequestV2{
		FetcherSpec: &data_processing.NFCreditFeaturesRequestV2_FetcherRequest{
			FetcherRequest: nationalCreditFileRequest,
		},
		OwnerDOB:  timestamppb.New(dob.ToTime()),
		OwnerName: fmt.Sprintf("%s %s", businessOwnerDetails.FirstName, businessOwnerDetails.LastName),
		Address:   ownerAddress,
	}

	if len(input.Submission.Info.GetEncryptedSSN()) != 0 {
		nationalCreditFileRequest.EncryptedSSN = input.Submission.Info.GetEncryptedSSN()
	}

	if len(input.Submission.Info.GetEncryptedSSNLastFour()) != 0 {
		ssnLastFour, err := application.DecryptSSNLastFour(ctx, o.deps.CryptoClient, input.Submission.Info.GetEncryptedSSNLastFour())
		if err != nil {
			return nil, errors.Wrap(err, "could not decrypt ssn last four")
		}
		nationalCreditFileRequest.SsnLastFour = ssnLastFour
		creditFeaturesRequest.SsnLastFour = ssnLastFour
	}

	nfCreditFeatures, err := processorClient.GetNFCreditFeaturesV2(ctx, creditFeaturesRequest)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get NF credit features")
	}

	return pointer_utils.ToPointer(nfCreditFeatures.NoHit), nil
}
