load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "operations",
    srcs = [
        "deps.go",
        "fx.go",
        "operations.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/underwriting_panels/operations",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/insurance_carriers_utils",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/nonfleet/common",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/interceptors_management",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/application",
        "//nirvana/nonfleet/calculators",
        "//nirvana/nonfleet/model",
        "//nirvana/nonfleet/underwriting_panels/base_panel",
        "//nirvana/nonfleet/underwriting_panels/enums",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/rating/data_fetching/lni_fetching",
        "//nirvana/rating/data_processing/lni_processing",
        "//nirvana/servers/quote_scraper/enums",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_oapi_codegen_runtime//types",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
    ],
)
