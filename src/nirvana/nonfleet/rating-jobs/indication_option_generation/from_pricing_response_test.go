package indication_option_generation

import (
	"regexp"
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nfenums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	"nirvanatech.com/nirvana/nonfleet/model"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func Test_GetIOInputsFromPricingResponse_WithNilInputs(t *testing.T) {
	output, err := GetIOInputsFromPricingResponse(nil, nil)
	require.Error(t, err)
	require.Regexp(t, "req is nil", err.Error())
	require.Nil(t, output)

	req := &ptypes.Request{}
	output, err = GetIOInputsFromPricingResponse(req, nil)
	require.Error(t, err)
	require.Regexp(t, "resp is nil", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithDuplicatePolicies(t *testing.T) {
	resp := &ptypes.Response{}
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_Unspecified,
			},
			{
				PolicyNumber: "policy2",
				PolicyName:   ptypes.PolicyName_PolicyName_Unspecified,
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "duplicate policy name found", err.Error())
	require.Nil(t, output)

	req = &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
			},
		},
	}
	output, err = GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "duplicate policy number found", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithInvalidChunksCount(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_Unspecified,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: make([]*ptypes.ChunkOutput, 0),
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "chunk outputs length is not 1 for policy", err.Error())
	require.Nil(t, output)

	resp.PolicyOutputs[0].ChunkOutputs = []*ptypes.ChunkOutput{
		{ChunkID: "chunk1"},
		{ChunkID: "chunk2"},
	}
	output, err = GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "chunk outputs length is not 1 for policy", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithNilChunkOutput(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_Unspecified,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{nil},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "chunk output is nil for policy", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithNilChargeInChunkOutput(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_Unspecified,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{nil},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "charge is nil", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithUnsupportedPolicyName(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_Unspecified,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "unsupported policy name", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithNilMetadata(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "response metadata is nil for policy", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithNilNFMetadata(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: nil,
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "response NF metadata is nil for policy", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithPUCountMismatch(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
			{
				PolicyNumber: "policy2",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A02,
									PuCount:    3,
								},
							},
						},
					},
				},
			},
			{
				PolicyNumber: "policy2",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A02,
									PuCount:    4,
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "puCount mismatch for policy", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithDuplicateVINInMetadata(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A02,
									VehiclesMetadata: []*ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata{
										{
											Vin: "vin1",
										},
										{
											Vin: "vin1",
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	expectedMessage := regexp.QuoteMeta("duplicate vehicle vin vin1 in policy")
	require.Regexp(t, expectedMessage, err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithInvalidCreditScore(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									CreditScore: pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_UNSPECIFIED),
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "failed to transform credit score", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithCreditScoreMismatch(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
			{
				PolicyNumber: "policy2",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore:  model.USDOTScore_US_DOT_SCORE_A02,
									CreditScore: pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_A2),
								},
							},
						},
					},
				},
			},
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									CreditScore: pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_B3),
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "credit score mismatch for policy", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithInvalidUSDOTScore(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_UNSPECIFIED,
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "failed to transform US DOT score", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithUSDOTScoreMismatch(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
			{
				PolicyNumber: "policy2",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
								},
							},
						},
					},
				},
			},
			{
				PolicyNumber: "policy2",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: make([]*ptypes.Charge, 0),
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_B04,
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "US DOT score mismatch for policy", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithErrorCalculatingAmount(t *testing.T) {
	type testCase struct {
		charges              []*ptypes.Charge
		expectedErrorMessage string
	}

	// The error that is being simulated is that the charges have
	// nil billing details.
	testCases := []testCase{
		{
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithSurplusTaxSurchargeType().
					Build(),
			},
			expectedErrorMessage: "failed to calculate surplus tax for policy",
		},
		{
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
					Build(),
			},
			expectedErrorMessage: "failed to calculate surplus tax for policy",
		},
		{
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithStampingFeeSurchargeType().
					Build(),
			},
			expectedErrorMessage: "failed to calculate stamping fee for policy",
		},
		{
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithStampingFeeFromFullyEarnedPremiumSurchargeType().
					Build(),
			},
			expectedErrorMessage: "failed to calculate stamping fee for policy",
		},
		{
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					Build(),
			},
			expectedErrorMessage: "failed to calculate total premium",
		},
	}

	for _, tc := range testCases {
		req := &ptypes.Request{
			PolicySpecs: []*ptypes.PolicySpec{
				{
					PolicyNumber: "policy1",
					PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
				},
			},
		}
		resp := &ptypes.Response{
			PolicyOutputs: []*ptypes.PolicyOutput{
				{
					PolicyNumber: "policy1",
					ChunkOutputs: []*ptypes.ChunkOutput{
						{
							Charges: tc.charges,
							Metadata: &ptypes.ChunkOutput_Metadata{
								ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
									NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
										UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
									},
								},
							},
						},
					},
				},
			},
		}
		output, err := GetIOInputsFromPricingResponse(req, resp)
		require.Error(t, err)
		require.Regexp(t, tc.expectedErrorMessage, err.Error())
		require.Nil(t, output)
	}
}

func Test_GetIOInputsFromPricingResponse_WithZeroPUCount(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "puCount is 0 after processing all policies", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithZeroTIV(t *testing.T) {
	testCases := []struct {
		name                 string
		charges              []*ptypes.Charge
		expectedErrorMessage *string
	}{
		{
			name: "With zero COLL and COMP premium",
		},
		{
			name: "With non-zero COLL premium",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithDefaultAmountBasedBillingDetails_TestOnly().
					WithBaseChargeTypeWithoutExtraInfo().
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Collision).
					Build(),
			},
			expectedErrorMessage: pointer_utils.ToPointer("policyTIV is 0 after processing all policies"),
		},
		{
			name: "With non-zero COMP premium",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithDefaultAmountBasedBillingDetails_TestOnly().
					WithBaseChargeTypeWithoutExtraInfo().
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Comprehensive).
					Build(),
			},
			expectedErrorMessage: pointer_utils.ToPointer("policyTIV is 0 after processing all policies"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t2 *testing.T) {
			req := &ptypes.Request{
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
					},
				},
			}
			resp := &ptypes.Response{
				PolicyOutputs: []*ptypes.PolicyOutput{
					{
						PolicyNumber: "policy1",
						ChunkOutputs: []*ptypes.ChunkOutput{
							{
								Charges: tc.charges,
								Metadata: &ptypes.ChunkOutput_Metadata{
									ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
										NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
											UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
											PuCount:    1,
										},
									},
								},
							},
						},
					},
				},
			}
			output, err := GetIOInputsFromPricingResponse(req, resp)

			if tc.expectedErrorMessage != nil {
				require.Error(t, err)
				require.Regexp(t, *tc.expectedErrorMessage, err.Error())
				require.Nil(t, output)
			} else {
				require.NoError(t, err)
				require.NotNil(t, output)
			}
		})
	}
}

func Test_GetIOInputsFromPricingResponse_WithInvalidSubCoverageGroup(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithDefaultAmountBasedBillingDetails_TestOnly().
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_Towing,
									ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
								).
								Build(),
						},
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
									PuCount:    1,
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "unknown sub coverage group ID", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithVehicleInChargeDistributionButMissingInMetadata(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithDefaultAmountBasedBillingDetails_TestOnly().
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       "vin1",
											Fraction: "1.0",
										},
									},
								).
								Build(),
						},
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
									PuCount:    1,
									VehiclesMetadata: []*ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata{
										{
											Vin:         "vin2",
											StatedValue: 10.0,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "couldn't find vehicle TIV for vin1", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithoutChargesDistribution(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithDefaultAmountBasedBillingDetails_TestOnly().
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_GeneralLiability,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       "vin1",
											Fraction: "1.0",
										},
									},
								).
								Build(),
						},
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
									PuCount:    1,
									VehiclesMetadata: []*ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata{
										{
											Vin:         "vin1",
											StatedValue: 10.0,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.NoError(t, err)
	require.NotNil(t, output)

	expectedRmlVehicles := map[string]application.RMLVehicle{
		"vin1": {
			ComputedFields: map[string]interface{}{
				"statedValueTIV":       "10",
				"compVehPremium":       "0",
				"collVehPremium":       "0",
				"liabVehPremium":       "0",
				"trlintCompVehPremium": "0",
				"trlintCollVehPremium": "0",
				"tlsVehPremium":        "0",
				"rentalVehPremium":     "0",
			},
		},
	}
	require.Equal(t, expectedRmlVehicles, output.Vehicles)
}

func Test_GetIOInputsFromPricingResponse_WithNilDistribution(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithDefaultAmountBasedBillingDetails_TestOnly().
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
								).
								WithDistributions(nil).
								Build(),
						},
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
									PuCount:    1,
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "distribution is nil", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithNilDistributionItem(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithDefaultAmountBasedBillingDetails_TestOnly().
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{nil},
								).
								Build(),
						},
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
									PuCount:    1,
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "distribution item is nil", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithInvalidDistributionItemFraction(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithDefaultAmountBasedBillingDetails_TestOnly().
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Fraction: "nan",
										},
									},
								).
								Build(),
						},
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
									PuCount:    1,
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.Error(t, err)
	require.Regexp(t, "failed to parse fraction", err.Error())
	require.Nil(t, output)
}

func Test_GetIOInputsFromPricingResponse_WithNilSurplusChargesAndNilCreditScore(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
									PuCount:    1,
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.NoError(t, err)
	require.NotNil(t, output)
	require.Nil(t, output.CreditScore)
	require.NotNil(t, output.ALAPDPlusFlatSurplusTax)
	require.NotNil(t, output.ALAPDPlusFlatStampingFee)
	require.NotNil(t, output.GLSurplusTax)
	require.NotNil(t, output.GLStampingFee)
	require.NotNil(t, output.MTCSurplusTax)
	require.NotNil(t, output.MTCStampingFee)
	require.Equal(t, 0.0, *output.ALAPDPlusFlatSurplusTax)
	require.Equal(t, 0.0, *output.ALAPDPlusFlatStampingFee)
	require.Equal(t, 0.0, *output.GLSurplusTax)
	require.Equal(t, 0.0, *output.GLStampingFee)
	require.Equal(t, 0.0, *output.MTCSurplusTax)
	require.Equal(t, 0.0, *output.MTCStampingFee)
}

func Test_GetIOInputsFromPricingResponse_WithOtherChargeDistributions(t *testing.T) {
	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{
							// Charge that could appear in rml vehicles,
							// but doesn't have any distribution.
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithDefaultAmountBasedBillingDetails_TestOnly().
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_Collision,
								).
								Build(),
							// Charge that could appear in rml vehicles,
							// but doesn't have a vehicle distribution.
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithDefaultAmountBasedBillingDetails_TestOnly().
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Unspecified,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       "id1",
											Fraction: "0.35",
										},
										{
											Id:       "id2",
											Fraction: "0.65",
										},
									},
								).
								Build(),
							// Charge that should appear in rml vehicles,
							// and has multiple distributions (one of them
							// being a vehicle distribution).
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("1234", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_Towing,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Unspecified,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       "id1",
											Fraction: "0.35",
										},
										{
											Id:       "id2",
											Fraction: "0.65",
										},
									},
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       "vin1",
											Fraction: "1.0",
										},
									},
								).
								Build(),
						},
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
									PuCount:    1,
									VehiclesMetadata: []*ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata{
										{
											Vin:         "vin1",
											StatedValue: 10.0,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.NoError(t, err)
	require.NotNil(t, output)

	expectedRmlVehicles := map[string]application.RMLVehicle{
		"vin1": {
			ComputedFields: map[string]interface{}{
				"statedValueTIV":       "10",
				"compVehPremium":       "0",
				"collVehPremium":       "0",
				"liabVehPremium":       "0",
				"trlintCompVehPremium": "0",
				"trlintCollVehPremium": "0",
				"tlsVehPremium":        "1234",
				"rentalVehPremium":     "0",
			},
		},
	}
	require.Equal(t, expectedRmlVehicles, output.Vehicles)
}

func Test_GetIOInputsFromPricingResponse_WithAllFieldsPresent(t *testing.T) {
	vin1 := "vin1"
	vin2 := "vin2"
	vin3 := "vin3"

	policy1 := "policy1"
	policy2 := "policy2"
	policy3 := "policy3"

	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: policy1,
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
			{
				PolicyNumber: policy2,
				PolicyName:   ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
			},
			{
				PolicyNumber: policy3,
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: policy1,
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("100", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("200", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("300", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("400", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_UnderInsuredMotorist,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("500", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_UMUIM,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("600", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("700", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("800", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("900", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("1000", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("1100", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_PropertyProtectionInsurance,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("1200", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_HiredAuto,
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithMCCASurchargeType().
								WithAmountBasedBillingDetails("1300.07", nil).
								WithChargeablePolicy(policy1).
								Build(),
							ptypes.NewChargeBuilder().
								WithNCRFSurchargeType().
								WithAmountBasedBillingDetails("1400.05", nil).
								WithChargeablePolicy(policy1).
								Build(),
							ptypes.NewChargeBuilder().
								WithSurplusTaxSurchargeType().
								WithAmountBasedBillingDetails("1500", nil).
								WithChargeablePolicy(policy1).
								Build(),
							ptypes.NewChargeBuilder().
								WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
								WithAmountBasedBillingDetails("1600", nil).
								WithChargeablePolicy(policy1).
								Build(),
							ptypes.NewChargeBuilder().
								WithStampingFeeSurchargeType().
								WithAmountBasedBillingDetails("1700", nil).
								WithChargeablePolicy(policy1).
								Build(),
							ptypes.NewChargeBuilder().
								WithStampingFeeFromFullyEarnedPremiumSurchargeType().
								WithAmountBasedBillingDetails("1800", nil).
								WithChargeablePolicy(policy1).
								Build(),
							ptypes.NewChargeBuilder().
								WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().
								WithAmountBasedBillingDetails("1900", nil).
								WithChargeablePolicy(policy1).
								Build(),
							ptypes.NewChargeBuilder().
								WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().
								WithAmountBasedBillingDetails("2000", nil).
								WithChargeablePolicy(policy1).
								Build(),
							ptypes.NewChargeBuilder().
								WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().
								WithAmountBasedBillingDetails("2100", nil).
								WithChargeablePolicy(policy1).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("2200", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_Collision,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("2300", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("2400", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_TrailerInterchange,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("2500", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_NonOwnedTrailer,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("2600", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_Towing,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("2700", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.33",
										},
										{
											Id:       vin2,
											Fraction: "0.33",
										},
										{
											Id:       vin3,
											Fraction: "0.34",
										},
									},
								).
								Build(),
						},
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore:  model.USDOTScore_US_DOT_SCORE_A04,
									CreditScore: pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_A2),
									PuCount:     9,
									VehiclesMetadata: []*ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata{
										{
											Vin:         vin1,
											StatedValue: 10.0,
										},
										{
											Vin:         vin2,
											StatedValue: 11.0,
										},
										{
											Vin:         vin3,
											StatedValue: 15.0,
										},
									},
								},
							},
						},
					},
				},
			},
			{
				PolicyNumber: policy2,
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("4000", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_GeneralLiability,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.40005",
										},
										{
											Id:       vin2,
											Fraction: "0.20005",
										},
										{
											Id:       vin3,
											Fraction: "0.3999",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithSurplusTaxSurchargeType().
								WithAmountBasedBillingDetails("4100", nil).
								WithChargeablePolicy(policy2).
								Build(),
							ptypes.NewChargeBuilder().
								WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
								WithAmountBasedBillingDetails("4200", nil).
								WithChargeablePolicy(policy2).
								Build(),
							ptypes.NewChargeBuilder().
								WithStampingFeeSurchargeType().
								WithAmountBasedBillingDetails("4300", nil).
								WithChargeablePolicy(policy2).
								Build(),
							ptypes.NewChargeBuilder().
								WithStampingFeeFromFullyEarnedPremiumSurchargeType().
								WithAmountBasedBillingDetails("4400", nil).
								WithChargeablePolicy(policy2).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithBlanketWaiverOfSubrogation().
								WithAmountBasedBillingDetails("4500", nil).
								WithChargeablePolicy(policy2).
								Build(),
						},
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore:  model.USDOTScore_US_DOT_SCORE_A04,
									CreditScore: pointer_utils.ToPointer(model.CreditScore_CREDIT_SCORE_A2),
									PuCount:     9,
									// We add a duplicated VIN for a non-AL/APD policy
									// to test that it doesn't fail, because it doesn't
									// care about these values
									VehiclesMetadata: []*ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata{
										{
											Vin: vin1,
										},
										{
											Vin: vin2,
										},
										{
											Vin: vin3,
										},
										{
											Vin: vin3,
										},
									},
								},
							},
						},
					},
				},
			},
			{
				PolicyNumber: policy3,
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("3000", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_Cargo,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.35",
										},
										{
											Id:       vin2,
											Fraction: "0.65",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("3100", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.35",
										},
										{
											Id:       vin2,
											Fraction: "0.65",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("3200", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_ReeferWithHumanError,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.35",
										},
										{
											Id:       vin2,
											Fraction: "0.65",
										},
									},
								).
								Build(),
							ptypes.NewChargeBuilder().
								WithSurplusTaxSurchargeType().
								WithAmountBasedBillingDetails("3300", nil).
								WithChargeablePolicy(policy3).
								Build(),
							ptypes.NewChargeBuilder().
								WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
								WithAmountBasedBillingDetails("3400", nil).
								WithChargeablePolicy(policy3).
								Build(),
							ptypes.NewChargeBuilder().
								WithStampingFeeSurchargeType().
								WithAmountBasedBillingDetails("3500", nil).
								WithChargeablePolicy(policy3).
								Build(),
							ptypes.NewChargeBuilder().
								WithStampingFeeFromFullyEarnedPremiumSurchargeType().
								WithAmountBasedBillingDetails("3600", nil).
								WithChargeablePolicy(policy3).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithBlanketRegularAdditionalInsured().
								WithAmountBasedBillingDetails("3700", nil).
								WithChargeablePolicy(policy3).
								Build(),
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().
								WithAmountBasedBillingDetails("3800", nil).
								WithChargeablePolicy(policy3).
								Build(),
						},
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore:  model.USDOTScore_US_DOT_SCORE_A04,
									CreditScore: nil, // we make this nil to test that it doesn't error out
									PuCount:     9,
									// We add a duplicated VIN for a non-AL/APD policy
									// to test that it doesn't fail, because it doesn't
									// care about these values
									VehiclesMetadata: []*ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata{
										{
											Vin:         vin1,
											StatedValue: 0,
										},
										{
											Vin:         vin2,
											StatedValue: 0,
										},
										{
											Vin:         vin3,
											StatedValue: 0,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.NoError(t, err)
	require.NotNil(t, output)

	require.Equal(t, 56500.12, output.TotalPremium)
	require.Equal(t, 35800.0, output.SubTotalPremium)
	require.Equal(t, 18000.0, output.FlatPremium)
	require.Equal(t, 2700.12, output.SurchargePremium)
	require.Equal(t, 7800.0, output.ALPremium)
	require.Equal(t, 14700.0, output.APDPremium)
	require.Equal(t, 4000.0, output.GLPremium)
	require.Equal(t, 9300.0, output.MTCPremium)
	require.Equal(t, 2400.0, output.TIPremium)
	require.Equal(t, 3100.0, output.ReeferPremium)
	require.Equal(t, 2600.0, output.TowingPremium)
	require.Equal(t, 2700.0, output.RentalPremium)
	require.Equal(t, 800.0, output.PIPPremium)

	require.Equal(t, 3100.0, *output.ALAPDPlusFlatSurplusTax)
	require.Equal(t, 3500.0, *output.ALAPDPlusFlatStampingFee)
	require.Equal(t, 8300.0, *output.GLSurplusTax)
	require.Equal(t, 8700.0, *output.GLStampingFee)
	require.Equal(t, 6700.0, *output.MTCSurplusTax)
	require.Equal(t, 7100.0, *output.MTCStampingFee)

	require.Equal(t, 9.0, output.PUCount)

	require.Equal(t, 36.0, output.TIV)

	require.Equal(t, 866.*********, output.ALPremiumPPU)
	require.Equal(t, 1033.*********, output.MTCPremiumPPU)

	require.Equal(t, 408.*********, output.APDPremiumPTIV)

	require.Equal(t, nfenums.CreditScoreA2, *output.CreditScore)

	expectedRmlCompany := application.RMLCompany{
		ComputedFields: map[string]interface{}{
			"usdotScoreGrp": "a04",
		},
	}
	require.Equal(t, expectedRmlCompany, output.Company)

	expectedRmlVehicles := map[string]application.RMLVehicle{
		"vin1": {
			ComputedFields: map[string]interface{}{
				"collVehPremium":       "726",
				"compVehPremium":       "759",
				"liabVehPremium":       "2178",
				"rentalVehPremium":     "891",
				"tlsVehPremium":        "858",
				"statedValueTIV":       "10",
				"trlintCollVehPremium": "792",
				"trlintCompVehPremium": "0",
			},
		},
		"vin2": {
			ComputedFields: map[string]interface{}{
				"collVehPremium":       "726",
				"compVehPremium":       "759",
				"liabVehPremium":       "2178",
				"rentalVehPremium":     "891",
				"tlsVehPremium":        "858",
				"statedValueTIV":       "11",
				"trlintCollVehPremium": "792",
				"trlintCompVehPremium": "0",
			},
		},
		"vin3": {
			ComputedFields: map[string]interface{}{
				"collVehPremium":       "748",
				"compVehPremium":       "782",
				"liabVehPremium":       "2244",
				"rentalVehPremium":     "918",
				"tlsVehPremium":        "884",
				"statedValueTIV":       "15",
				"trlintCollVehPremium": "816",
				"trlintCompVehPremium": "0",
			},
		},
	}
	require.Equal(t, expectedRmlVehicles, output.Vehicles)
}

func Test_GetIOInputsFromPricingResponse_WithRoundingDueToDistribution(t *testing.T) {
	vin1 := "vin1"
	vin2 := "vin2"

	req := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			},
		},
	}
	resp := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: "policy1",
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						Charges: []*ptypes.Charge{
							ptypes.NewChargeBuilder().
								WithBaseChargeTypeWithoutExtraInfo().
								WithAmountBasedBillingDetails("2681", nil).
								WithChargeableSubCoverageGroup(
									ptypes.SubCoverageType_SubCoverageType_Collision,
								).
								WithDistribution(
									ptypes.Charge_DistributionType_Vehicle,
									[]*ptypes.Charge_DistributionItem{
										{
											Id:       vin1,
											Fraction: "0.5569",
										},
										{
											Id:       vin2,
											Fraction: "0.4431",
										},
									},
								).
								Build(),
						},
						Metadata: &ptypes.ChunkOutput_Metadata{
							ProgramSpecificMetadata: &ptypes.ChunkOutput_Metadata_NonFleetChunkOutputMetadata{
								NonFleetChunkOutputMetadata: &ptypes.NonFleet_ChunkOutputMetadata{
									UsDOTScore: model.USDOTScore_US_DOT_SCORE_A04,
									PuCount:    2,
									VehiclesMetadata: []*ptypes.NonFleet_ChunkOutputMetadata_VehicleMetadata{
										{
											Vin:         vin1,
											StatedValue: 10.0,
										},
										{
											Vin:         vin2,
											StatedValue: 15.0,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	output, err := GetIOInputsFromPricingResponse(req, resp)
	require.NoError(t, err)
	require.NotNil(t, output)

	expectedRmlVehicles := map[string]application.RMLVehicle{
		"vin1": {
			ComputedFields: map[string]interface{}{
				"statedValueTIV":       "10",
				"compVehPremium":       "0",
				"collVehPremium":       "1493",
				"liabVehPremium":       "0",
				"trlintCompVehPremium": "0",
				"trlintCollVehPremium": "0",
				"tlsVehPremium":        "0",
				"rentalVehPremium":     "0",
			},
		},
		"vin2": {
			ComputedFields: map[string]interface{}{
				"statedValueTIV":       "15",
				"compVehPremium":       "0",
				"collVehPremium":       "1188",
				"liabVehPremium":       "0",
				"trlintCompVehPremium": "0",
				"trlintCollVehPremium": "0",
				"tlsVehPremium":        "0",
				"rentalVehPremium":     "0",
			},
		},
	}
	require.Equal(t, expectedRmlVehicles, output.Vehicles)
}
