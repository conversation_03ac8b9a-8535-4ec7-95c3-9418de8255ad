package indication_option_generation

import (
	"math"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nf_admitted_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	nf_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	"nirvanatech.com/nirvana/nonfleet/quote_generator"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

const unspecifiedRmlField = "unspecifiedRmlField"

var (
	biSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
		},
	}
	pdSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
		},
	}
	umSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist,
		},
	}
	uimSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_UnderInsuredMotorist,
		},
	}
	umuimSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_UMUIM,
		},
	}
	umpdSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
		},
	}
	medPaySubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
		},
	}
	pipSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection,
		},
	}
	pipWorkLossAndRPLServiceSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService,
		},
	}
	pipAttendantCareSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare,
		},
	}
	ppiAttendantCareSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_PropertyProtectionInsurance,
		},
	}
	hiredAutoAttendantCareSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_HiredAuto,
		},
	}
	collSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_Collision,
		},
	}
	compSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_Comprehensive,
		},
	}
	tiSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_TrailerInterchange,
		},
	}
	notSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_NonOwnedTrailer,
		},
	}
	towingSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_Towing,
		},
	}
	rentalSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
		},
	}
	glSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_GeneralLiability,
		},
	}
	cargoSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_Cargo,
		},
	}
	reeferWithoutHumanErrorSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError,
		},
	}
	reeferWithHumanErrorSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_ReeferWithHumanError,
		},
	}
)

var (
	biSubCoverageGroupID                       = biSubCoverageGroup.GetID()
	pdSubCoverageGroupID                       = pdSubCoverageGroup.GetID()
	umSubCoverageGroupID                       = umSubCoverageGroup.GetID()
	uimSubCoverageGroupID                      = uimSubCoverageGroup.GetID()
	umuimSubCoverageGroupID                    = umuimSubCoverageGroup.GetID()
	umpdSubCoverageGroupID                     = umpdSubCoverageGroup.GetID()
	medPaySubCoverageGroupID                   = medPaySubCoverageGroup.GetID()
	pipSubCoverageGroupID                      = pipSubCoverageGroup.GetID()
	pipWorkLossAndRPLServiceSubCoverageGroupID = pipWorkLossAndRPLServiceSubCoverageGroup.GetID()
	pipAttendantCareSubCoverageGroupID         = pipAttendantCareSubCoverageGroup.GetID()
	ppiAttendantCareSubCoverageGroupID         = ppiAttendantCareSubCoverageGroup.GetID()
	hiredAutoAttendantCareSubCoverageGroupID   = hiredAutoAttendantCareSubCoverageGroup.GetID()
	collSubCoverageGroupID                     = collSubCoverageGroup.GetID()
	compSubCoverageGroupID                     = compSubCoverageGroup.GetID()
	tiSubCoverageGroupID                       = tiSubCoverageGroup.GetID()
	notSubCoverageGroupID                      = notSubCoverageGroup.GetID()
	towingSubCoverageGroupID                   = towingSubCoverageGroup.GetID()
	rentalSubCoverageGroupID                   = rentalSubCoverageGroup.GetID()
	glSubCoverageGroupID                       = glSubCoverageGroup.GetID()
	cargoSubCoverageGroupID                    = cargoSubCoverageGroup.GetID()
	reeferWithoutHumanErrorSubCoverageGroupID  = reeferWithoutHumanErrorSubCoverageGroup.GetID()
	reeferWithHumanErrorSubCoverageGroupID     = reeferWithHumanErrorSubCoverageGroup.GetID()
)

func GetIOInputsFromPricingResponse(
	req *ptypes.Request,
	resp *ptypes.Response,
) (*quote_generator.IndicationOptionInputs, error) {
	if req == nil {
		return nil, errors.New("req is nil")
	}
	if resp == nil {
		return nil, errors.New("resp is nil")
	}

	seenPolicyNames := map[ptypes.PolicyName]struct{}{}
	policyNumberToPolicyName := make(map[string]ptypes.PolicyName)
	for _, policySpec := range req.PolicySpecs {
		policyNumber := policySpec.PolicyNumber
		policyName := policySpec.PolicyName

		if _, ok := seenPolicyNames[policyName]; ok {
			return nil, errors.Newf("duplicate policy name found: %s", policyName)
		}

		if _, ok := policyNumberToPolicyName[policyNumber]; ok {
			return nil, errors.Newf("duplicate policy number found: %s", policyNumber)
		}

		seenPolicyNames[policyName] = struct{}{}
		policyNumberToPolicyName[policyNumber] = policyName
	}

	var (
		aggregateCharges []*ptypes.Charge

		alAPDPlusFlatSurplusTax  decimal.Decimal
		alAPDPlusFlatStampingFee decimal.Decimal
		glSurplusTax             decimal.Decimal
		glStampingFee            decimal.Decimal
		mtcSurplusTax            decimal.Decimal
		mtcStampingFee           decimal.Decimal

		puCount *float64

		creditScore *nf_admitted_enums.CreditScore

		usDOTScore *nf_enums.USDotScore
	)

	vehiclesTIV := make(map[string]float64)

	for _, policyResult := range resp.PolicyOutputs {
		policyNumber := policyResult.PolicyNumber

		chunkOutputs := policyResult.ChunkOutputs
		if len(chunkOutputs) != 1 {
			return nil, errors.Newf("chunk outputs length is not 1 for policy %s", policyNumber)
		}

		chunkOutput := chunkOutputs[0]
		if chunkOutput == nil {
			return nil, errors.Newf("chunk output is nil for policy %s", policyNumber)
		}

		charges := chunkOutput.Charges
		aggregateCharges = append(aggregateCharges, charges...)

		surplusTax, err := calculateSurplusTax(charges)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to calculate surplus tax for policy %s", policyNumber)
		}

		stampingFee, err := calculateStampingFee(charges)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to calculate stamping fee for policy %s", policyNumber)
		}

		policyName := policyNumberToPolicyName[policyNumber]

		//nolint:exhaustive
		switch policyName {
		case ptypes.PolicyName_PolicyName_MOTOR_CARRIER:
			alAPDPlusFlatSurplusTax = surplusTax
			alAPDPlusFlatStampingFee = stampingFee
		case ptypes.PolicyName_PolicyName_GENERAL_LIABILITY:
			glSurplusTax = surplusTax
			glStampingFee = stampingFee
		case ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO:
			mtcSurplusTax = surplusTax
			mtcStampingFee = stampingFee
		default:
			return nil, errors.Newf("unsupported policy name %s for policy %s", policyName, policyNumber)
		}

		metadata := chunkOutput.GetMetadata()
		if metadata == nil {
			return nil, errors.Newf("response metadata is nil for policy %s", policyNumber)
		}

		nfMetadata := metadata.GetNonFleetChunkOutputMetadata()
		if nfMetadata == nil {
			return nil, errors.Newf("response NF metadata is nil for policy %s", policyNumber)
		}

		policyPUCount := pointer_utils.ToPointer(float64(nfMetadata.PuCount))
		if puCount == nil {
			puCount = policyPUCount
		} else if *puCount != *policyPUCount {
			return nil, errors.Newf(
				"puCount mismatch for policy %s: %f | %f",
				policyNumber,
				*puCount,
				*policyPUCount,
			)
		}

		if policyName == ptypes.PolicyName_PolicyName_MOTOR_CARRIER {
			policyVehiclesMetadata := nfMetadata.VehiclesMetadata
			for _, vehicleMetadata := range policyVehiclesMetadata {
				vin := vehicleMetadata.Vin

				_, ok := vehiclesTIV[vin]
				if ok {
					return nil, errors.Newf(
						"duplicate vehicle vin %s in policy %s",
						vin,
						policyNumber,
					)
				}

				vehiclesTIV[vin] = vehicleMetadata.StatedValue
			}
		}

		policyCreditScore := nfMetadata.CreditScore
		if policyCreditScore != nil {
			transformedPolicyCreditScore, err := nf_admitted_enums.GetNFCreditScoreEnumFromNFCreditScoreProto(*policyCreditScore)
			if err != nil {
				return nil, errors.Wrapf(
					err,
					"failed to transform credit score %s for policy %s",
					policyCreditScore,
					policyNumber,
				)
			}
			if creditScore == nil {
				creditScore = transformedPolicyCreditScore
			} else if *creditScore != *transformedPolicyCreditScore {
				return nil, errors.Newf(
					"credit score mismatch for policy %s: %v | %v",
					policyNumber,
					*creditScore,
					*transformedPolicyCreditScore,
				)
			}
		}

		policyUSDOTScore := nfMetadata.UsDOTScore
		transformedPolicyUSDOTScore, err := nf_enums.GetNFUSDotEnumFromNFUSDOTProto(policyUSDOTScore)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to transform US DOT score %s for policy %s",
				policyUSDOTScore,
				policyNumber,
			)
		}
		if usDOTScore == nil {
			usDOTScore = transformedPolicyUSDOTScore
		} else if *usDOTScore != *transformedPolicyUSDOTScore {
			return nil, errors.Newf(
				"US DOT score mismatch for policy %s: %s | %s",
				policyNumber,
				*usDOTScore,
				*transformedPolicyUSDOTScore,
			)
		}
	}

	// Not possible, but adding line helps with editor checks.
	if puCount == nil {
		panic("puCount is nil after processing all policies")
	}

	// Not possible, but adding line helps with editor checks.
	if usDOTScore == nil {
		panic("usDOTScore is nil after processing all policies")
	}

	totalPremium, err := calculateTotalPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate total premium")
	}

	subTotalPremium, err := calculateSubTotalPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate sub total premium")
	}

	feePremium, err := calculateFeePremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate fee premium")
	}

	surchargePremium, err := calculateSurchargePremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate surcharge premium")
	}

	alPremium, err := calculateALPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate AL premium")
	}

	pipPremium, err := calculatePIPPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate PIP premium")
	}

	apdPremium, err := calculateAPDPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate APD premium")
	}

	collPremium, err := calculateCollPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate COLL premium")
	}

	compPremium, err := calculateCompPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate COMP premium")
	}

	tiPremium, err := calculateTIPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate TI premium")
	}

	towingPremium, err := calculateTowingPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate towing premium")
	}

	rentalPremium, err := calculateRentalPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate rental premium")
	}

	glPremium, err := calculateGLPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate GL premium")
	}

	mtcPremium, err := calculateMTCPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate MTC premium")
	}

	reeferPremium, err := calculateReeferWithoutHumanErrorPremium(aggregateCharges)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to calculate reefer premium")
	}

	totalPremiumF, _ := totalPremium.Float64()
	subTotalPremiumF, _ := subTotalPremium.Float64()

	feePremiumF, _ := feePremium.Float64()
	surchargePremiumF, _ := surchargePremium.Float64()

	alAPDPlusFlatSurplusTaxF, _ := alAPDPlusFlatSurplusTax.Float64()
	alAPDPlusFlatStampingFeeF, _ := alAPDPlusFlatStampingFee.Float64()
	glSurplusTaxF, _ := glSurplusTax.Float64()
	glStampingFeeF, _ := glStampingFee.Float64()
	mtcSurplusTaxF, _ := mtcSurplusTax.Float64()
	mtcStampingFeeF, _ := mtcStampingFee.Float64()

	alPremiumF, _ := alPremium.Float64()
	apdPremiumF, _ := apdPremium.Float64()
	glPremiumF, _ := glPremium.Float64()
	mtcPremiumF, _ := mtcPremium.Float64()
	tiPremiumF, _ := tiPremium.Float64()
	reeferPremiumF, _ := reeferPremium.Float64()
	towingPremiumF, _ := towingPremium.Float64()
	rentalPremiumF, _ := rentalPremium.Float64()
	pipPremiumF, _ := pipPremium.Float64()

	puCountD := decimal.NewFromFloat(*puCount)
	if puCountD.IsZero() {
		return nil, errors.Newf("puCount is 0 after processing all policies")
	}

	alPremiumPPUF, _ := alPremium.Div(puCountD).Round(9).Float64()
	mtcPremiumPPUF, _ := mtcPremium.Div(puCountD).Round(9).Float64()

	policyTIV := 0.0
	for _, vehicleTIV := range vehiclesTIV {
		policyTIV += vehicleTIV
	}
	policyTIV = math.Round(policyTIV)

	// policyTIV can be zero when the customer doesn't select APD coverage
	// (only consider COLL and COMP for this)
	shouldPolicyTIVBeNonZero := !collPremium.IsZero() || !compPremium.IsZero()
	if shouldPolicyTIVBeNonZero && policyTIV == 0 {
		return nil, errors.Newf("policyTIV is 0 after processing all policies, but COLL/COMP premium is not 0")
	}

	apdPremiumPTIVF := 0.0
	if policyTIV != 0 {
		tivD := decimal.NewFromFloat(policyTIV)
		apdPremiumPTIVF, _ = apdPremium.Div(tivD).Round(9).Float64()
	}

	vehicles, err := buildRMLVehicles(aggregateCharges, vehiclesTIV)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to build RML vehicles")
	}

	return &quote_generator.IndicationOptionInputs{
		TotalPremium:             totalPremiumF,
		SubTotalPremium:          subTotalPremiumF,
		ALPremium:                alPremiumF,
		ALPremiumPPU:             alPremiumPPUF,
		APDPremium:               apdPremiumF,
		APDPremiumPTIV:           apdPremiumPTIVF,
		GLPremium:                glPremiumF,
		MTCPremium:               mtcPremiumF,
		MTCPremiumPPU:            mtcPremiumPPUF,
		TIPremium:                tiPremiumF,
		ReeferPremium:            reeferPremiumF,
		TowingPremium:            towingPremiumF,
		RentalPremium:            rentalPremiumF,
		PIPPremium:               pipPremiumF,
		FlatPremium:              feePremiumF,
		SurchargePremium:         surchargePremiumF,
		ALAPDPlusFlatSurplusTax:  &alAPDPlusFlatSurplusTaxF,
		ALAPDPlusFlatStampingFee: &alAPDPlusFlatStampingFeeF,
		GLSurplusTax:             &glSurplusTaxF,
		GLStampingFee:            &glStampingFeeF,
		MTCSurplusTax:            &mtcSurplusTaxF,
		MTCStampingFee:           &mtcStampingFeeF,
		PUCount:                  *puCount,
		TIV:                      policyTIV,
		CreditScore:              creditScore,
		Vehicles:                 vehicles,
		Company: application.RMLCompany{
			ComputedFields: map[string]interface{}{
				companyUSDOTScoreRmlField: strings.ToLower(usDOTScore.String()),
			},
		},
	}, nil
}

func calculateTotalPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateFilteredChargesTotalPremium(charges, func(charge *ptypes.Charge) (bool, error) {
		if charge == nil {
			return false, errors.New("charge is nil")
		}
		if isChargeSurplusTaxOrStampingFee(charge) {
			return false, nil
		}
		return true, nil
	})
}

func calculateSubTotalPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateFilteredChargesTotalPremium(charges, func(charge *ptypes.Charge) (bool, error) {
		if charge == nil {
			return false, errors.New("charge is nil")
		}
		if charge.IsFullyEarnedCharge() || charge.IsSurcharge() {
			return false, nil
		}
		return true, nil
	})
}

func calculateFeePremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateFilteredChargesTotalPremium(charges, func(charge *ptypes.Charge) (bool, error) {
		if charge == nil {
			return false, errors.New("charge is nil")
		}
		return charge.IsFullyEarnedCharge(), nil
	})
}

func calculateSurchargePremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateFilteredChargesTotalPremium(charges, func(charge *ptypes.Charge) (bool, error) {
		if charge == nil {
			return false, errors.New("charge is nil")
		}

		if !charge.IsSurcharge() {
			return false, nil
		}

		if isChargeSurplusTaxOrStampingFee(charge) {
			return false, nil
		}

		return true, nil
	})
}

func calculateSurplusTax(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateFilteredChargesTotalPremium(charges, func(charge *ptypes.Charge) (bool, error) {
		if charge == nil {
			return false, errors.New("charge is nil")
		}
		return isChargeSurplusTax(charge), nil
	})
}

func calculateStampingFee(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateFilteredChargesTotalPremium(charges, func(charge *ptypes.Charge) (bool, error) {
		if charge == nil {
			return false, errors.New("charge is nil")
		}
		return isChargeStampingFee(charge), nil
	})
}

func isChargeSurplusTaxOrStampingFee(charge *ptypes.Charge) bool {
	return isChargeSurplusTax(charge) || isChargeStampingFee(charge)
}

func isChargeSurplusTax(charge *ptypes.Charge) bool {
	return charge.IsSurplusTaxSurcharge() || charge.IsSurplusTaxSurchargeFromFullyEarnedPremium()
}

func isChargeStampingFee(charge *ptypes.Charge) bool {
	return charge.IsStampingFeeSurcharge() || charge.IsStampingFeeSurchargeFromFullyEarnedPremium()
}

func calculateALPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateSubCoveragesPremium(
		charges,
		biSubCoverageGroup,
		pdSubCoverageGroup,
		umSubCoverageGroup,
		uimSubCoverageGroup,
		umuimSubCoverageGroup,
		umpdSubCoverageGroup,
		medPaySubCoverageGroup,
		pipSubCoverageGroup,
		pipWorkLossAndRPLServiceSubCoverageGroup,
		pipAttendantCareSubCoverageGroup,
		ppiAttendantCareSubCoverageGroup,
		hiredAutoAttendantCareSubCoverageGroup,
	)
}

func calculateAPDPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateSubCoveragesPremium(
		charges,
		collSubCoverageGroup,
		compSubCoverageGroup,
		tiSubCoverageGroup,
		notSubCoverageGroup,
		towingSubCoverageGroup,
		rentalSubCoverageGroup,
	)
}

func calculateGLPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateSubCoveragesPremium(charges, glSubCoverageGroup)
}

func calculateMTCPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateSubCoveragesPremium(
		charges,
		cargoSubCoverageGroup,
		reeferWithoutHumanErrorSubCoverageGroup,
		reeferWithHumanErrorSubCoverageGroup,
	)
}

func calculateCollPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateSubCoveragesPremium(charges, collSubCoverageGroup)
}

func calculateCompPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateSubCoveragesPremium(charges, compSubCoverageGroup)
}

func calculateTIPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateSubCoveragesPremium(charges, tiSubCoverageGroup)
}

func calculateReeferWithoutHumanErrorPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateSubCoveragesPremium(charges, reeferWithoutHumanErrorSubCoverageGroup)
}

func calculateTowingPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateSubCoveragesPremium(charges, towingSubCoverageGroup)
}

func calculateRentalPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateSubCoveragesPremium(charges, rentalSubCoverageGroup)
}

func calculatePIPPremium(charges []*ptypes.Charge) (decimal.Decimal, error) {
	return calculateSubCoveragesPremium(charges, pipSubCoverageGroup)
}

func calculateSubCoveragesPremium(
	charges []*ptypes.Charge,
	targetSubCoverageGroups ...*ptypes.SubCoverageGroup,
) (decimal.Decimal, error) {
	targetSubCoverageGroupIDs := make(map[string]struct{})
	for _, group := range targetSubCoverageGroups {
		targetSubCoverageGroupIDs[group.GetID()] = struct{}{}
	}

	return calculateFilteredChargesTotalPremium(charges, func(charge *ptypes.Charge) (bool, error) {
		if charge == nil {
			return false, errors.New("charge is nil")
		}

		if charge.IsFullyEarnedCharge() || charge.IsSurcharge() {
			return false, nil
		}
		if !charge.AppliesToSubCoverageGroup() {
			return false, nil
		}

		groupID := charge.GetChargedSubCoverageGroup().GetGroup().GetID()
		if _, ok := targetSubCoverageGroupIDs[groupID]; !ok {
			return false, nil
		}

		return true, nil
	})
}

func calculateFilteredChargesTotalPremium(
	charges []*ptypes.Charge,
	filterFn func(charge *ptypes.Charge) (bool, error),
) (decimal.Decimal, error) {
	filteredCharges, err := filterCharges(charges, filterFn)
	if err != nil {
		return decimal.Zero, err
	}
	return calculateChargesTotalPremium(filteredCharges)
}

func filterCharges(
	charges []*ptypes.Charge,
	filterFn func(charge *ptypes.Charge) (bool, error),
) ([]*ptypes.Charge, error) {
	filteredCharges := make([]*ptypes.Charge, 0)
	for _, charge := range charges {
		if charge == nil {
			return nil, errors.New("charge is nil")
		}

		shouldKeep, err := filterFn(charge)
		if err != nil {
			return nil, err
		}

		if shouldKeep {
			filteredCharges = append(filteredCharges, charge)
		}
	}

	return filteredCharges, nil
}

// calculateChargesTotalPremium assumes that all charges are
// charges with amount based billing details that don't need
// exposure to calculate their total premium (which is true
// for NF charges).
func calculateChargesTotalPremium(
	charges []*ptypes.Charge,
) (decimal.Decimal, error) {
	res := decimal.Zero
	for _, charge := range charges {
		if charge == nil {
			return res, errors.New("charge is nil")
		}

		amount, err := charge.Calculate(nil)
		if err != nil {
			return decimal.Zero, err
		}

		res = res.Add(amount)
	}

	return res, nil
}

func buildRMLVehicles(
	charges []*ptypes.Charge,
	vehiclesTIV map[string]float64,
) (map[string]application.RMLVehicle, error) {
	// Map is VIN -> RmlFieldID -> Amount
	vehiclesInfo := make(map[string]map[string]decimal.Decimal)
	for _, charge := range charges {
		if charge == nil {
			return nil, errors.New("charge is nil")
		}

		if charge.IsFullyEarnedCharge() || charge.IsSurcharge() {
			continue
		}

		if !charge.AppliesToSubCoverageGroup() {
			continue
		}

		groupID := charge.GetChargedSubCoverageGroup().GetGroup().GetID()
		rmlFieldID, err := getRmlFieldIDFromSubCoverageGroupID(groupID)
		if err != nil {
			return nil, err
		}

		amount, err := charge.Calculate(nil)
		if err != nil {
			return nil, err
		}

		distributions := charge.Distributions
		if len(distributions) == 0 {
			continue
		}

		var vehicleDistribution *ptypes.Charge_Distribution
		for _, distribution := range distributions {
			if distribution == nil {
				return nil, errors.New("distribution is nil")
			}

			if distribution.GetType() == ptypes.Charge_DistributionType_Vehicle {
				vehicleDistribution = distribution
				break
			}
		}

		if vehicleDistribution == nil {
			continue
		}

		curr := decimal.Zero
		items := vehicleDistribution.GetItems()
		for i, item := range items {
			if item == nil {
				return nil, errors.New("distribution item is nil")
			}

			vehicleID := item.GetId()

			vehicleInfo, ok := vehiclesInfo[vehicleID]
			if !ok {
				vehicleInfo = make(map[string]decimal.Decimal)
				vehiclesInfo[vehicleID] = vehicleInfo
			}

			fractionAsString := item.GetFraction()
			fraction, err := decimal.NewFromString(fractionAsString)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to parse fraction %s", fractionAsString)
			}

			var vehicleAmount decimal.Decimal
			if i < len(items)-1 {
				vehicleAmount = amount.Mul(fraction).Round(0)
				curr = curr.Add(vehicleAmount)
			} else {
				vehicleAmount = amount.Sub(curr).Round(0)
			}

			vehicleInfo[rmlFieldID] = vehicleInfo[rmlFieldID].Add(vehicleAmount)
		}
	}

	rmlVehicles := make(map[string]application.RMLVehicle)
	for vehicleID, vehicleInfo := range vehiclesInfo {
		vehicleTIV, ok := vehiclesTIV[vehicleID]
		if !ok {
			return nil, errors.Newf("couldn't find vehicle TIV for %s", vehicleID)
		}

		vehicleTIVD := decimal.NewFromFloat(vehicleTIV)

		// We don't have the split of TI COLL and TI COMP in the new
		// Pricing API, so we just put the TI total premium into the
		// TI COLL field, and leave the TI COMP field as 0.
		//
		// We also want all fields to be present with a default of zero
		// to match the behavior of the old Pricing API.
		computedFields := map[string]interface{}{
			vehicleStatedValueRmlField:   vehicleTIVD.String(),
			vehicleCollPremiumRmlField:   "0",
			vehicleCompPremiumRmlField:   "0",
			vehicleBIPDPremiumRmlField:   "0",
			vehicleTICollPremiumRmlField: "0",
			vehicleTICompPremiumRmlField: "0",
			vehicleTowingPremiumRmlField: "0",
			vehicleRentalPremiumRmlField: "0",
		}

		for rmlFieldID, amount := range vehicleInfo {
			if rmlFieldID == unspecifiedRmlField {
				continue
			}
			computedFields[rmlFieldID] = amount.String()
		}

		rmlVehicles[vehicleID] = application.RMLVehicle{
			ComputedFields: computedFields,
		}
	}

	return rmlVehicles, nil
}

func getRmlFieldIDFromSubCoverageGroupID(subCoverageGroupID string) (string, error) {
	switch subCoverageGroupID {
	case biSubCoverageGroupID:
		return vehicleBIPDPremiumRmlField, nil
	case pdSubCoverageGroupID:
		return vehicleBIPDPremiumRmlField, nil
	case umSubCoverageGroupID:
		return vehicleBIPDPremiumRmlField, nil
	case uimSubCoverageGroupID:
		return vehicleBIPDPremiumRmlField, nil
	case umuimSubCoverageGroupID:
		return vehicleBIPDPremiumRmlField, nil
	case umpdSubCoverageGroupID:
		return vehicleBIPDPremiumRmlField, nil
	case medPaySubCoverageGroupID:
		return vehicleBIPDPremiumRmlField, nil
	case pipSubCoverageGroupID:
		return vehicleBIPDPremiumRmlField, nil
	case pipWorkLossAndRPLServiceSubCoverageGroupID:
		return vehicleBIPDPremiumRmlField, nil
	case pipAttendantCareSubCoverageGroupID:
		return vehicleBIPDPremiumRmlField, nil
	case ppiAttendantCareSubCoverageGroupID:
		return vehicleBIPDPremiumRmlField, nil
	case hiredAutoAttendantCareSubCoverageGroupID:
		return unspecifiedRmlField, nil
	case collSubCoverageGroupID:
		return vehicleCollPremiumRmlField, nil
	case compSubCoverageGroupID:
		return vehicleCompPremiumRmlField, nil
	case tiSubCoverageGroupID:
		return vehicleTICollPremiumRmlField, nil
	case notSubCoverageGroupID:
		return unspecifiedRmlField, nil
	case towingSubCoverageGroupID:
		return vehicleTowingPremiumRmlField, nil
	case rentalSubCoverageGroupID:
		return vehicleRentalPremiumRmlField, nil
	case glSubCoverageGroupID:
		return unspecifiedRmlField, nil
	case cargoSubCoverageGroupID:
		return unspecifiedRmlField, nil
	case reeferWithoutHumanErrorSubCoverageGroupID:
		return unspecifiedRmlField, nil
	case reeferWithHumanErrorSubCoverageGroupID:
		return unspecifiedRmlField, nil
	default:
		return "", errors.Newf("unknown sub coverage group ID %s", subCoverageGroupID)
	}
}
