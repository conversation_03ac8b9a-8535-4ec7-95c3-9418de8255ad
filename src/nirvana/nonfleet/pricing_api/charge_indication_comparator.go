package pricing_api

import (
	"context"

	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/common-go/log"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	nfapp "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/insurance-core/coverage"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func CompareQPCChargesAndIndicationOption(
	ctx context.Context,
	charges []*ptypes.Charge,
	indOpt nfapp.IndicationOption,
	subId string,
) (decimal.Decimal, bool) {
	if charges == nil || indOpt == nil || len(charges) == 0 {
		log.Warn(
			ctx, "Charges or indication option is nil or empty",
			log.SubID(subId), log.Any("charges", charges), log.Any("indOpt", indOpt),
		)
		return decimal.Zero, false
	}
	pkgType := indOpt.GetPackage()
	totalPremFromCharges := decimal.Zero
	primaryCovPremiumChargeMap := make(map[string]decimal.Decimal)
	compPremium := decimal.Zero
	collPremium := decimal.Zero
	surchargePremium := decimal.Zero
	feePremium := decimal.Zero
	for _, charge := range charges {
		chargePrem, err := charge.Calculate(nil)
		if err != nil {
			log.Warn(ctx, "Error calculating charge premium", log.Err(err), log.SubID(subId))
			return decimal.Zero, false
		}
		totalPremFromCharges = totalPremFromCharges.Add(chargePrem)

		if charge.IsBaseCharge() {
			compPremium, collPremium = populateCollAndCompPremiumFromCharges(
				charge,
				compPremium,
				collPremium,
				chargePrem,
			)

			if chargeableSubCovGroup := charge.GetChargedSubCoverageGroup(); chargeableSubCovGroup != nil {
				primaryCov, exists := getPrimaryCoverageFromPricingSubCov(ctx, chargeableSubCovGroup, subId)
				if !exists {
					continue
				}
				primaryCovPremiumChargeMap = addChargesToPrimaryCoverage(
					primaryCovPremiumChargeMap,
					*primaryCov,
					chargePrem,
				)
			} else if charge.IsFullyEarnedCharge() {
				feePremium = feePremium.Add(chargePrem)
			} else {
				log.Warn(ctx, "Unknown base charge", log.Any("charge", charge), log.SubID(subId))
			}
		} else if charge.IsSurcharge() {
			surchargePremium = surchargePremium.Add(chargePrem)
		} else {
			log.Warn(ctx, "Unknown charge type", log.Any("charge", charge), log.SubID(subId))
		}
	}

	comparePrimaryCoveragePremiums(ctx, indOpt, primaryCovPremiumChargeMap, subId, pkgType)
	indOptCompPremium := getCompPremiumFromIndicationOption(ctx, indOpt, subId, pkgType)
	compareDecimal(
		ctx,
		"Comprehensive Premium",
		compPremium,
		decimal.NewFromFloat(indOptCompPremium),
		subId, pkgType,
	)
	indOptCollPremium := getCollPremiumFromIndicationOption(ctx, indOpt, subId, pkgType)
	compareDecimal(ctx, "Collision Premium", collPremium, decimal.NewFromFloat(indOptCollPremium), subId, pkgType)
	compareDecimal(ctx, "Flat Charge Premium", feePremium,
		decimal.NewFromInt(int64(indOpt.GetFlatCharges())), subId, pkgType)
	compareDecimal(ctx, "Surcharge Premium", surchargePremium,
		decimal.NewFromInt(int64(indOpt.GetStateSurchargePremium())), subId, pkgType)
	// Total should match and hence only using this to log a warning
	indOptionTotalPremDecimal := decimal.NewFromInt32(indOpt.GetTotalPremium())
	if totalPremFromCharges.Sub(indOptionTotalPremDecimal).Abs().IntPart() <= 4 {
		log.Info(ctx, "Total Premiums are within $4", log.SubID(subId),
			log.String("packageType", pkgType.String()))
	} else {
		log.Info(ctx, "Total Premiums are not within $4", log.SubID(subId),
			log.String("packageType", pkgType.String()))
	}

	// We return the new premium value - the old premium value
	return totalPremFromCharges.Sub(indOptionTotalPremDecimal),
		compareDecimal(ctx, "Total Premium", totalPremFromCharges, indOptionTotalPremDecimal, subId, pkgType)
}

func populateCollAndCompPremiumFromCharges(
	charge *ptypes.Charge,
	compPremium decimal.Decimal,
	collPremium decimal.Decimal,
	chargePrem decimal.Decimal,
) (decimal.Decimal, decimal.Decimal) {
	if chargeableSubCovGroup := charge.GetChargedSubCoverageGroup(); chargeableSubCovGroup != nil {
		subCovGroup := chargeableSubCovGroup.GetGroup()
		subCovs := subCovGroup.GetSubCoverages()
		if len(subCovs) == 1 {
			// nolint:exhaustive
			switch subCovs[0] {
			case ptypes.SubCoverageType_SubCoverageType_Comprehensive:
				compPremium = compPremium.Add(chargePrem)
			case ptypes.SubCoverageType_SubCoverageType_Collision:
				collPremium = collPremium.Add(chargePrem)
			}
		}
	}
	return compPremium, collPremium
}

func getCompPremiumFromIndicationOption(
	ctx context.Context,
	indOpt nfapp.IndicationOption,
	subId string,
	pkgType appenums.IndicationOptionTag,
) float64 {
	indOptCompPremium, err := indOpt.GetTotalComprehensivePremium()
	if err != nil {
		log.Warn(ctx, "Error getting comprehensive premium from indication option", log.Err(err),
			log.SubID(subId), log.String("packageType", pkgType.String()))
	}
	return indOptCompPremium
}

func getCollPremiumFromIndicationOption(
	ctx context.Context,
	indOpt nfapp.IndicationOption,
	subId string,
	pkgType appenums.IndicationOptionTag,
) float64 {
	indOptCompPremium, err := indOpt.GetTotalCollisionPremium()
	if err != nil {
		log.Warn(ctx, "Error getting collision premium from indication option", log.Err(err),
			log.SubID(subId), log.String("packageType", pkgType.String()))
	}
	return indOptCompPremium
}

func getPrimaryCoverageFromPricingSubCov(
	ctx context.Context,
	chargeableSubCovGroup *ptypes.ChargeableSubCoverageGroup,
	subId string,
) (*appenums.Coverage, bool) {
	subCovGroup := chargeableSubCovGroup.GetGroup()
	subCovs := chargeableSubCovGroup.GetGroup().GetSubCoverages()
	if len(subCovs) == 0 {
		log.Error(
			ctx,
			"sub coverage group has no sub coverages",
			log.Any("subCovGroup", subCovGroup),
			log.SubID(subId),
		)
		return nil, false
	}

	if len(subCovs) > 1 {
		log.Error(
			ctx,
			"sub coverage group has more than one coverage",
			log.Any("subCovs", subCovs),
			log.SubID(subId),
		)
		return nil, false
	}

	subCov := subCovs[0]
	appCov, err := coverage.GetAppCoverageFromPricingSubCoverage(subCov)
	if err != nil || appCov == nil {
		log.Error(
			ctx,
			"Error getting app coverage from pricing sub coverage",
			log.String("SubCov", subCov.String()),
			log.SubID(subId),
		)
		return nil, false
	}

	primaryCov, err := appenums.GetPrimaryCoverageFromCoverage(*appCov)
	if err != nil {
		log.Error(ctx, "sub coverage not found in AncCoverageToPrimaryCoverage map",
			log.String("ancCoverageType", appCov.String()), log.SubID(subId), log.Err(err))
		return nil, false
	}
	return primaryCov, true
}

func comparePrimaryCoveragePremiums(
	ctx context.Context,
	indOpt nfapp.IndicationOption,
	primaryCovPremiumChargeMap map[string]decimal.Decimal,
	subId string,
	pkgType appenums.IndicationOptionTag,
) {
	for _, cov := range indOpt.GetCoverages() {
		indPremium := 0
		if cov.Premium != nil {
			indPremium = *cov.Premium
		}
		if !cov.CoverageType.IsPrimaryCoverage() {
			continue
		}
		chargePremium, ok := primaryCovPremiumChargeMap[cov.CoverageType.String()]
		if !ok {
			log.Warn(ctx, "Primary coverage not found in new charge computation", log.SubID(subId),
				log.String("packageType", pkgType.String()))
		}
		compareDecimal(
			ctx,
			cov.CoverageType.String(),
			chargePremium,
			decimal.NewFromInt(int64(indPremium)),
			subId, pkgType,
		)
	}
}

func addChargesToPrimaryCoverage(
	covPremiumChargeMap map[string]decimal.Decimal,
	coverage appenums.Coverage,
	chargePrem decimal.Decimal,
) map[string]decimal.Decimal {
	if _, ok := covPremiumChargeMap[coverage.String()]; !ok {
		covPremiumChargeMap[coverage.String()] = decimal.Zero
	}
	existingCharge := covPremiumChargeMap[coverage.String()]
	covPremiumChargeMap[coverage.String()] = existingCharge.Add(chargePrem)
	return covPremiumChargeMap
}

func compareDecimal(
	ctx context.Context,
	field string,
	charge,
	indication decimal.Decimal,
	subId string,
	packageType appenums.IndicationOptionTag,
) bool {
	if charge.Equal(indication) {
		return true
	}
	log.Warn(ctx, "Indication and ChargeValues are not equal",
		log.String("field", field),
		log.String("charge", charge.String()),
		log.String("indication", indication.String()),
		log.SubID(subId),
		log.String("packageType", packageType.String()),
	)
	return false
}
