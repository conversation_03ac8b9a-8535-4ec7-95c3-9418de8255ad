package quote_generator

import (
	"time"

	"nirvanatech.com/nirvana/common-go/us_states"

	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"

	"github.com/google/uuid"
)

// Admitted Application PDF Structs //

type AdmittedApplicationPDFInput struct {
	State              string                         `json:"state"`
	InsuredCompanyName string                         `json:"insuredCompanyName"`
	DotNumber          string                         `json:"dotNumber"`
	AgencyName         string                         `json:"agencyName"`
	ProducerName       string                         `json:"reviewerName"`
	CreatedAt          time.Time                      `json:"createdAt"`
	FormCode           string                         `json:"formCode"`
	InsuranceCarrier   string                         `json:"insuranceCarrier"`
	Operations         *AdmittedOperationRecord       `json:"operations,omitempty"`
	EquipmentList      *[]AdmittedEquipmentListRecord `json:"equipmentList,omitempty"`
	DriverList         *[]AdmittedDriverListRecord    `json:"driversList,omitempty"`
}

type AdmittedOperationRecord struct {
	EffectiveDate             time.Time                  `json:"effectiveDate"`
	Coverages                 []string                   `json:"coverages"`
	BusinessOwner             *AdmittedBusinessOwner     `json:"businessOwner,omitempty"`
	TerminalLocation          *AdmittedTerminalLocation  `json:"terminalLocation,omitempty"`
	FarthestRadiusOfOperation string                     `json:"farthestRadiusOfOperation"`
	AtFaultAlClaims           int                        `json:"atFaultAlClaims"`
	CommoditiesHauled         *[]AdmittedCommodityRecord `json:"commoditiesHauled,omitempty"`
}

type AdmittedBusinessOwner struct {
	FirstName string           `json:"firstName"`
	LastName  string           `json:"lastName"`
	Address   *AdmittedAddress `json:"address"`
}

type AdmittedAddress struct {
	City    string `json:"city"`
	State   string `json:"state"`
	Street  string `json:"street"`
	ZipCode string `json:"zipCode"`
}

type AdmittedTerminalLocation struct {
	AddressLineOne string `json:"addressLineOne"`
	AddressLineTwo string `json:"addressLineTwo"`
	USState        string `json:"usState"`
	ZipCode        string `json:"zipCode"`
}

type AdmittedCommodityRecord struct {
	Category          string `json:"category"`
	Commodity         string `json:"commodity"`
	PercentageOfHauls int    `json:"percentageOfHauls"`
}

type AdmittedEquipmentListRecord struct {
	VIN          string `json:"vin"`
	VehicleType  string `json:"vehicleType"`
	VehicleClass string `json:"vehicleClass"`
	Year         string `json:"year"`
	Make         string `json:"make"`
	Model        string `json:"model"`
	StatedValue  *int   `json:"statedValue"`
	WeightClass  string `json:"weightClass"`
}

type AdmittedDriverListRecord struct {
	LicenseNumber string `json:"licenseNumber"`
	FirstName     string `json:"firstName"`
	LastName      string `json:"lastName"`
	LicenseState  string `json:"licenseState"`
	YearsOfCDL    int    `json:"yearsOfCDL"`
	DateOfBirth   string `json:"dateOfBirth"`
	DateOfHire    string `json:"dateOfHire"`
}

type pdfPayloadDetails struct {
	ApplicationID uuid.UUID
	SubmissionID  uuid.UUID
	AgencyID      uuid.UUID
	CompanyName   string
	Error         string
}

// Admitted QUOTE PDFGenDeps Structs //

type AdmittedQuotePDFInput struct {
	USState                      us_states.USState `json:"usState"`
	InsuredCompanyName           string            `json:"insuredCompanyName"`
	AgencyName                   string            `json:"agencyName"`
	ProducerName                 string            `json:"producerName"`
	EffectiveDateOfCoverageStart time.Time         `json:"effectiveDateOfCoverageStart"`
	EffectiveDateOfCoverageEnd   time.Time         `json:"effectiveDateOfCoverageEnd"`
	PackageType                  string            `json:"packageType"`
	CombinedAPDMTCDeductible     bool              `json:"combinedAPDMTCDeductible"`
	InsuranceCarrier             string            `json:"insuranceCarrier"`

	CoverageAL  *AdmittedCoverageAL  `json:"coverageAL"`
	CoverageAPD *AdmittedCoverageAPD `json:"coverageAPD,omitempty"`
	CoverageGL  *AdmittedCoverageGL  `json:"coverageGL,omitempty"`
	CoverageMTC *AdmittedCoverageMTC `json:"coverageMTC,omitempty"`

	PremiumDetails *AdmittedPremiumDetails `json:"premiumDetails,omitempty"`

	CoverageList    *[]AdmittedCoverageListRecord `json:"coverageList,omitempty"`
	ExcludedDrivers *string                       `json:"excludedDrivers,omitempty"`
	FormsDetails    *[]FormDetailRecord           `json:"formsDetails,omitempty"`
}

type AdmittedCoverageAL struct {
	Name                   string `json:"name"`
	Premium                string `json:"premium"`
	PremiumPerHundredMiles string `json:"premiumPerHundredMiles"`
	PremiumPerUnit         string `json:"premiumPerUnit"`
	Deductible             string `json:"deductible"`
	Limit                  string `json:"limit"`
}

type AdmittedCoverageAPD struct {
	Name           string `json:"name"`
	Premium        string `json:"premium"`
	TIVPercentage  string `json:"tivPercentage"`
	Deductible     string `json:"deductible"`
	Limit          string `json:"limit"`
	SecondaryLimit string `json:"secondaryLimit"`
}

type AdmittedCoverageGL struct {
	Name            string `json:"name"`
	Premium         string `json:"premium"`
	Deductible      string `json:"deductible"`
	Limit           string `json:"limit"`
	OccurrenceLimit string `json:"occurrenceLimit"`
	AggregateLimit  string `json:"aggregateLimit"`
}

type AdmittedCoverageMTC struct {
	Name                   string `json:"name"`
	Premium                string `json:"premium"`
	PremiumPerHundredMiles string `json:"premiumPerHundredMiles"`
	PremiumPerUnit         string `json:"premiumPerUnit"`
	Deductible             string `json:"deductible"`
	Limit                  string `json:"limit"`
}

type AdmittedPremiumDetails struct {
	TotalUnits               string  `json:"totalUnits"`
	TIV                      string  `json:"tiv"`
	SubtotalPremium          string  `json:"subtotalPremium"`
	FlatCharges              string  `json:"flatCharges"`
	SafetyDiscountPremium    string  `json:"safetyDiscountPremium"`
	SafetyDiscountPercentage string  `json:"safetyDiscountPercentage"`
	TotalPremium             string  `json:"totalPremium"`
	TotalPowerUnits          string  `json:"totalPowerUnits"`
	PremiumPerUnit           string  `json:"premiumPerUnit"`
	SafetyDiscountRanking    string  `json:"safetyDiscountRanking"`
	TotalSurchargePremium    *string `json:"totalSurchargePremium"`
	TotalSurplusLineTax      *string `json:"totalSurplusLineTax"`
	TotalStampingFee         *string `json:"totalStampingFee"`
	GrandTotalPremium        *string `json:"grandTotalPremium"`
}

type AdmittedCoverageListRecord struct {
	Name                  string                                `json:"name"`
	Limit                 string                                `json:"limit"`
	Deductible            string                                `json:"deductible"`
	IsAncillary           bool                                  `json:"isAncillary"`
	CoverageType          app_enums.Coverage                    `json:"coverageType"`
	SymbolsAndDefinitions *[]AdmittedSymbolsAndDefinitionRecord `json:"symbolsAndDefinitions"`
}

type AdmittedSymbolsAndDefinitionRecord struct {
	Symbol     string `json:"symbol"`
	Definition string `json:"definition"`
}

// Common Quote PDF Structs

type FormDetailRecord struct {
	Code     string `json:"code"`
	Name     string `json:"name"`
	URL      string `json:"url"`
	Coverage string `json:"coverage"`
}
