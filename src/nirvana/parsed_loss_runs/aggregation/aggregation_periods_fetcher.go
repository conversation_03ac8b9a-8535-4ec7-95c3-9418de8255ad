package aggregation

import (
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func FetchAggregationPeriods(coverageSummaries []application.LossRunSummaryPerCoverage) ([]CoverageAggregationPeriods, error) {
	var coveragePeriodsList []CoverageAggregationPeriods
	for _, coverageSummary := range coverageSummaries {
		var periodsForCoverage []pibit.PeriodWithPUCount
		for _, record := range coverageSummary.Summary {
			periodWithPUCount := pibit.PeriodWithPUCount{
				Period: pibit.Period{
					FromDate: record.PolicyPeriodStartDate,
					ToDate:   record.PolicyPeriodEndDate,
				},
				NumberOfPowerUnits: record.NumberOfPowerUnits,
			}
			periodsForCoverage = append(periodsForCoverage, periodWithPUCount)
		}
		coveragePeriod := CoverageAggregationPeriods{
			Coverage: coverageSummary.CoverageType,
			Periods:  periodsForCoverage,
		}
		coveragePeriodsList = append(coveragePeriodsList, coveragePeriod)
	}

	return coveragePeriodsList, nil
}
