package aggregation_test

import (
	"fmt"
	"nirvanatech.com/nirvana/parsed_loss_runs/aggregation"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func strPtr(s string) *string { return &s }
func timePtr(s string) *time.Time {
	t, err := time.Parse("2006-01-02", s)
	if err != nil {
		panic(fmt.Sprintf("invalid date format: %s", s))
	}
	return &t
}

func makeLossLine(policyNo, docID, reportDate, lossID string) aggregation.LossLineWithPolicyAndClaim {
	return aggregation.LossLineWithPolicyAndClaim{
		Policy: pibit.PolicyData{
			PolicyNo:             strPtr(policyNo),
			EffDate:              timePtr("2023-01-01"),
			ExpDate:              timePtr("2023-12-31"),
			Lob:                  strPtr("Auto"),
			Insurer:              strPtr("InsurerX"),
			DocumentId:           strPtr(docID),
			ReportGenerationDate: timePtr(reportDate),
		},
		Loss: pibit.LossData{
			LossSeqID: strPtr(lossID),
		},
	}
}

func TestDeduplicateLossLines_TableDriven(t *testing.T) {
	tests := []struct {
		name           string
		input          []aggregation.LossLineWithPolicyAndClaim
		wantSelected   []string // loss IDs of selected loss lines
		wantDuplicates []string // loss IDs of duplicates
	}{
		{
			name: "Single document, all selected",
			input: []aggregation.LossLineWithPolicyAndClaim{
				makeLossLine("P1", "DOC1", "2023-01-01", "L1"),
				makeLossLine("P1", "DOC1", "2023-01-01", "L2"),
			},
			wantSelected:   []string{"L1", "L2"},
			wantDuplicates: []string{},
		},
		{
			name: "Two documents, newer wins",
			input: []aggregation.LossLineWithPolicyAndClaim{
				makeLossLine("P1", "DOC1", "2023-01-01", "L1"),
				makeLossLine("P1", "DOC2", "2024-01-01", "L2"),
			},
			wantSelected:   []string{"L2"},
			wantDuplicates: []string{"L1"},
		},
		{
			name: "Same report date, more complete wins",
			input: []aggregation.LossLineWithPolicyAndClaim{
				makeLossLine("P1", "DOC1", "2023-01-01", "L1"),
				makeLossLine("P1", "DOC2", "2023-01-01", "L2"),
				makeLossLine("P1", "DOC2", "2023-01-01", "L3"),
			},
			wantSelected:   []string{"L2", "L3"},
			wantDuplicates: []string{"L1"},
		},
		{
			name: "Separate policy identities — dedup separately",
			input: []aggregation.LossLineWithPolicyAndClaim{
				makeLossLine("P1", "DOC1", "2023-01-01", "L1"),
				makeLossLine("P2", "DOC1", "2023-01-01", "L2"),
				makeLossLine("P2", "DOC2", "2024-01-01", "L3"),
			},
			wantSelected:   []string{"L1", "L3"},
			wantDuplicates: []string{"L2"},
		},
		{
			name: "Same doc, different loss lines",
			input: []aggregation.LossLineWithPolicyAndClaim{
				makeLossLine("P1", "DOC1", "2023-01-01", "L1"),
				makeLossLine("P1", "DOC1", "2023-01-01", "L2"),
			},
			wantSelected:   []string{"L1", "L2"},
			wantDuplicates: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := aggregation.DeduplicateLossLines(tt.input)
			assert.NoError(t, err)

			extractLossIDs := func(lines []aggregation.LossLineWithPolicyAndClaim) []string {
				var ids []string
				for _, l := range lines {
					if l.Loss.LossSeqID != nil {
						ids = append(ids, *l.Loss.LossSeqID)
					}
				}
				return ids
			}

			assert.ElementsMatch(t, tt.wantSelected, extractLossIDs(result.SelectedLosses), "Selected loss lines mismatch")
			assert.ElementsMatch(t, tt.wantDuplicates, extractLossIDs(result.Duplicates), "Duplicate loss lines mismatch")
		})
	}
}
