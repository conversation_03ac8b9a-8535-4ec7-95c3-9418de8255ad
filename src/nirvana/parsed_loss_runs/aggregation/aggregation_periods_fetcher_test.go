package aggregation

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
)

func TestFetchAggregationPeriods(t *testing.T) {
	fromDate := time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)
	toDate := time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)

	tests := []struct {
		name                  string
		input                 []application.LossRunSummaryPerCoverage
		expectedNumGroups     int
		expectedPeriodsPerCov map[app_enums.Coverage]int
	}{
		{
			name: "Single coverage with 2 periods",
			input: []application.LossRunSummaryPerCoverage{
				{
					CoverageType: app_enums.CoverageAutoLiability,
					Summary: []application.LossRunSummaryRecord{
						{
							PolicyPeriodStartDate: fromDate,
							PolicyPeriodEndDate:   toDate,
							NumberOfPowerUnits:    5,
						},
						{
							PolicyPeriodStartDate: fromDate.AddDate(1, 0, 0),
							PolicyPeriodEndDate:   toDate.AddDate(1, 0, 0),
							NumberOfPowerUnits:    6,
						},
					},
				},
			},
			expectedNumGroups: 1,
			expectedPeriodsPerCov: map[app_enums.Coverage]int{
				app_enums.CoverageAutoLiability: 2,
			},
		},
		{
			name: "Multiple coverages with 1 period each",
			input: []application.LossRunSummaryPerCoverage{
				{
					CoverageType: app_enums.CoverageAutoLiability,
					Summary: []application.LossRunSummaryRecord{
						{
							PolicyPeriodStartDate: fromDate,
							PolicyPeriodEndDate:   toDate,
							NumberOfPowerUnits:    3,
						},
					},
				},
				{
					CoverageType: app_enums.CoverageAutoPhysicalDamage,
					Summary: []application.LossRunSummaryRecord{
						{
							PolicyPeriodStartDate: fromDate,
							PolicyPeriodEndDate:   toDate,
							NumberOfPowerUnits:    4,
						},
					},
				},
			},
			expectedNumGroups: 2,
			expectedPeriodsPerCov: map[app_enums.Coverage]int{
				app_enums.CoverageAutoLiability:      1,
				app_enums.CoverageAutoPhysicalDamage: 1,
			},
		},
		{
			name:                  "No coverages",
			input:                 []application.LossRunSummaryPerCoverage{},
			expectedNumGroups:     0,
			expectedPeriodsPerCov: map[app_enums.Coverage]int{},
		},
		{
			name: "Multiple coverages with multiple periods",
			input: []application.LossRunSummaryPerCoverage{
				{
					CoverageType: app_enums.CoverageAutoLiability,
					Summary: []application.LossRunSummaryRecord{
						{
							PolicyPeriodStartDate: fromDate,
							PolicyPeriodEndDate:   toDate,
							NumberOfPowerUnits:    2,
						},
						{
							PolicyPeriodStartDate: fromDate.AddDate(1, 0, 0),
							PolicyPeriodEndDate:   toDate.AddDate(1, 0, 0),
							NumberOfPowerUnits:    3,
						},
					},
				},
				{
					CoverageType: app_enums.CoverageAutoPhysicalDamage,
					Summary: []application.LossRunSummaryRecord{
						{
							PolicyPeriodStartDate: fromDate,
							PolicyPeriodEndDate:   toDate,
							NumberOfPowerUnits:    4,
						},
						{
							PolicyPeriodStartDate: fromDate.AddDate(1, 0, 0),
							PolicyPeriodEndDate:   toDate.AddDate(1, 0, 0),
							NumberOfPowerUnits:    5,
						},
						{
							PolicyPeriodStartDate: fromDate.AddDate(2, 0, 0),
							PolicyPeriodEndDate:   toDate.AddDate(2, 0, 0),
							NumberOfPowerUnits:    6,
						},
					},
				},
			},
			expectedNumGroups: 2,
			expectedPeriodsPerCov: map[app_enums.Coverage]int{
				app_enums.CoverageAutoLiability:      2,
				app_enums.CoverageAutoPhysicalDamage: 3,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := FetchAggregationPeriods(tt.input)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedNumGroups, len(result), "unexpected number of groups")

			for _, group := range result {
				expectedPeriods := tt.expectedPeriodsPerCov[group.Coverage]
				assert.Equal(t, expectedPeriods, len(group.Periods), "unexpected number of periods for coverage %v", group.Coverage)

				// Find the input record corresponding to this group
				var matchingSummary []application.LossRunSummaryRecord
				for _, in := range tt.input {
					if in.CoverageType == group.Coverage {
						matchingSummary = in.Summary
						break
					}
				}

				for i, p := range group.Periods {
					expectedInput := matchingSummary[i]
					assert.Equal(t, expectedInput.PolicyPeriodStartDate, p.Period.FromDate)
					assert.Equal(t, expectedInput.PolicyPeriodEndDate, p.Period.ToDate)
					assert.Equal(t, expectedInput.NumberOfPowerUnits, p.NumberOfPowerUnits)
				}
			}
		})
	}
}
