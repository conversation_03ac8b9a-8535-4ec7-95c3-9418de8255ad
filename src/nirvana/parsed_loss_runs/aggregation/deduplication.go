package aggregation

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

// The deduplication logic in this file is designed to resolve conflicts when multiple
// versions of loss data for the same policy exist across different source documents.
// For instance, a carrier might send a loss run report in January and an updated
// version in February. This logic ensures that only the most reliable version is used
// for aggregation.
//
// The flow is as follows:
// 1. Group all incoming loss lines by a normalized policy identity key. This key is
//    a composite of the policy number, effective/expiration dates, LOB, and insurer.
//
// 2. For each group of identical policies:
//    a. If all loss lines originate from the same source document, they are all kept.
//       No deduplication is needed within a single document's data.
//    B. If the loss lines come from multiple documents, a "best" version is selected.
//
// 3. The "best" document is chosen based on two criteria, in order of priority:
//    i.  The document with the most recent 'ReportGenerationDate'.
//    Ii. If dates are equal or absent, the document containing the highest number
//        of loss lines (i.e., the most complete report) is preferred.
//
// 4. All loss lines from the selected "best" document are marked as 'SelectedLosses'.
//    Loss lines for the same policy from all other documents are marked as 'Duplicates'.

// DeduplicationResult separates deduplicated loss lines into ones to keep and ones to discard.
type DeduplicationResult struct {
	SelectedLosses []LossLineWithPolicyAndClaim
	Duplicates     []LossLineWithPolicyAndClaim
}

type policyIdentityKey string

// DeduplicateLossLines removes duplicate versions of the same policy's loss lines.
func DeduplicateLossLines(lossLines []LossLineWithPolicyAndClaim) (*DeduplicationResult, error) {
	groupedByPolicy := groupByPolicyIdentity(lossLines)

	var result DeduplicationResult

	for _, group := range groupedByPolicy {
		if len(group) == 1 || allFromSameDocument(group) {
			result.SelectedLosses = append(result.SelectedLosses, group...)
			continue
		}

		bestVersion, duplicates, err := selectBestVersionAmongDocuments(group)
		if err != nil {
			return nil, errors.Wrapf(err, "could not select best version among documents")
		}
		result.SelectedLosses = append(result.SelectedLosses, bestVersion...)
		result.Duplicates = append(result.Duplicates, duplicates...)
	}

	return &result, nil
}

// allFromSameDocument checks if all records in the group came from the same document.
func allFromSameDocument(records []LossLineWithPolicyAndClaim) bool {
	firstDocID := records[0].Policy.DocumentId
	for _, record := range records[1:] {
		if record.Policy.DocumentId == nil || firstDocID == nil || *record.Policy.DocumentId != *firstDocID {
			return false
		}
	}
	return true
}

// groupByPolicyIdentity groups all loss lines by their cleaned policy identity.
func groupByPolicyIdentity(records []LossLineWithPolicyAndClaim) map[policyIdentityKey][]LossLineWithPolicyAndClaim {
	grouped := make(map[policyIdentityKey][]LossLineWithPolicyAndClaim)
	for _, record := range records {
		key := policyIdentityKey(generatePolicyIdentityKey(record.Policy))
		grouped[key] = append(grouped[key], record)
	}
	return grouped
}

func generatePolicyIdentityKey(p pibit.PolicyData) string {
	normalize := func(s *string) string {
		if s == nil {
			return ""
		}
		value := strings.ToLower(strings.TrimSpace(*s))
		value = strings.ReplaceAll(value, " ", "")
		value = strings.ReplaceAll(value, "-", "")
		return value
	}

	formatDate := func(t *time.Time) string {
		if t == nil {
			return ""
		}
		return t.Format("2006-01-02")
	}

	return fmt.Sprintf("%s|%s|%s|%s|%s",
		normalize(p.PolicyNo),
		formatDate(p.EffDate),
		formatDate(p.ExpDate),
		normalize(p.Lob),
		normalize(p.Insurer),
	)
}

// selectBestVersionAmongDocuments chooses the most complete and recent set of loss lines among multiple documents.
func selectBestVersionAmongDocuments(records []LossLineWithPolicyAndClaim) ([]LossLineWithPolicyAndClaim, []LossLineWithPolicyAndClaim, error) {
	byDocumentID, err := groupByDocumentID(records)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "could not group loss lines by document id")
	}
	bestDocumentRecords := pickMostCompleteAndRecentDocument(byDocumentID)
	bestDocID := *bestDocumentRecords[0].Policy.DocumentId

	var selected, duplicates []LossLineWithPolicyAndClaim
	for _, record := range records {
		if record.Policy.DocumentId != nil && *record.Policy.DocumentId == bestDocID {
			selected = append(selected, record)
		} else {
			duplicates = append(duplicates, record)
		}
	}

	return selected, duplicates, nil
}

// groupByDocumentID groups loss lines by their originating document.
func groupByDocumentID(records []LossLineWithPolicyAndClaim) (map[string][]LossLineWithPolicyAndClaim, error) {
	grouped := make(map[string][]LossLineWithPolicyAndClaim)
	for _, record := range records {
		if record.Policy.DocumentId == nil {
			return nil, errors.New("nil documentId for record")
		}
		docID := *record.Policy.DocumentId
		grouped[docID] = append(grouped[docID], record)
	}
	return grouped, nil
}

// pickMostCompleteAndRecentDocument selects the best version of the policy's loss lines.
func pickMostCompleteAndRecentDocument(groupedByDocID map[string][]LossLineWithPolicyAndClaim) []LossLineWithPolicyAndClaim {
	type documentCandidate struct {
		docID   string
		records []LossLineWithPolicyAndClaim
	}

	var candidates []documentCandidate
	for docID, records := range groupedByDocID {
		candidates = append(candidates, documentCandidate{docID: docID, records: records})
	}

	if candidates == nil {
		return nil
	}

	sort.SliceStable(candidates, func(i, j int) bool {
		dateI := candidates[i].records[0].Policy.ReportGenerationDate
		dateJ := candidates[j].records[0].Policy.ReportGenerationDate
		if dateI != nil && dateJ != nil && !dateI.Equal(*dateJ) {
			return dateI.After(*dateJ) // prefer newer
		}
		return len(candidates[i].records) > len(candidates[j].records) // prefer more complete
	})

	return candidates[0].records
}
