package aggregation

import (
	"context"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func GetAllDocuments(ctx context.Context, applicationId string, appWrapper application.DataWrapper, parsedLossRunWrapper pibit.DataWrapper) ([]pibit.Document, error) {
	submissions, err := appWrapper.GetSubmissionsByAppId(ctx, applicationId)
	if err != nil {
		return nil, errors.Wrapf(err, "error finding submissions for appId %s", applicationId)
	}
	submissionIds := slice_utils.Map(submissions, func(sub application.SubmissionObject) string {
		return sub.ID
	})

	documents, err := parsedLossRunWrapper.GetDocumentsBySubmissionIds(ctx, submissionIds)
	if err != nil {
		return nil, errors.Wrapf(err, "error finding documents for submissionIds %v", submissionIds)
	}
	return documents, nil
}

func GetParsedLossRunData(ctx context.Context, documents []pibit.Document, parsedLossRunWrapper pibit.DataWrapper) (*pibit.MappedDBObjects, error) {
	// filter only received documents
	receivedDocuments := slice_utils.Filter(documents, func(document pibit.Document) bool {
		return document.Status == pibit.DocumentStatusReceived
	})

	var policies []pibit.PolicyData
	var claims []pibit.ClaimData
	var losses []pibit.LossData
	// fetch data per document and append
	for _, document := range receivedDocuments {
		docPolicies, err := getParsedPolicies(ctx, document.ID, parsedLossRunWrapper)
		if err != nil {
			return nil, err
		}
		docClaims, err := getParsedClaims(ctx, document.ID, parsedLossRunWrapper)
		if err != nil {
			return nil, err
		}
		docLosses, err := getParsedLosses(ctx, document.ID, parsedLossRunWrapper)
		if err != nil {
			return nil, err
		}

		policies = append(policies, docPolicies...)
		claims = append(claims, docClaims...)
		losses = append(losses, docLosses...)
	}

	return &pibit.MappedDBObjects{
		Policies: policies,
		Claims:   claims,
		Losses:   losses,
	}, nil
}

func getParsedPolicies(ctx context.Context, documentId string, parsedLossRunWrapper pibit.DataWrapper) ([]pibit.PolicyData, error) {
	policies, err := parsedLossRunWrapper.GetAllParsedLossRunPolicyByDocumentId(ctx, documentId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch parsed policies for docId %s", documentId)
	}
	return policies, nil
}

func getParsedClaims(ctx context.Context, documentId string, parsedLossRunWrapper pibit.DataWrapper) ([]pibit.ClaimData, error) {
	claims, err := parsedLossRunWrapper.GetAllParsedLossRunClaimByDocumentId(ctx, documentId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch parsed claims for docId %s", documentId)
	}
	return claims, nil
}

func getParsedLosses(ctx context.Context, documentId string, parsedLossRunWrapper pibit.DataWrapper) ([]pibit.LossData, error) {
	losses, err := parsedLossRunWrapper.GetAllParsedLossRunLossByDocumentId(ctx, documentId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch parsed losses for docId %s", documentId)
	}
	return losses, nil
}
