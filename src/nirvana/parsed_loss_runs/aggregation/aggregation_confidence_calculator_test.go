package aggregation

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func TestComputeAggregationConfidence(t *testing.T) {
	tests := []struct {
		name                         string
		processedLosses              []pibit.ProcessedLoss
		periodsWithGaps              []CoveragePeriodsWithGaps
		expectedLevel                pibit.ConfidenceLevel
		expectedLowConfidenceReasons []pibit.LowConfidenceAggregationReason
		expectErr                    bool
	}{
		{
			name: "All high confidence, no gaps",
			processedLosses: []pibit.ProcessedLoss{
				{
					ID:             "1",
					Unmapped:       boolPtr(false),
					ConfidenceInfo: pibit.LossConfidenceInfo{Level: pibit.ConfidenceLevelHigh},
				},
			},
			periodsWithGaps:              nil,
			expectedLevel:                pibit.ConfidenceLevelHigh,
			expectedLowConfidenceReasons: nil,
			expectErr:                    false,
		},
		{
			name: "Unmapped with missing coverage",
			processedLosses: []pibit.ProcessedLoss{
				{
					ID:              "2",
					Unmapped:        boolPtr(true),
					UnmappedReasons: []pibit.UnmappedLossReason{pibit.UnmappedLossReasonMissingCoverage},
					ConfidenceInfo:  pibit.LossConfidenceInfo{Level: pibit.ConfidenceLevelHigh},
				},
			},
			periodsWithGaps: nil,
			expectedLevel:   pibit.ConfidenceLevelLow,
			expectedLowConfidenceReasons: []pibit.LowConfidenceAggregationReason{
				pibit.LowConfidenceAggregationReasonUnaggregatedNonDuplicateLosses,
			},
			expectErr: false,
		},
		{
			name: "Loss with low confidence level",
			processedLosses: []pibit.ProcessedLoss{
				{
					ID:             "3",
					Unmapped:       boolPtr(false),
					ConfidenceInfo: pibit.LossConfidenceInfo{Level: pibit.ConfidenceLevelLow},
				},
			},
			periodsWithGaps: nil,
			expectedLevel:   pibit.ConfidenceLevelLow,
			expectedLowConfidenceReasons: []pibit.LowConfidenceAggregationReason{
				pibit.LowConfidenceAggregationReasonLowConfidenceLosses,
			},
			expectErr: false,
		},
		{
			name: "Missing periods in coverage",
			processedLosses: []pibit.ProcessedLoss{
				{
					ID:             "4",
					Unmapped:       boolPtr(false),
					ConfidenceInfo: pibit.LossConfidenceInfo{Level: pibit.ConfidenceLevelHigh},
				},
			},
			periodsWithGaps: []CoveragePeriodsWithGaps{
				{
					Periods: []PeriodsWithGaps{
						{
							MissingSubPeriods: []pibit.MissingSubPeriod{
								{LargerThanThreshold: true},
							},
						},
					},
				},
			},
			expectedLevel: pibit.ConfidenceLevelLow,
			expectedLowConfidenceReasons: []pibit.LowConfidenceAggregationReason{
				pibit.LowConfidenceAggregationReasonMissingPolicyPeriods,
			},
			expectErr: false,
		},
		{
			name: "Nil unmapped field triggers error",
			processedLosses: []pibit.ProcessedLoss{
				{
					ID:             "5",
					Unmapped:       nil,
					ConfidenceInfo: pibit.LossConfidenceInfo{Level: pibit.ConfidenceLevelHigh},
				},
			},
			periodsWithGaps: nil,
			expectErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			confidenceInfo, err := ComputeAggregationConfidence(tt.processedLosses, tt.periodsWithGaps)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, confidenceInfo)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.expectedLevel, confidenceInfo.Level)
			assert.ElementsMatch(t, tt.expectedLowConfidenceReasons, confidenceInfo.LowConfidenceAggregationReason)
		})
	}
}
