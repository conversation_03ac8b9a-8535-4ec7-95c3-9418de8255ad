package aggregation

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func TestGenerateAggregationSummary(t *testing.T) {
	ctx := context.Background()
	coverage := app_enums.CoverageGeneralLiability
	start := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	end := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)

	// Base loss objects for reuse in tests.
	loss1 := pibit.ProcessedLoss{
		ID:                 "loss1",
		ClaimID:            stringPtr("claim-abc"),
		Coverage:           &coverage,
		PeriodStartDate:    &start,
		PeriodEndDate:      &end,
		LossPaid:           float64Ptr(1000),
		LossReserved:       float64Ptr(500),
		AlaePaid:           float64Ptr(100),
		AlaeExpenseReserve: float64Ptr(50),
		SubrogationAmount:  float64Ptr(200),
		SalvageAmount:      float64Ptr(100),
		OtherRecovery:      float64Ptr(50),
		Unmapped:           boolPtr(false),
	}
	loss2 := pibit.ProcessedLoss{
		ID:              "loss2",
		ClaimID:         stringPtr("claim-xyz"),
		Coverage:        &coverage,
		PeriodStartDate: &start,
		PeriodEndDate:   &end,
		LossPaid:        float64Ptr(300),
		Unmapped:        boolPtr(false),
	}

	tests := []struct {
		name                   string
		processedLosses        []pibit.ProcessedLoss
		coveragePeriods        []CoveragePeriodsWithGaps
		expectedSummaryPeriods int
		expectedClaimCount     int
		expectedGrossLoss      float64
		expectErr              bool
	}{
		{
			name:            "Basic aggregation with one mapped loss",
			processedLosses: []pibit.ProcessedLoss{loss1},
			coveragePeriods: []CoveragePeriodsWithGaps{
				{
					Coverage: coverage,
					Periods: []PeriodsWithGaps{
						{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}},
					},
				},
			},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     1,
			expectedGrossLoss:      1000, // (1000 + 500) - (100 + 50) - (200 + 100 + 50) = 1000
			expectErr:              false,
		},
		{
			name:            "Multiple losses with same claim ID count as one claim",
			processedLosses: []pibit.ProcessedLoss{loss1, {ID: "loss1-part2", ClaimID: stringPtr("claim-abc"), Coverage: &coverage, PeriodStartDate: &start, PeriodEndDate: &end, LossPaid: float64Ptr(200), Unmapped: boolPtr(false)}},
			coveragePeriods: []CoveragePeriodsWithGaps{
				{
					Coverage: coverage,
					Periods: []PeriodsWithGaps{
						{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}},
					},
				},
			},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     1,
			expectedGrossLoss:      1200, // 1000 + 200
			expectErr:              false,
		},
		{
			name:            "Multiple distinct claims are counted correctly",
			processedLosses: []pibit.ProcessedLoss{loss1, loss2},
			coveragePeriods: []CoveragePeriodsWithGaps{
				{
					Coverage: coverage,
					Periods: []PeriodsWithGaps{
						{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}},
					},
				},
			},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     2,
			expectedGrossLoss:      1300, // 1000 + 300
			expectErr:              false,
		},
		{
			name:            "Loss with nil claim ID is not counted but financially included",
			processedLosses: []pibit.ProcessedLoss{loss1, {ID: "loss-nil-claimid", ClaimID: nil, Coverage: &coverage, PeriodStartDate: &start, PeriodEndDate: &end, LossPaid: float64Ptr(50), Unmapped: boolPtr(false)}},
			coveragePeriods: []CoveragePeriodsWithGaps{
				{
					Coverage: coverage,
					Periods: []PeriodsWithGaps{
						{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}},
					},
				},
			},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     1,
			expectedGrossLoss:      1050, // 1000 + 50
			expectErr:              false,
		},
		{
			name: "Gross loss is correctly rounded to two decimal places",
			processedLosses: []pibit.ProcessedLoss{
				{
					ID:              "loss-rounding",
					ClaimID:         stringPtr("claim-rounding"),
					Coverage:        &coverage,
					PeriodStartDate: &start,
					PeriodEndDate:   &end,
					Unmapped:        boolPtr(false),
					LossPaid:        float64Ptr(123.456), // has 3 decimal places
					LossReserved:    float64Ptr(10.111),
					AlaePaid:        float64Ptr(5.555),
				},
			},
			coveragePeriods: []CoveragePeriodsWithGaps{
				{
					Coverage: coverage,
					Periods: []PeriodsWithGaps{
						{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}},
					},
				},
			},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     1,
			// (123.456 + 10.111) - 5.555 = 133.567 - 5.555 = 128.012 -> rounded to 128.01
			expectedGrossLoss: 128.01,
			expectErr:         false,
		},
		{
			name:            "No processed losses returns empty summary",
			processedLosses: []pibit.ProcessedLoss{},
			coveragePeriods: []CoveragePeriodsWithGaps{
				{
					Coverage: coverage,
					Periods: []PeriodsWithGaps{
						{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}},
					},
				},
			},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     0,
			expectedGrossLoss:      0,
			expectErr:              false,
		},
		{
			name:            "Unmapped loss is skipped",
			processedLosses: []pibit.ProcessedLoss{{ID: "loss-unmapped", ClaimID: stringPtr("claim-unmapped"), Coverage: &coverage, PeriodStartDate: &start, PeriodEndDate: &end, Unmapped: boolPtr(true)}},
			coveragePeriods: []CoveragePeriodsWithGaps{
				{
					Coverage: coverage,
					Periods: []PeriodsWithGaps{
						{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}},
					},
				},
			},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     0,
			expectedGrossLoss:      0,
			expectErr:              false,
		},
		{
			name:            "Missing coverage in loss triggers error",
			processedLosses: []pibit.ProcessedLoss{{ID: "loss-err-1", ClaimID: stringPtr("claim-err-1"), Unmapped: boolPtr(false), PeriodStartDate: &start, PeriodEndDate: &end}},
			coveragePeriods: []CoveragePeriodsWithGaps{
				{
					Coverage: coverage,
					Periods: []PeriodsWithGaps{
						{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}},
					},
				},
			},
			expectErr: true,
		},
		{
			name:            "Nil Unmapped in loss triggers error",
			processedLosses: []pibit.ProcessedLoss{{ID: "loss-err-2", ClaimID: stringPtr("claim-err-2"), Coverage: &coverage, PeriodStartDate: &start, PeriodEndDate: &end, Unmapped: nil}},
			coveragePeriods: []CoveragePeriodsWithGaps{
				{
					Coverage: coverage,
					Periods: []PeriodsWithGaps{
						{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}},
					},
				},
			},
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			summary, err := GenerateAggregationSummary(ctx, tt.processedLosses, tt.coveragePeriods)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, summary)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, summary)
				assert.Equal(t, 1, len(summary.PeriodSummary))
				assert.Equal(t, tt.expectedSummaryPeriods, len(summary.PeriodSummary[0].PeriodSummary))

				// Check a non-empty summary period
				if len(summary.PeriodSummary[0].PeriodSummary) > 0 {
					period := summary.PeriodSummary[0].PeriodSummary[0]
					assert.Equal(t, tt.expectedClaimCount, period.ClaimCount)
					assert.Equal(t, tt.expectedGrossLoss, period.GrossLoss)
				}
			}
		})
	}
}

// Helper functions to create pointers
func boolPtr(b bool) *bool          { return &b }
func float64Ptr(f float64) *float64 { return &f }
func stringPtr(s string) *string    { return &s }
