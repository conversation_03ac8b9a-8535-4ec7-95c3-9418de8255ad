package aggregation

import (
	"fmt"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

// GetLossLinesWithPolicyAndClaimData joins loss data with corresponding policy and claim records.
func GetLossLinesWithPolicyAndClaimData(data pibit.MappedDBObjects) ([]LossLineWithPolicyAndClaim, error) {
	policiesByKey, err := buildPolicyMap(data.Policies)
	if err != nil {
		return nil, err
	}

	claimsByKey, err := buildClaimMap(data.Claims)
	if err != nil {
		return nil, err
	}

	var lossLines []LossLineWithPolicyAndClaim
	for _, loss := range data.Losses {
		line, err := enrichLossWithPolicyAndClaim(loss, policiesByKey, claimsByKey)
		if err != nil {
			return nil, err
		}
		lossLines = append(lossLines, line)
	}

	return lossLines, nil
}

func buildPolicyMap(policies []pibit.PolicyData) (map[string]pibit.PolicyData, error) {
	result := make(map[string]pibit.PolicyData, len(policies))
	for _, p := range policies {
		if p.DocumentId == nil || p.PolicySn == nil {
			return nil, errors.New("policy missing DocumentId or PolicySn")
		}
		key := fmt.Sprintf("%s-%d", *p.DocumentId, *p.PolicySn)
		result[key] = p
	}
	return result, nil
}

func buildClaimMap(claims []pibit.ClaimData) (map[string]pibit.ClaimData, error) {
	result := make(map[string]pibit.ClaimData, len(claims))
	for _, c := range claims {
		if c.DocumentId == nil || c.PolicySn == nil || c.ClaimSn == nil {
			return nil, errors.New("claim missing DocumentId, PolicySn, or ClaimSn")
		}
		key := fmt.Sprintf("%s-%d-%d", *c.DocumentId, *c.PolicySn, *c.ClaimSn)
		result[key] = c
	}
	return result, nil
}

func enrichLossWithPolicyAndClaim(
	loss pibit.LossData,
	policies map[string]pibit.PolicyData,
	claims map[string]pibit.ClaimData,
) (LossLineWithPolicyAndClaim, error) {
	if loss.DocumentId == nil || loss.PolicySn == nil || loss.ClaimSn == nil {
		return LossLineWithPolicyAndClaim{}, errors.New("loss missing DocumentId, PolicySn, or ClaimSn")
	}

	policyKey := fmt.Sprintf("%s-%d", *loss.DocumentId, *loss.PolicySn)
	claimKey := fmt.Sprintf("%s-%d-%d", *loss.DocumentId, *loss.PolicySn, *loss.ClaimSn)

	policy, ok := policies[policyKey]
	if !ok {
		return LossLineWithPolicyAndClaim{}, fmt.Errorf("policy not found for key %s", policyKey)
	}

	claim, ok := claims[claimKey]
	if !ok {
		return LossLineWithPolicyAndClaim{}, fmt.Errorf("claim not found for key %s", claimKey)
	}

	return LossLineWithPolicyAndClaim{
		Loss:   loss,
		Policy: policy,
		Claim:  claim,
	}, nil
}
