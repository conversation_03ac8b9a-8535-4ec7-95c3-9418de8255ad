package aggregation

import (
	"testing"

	"github.com/stretchr/testify/require"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func strPtr(s string) *string { return &s }
func int32Ptr(i int32) *int32 { return &i }

func TestGetLossLinesWithPolicyAndClaimData(t *testing.T) {
	tests := []struct {
		name        string
		input       pibit.MappedDBObjects
		expectErr   bool
		expectedLen int
	}{
		{
			name: "happy path with 1 loss matched",
			input: pibit.MappedDBObjects{
				Policies: []pibit.PolicyData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101)},
				},
				Claims: []pibit.ClaimData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101), ClaimSn: int32Ptr(201)},
				},
				Losses: []pibit.LossData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101), ClaimSn: int32Ptr(201)},
				},
			},
			expectErr:   false,
			expectedLen: 1,
		},
		{
			name: "missing policy key",
			input: pibit.MappedDBObjects{
				Policies: []pibit.PolicyData{},
				Claims: []pibit.ClaimData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101), ClaimSn: int32Ptr(201)},
				},
				Losses: []pibit.LossData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101), ClaimSn: int32Ptr(201)},
				},
			},
			expectErr: true,
		},
		{
			name: "missing claim key",
			input: pibit.MappedDBObjects{
				Policies: []pibit.PolicyData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101)},
				},
				Claims: []pibit.ClaimData{},
				Losses: []pibit.LossData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101), ClaimSn: int32Ptr(201)},
				},
			},
			expectErr: true,
		},
		{
			name: "loss missing DocumentId",
			input: pibit.MappedDBObjects{
				Policies: []pibit.PolicyData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101)},
				},
				Claims: []pibit.ClaimData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101), ClaimSn: int32Ptr(201)},
				},
				Losses: []pibit.LossData{
					{PolicySn: int32Ptr(101), ClaimSn: int32Ptr(201)},
				},
			},
			expectErr: true,
		},
		{
			name: "policy missing DocumentId",
			input: pibit.MappedDBObjects{
				Policies: []pibit.PolicyData{
					{PolicySn: int32Ptr(101)},
				},
				Claims: []pibit.ClaimData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101), ClaimSn: int32Ptr(201)},
				},
				Losses: []pibit.LossData{},
			},
			expectErr: true,
		},
		{
			name: "claim missing ClaimSn",
			input: pibit.MappedDBObjects{
				Policies: []pibit.PolicyData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101)},
				},
				Claims: []pibit.ClaimData{
					{DocumentId: strPtr("doc1"), PolicySn: int32Ptr(101)},
				},
				Losses: []pibit.LossData{},
			},
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetLossLinesWithPolicyAndClaimData(tt.input)
			if tt.expectErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Len(t, result, tt.expectedLen)
			}
		})
	}
}
