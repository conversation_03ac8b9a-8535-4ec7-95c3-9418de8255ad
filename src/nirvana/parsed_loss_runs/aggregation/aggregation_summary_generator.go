package aggregation

import (
	"context"
	"math"
	"time"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

type CoveragePeriodKey struct {
	Coverage        app_enums.Coverage
	PeriodStartDate time.Time
	PeriodEndDate   time.Time
}

func GenerateAggregationSummary(
	ctx context.Context,
	processedLosses []pibit.ProcessedLoss,
	coveragePeriodsWithGaps []CoveragePeriodsWithGaps,
) (*pibit.AggregationSummary, error) {
	// Group losses by coverage and (period start, end)
	lossesByCoveragePeriod, err := groupLossesByCoveragePeriod(ctx, processedLosses)
	if err != nil {
		return nil, errors.Wrapf(err, "could not group losses by coverage and period")
	}

	var coverageSummaries []pibit.AggregationCoveragePeriodSummary
	for _, coverageWithGaps := range coveragePeriodsWithGaps {
		coverage := coverageWithGaps.Coverage
		var periodSummaries []pibit.PeriodSummary

		for _, periodGapInfo := range coverageWithGaps.Periods {
			start := periodGapInfo.AggregationPeriod.FromDate
			end := periodGapInfo.AggregationPeriod.ToDate
			key := CoveragePeriodKey{
				Coverage:        coverage,
				PeriodStartDate: start,
				PeriodEndDate:   end,
			}
			losses := lossesByCoveragePeriod[key]

			periodSummaries = append(periodSummaries, pibit.PeriodSummary{
				PeriodStartDate:   start,
				PeriodEndDate:     end,
				MissingSubPeriods: periodGapInfo.MissingSubPeriods,
				ClaimCount:        countUniqueClaims(losses),
				GrossLoss:         calculateGrossLoss(losses),
				NumberOfPUs:       periodGapInfo.NumberOfPowerUnits,
			})
		}

		coverageSummaries = append(coverageSummaries, pibit.AggregationCoveragePeriodSummary{
			Coverage:      coverage,
			PeriodSummary: periodSummaries,
		})
	}

	return &pibit.AggregationSummary{PeriodSummary: coverageSummaries}, nil
}

// countUniqueClaims counts unique claims from a slice of loss entries.
func countUniqueClaims(losses []pibit.ProcessedLoss) int {
	// Use a map to store unique claim IDs encountered.
	uniqueClaimIDs := make(map[string]struct{})
	for _, l := range losses {
		// Safely handle nil pointer for ClaimID, as it is a rare edge case.
		if l.ClaimID != nil {
			uniqueClaimIDs[*l.ClaimID] = struct{}{}
		}
	}
	return len(uniqueClaimIDs)
}

func groupLossesByCoveragePeriod(ctx context.Context, losses []pibit.ProcessedLoss) (map[CoveragePeriodKey][]pibit.ProcessedLoss, error) {
	grouped := make(map[CoveragePeriodKey][]pibit.ProcessedLoss)
	log.Info(ctx, "Grouping losses by coverage and period", log.Any("losses", losses))
	for _, loss := range losses {
		if loss.Unmapped == nil {
			return nil, errors.Newf("loss id %s has unmapped = nil", loss.ID)
		}
		if *loss.Unmapped {
			log.Info(ctx, "Skipping unmapped loss for coverage period grouping", log.String("loss", loss.ID))
			continue // skip unmapped losses
		}
		if loss.Coverage == nil || loss.PeriodStartDate == nil || loss.PeriodEndDate == nil {
			log.Error(ctx, "Triggered sanity check. Loss without aggregation period, coverage is mapped", log.String("loss", loss.ID))
			return nil, errors.Newf("loss id %s without aggregation period, coverage is mapped", loss.ID)
		}
		key := CoveragePeriodKey{
			Coverage:        *loss.Coverage,
			PeriodStartDate: *loss.PeriodStartDate,
			PeriodEndDate:   *loss.PeriodEndDate,
		}
		grouped[key] = append(grouped[key], loss)
	}
	return grouped, nil
}

func calculateGrossLoss(losses []pibit.ProcessedLoss) float64 {
	var total float64
	for _, l := range losses {
		lossPaid := pointer_utils.Float64ValOr(l.LossPaid, 0)
		lossReserved := pointer_utils.Float64ValOr(l.LossReserved, 0)
		alaePaid := pointer_utils.Float64ValOr(l.AlaePaid, 0)
		alaeReserve := pointer_utils.Float64ValOr(l.AlaeExpenseReserve, 0)
		subrogation := pointer_utils.Float64ValOr(l.SubrogationAmount, 0)
		salvage := pointer_utils.Float64ValOr(l.SalvageAmount, 0)
		otherRecovery := pointer_utils.Float64ValOr(l.OtherRecovery, 0)

		total += (lossPaid + lossReserved) - (alaePaid + alaeReserve) - (subrogation + salvage + otherRecovery)
	}
	// Round the total to two decimal places
	return math.Round(total*100) / 100
}
