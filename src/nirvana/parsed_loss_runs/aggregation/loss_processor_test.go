package aggregation

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func TestBuildProcessedLosses(t *testing.T) {
	coverage := app_enums.CoverageAutoLiability
	periodStart := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
	periodEnd := time.Date(2023, 12, 31, 0, 0, 0, 0, time.UTC)

	testUUID := uuid.New()
	validDateOfLoss := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	tests := []struct {
		name     string
		lines    []LossLineWithPolicyAndClaim
		periods  []CoverageAggregationPeriods
		expected func(t *testing.T, results []pibit.ProcessedLoss)
	}{
		{
			name: "happy path",
			lines: []LossLineWithPolicyAndClaim{
				{
					Policy: pibit.PolicyData{
						PolicyNo: pointerStr("P-001"),
					},
					Claim: pibit.ClaimData{
						ClaimID:    pointerStr("C-001"),
						DateOfLoss: &validDateOfLoss,
					},
					Loss: pibit.LossData{
						CoverageInferred: pointerCoverage(pibit.CoverageInferredAutoLiability),
						ClaimLossType:    pointerStr("collision"),
					},
				},
			},
			periods: []CoverageAggregationPeriods{
				{
					Coverage: coverage,
					Periods: []pibit.PeriodWithPUCount{
						{
							Period: pibit.Period{
								FromDate: periodStart,
								ToDate:   periodEnd,
							},
							NumberOfPowerUnits: 0,
						},
					},
				},
			},
			expected: func(t *testing.T, results []pibit.ProcessedLoss) {
				assert.Len(t, results, 1)
				pl := results[0]
				assert.Equal(t, "P-001", *pl.PolicyNo)
				assert.Equal(t, "C-001", *pl.ClaimID)
				assert.Equal(t, coverage, *pl.Coverage)
				assert.Equal(t, pibit.ConfidenceLevelHigh, pl.ConfidenceInfo.Level)
				assert.False(t, *pl.Unmapped)
				assert.Equal(t, periodStart, *pl.PeriodStartDate)
				assert.Equal(t, periodEnd, *pl.PeriodEndDate)
			},
		},
		{
			name: "missing coverage inferred",
			lines: []LossLineWithPolicyAndClaim{
				{
					Claim: pibit.ClaimData{
						ClaimID:    pointerStr("C-002"),
						DateOfLoss: &validDateOfLoss,
					},
					Loss: pibit.LossData{}, // missing CoverageInferred
				},
			},
			periods: []CoverageAggregationPeriods{}, // no coverage periods at all
			expected: func(t *testing.T, results []pibit.ProcessedLoss) {
				assert.Len(t, results, 1)
				assert.True(t, *results[0].Unmapped)
				assert.Contains(t, results[0].UnmappedReasons, pibit.UnmappedLossReasonMissingCoverage)
			},
		},
		{
			name: "missing date of loss",
			lines: []LossLineWithPolicyAndClaim{
				{
					Claim: pibit.ClaimData{
						ClaimID: pointerStr("C-003"),
					},
					Loss: pibit.LossData{
						CoverageInferred: pointerCoverage(pibit.CoverageInferredAutoLiability),
					},
				},
			},
			periods: []CoverageAggregationPeriods{
				{
					Coverage: coverage,
					Periods: []pibit.PeriodWithPUCount{
						{Period: pibit.Period{FromDate: periodStart, ToDate: periodEnd}, NumberOfPowerUnits: 0},
					},
				},
			},
			expected: func(t *testing.T, results []pibit.ProcessedLoss) {
				assert.Len(t, results, 1)
				assert.True(t, *results[0].Unmapped)
				assert.Contains(t, results[0].UnmappedReasons, pibit.UnmappedLossReasonMissingDateOfLoss)
				assert.Contains(t, results[0].ConfidenceInfo.LowConfidenceReasons,
					pibit.LowConfidenceLossReasonMissingDateOfLoss)
			},
		},
		{
			name: "no matching aggregation period",
			lines: []LossLineWithPolicyAndClaim{
				{
					Claim: pibit.ClaimData{
						ClaimID:    pointerStr("C-004"),
						DateOfLoss: pointerTime(time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					Loss: pibit.LossData{
						CoverageInferred: pointerCoverage(pibit.CoverageInferredAutoLiability),
					},
				},
			},
			periods: []CoverageAggregationPeriods{
				{
					Coverage: coverage,
					Periods: []pibit.PeriodWithPUCount{
						{Period: pibit.Period{FromDate: periodStart, ToDate: periodEnd}, NumberOfPowerUnits: 0},
					},
				},
			},
			expected: func(t *testing.T, results []pibit.ProcessedLoss) {
				assert.Len(t, results, 1)
				assert.True(t, *results[0].Unmapped)
				assert.Contains(t, results[0].UnmappedReasons, pibit.UnmappedLossReasonOutsideAggregationPeriods)
			},
		},
		{
			name: "missing multiple fields causes low confidence",
			lines: []LossLineWithPolicyAndClaim{
				{
					Policy: pibit.PolicyData{}, // No PolicyNo
					Claim:  pibit.ClaimData{},  // Missing DateOfLoss and ClaimID
					Loss: pibit.LossData{
						CoverageInferred: pointerCoverage(pibit.CoverageInferredAutoLiability),
					},
				},
			},
			periods: []CoverageAggregationPeriods{
				{
					Coverage: coverage,
					Periods: []pibit.PeriodWithPUCount{
						{Period: pibit.Period{FromDate: periodStart, ToDate: periodEnd}, NumberOfPowerUnits: 0},
					},
				},
			},
			expected: func(t *testing.T, results []pibit.ProcessedLoss) {
				assert.Len(t, results, 1)
				assert.Equal(t, pibit.ConfidenceLevelLow, results[0].ConfidenceInfo.Level)
				assert.ElementsMatch(t, results[0].ConfidenceInfo.LowConfidenceReasons, []pibit.LowConfidenceLossReason{
					pibit.LowConfidenceLossReasonMissingDateOfLoss,
					pibit.LowConfidenceLossReasonMissingClaimId,
					pibit.LowConfidenceLossReasonMissingPolicyNo,
					pibit.LowConfidenceLossReasonMissingClaimLossType,
				})
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			aggregationID := testUUID
			result := BuildProcessedLosses(aggregationID, tt.lines, tt.periods)
			tt.expected(t, result)
		})
	}
}

func pointerStr(s string) *string {
	return &s
}

func pointerCoverage(c pibit.CoverageInferred) *pibit.CoverageInferred {
	return &c
}

func pointerTime(t time.Time) *time.Time {
	return &t
}
