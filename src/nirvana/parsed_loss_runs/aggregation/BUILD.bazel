load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "aggregation",
    srcs = [
        "aggregation_confidence_calculator.go",
        "aggregation_periods_fetcher.go",
        "aggregation_summary_generator.go",
        "data_fetcher.go",
        "deduplication.go",
        "loss_line_policy_claim_mapper.go",
        "loss_processor.go",
        "missing_periods_identifier.go",
        "types.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/parsed_loss_runs/aggregation",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/external/pibit",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
    ],
)

go_test(
    name = "aggregation_test",
    srcs = [
        "aggregation_confidence_calculator_test.go",
        "aggregation_periods_fetcher_test.go",
        "aggregation_summary_generator_test.go",
        "deduplication_test.go",
        "loss_line_policy_claim_mapper_test.go",
        "loss_processor_test.go",
        "missing_periods_identifier_test.go",
    ],
    embed = [":aggregation"],
    deps = [
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/external/pibit",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
