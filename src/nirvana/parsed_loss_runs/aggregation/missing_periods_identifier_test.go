package aggregation

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

// TestAddGapsToCoveragePeriods is a comprehensive suite for verifying gap detection logic.
func TestAddGapsToCoveragePeriods(t *testing.T) {
	ctx := context.Background()

	// Define common aggregation periods for use in tests.
	aggPeriod2023 := pibit.PeriodWithPUCount{
		Period:             pibit.Period{FromDate: parseDate("2023-01-01"), ToDate: parseDate("2023-12-31")},
		NumberOfPowerUnits: 10,
	}
	aggPeriod2022 := pibit.PeriodWithPUCount{
		Period:             pibit.Period{FromDate: parseDate("2022-01-01"), ToDate: parseDate("2022-12-31")},
		NumberOfPowerUnits: 8,
	}

	testCases := []struct {
		name               string
		aggregationPeriods []CoverageAggregationPeriods
		losses             []LossLineWithPolicyAndClaim
		want               []CoveragePeriodsWithGaps
		wantErr            bool
	}{
		// --- Scenarios with NO GAPS (Expecting nil slices) ---
		{
			name:               "no_gaps_when_a_single_policy_provides_full_coverage",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses:             []LossLineWithPolicyAndClaim{makeLoss("P1", "2023-01-01", "2023-12-31", pibit.CoverageInferredAutoLiability)},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, nil), // Expect nil for no gaps
				),
			},
		},
		{
			name:               "no_gaps_when_contiguous_policies_provide_full_coverage",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses: []LossLineWithPolicyAndClaim{
				makeLoss("P1", "2023-01-01", "2023-06-30", pibit.CoverageInferredAutoLiability),
				makeLoss("P2", "2023-07-01", "2023-12-31", pibit.CoverageInferredAutoLiability),
			},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, nil), // Expect nil for no gaps
				),
			},
		},
		{
			name:               "no_gaps_when_overlapping_policies_provide_full_coverage",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses: []LossLineWithPolicyAndClaim{
				makeLoss("P1", "2023-01-01", "2023-08-01", pibit.CoverageInferredAutoLiability),
				makeLoss("P2", "2023-05-01", "2023-12-31", pibit.CoverageInferredAutoLiability),
			},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, nil), // Expect nil for no gaps
				),
			},
		},

		// --- Scenarios WITH GAPS ---
		{
			name:               "gap_when_no_policies_exist_for_the_period",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses:             nil, // No policies at all.
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, []pibit.MissingSubPeriod{
						makeMissingSubPeriod("2023-01-01", "2023-12-31"),
					}),
				),
			},
		},
		{
			name:               "gap_at_start_and_end_of_period",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses:             []LossLineWithPolicyAndClaim{makeLoss("P1", "2023-03-01", "2023-09-30", pibit.CoverageInferredAutoLiability)},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, []pibit.MissingSubPeriod{
						makeMissingSubPeriod("2023-01-01", "2023-02-28"), // Gap 1
						makeMissingSubPeriod("2023-10-01", "2023-12-31"), // Gap 2
					}),
				),
			},
		},
		{
			name:               "gap_in_the_middle_of_the_period",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses: []LossLineWithPolicyAndClaim{
				makeLoss("P1", "2023-01-01", "2023-04-30", pibit.CoverageInferredAutoLiability),
				makeLoss("P2", "2023-08-01", "2023-12-31", pibit.CoverageInferredAutoLiability),
			},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, []pibit.MissingSubPeriod{
						makeMissingSubPeriod("2023-05-01", "2023-07-31"),
					}),
				),
			},
		},
		{
			name:               "multiple_complex_gaps_in_a_single_period",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses: []LossLineWithPolicyAndClaim{
				makeLoss("P1", "2023-02-01", "2023-02-28", pibit.CoverageInferredAutoLiability),
				makeLoss("P2", "2023-06-01", "2023-07-31", pibit.CoverageInferredAutoLiability),
				makeLoss("P3", "2023-12-01", "2023-12-31", pibit.CoverageInferredAutoLiability),
			},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, []pibit.MissingSubPeriod{
						makeMissingSubPeriod("2023-01-01", "2023-01-31"), // Gap 1
						makeMissingSubPeriod("2023-03-01", "2023-05-31"), // Gap 2
						makeMissingSubPeriod("2023-08-01", "2023-11-30"), // Gap 3
					}),
				),
			},
		},

		// --- Scenarios with Multiple Coverages & Edge Cases ---
		{
			name: "multiple_coverages_one_fully_covered_one_with_gap",
			aggregationPeriods: []CoverageAggregationPeriods{
				makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023),
				makeCoverageAggPeriod(enums.CoverageAutoPhysicalDamage, aggPeriod2023),
			},
			losses: []LossLineWithPolicyAndClaim{
				// Liability has a gap at the end
				makeLoss("P-AL", "2023-01-01", "2023-06-30", pibit.CoverageInferredAutoLiability),
				// Physical Damage is fully covered
				makeLoss("P-PD", "2023-01-01", "2023-12-31", pibit.CoverageInferredAutoPhysicalDamage),
			},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, []pibit.MissingSubPeriod{
						makeMissingSubPeriod("2023-07-01", "2023-12-31"),
					}),
				),
				makeExpectedResult(enums.CoverageAutoPhysicalDamage,
					makeSinglePeriodWithGaps(aggPeriod2023, nil), // No gaps for PD, so expect nil
				),
			},
		},
		{
			name: "multiple_aggregation_periods_one_with_gaps_one_without",
			aggregationPeriods: []CoverageAggregationPeriods{
				{ // A single coverage with two distinct aggregation periods
					Coverage: enums.CoverageAutoLiability,
					Periods:  []pibit.PeriodWithPUCount{aggPeriod2022, aggPeriod2023},
				},
			},
			losses: []LossLineWithPolicyAndClaim{
				makeLoss("P-2022", "2022-01-01", "2022-12-31", pibit.CoverageInferredAutoLiability), // Full coverage for 2022
				makeLoss("P-2023", "2023-01-01", "2023-06-30", pibit.CoverageInferredAutoLiability), // Partial coverage for 2023
			},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					// Period 1 (2022) has no gaps, expect nil
					makeSinglePeriodWithGaps(aggPeriod2022, nil),
					// Period 2 (2023) has a gap
					makeSinglePeriodWithGaps(aggPeriod2023, []pibit.MissingSubPeriod{
						makeMissingSubPeriod("2023-07-01", "2023-12-31"),
					}),
				),
			},
		},
		{
			name:               "policy_outside_aggregation_period_is_ignored",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses:             []LossLineWithPolicyAndClaim{makeLoss("P-Outside", "2024-01-01", "2024-12-31", pibit.CoverageInferredAutoLiability)},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, []pibit.MissingSubPeriod{
						makeMissingSubPeriod("2023-01-01", "2023-12-31"),
					}),
				),
			},
		},
		{
			name:               "duplicate_policies_are_handled_correctly",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses: []LossLineWithPolicyAndClaim{
				makeLoss("P1", "2023-01-01", "2023-06-30", pibit.CoverageInferredAutoLiability),
				makeLoss("P1", "2023-01-01", "2023-06-30", pibit.CoverageInferredAutoLiability), // Identical duplicate
			},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, []pibit.MissingSubPeriod{
						makeMissingSubPeriod("2023-07-01", "2023-12-31"),
					}),
				),
			},
		},
		{
			name:               "no_inputs_returns_no_gaps_and_no_error",
			aggregationPeriods: nil,
			losses:             nil,
			want:               nil, // Function returns nil for nil input
			wantErr:            false,
		},

		// --- Additional Edge Cases ---
		{
			name:               "policy_partially_overlapping_start_of_aggregation_period",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses:             []LossLineWithPolicyAndClaim{makeLoss("P-OverlapStart", "2022-11-01", "2023-03-31", pibit.CoverageInferredAutoLiability)},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, []pibit.MissingSubPeriod{
						makeMissingSubPeriod("2023-04-01", "2023-12-31"),
					}),
				),
			},
		},
		{
			name:               "single_day_gap_is_detected",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses: []LossLineWithPolicyAndClaim{
				makeLoss("P1", "2023-01-01", "2023-06-29", pibit.CoverageInferredAutoLiability),
				makeLoss("P2", "2023-07-01", "2023-12-31", pibit.CoverageInferredAutoLiability),
			},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, []pibit.MissingSubPeriod{
						makeMissingSubPeriod("2023-06-30", "2023-06-30"),
					}),
				),
			},
		},
		{
			name:               "loss_with_nil_dates_or_coverage_is_ignored",
			aggregationPeriods: []CoverageAggregationPeriods{makeCoverageAggPeriod(enums.CoverageAutoLiability, aggPeriod2023)},
			losses: []LossLineWithPolicyAndClaim{
				// This valid loss should be the only one considered
				makeLoss("P-Valid", "2023-01-01", "2023-03-31", pibit.CoverageInferredAutoLiability),
				// Invalid losses that should be filtered out
				{
					Policy: pibit.PolicyData{PolicyNo: strPtr("P-NilEffDate"), ExpDate: timePtr(parseDate("2023-12-31"))},
					Loss:   pibit.LossData{CoverageInferred: coverageInferredPtr(pibit.CoverageInferredAutoLiability)},
				},
				{
					Policy: pibit.PolicyData{PolicyNo: strPtr("P-NilExpDate"), EffDate: timePtr(parseDate("2023-01-01"))},
					Loss:   pibit.LossData{CoverageInferred: coverageInferredPtr(pibit.CoverageInferredAutoLiability)},
				},
				{
					Policy: pibit.PolicyData{PolicyNo: strPtr("P-NilCoverage"), EffDate: timePtr(parseDate("2023-01-01")), ExpDate: timePtr(parseDate("2023-12-31"))},
					Loss:   pibit.LossData{CoverageInferred: nil},
				},
			},
			want: []CoveragePeriodsWithGaps{
				makeExpectedResult(enums.CoverageAutoLiability,
					makeSinglePeriodWithGaps(aggPeriod2023, []pibit.MissingSubPeriod{
						makeMissingSubPeriod("2023-04-01", "2023-12-31"),
					}),
				),
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := DeduplicationResult{SelectedLosses: tc.losses}
			got, err := AddGapsToCoveragePeriods(ctx, result, tc.aggregationPeriods)

			if tc.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

			assert.Equal(t, tc.want, got)
		})
	}
}

// --- Test Helper Functions ---

// makeLoss creates a loss line with its policy data for testing.
func makeLoss(policyNo, eff, exp string, inferred pibit.CoverageInferred) LossLineWithPolicyAndClaim {
	return LossLineWithPolicyAndClaim{
		Policy: pibit.PolicyData{
			PolicyNo: strPtr(policyNo),
			EffDate:  timePtr(parseDate(eff)),
			ExpDate:  timePtr(parseDate(exp)),
		},
		Loss: pibit.LossData{
			CoverageInferred: coverageInferredPtr(inferred),
		},
	}
}

// makeCoverageAggPeriod creates the input structure for a single coverage type and aggregation period.
func makeCoverageAggPeriod(coverage enums.Coverage, period pibit.PeriodWithPUCount) CoverageAggregationPeriods {
	return CoverageAggregationPeriods{
		Coverage: coverage,
		Periods:  []pibit.PeriodWithPUCount{period},
	}
}

// makeExpectedResult builds the complex `want` structure for a test case with multiple periods.
func makeExpectedResult(coverage enums.Coverage, periods ...PeriodsWithGaps) CoveragePeriodsWithGaps {
	return CoveragePeriodsWithGaps{
		Coverage: coverage,
		Periods:  periods,
	}
}

// makeSinglePeriodWithGaps is a helper to create the `PeriodsWithGaps` struct.
func makeSinglePeriodWithGaps(aggPeriod pibit.PeriodWithPUCount, missing []pibit.MissingSubPeriod) PeriodsWithGaps {
	// If the incoming slice is nil, we ensure the struct field is also nil.
	// If it's an empty slice, the field will be an empty slice. This helper now correctly handles both.
	if missing == nil {
		return PeriodsWithGaps{
			AggregationPeriod:  aggPeriod.Period,
			MissingSubPeriods:  nil,
			NumberOfPowerUnits: aggPeriod.NumberOfPowerUnits,
		}
	}
	return PeriodsWithGaps{
		AggregationPeriod:  aggPeriod.Period,
		MissingSubPeriods:  missing,
		NumberOfPowerUnits: aggPeriod.NumberOfPowerUnits,
	}
}

// makeMissingSubPeriod creates a single gap period.
func makeMissingSubPeriod(from, to string) pibit.MissingSubPeriod {
	p := pibit.Period{FromDate: parseDate(from), ToDate: parseDate(to)}
	return pibit.MissingSubPeriod{
		Period:              p,
		LargerThanThreshold: isPeriodLargerThanThreshold(p),
	}
}

// timePtr returns a pointer to a time.Time.
func timePtr(t time.Time) *time.Time {
	return &t
}

// coverageInferredPtr returns a pointer to a pibit.CoverageInferred.
func coverageInferredPtr(c pibit.CoverageInferred) *pibit.CoverageInferred {
	return &c
}

// parseDate parses a date string in YYYY-MM-DD format.
func parseDate(s string) time.Time {
	t, _ := time.Parse("2006-01-02", s)
	return t
}
