package aggregation

import (
	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func ComputeAggregationConfidence(processedLosses []pibit.ProcessedLoss, aggregationPeriodsWithGaps []CoveragePeriodsWithGaps) (*pibit.AggregationConfidenceInfo, error) {
	unmappedReasonsForLowConfidence := []pibit.UnmappedLossReason{
		pibit.UnmappedLossReasonMissingCoverage,
		pibit.UnmappedLossReasonMissingDateOfLoss,
	}

	var lowConfidenceAggregationReasons []pibit.LowConfidenceAggregationReason
	addedLowConfidenceLossesReason := false
	addedUnaggregatedNonDuplicateLossesReason := false

	for _, loss := range processedLosses {
		if loss.Unmapped == nil {
			return nil, errors.Newf("processed loss %s with unmapped nil", loss.ID)
		}
		if *loss.Unmapped && !addedUnaggregatedNonDuplicateLossesReason &&
			slice_utils.ContainsAny(loss.UnmappedReasons, unmappedReasonsForLowConfidence...) {
			lowConfidenceAggregationReasons = append(lowConfidenceAggregationReasons, pibit.LowConfidenceAggregationReasonUnaggregatedNonDuplicateLosses)
			addedUnaggregatedNonDuplicateLossesReason = true
		}
		if loss.ConfidenceInfo.Level == pibit.ConfidenceLevelLow && !addedLowConfidenceLossesReason {
			lowConfidenceAggregationReasons = append(lowConfidenceAggregationReasons, pibit.LowConfidenceAggregationReasonLowConfidenceLosses)
			addedLowConfidenceLossesReason = true
		}
	}

	if hasMissingPeriods(aggregationPeriodsWithGaps) {
		lowConfidenceAggregationReasons = append(lowConfidenceAggregationReasons, pibit.LowConfidenceAggregationReasonMissingPolicyPeriods)
	}

	confidenceLevel := pibit.ConfidenceLevelHigh
	if len(lowConfidenceAggregationReasons) > 0 {
		confidenceLevel = pibit.ConfidenceLevelLow
	}

	return &pibit.AggregationConfidenceInfo{
		Level:                          confidenceLevel,
		LowConfidenceAggregationReason: lowConfidenceAggregationReasons,
	}, nil
}

func hasMissingPeriods(aggregationPeriodsWithGaps []CoveragePeriodsWithGaps) bool {
	for _, coveragePeriod := range aggregationPeriodsWithGaps {
		for _, period := range coveragePeriod.Periods {
			for _, missingSubPeriod := range period.MissingSubPeriods {
				if missingSubPeriod.LargerThanThreshold {
					return true
				}
			}
		}
	}
	return false
}
