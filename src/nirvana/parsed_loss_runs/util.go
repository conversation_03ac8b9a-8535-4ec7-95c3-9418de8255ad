package parsed_loss_runs

import (
	"context"
	"database/sql"
	"math"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/interceptors/external/deps"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

const (
	HOURS_PER_DAY        = 24
	RECENCY_ALLOWED_DAYS = 180
)

var ErrValidNil = errors.New("valid nil value")

// validateErrorInformation validates error information for presence of constituents fields
func validateErrorInformation(req CreateParsedLossesRequest) error {
	if req.Status == RequestDocumentStatusDocError {
		switch {
		case req.Metadata.ErrorMsg == nil || *req.Metadata.ErrorMsg == "":
			return errors.Wrap(ValidationError, "error message is nil")
		}
	}
	return nil
}

func validateDocumentIdFormat(documentId string) error {
	if documentId == "" {
		return errors.Wrap(ValidationError, "document id is nil")
	}
	_, err := uuid.Parse(documentId)
	if err != nil {
		return errors.Wrap(ValidationError, "document id is not in the required format")
	}
	return nil
}

func validateJsonUrl(req CreateParsedLossesRequest) error {
	if req.Status == RequestDocumentStatusDocComplete && (req.JsonFileDownloadLink == nil || *req.JsonFileDownloadLink == "") {
		return errors.Wrap(ValidationError, "json file download link for DOC_COMPLETE request is nil")
	}
	return nil
}

func validateApplicationRecency(ctx context.Context, req CreateParsedLossesRequest, deps deps.Deps) error {
	appId := req.AdditionalDetails.ApplicationID
	app, err := deps.ApplicationWrapper.GetAppById(ctx, appId)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return errors.Wrapf(NotFoundError, "application %s not found", appId)
		}
		return errors.Wrapf(err, "failed to get application %s", appId)
	}
	if app.CreatedAt.Before(deps.Clk.Now().Add(time.Hour * HOURS_PER_DAY * RECENCY_ALLOWED_DAYS * -1)) {
		return errors.Wrapf(ForbiddenError, "application %s is older than %d days", appId, RECENCY_ALLOWED_DAYS)
	}
	return nil
}

func getErrorInfoFromRequest(metadata ParsedLossRunMetaData) (*pibit.DocumentErrorInfo, error) {
	if metadata.ErrorMsg == nil || *metadata.ErrorMsg == "" {
		return nil, errors.Wrap(ValidationError, "error message is nil")
	}
	return &pibit.DocumentErrorInfo{
		ErrorMessage: *metadata.ErrorMsg,
	}, nil
}

func validateAndGetDateTime(
	dateString *string,
	dateTimeLayout string,
	isNullable bool,
) (*time.Time, error) {
	if dateString == nil || *dateString == "" {
		if !isNullable {
			return nil, errors.Wrap(ValidationError, "date string is nil or empty but not nullable")
		}
		return nil, ErrValidNil
	}
	dt, err := time.Parse(dateTimeLayout, *dateString)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse date string: %s with layout: %s", *dateString, dateTimeLayout)
	}
	return pointer_utils.ToPointer(dt), nil
}

func validateAndGetInt32(
	num *int, isNullable bool,
) (*int32, error) {
	if num == nil {
		if !isNullable {
			return nil, errors.Wrap(ValidationError, "number is nil but not nullable")
		}
		// If the number is nil and nullable, return nil without error
		return nil, ErrValidNil
	}
	if *num > math.MaxInt32 || *num < math.MinInt32 {
		return nil, errors.Wrapf(ValidationError, "number %d exceeds int32 range", *num)
	}
	newNum := int32(*num)
	return &newNum, nil
}

func validatePolicyIdentifier(policyNumber *string, effDate, expDate *time.Time) error {
	if isNilOrEmpty(policyNumber) && effDate == nil && expDate == nil {
		return errors.Wrap(ValidationError, "policy number, eff date, and exp date are all nil")
	}
	return nil
}

func validateClaimIdentifier(claimId *string, dateOfLoss *time.Time) error {
	if isNilOrEmpty(claimId) && dateOfLoss == nil {
		return errors.Wrap(ValidationError, "claim id and date of loss are both nil")
	}
	return nil
}

func isNilOrEmpty(s *string) bool {
	return s == nil || *s == ""
}
