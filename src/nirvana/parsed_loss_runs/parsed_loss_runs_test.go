package parsed_loss_runs

import (
	"context"
	"database/sql"
	"testing"

	"go.uber.org/mock/gomock"

	"github.com/benbjohnson/clock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/api-server/interceptors/external/deps"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
)

func strPtr(s string) *string { return &s }

func TestValidateRequest(t *testing.T) {
	// Use a controllable/mock clock!
	clk := clock.NewMock()
	now := clk.Now()
	docId := uuid.NewString()
	oldDocId := ""         // Invalid because empty
	invalidDocId := "abcd" // Invalid format

	tests := []struct {
		name          string
		request       CreateParsedLossesRequest
		documentId    string
		setupMock     func(m *application.MockDataWrapper)
		wantErrSubstr string
	}{
		{
			name: "success DOC_COMPLETE",
			request: CreateParsedLossesRequest{
				Status:               RequestDocumentStatusDocComplete,
				JsonFileDownloadLink: strPtr("url"),
				Metadata:             ParsedLossRunMetaData{},
				AdditionalDetails:    ParsedLossRunAdditionalDetails{ApplicationID: "app"},
			},
			documentId: docId,
			setupMock: func(m *application.MockDataWrapper) {
				m.EXPECT().GetAppById(gomock.Any(), "app").
					Return(&application.Application{CreatedAt: now}, nil)
			},
		},
		{
			name: "invalid documentId (empty)",
			request: CreateParsedLossesRequest{
				Metadata:          ParsedLossRunMetaData{},
				AdditionalDetails: ParsedLossRunAdditionalDetails{ApplicationID: "app"},
			},
			documentId:    oldDocId,
			setupMock:     nil,
			wantErrSubstr: "document id is nil",
		},
		{
			name: "invalid documentId (not UUID)",
			request: CreateParsedLossesRequest{
				Metadata:          ParsedLossRunMetaData{},
				AdditionalDetails: ParsedLossRunAdditionalDetails{ApplicationID: "app"},
			},
			documentId:    invalidDocId,
			setupMock:     nil,
			wantErrSubstr: "document id is not in the required format",
		},
		{
			name: "error info required but nil",
			request: CreateParsedLossesRequest{
				Status:            RequestDocumentStatusDocError,
				Metadata:          ParsedLossRunMetaData{ErrorMsg: nil},
				AdditionalDetails: ParsedLossRunAdditionalDetails{ApplicationID: "app"},
			},
			documentId:    docId,
			setupMock:     nil,
			wantErrSubstr: "error message is nil",
		},
		{
			name: "error info required but empty",
			request: CreateParsedLossesRequest{
				Status:            RequestDocumentStatusDocError,
				Metadata:          ParsedLossRunMetaData{ErrorMsg: strPtr("")},
				AdditionalDetails: ParsedLossRunAdditionalDetails{ApplicationID: "app"},
			},
			documentId:    docId,
			setupMock:     nil,
			wantErrSubstr: "error message is nil",
		},
		{
			name: "DOC_COMPLETE missing json url",
			request: CreateParsedLossesRequest{
				Status:            RequestDocumentStatusDocComplete,
				Metadata:          ParsedLossRunMetaData{},
				AdditionalDetails: ParsedLossRunAdditionalDetails{ApplicationID: "app"},
				// JsonFileDownloadLink is nil
			},
			documentId:    docId,
			setupMock:     nil,
			wantErrSubstr: "json file download link",
		},
		{
			name: "application not found",
			request: CreateParsedLossesRequest{
				Status:               RequestDocumentStatusDocComplete,
				Metadata:             ParsedLossRunMetaData{},
				AdditionalDetails:    ParsedLossRunAdditionalDetails{ApplicationID: "notfound"},
				JsonFileDownloadLink: strPtr("something"),
			},
			documentId: docId,
			setupMock: func(m *application.MockDataWrapper) {
				m.EXPECT().GetAppById(gomock.Any(), "notfound").
					Return(nil, sql.ErrNoRows)
			},
			wantErrSubstr: "not found",
		},
		{
			name: "application too old",
			request: CreateParsedLossesRequest{
				Status:               RequestDocumentStatusDocComplete,
				Metadata:             ParsedLossRunMetaData{},
				AdditionalDetails:    ParsedLossRunAdditionalDetails{ApplicationID: "old"},
				JsonFileDownloadLink: strPtr("something"),
			},
			documentId: docId,
			setupMock: func(m *application.MockDataWrapper) {
				oldTime := now.AddDate(0, 0, -RECENCY_ALLOWED_DAYS-1)
				m.EXPECT().GetAppById(gomock.Any(), "old").
					Return(&application.Application{CreatedAt: oldTime}, nil)
			},
			wantErrSubstr: "older than 180 days",
		},
		{
			name: "application get failure (not ErrNoRows)",
			request: CreateParsedLossesRequest{
				Status:               RequestDocumentStatusDocComplete,
				Metadata:             ParsedLossRunMetaData{},
				AdditionalDetails:    ParsedLossRunAdditionalDetails{ApplicationID: "fail"},
				JsonFileDownloadLink: strPtr("something"),
			},
			documentId: docId,
			setupMock: func(m *application.MockDataWrapper) {
				m.EXPECT().GetAppById(gomock.Any(), "fail").
					Return(nil, sql.ErrConnDone)
			},
			wantErrSubstr: "failed to get application",
		},
	}

	for _, tc := range tests {
		tc := tc // capture range var
		t.Run(tc.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockApplicationWrapper := application.NewMockDataWrapper(ctrl)
			if tc.setupMock != nil {
				tc.setupMock(mockApplicationWrapper)
			}
			dependencies := deps.Deps{
				ApplicationWrapper: mockApplicationWrapper,
				Clk:                clk,
			}
			err := validateRequest(context.Background(), tc.request, tc.documentId, dependencies)
			if tc.wantErrSubstr == "" {
				require.NoError(t, err)
			} else {
				require.Error(t, err)
				require.Contains(t, err.Error(), tc.wantErrSubstr)
			}
		})
	}
}
