package parsed_loss_runs

import (
	"fmt"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/pibit_ai"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

// MapLossRunPoliciesToDBObjects maps API response loss run policies to database-ready objects.
func MapLossRunPoliciesToDBObjects(apiLossRunPolicies []pibit_ai.LossRunPolicy, documentID string) (*pibit.MappedDBObjects, error) {
	var dbObjects pibit.MappedDBObjects

	docID := pointer_utils.ToPointer(documentID) // Convert documentID to a pointer once

	policySnCounter := int32(1)
	claimSnCounter := int32(1)
	lossSeqIDCounter := int32(1) // Assuming sequential ID for losses across all claims/policies for simplicity

	for _, apiPolicy := range apiLossRunPolicies {
		pData, err := mapLossRunPolicyToPolicyData(documentID, apiPolicy, policySnCounter)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to map policy data for policy %v", apiPolicy.PolicyNo)
		}
		dbObjects.Policies = append(dbObjects.Policies, *pData)

		for _, apiClaim := range apiPolicy.ClaimsData { // Renamed from apiClaim to apiLossRunClaim
			cData, err := mapLossRunClaimToClaimData(apiClaim, docID, claimSnCounter, policySnCounter, &apiPolicy)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to map claim data for claim %v in policy %v", apiClaim.ClaimID, apiPolicy.PolicyNo)
			}
			dbObjects.Claims = append(dbObjects.Claims, *cData)

			for _, apiLoss := range apiClaim.LossData {
				lData, err := mapLossRunLossToLossData(documentID, apiLoss, claimSnCounter, policySnCounter, lossSeqIDCounter)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to map loss data for loss in claim %v in policy %v", apiClaim.ClaimID, apiPolicy.PolicyNo)
				}
				dbObjects.Losses = append(dbObjects.Losses, *lData)
				lossSeqIDCounter++
			}
			claimSnCounter++
		}
		policySnCounter++
	}

	return &dbObjects, nil
}

func mapLossRunPolicyToPolicyData(
	documentID string,
	apiPolicy pibit_ai.LossRunPolicy,
	policySnCounter int32,
) (*pibit.PolicyData, error) {
	cancelDate, err := validateAndGetDateTime(apiPolicy.CancelDate, dateLayout, true)
	if err != nil && !errors.Is(err, ErrValidNil) {
		return nil, errors.Wrapf(err, "failed to parse CancelDate for policy %v", apiPolicy.PolicyNo)
	}
	effDate, err := validateAndGetDateTime(apiPolicy.EffDate, dateLayout, true)
	if err != nil && !errors.Is(err, ErrValidNil) {
		return nil, errors.Wrapf(err, "failed to parse EffDate for policy %v", apiPolicy.PolicyNo)
	}
	expDate, err := validateAndGetDateTime(apiPolicy.ExpDate, dateLayout, true)
	if err != nil && !errors.Is(err, ErrValidNil) {
		return nil, errors.Wrapf(err, "failed to parse ExpDate for policy %v", apiPolicy.PolicyNo)
	}
	reportGenerationDate, err := validateAndGetDateTime(apiPolicy.ReportGenerationDate, dateLayout, true)
	if err != nil && !errors.Is(err, ErrValidNil) {
		return nil, errors.Wrapf(err, "failed to parse ReportGenerationDate for policy %v", apiPolicy.PolicyNo)
	}
	err = validatePolicyIdentifier(apiPolicy.PolicyNo, effDate, expDate)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to validate policy identifier for policy %v", apiPolicy.PolicyNo)
	}
	// Map Policy to PolicyData
	pData := pibit.PolicyData{
		DocumentId:           pointer_utils.ToPointer(documentID),
		PolicySn:             pointer_utils.ToPointer(policySnCounter),
		Lob:                  apiPolicy.Lob,
		Insurer:              apiPolicy.Insurer,
		Insured:              apiPolicy.Insured,
		Agent:                apiPolicy.Agent, // Already *string
		PolicyNo:             apiPolicy.PolicyNo,
		EffDate:              effDate,
		ExpDate:              expDate,
		ReportGenerationDate: reportGenerationDate,
		CancelDate:           cancelDate, // Parse string to *time.Time
		NoLoss:               pointer_utils.ToPointer(int32(apiPolicy.NoLoss)),
		TotalPaid:            apiPolicy.TotalPaid,
		TotalReserve:         apiPolicy.TotalReserve,
		TotalRecovered:       apiPolicy.TotalRecovered,
		TotalIncurred:        pointer_utils.ToPointer(apiPolicy.TotalIncurred),
	}
	return &pData, nil
}

func mapLossRunClaimToClaimData(
	apiClaim pibit_ai.LossRunClaim,
	documentID *string,
	claimSnCounter int32,
	policySnCounter int32,
	apiPolicy *pibit_ai.LossRunPolicy,
) (*pibit.ClaimData, error) {
	// Map Claim to ClaimData
	dateOfLoss, err := validateAndGetDateTime(apiClaim.DateOfLoss, dateLayout, true)
	if err != nil && !errors.Is(err, ErrValidNil) {
		return nil, errors.Wrapf(err, "failed to parse DateOfLoss for claim %v in policy %v", apiClaim.ClaimID, apiPolicy.PolicyNo)
	}
	dateReported, err := validateAndGetDateTime(apiClaim.DateReported, dateLayout, true)
	if err != nil && !errors.Is(err, ErrValidNil) {
		return nil, errors.Wrapf(err, "failed to parse DateReported for claim %v in policy %v", apiClaim.ClaimID, apiPolicy.PolicyNo)
	}
	timeOfLoss, err := validateAndGetDateTime(apiClaim.TimeOfLoss, timeLayout, true)
	if err != nil && !errors.Is(err, ErrValidNil) {
		return nil, errors.Wrapf(err, "failed to parse TimeOfLoss for claim %v in policy %v", apiClaim.ClaimID, apiPolicy.PolicyNo)
	}
	closedDate, err := validateAndGetDateTime(apiClaim.ClosedDate, dateLayout, true)
	if err != nil && !errors.Is(err, ErrValidNil) {
		return nil, errors.Wrapf(err, "failed to parse ClosedDate for claim %v in policy %v", apiClaim.ClaimID, apiPolicy.PolicyNo)
	}
	dob, err := validateAndGetDateTime(apiClaim.Dob, dateLayout, true)
	if err != nil && !errors.Is(err, ErrValidNil) {
		return nil, errors.Wrapf(err, "failed to parse DOB for claim %v in policy %v", apiClaim.ClaimID, apiPolicy.PolicyNo)
	}
	hiringDate, err := validateAndGetDateTime(apiClaim.HiringDate, dateLayout, true)
	if err != nil && !errors.Is(err, ErrValidNil) {
		return nil, errors.Wrapf(err, "failed to parse HiringDate for claim %v in policy %v", apiClaim.ClaimID, apiPolicy.PolicyNo)
	}
	age, err := validateAndGetInt32(apiClaim.Age, true)
	if err != nil && !errors.Is(err, ErrValidNil) {
		return nil, errors.Wrapf(err, "failed to parse Age for claim %v in policy %v", apiClaim.ClaimID, apiPolicy.PolicyNo)
	}
	err = validateClaimIdentifier(apiClaim.ClaimID, dateOfLoss)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to validate claim identifier for claim %v in policy %v", apiClaim.ClaimID, apiPolicy.PolicyNo)
	}
	cData := pibit.ClaimData{
		DocumentId:             documentID,
		ClaimSn:                pointer_utils.ToPointer(claimSnCounter),
		PolicySn:               pointer_utils.ToPointer(policySnCounter),
		ClaimID:                apiClaim.ClaimID,
		OccurrenceID:           apiClaim.OccurrenceID,
		DateOfLoss:             dateOfLoss,
		DateReported:           dateReported,
		TimeOfLoss:             timeOfLoss,
		ClosedDate:             closedDate,
		CauseOfLossSummary:     apiClaim.CauseOfLossSummary,
		CauseOfLossDescription: apiClaim.CauseOfLossDescription,
		VIN:                    apiClaim.Vin,  // Case change
		Type:                   apiClaim.Type, // Case change
		DriverID:               apiClaim.DriverID,
		Name:                   apiClaim.Name,
		Age:                    age, // Convert *int to *int32
		Gender:                 apiClaim.Gender,
		DOB:                    dob,        // Parse string to *time.Time
		HiringDate:             hiringDate, // Parse string to *time.Time
		Street:                 apiClaim.Street,
		CityCounty:             apiClaim.CityCounty,
		State:                  apiClaim.State,
		Zip:                    apiClaim.Zip, // Convert *int to *int32
		LossLocation:           apiClaim.LossLocation,
		ClaimStatus:            apiClaim.ClaimStatus,
	}
	return &cData, nil
}

func mapLossRunLossToLossData(
	documentId string,
	apiLoss pibit_ai.Loss,
	claimSnCounter int32,
	policySnCounter int32,
	lossSeqIDCounter int32,
) (*pibit.LossData, error) {
	var coverageInferred *pibit.CoverageInferred
	if apiLoss.CoverageInferred != nil && *apiLoss.CoverageInferred != "" {
		parsedCoverageInferred, err := pibit.ParseCoverageInferred(*apiLoss.CoverageInferred)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to parse coverage inferred")
		}
		coverageInferred = pointer_utils.ToPointer(parsedCoverageInferred)
	}
	// Map Loss to LossData
	lData := pibit.LossData{
		DocumentId:            pointer_utils.ToPointer(documentId),
		ClaimSn:               pointer_utils.ToPointer(claimSnCounter),
		PolicySn:              pointer_utils.ToPointer(policySnCounter),
		LossSeqID:             pointer_utils.ToPointer(fmt.Sprintf("LOSS-%d", lossSeqIDCounter)), // Generate simple sequence ID
		Claimant:              apiLoss.Claimant,
		CoverageInferred:      coverageInferred,
		ClaimLossType:         apiLoss.ClaimLossType,
		ClaimLossTypeInferred: apiLoss.ClaimLossTypeInferred,
		Examiner:              apiLoss.Examiner,
		LossReserved:          apiLoss.LossReserved,
		LossPaid:              apiLoss.LossPaid,
		ExpenseReserve:        apiLoss.ExpenseReserve,
		ExpensePaid:           apiLoss.ExpensePaid,
		TotalReserve:          apiLoss.TotalReserve,
		TotalPaid:             apiLoss.TotalPaid,
		TotalRecovered:        apiLoss.TotalRecovered,
		TotalIncurred:         pointer_utils.ToPointer(apiLoss.TotalIncurred),
		DeductibleAmount:      apiLoss.DeductibleAmount,
		SubrogationAmount:     apiLoss.SubrogationAmount,
		SalvageAmount:         apiLoss.SalvageAmount,
		OtherRecovery:         apiLoss.OtherRecovery,
		LegalReserve:          apiLoss.LegalReserve,
		LegalPaid:             apiLoss.LegalPaid,
		LegalIncurred:         apiLoss.LegalIncurred,
		AlaeExpenseReserve:    apiLoss.AlaeExpenseReserve,
		AlaePaid:              apiLoss.AlaePaid,
		AlaeIncurred:          apiLoss.AlaeIncurred,
		OtherExpenseReserve:   apiLoss.OtherExpenseReserve,
		OtherExpensePaid:      apiLoss.OtherExpensePaid,
		OtherExpenseIncurred:  apiLoss.OtherExpenseIncurred,
	}
	return &lData, nil
}
