package app_state_machine

import (
	"context"
	"sort"
	"time"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw/appetite_factors"

	"nirvanatech.com/nirvana/underwriting/appetite_factors/appetite_score_rubric"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/problem"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/policy"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	app_chkr "nirvanatech.com/nirvana/quoting/appetite_checker"
	app_chkr_enums "nirvanatech.com/nirvana/quoting/appetite_checker/enums"
	"nirvanatech.com/nirvana/underwriting/appetite_factors/input"
	"nirvanatech.com/nirvana/underwriting/common"
)

// loadStateFn is a function to load a new state for an application. This fn
// receives an instance of the AppStateMachine, which is required to create
// an ApplicationState instance.
type loadStateFn func(deps ASMDeps, appId string) ApplicationState

// states is a map that holds all the possible states for the AppStateMachine.
// Each key returns a func, so that we only generate a state instance when
// needed, avoiding unnecessary memory allocation.
var states = map[state_enums.AppState]loadStateFn{
	state_enums.AppStateUnsubmitted:         newAppStateUnsubmitted,
	state_enums.AppStateDeclined:            newAppStateDeclined,
	state_enums.AppStatePanic:               newAppStatePanic,
	state_enums.AppStateIndicationGenerated: newAppStateIndicationGenerated,
	state_enums.AppStateUnderUWReview:       newAppStateUnderUWReview,
	state_enums.AppStateQuoteGenerated:      newAppStateQuoteGenerated,
	state_enums.AppStatePolicyCreated:       newAppStatePolicyCreated,
	state_enums.AppStateApproved:            newAppStateApproved,
	state_enums.AppStateClosed:              newAppStateClosed,
}

// fetchPrimaryCategoryFromDistribution picks the dominant category
// for rating AL/APD. This is done by
// 1. Grouping commodities by category
// 2. Sorting based on % hauls, max dollar, avg dollar
// & commodity name in that order
func fetchPrimaryCategoryFromDistribution(
	commodities []application.WeightedCommodityRecord,
) (*app_enums.CommodityCategory, error) {
	commoditiesCopy := groupCommoditiesByCategory(commodities)

	sort.Slice(commoditiesCopy, func(i, j int) bool {
		// Sort by % Hauls first
		if commoditiesCopy[i].PercentageOfHauls > commoditiesCopy[j].PercentageOfHauls {
			return true
		}
		if commoditiesCopy[i].PercentageOfHauls < commoditiesCopy[j].PercentageOfHauls {
			return false
		}

		// Sort by Maximum Dollar Value Hauled Second
		if commoditiesCopy[i].MaxDollarValueHauled > commoditiesCopy[j].MaxDollarValueHauled {
			return true
		}
		if commoditiesCopy[i].MaxDollarValueHauled < commoditiesCopy[j].MaxDollarValueHauled {
			return false
		}

		// Sort by Avg Dollar Value Hauled Third
		if commoditiesCopy[i].AvgDollarValueHauled > commoditiesCopy[j].AvgDollarValueHauled {
			return true
		}
		if commoditiesCopy[i].AvgDollarValueHauled < commoditiesCopy[j].AvgDollarValueHauled {
			return false
		}

		// Sort by Commodity Fourth
		return commoditiesCopy[i].Commodity.Label > commoditiesCopy[j].Commodity.Label
	})
	// Set the Primary Category
	primCategory, err := app_enums.CommodityCategoryString(commoditiesCopy[0].Category.String())
	if err != nil {
		return nil, errors.Wrapf(err, "unsupported primary category %s",
			commoditiesCopy[0].Category.String())
	}
	return &primCategory, nil
}

// groupCommoditiesByCategory groups all the commodities by
// category, so we can sort that and get the primary category
func groupCommoditiesByCategory(
	commodities []application.WeightedCommodityRecord,
) []application.WeightedCommodityRecord {
	catComMap := make(map[app_enums.CommodityCategory]application.WeightedCommodityRecord)
	for _, com := range commodities {
		if t, ok := catComMap[com.Category]; ok {
			catComMap[com.Category] = application.WeightedCommodityRecord{
				Commodity: application.Commodity{
					// Since the label is also a part of the primary category
					// sorting criteria, we need to pick the max label.
					Label: max(com.Commodity.Label, t.Commodity.Label),
				},
				Category:             com.Category,
				AvgDollarValueHauled: t.AvgDollarValueHauled + com.AvgDollarValueHauled,
				MaxDollarValueHauled: t.MaxDollarValueHauled + com.MaxDollarValueHauled,
				PercentageOfHauls:    t.PercentageOfHauls + com.PercentageOfHauls,
			}
			continue
		}
		// If the category is not present in the map, add it
		catComMap[com.Category] = application.WeightedCommodityRecord{
			Commodity: application.Commodity{
				Label: com.Commodity.Label,
			},
			Category:             com.Category,
			AvgDollarValueHauled: com.AvgDollarValueHauled,
			MaxDollarValueHauled: com.MaxDollarValueHauled,
			PercentageOfHauls:    com.PercentageOfHauls,
		}
	}
	retval := make([]application.WeightedCommodityRecord, 0, len(catComMap))

	for _, value := range catComMap {
		retval = append(retval, value)
	}

	return retval
}

func updateRenewalFormCoverages(formCoverages *[]application.CoverageDetails, appCoverages []application.CoverageDetails) {
	coverageMap := make(map[app_enums.Coverage]application.CoverageDetails)

	// Create a map for quick lookup of appCoverages
	for _, appCoverage := range appCoverages {
		coverageMap[appCoverage.CoverageType] = appCoverage
	}
	// Iterate and update the fields if coverage exists in appCoverages
	for i, formCoverage := range *formCoverages {
		if appCoverage, ok := coverageMap[formCoverage.CoverageType]; ok {
			(*formCoverages)[i].Limit = appCoverage.Limit
			(*formCoverages)[i].Deductible = appCoverage.Deductible
		}
	}
}

func CreateLossRunSummaryForRenewalApplication(LossRunSummary []application.LossRunSummaryPerCoverage, lastApplicationEffectiveDate time.Time) []application.LossRunSummaryPerCoverage {
	lossRunSummary := make([]application.LossRunSummaryPerCoverage, 0)
	for _, summaryPerCovDB := range LossRunSummary {
		coverageSummary := summaryPerCovDB.Summary
		isNirvanaPeriod := true

		newRecord := application.LossRunSummaryRecord{
			PolicyPeriodStartDate: lastApplicationEffectiveDate,
			PolicyPeriodEndDate:   lastApplicationEffectiveDate.AddDate(1, 0, 0),
			NumberOfPowerUnits:    0,
			LossIncurred:          0,
			NumberOfClaims:        0,
			IsNirvanaPeriod:       &isNirvanaPeriod,
		}

		if len(coverageSummary) > 0 {
			// Remove the last element from original coverageSummary
			coverageSummary = coverageSummary[:len(coverageSummary)-1]
		}

		// Prepend the new LossRunSummaryRecord to the start of the list
		coverageSummary = append([]application.LossRunSummaryRecord{newRecord}, coverageSummary...)

		lossRunSummary = append(lossRunSummary, application.LossRunSummaryPerCoverage{
			CoverageType: summaryPerCovDB.CoverageType,
			Summary:      coverageSummary,
		})
	}
	return lossRunSummary
}

func CreateMTCLossInfoForRenewalApplication(originalApp application.Application, lossRunSummary []application.LossRunSummaryPerCoverage, lastApplicationEffectiveDate time.Time) []application.LossRunSummaryPerCoverage {
	if originalApp.LossInfo == nil {
		return lossRunSummary
	}
	// Carryover LossSummary from original application if it was selected by agent but got rejected by UA
	var mtcLossSummary application.LossRunSummaryPerCoverage
	for _, summaryPerCovDB := range originalApp.LossInfo.LossRunSummary {
		if summaryPerCovDB.CoverageType.IsMTCCoverage() {
			mtcLossSummary = summaryPerCovDB
			break
		}
	}
	mtcCoverageSummary := mtcLossSummary.Summary
	newRecord := application.LossRunSummaryRecord{
		PolicyPeriodStartDate: lastApplicationEffectiveDate,
		PolicyPeriodEndDate:   lastApplicationEffectiveDate.AddDate(1, 0, 0),
		NumberOfPowerUnits:    0,
		LossIncurred:          0,
		NumberOfClaims:        0,
	}
	if len(mtcCoverageSummary) > 0 {
		// Remove the last element from original coverageSummary
		mtcCoverageSummary = mtcCoverageSummary[:len(mtcCoverageSummary)-1]
	}
	// Prepend the new LossRunSummaryRecord to the start of the list
	mtcLossSummary.Summary = append([]application.LossRunSummaryRecord{newRecord}, mtcCoverageSummary...)
	newLossRunSummary := append(lossRunSummary, mtcLossSummary)
	return newLossRunSummary
}

func GetFlagsFromProblems(problems *problem.Problems) (*EvaluationFlags, error) {
	flags := EvaluationFlags{
		ClearanceConflict: false,
	}
	// Check for clearance conflict
	clearanceProblem := problems.Get(app_chkr.AppetiteCheckProblemTag, app_chkr_enums.AppetiteCheckRuleClearanceCheck.String())
	if clearanceProblem != nil {
		p, ok := clearanceProblem.(*app_chkr.AppetiteCheckProblem)
		if !ok {
			return nil, errors.New("unable to cast problem as appetite check problem")
		}
		if _, ok = p.Details[app_chkr.ClearanceSubmissions]; ok {
			flags.ClearanceConflict = true
		}
	}
	return &flags, nil
}

func shouldCreateOverviewPanel(
	ctx context.Context,
	featFlagClient feature_flag_lib.Client,
	devUser, underwriterUser *authz.User,
) (bool, error) {
	var isFeatureTurnedOn bool
	if devUser != nil {
		isTurnedOn, err := isAppetiteScoreFeatureTurnedOn(ctx, featFlagClient, devUser)
		if err != nil {
			return false, err
		}
		isFeatureTurnedOn = isFeatureTurnedOn || isTurnedOn
	}
	if isFeatureTurnedOn {
		// avoid one more API call if objective is achieved
		return isFeatureTurnedOn, nil
	}
	if underwriterUser != nil {
		isTurnedOn, err := isAppetiteScoreFeatureTurnedOn(ctx, featFlagClient, underwriterUser)
		if err != nil {
			return false, err
		}
		isFeatureTurnedOn = isFeatureTurnedOn || isTurnedOn
	}
	return isFeatureTurnedOn, nil
}

func isAppetiteScoreFeatureTurnedOn(
	ctx context.Context,
	featFlagClient feature_flag_lib.Client,
	user *authz.User,
) (bool, error) {
	isFeatureTurnedOn, err := featFlagClient.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(*user), feature_flag_lib.FeatureAppetiteScore, false)
	if err != nil {
		log.Error(
			ctx, "failed to get feature flag value", log.Err(err),
			log.Stringer("FeatureFlag", feature_flag_lib.FeatureAppetiteScore),
		)
		return false, errors.Wrapf(err, "failed to get feature flag value for %s",
			feature_flag_lib.FeatureAppetiteScore)
	}
	return isFeatureTurnedOn, nil
}

// shouldUpdateLegalDataPullLimit checks that if we should update a legal data pull limit given that the connection's
// handleId is not being used by other application. For proving ownership over the connection, we check that other
// applications with the same handleId have the same agency. In case this is true, we can't update the data pull
// legal limit because other application in non-terminal states depend on it.
func shouldUpdateLegalDataPullLimit(
	ctx context.Context,
	appWrapper application.DataWrapper,
	policyWrapper policy.Client,
	connHandleId uuid.UUID,
	agencyId uuid.UUID,
) (bool, error) {
	apps, err := appWrapper.GetApplications(
		ctx,
		application.AgencyIdIs(agencyId),
		application.TSPConnectionHandleIdIs(connHandleId),
		application.AppStateIn(
			state_enums.AppStatePanic.String(),
			state_enums.AppStateUnderUWReview.String(),
			state_enums.AppStateQuoteGenerated.String(),
			state_enums.AppStatePolicyCreated.String(),
			state_enums.AppStateApproved.String(),
		),
	)
	if err != nil {
		return false, err
	}
	var boundAppIds []uuid.UUID
	var nonBoundAppsIds []uuid.UUID
	for _, app := range apps {
		if app.State == state_enums.AppStatePolicyCreated {
			boundAppIds = append(boundAppIds, uuid.MustParse(app.ID))
			continue
		}
		// If the application is in panic but the UwSubmissionID is nil, we should skip it because it hasn't reached the
		// submitted stage
		if app.State == state_enums.AppStatePanic && app.UwSubmissionID == nil {
			continue
		}
		nonBoundAppsIds = append(nonBoundAppsIds, uuid.MustParse(app.ID))
	}
	if len(nonBoundAppsIds) != 0 {
		return false, nil
	}
	return checkBoundAppsExpiredPolicy(ctx, policyWrapper, boundAppIds)
}

// checkBoundAppsExpiredPolicy takes into bounded app IDs and checks whether the associated policies are expired or
// not. If all are expired, we return true otherwise false.
func checkBoundAppsExpiredPolicy(
	ctx context.Context,
	policyClient policy.Client,
	boundedAppIds []uuid.UUID,
) (bool, error) {
	if len(boundedAppIds) == 0 {
		return false, nil
	}
	policies, err := policyClient.GetPolicies(
		ctx, policy.GetPoliciesFilter{
			ActiveInInterval: &policy.Interval{
				Start: pointer_utils.ToPointer(time.Now()),
			},
			AppIDs: boundedAppIds,
		},
	)
	if err != nil {
		return false, err
	}
	if len(policies) != 0 {
		return false, nil
	}
	return true, nil
}

func getAppetiteScoreVersion(
	ctx context.Context,
	featFlagClient feature_flag_lib.Client,
	logicResolver appetite_score_rubric.LogicResolver,
	dotNumber int64,
	uwSafetyFetcher *common.UWSafetyFetcher,
	user *authz.User,
) (*appetite_factors.Version, error) {
	//featureName := feature_flag_lib.FeatureAppetiteScoreV8
	//isFeatureTurnedOn, err := featFlagClient.BoolVariation(feature_flag_lib.BuildLookupAttributes(*user), featureName,
	//	false)
	//if err != nil {
	//	log.Error(ctx, "failed to get feature flag value", log.Err(err),
	//		log.Stringer("FeatureFlag", featureName))
	//	return nil, errors.Wrapf(err, "failed to get feature flag value for %s", featureName.String())
	//}
	//basicAlertsConclusive := areBasicAlertsConclusive(ctx, dotNumber, uwSafetyFetcher)
	//// enable v8 only if feature flag is turned on and basic alerts are conclusive. Else fall back to v7
	//if isFeatureTurnedOn && basicAlertsConclusive {
	//	return pointer_utils.ToPointer(logicResolver.GetLatestVersion()), nil
	//}

	// Return the penultimate major version for now, update to latest version later
	return pointer_utils.ToPointer(logicResolver.GetLatestVersion()), nil
}

func areBasicAlertsConclusive(ctx context.Context, dotNumber int64, fetcher *common.UWSafetyFetcher) bool {
	basicScoreThresholds, err := fetcher.GetBasicScoresFromComputedMeasures(ctx, dotNumber)
	if err != nil {
		log.Error(ctx, "failed to get basic scores", log.Err(err), log.Int64("DOTNumber", dotNumber))
		return false
	}
	basicAlertCount := input.GetBasicAlertsCount(basicScoreThresholds)
	if basicAlertCount == nil {
		log.Error(ctx, "failed to get basic alert count")
		return false
	}
	return basicAlertCount.IsConclusive
}
