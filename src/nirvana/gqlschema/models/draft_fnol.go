package models

import (
	"context"
	"time"

	"github.com/google/uuid"

	fnols_db "nirvanatech.com/nirvana/claims/fnols/db"
	"nirvanatech.com/nirvana/claims/fnols/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
)

type DraftFnol struct {
	Id                  uuid.UUID `graphql:"id,key"`
	DotNumber           *int64    `graphql:"dotNumber"`
	PolicyNumber        *string
	IsTestpolicy        *bool `graphql:"isTestPolicy"`
	InsuredName         *string
	LossDatetime        *time.Time
	LossLocation        *string
	LossState           *string
	PoliceAgencyName    *string
	PoliceReportNumber  *string
	IncidentDescription *string
	NoticeType          enums.FnolNoticeType
	InjuriesInvolved    *bool
	CreatedBy           string
	CreatedAt           time.Time
	UpdatedAt           time.Time
	ArchivedAt          *time.Time
	FnolId              *uuid.UUID
	SubmittedFrom       enums.FnolSource
	Contacts            []DraftFnolContact
	Vehicles            []DraftFnolVehicle
}

func FnolDraftFromDb(ctx context.Context, fnol fnols_db.ClaimFnol) *DraftFnol {
	// We use the reporter's email as the createdBy field to maintain consistency with the
	// original FnolDraftFromDb function. This is because the createdBy field in the
	// DraftFnol was the email of the user who reported the FNOL, while the createdBy field in the
	// ClaimFnol is the user who created the FNOL in the system.
	var createdBy string
	reporter := fnol.GetReporter()
	if reporter != nil {
		createdBy = pointer_utils.StringValOr(reporter.Email, "")
	}

	// TODO: we should send the status to the frontend so the frontend can determine if the FNOL is
	// a draft or not.
	var backwardsCompatibleFnolId *uuid.UUID
	if fnol.Status != enums.FnolStatusDraft {
		backwardsCompatibleFnolId = &fnol.Id
	}

	return &DraftFnol{
		Id:                  fnol.Id,
		DotNumber:           pointer_utils.Int64(0), // TODO (IE-954): We should remove this field, as it is not used on the frontend and doesn't make sense we are storing it.
		PolicyNumber:        fnol.PolicyNumber,
		InsuredName:         fnol.InsuredName,
		LossDatetime:        fnol.LossDatetime,
		LossLocation:        fnol.LossLocation,
		LossState:           fnol.LossState,
		PoliceAgencyName:    fnol.PoliceAgencyName,
		PoliceReportNumber:  fnol.PoliceReportNumber,
		IncidentDescription: fnol.IncidentDescription,
		NoticeType:          pointer_utils.FromPointerOr(fnol.NoticeType, enums.FnolNoticeTypeClaim),
		InjuriesInvolved:    fnol.InjuriesInvolved,
		Contacts:            slice_utils.Map(fnol.Contacts, draftFnolContactFromDb),
		Vehicles:            slice_utils.Map(fnol.Vehicles, fnolVehicleFromDb),
		CreatedBy:           createdBy,
		CreatedAt:           fnol.CreatedAt,
		UpdatedAt:           fnol.UpdatedAt,
		ArchivedAt:          fnol.ArchivedAt,
		SubmittedFrom:       fnol.SubmittedFrom,
		FnolId:              backwardsCompatibleFnolId,
	}
}

func (d *DraftFnol) WithIsTestPolicy(isTestPolicy bool) *DraftFnol {
	d.IsTestpolicy = pointer_utils.Bool(isTestPolicy)
	return d
}
