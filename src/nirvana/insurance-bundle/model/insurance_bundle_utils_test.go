package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestExposureEntity_validate(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		entity      *ExposureEntity
		wantErr     bool
		expectedErr string
	}{
		{
			name: "valid entity",
			entity: &ExposureEntity{
				Id:   "vehicle-123",
				Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
			},
			wantErr: false,
		},
		{
			name:        "nil entity",
			entity:      nil,
			wantErr:     true,
			expectedErr: "nil ExposureEntity",
		},
		{
			name: "empty id",
			entity: &ExposureEntity{
				Id:   "",
				Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
			},
			wantErr:     true,
			expectedErr: "missing ExposureEntity id",
		},
		{
			name: "invalid type",
			entity: &ExposureEntity{
				Id:   "vehicle-123",
				Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_INVALID,
			},
			wantErr:     true,
			expectedErr: "invalid ExposureEntity type EXPOSURE_ENTITY_TYPE_INVALID",
		},
		{
			name: "valid terminal cargo entity",
			entity: &ExposureEntity{
				Id:   "terminal-456",
				Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
			},
			wantErr: false,
		},
		{
			name: "valid named shipper cargo entity",
			entity: &ExposureEntity{
				Id:   "shipper-789",
				Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_NAMED_SHIPPER_CARGO,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.entity.validate()
			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestExposureEntity_Key(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		entity      *ExposureEntity
		expectedKey string
	}{
		{
			name: "valid vehicle entity",
			entity: &ExposureEntity{
				Id:   "vehicle-123",
				Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
			},
			expectedKey: "vehicle-123:EXPOSURE_ENTITY_TYPE_VEHICLE",
		},
		{
			name: "valid terminal cargo entity",
			entity: &ExposureEntity{
				Id:   "terminal-456",
				Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
			},
			expectedKey: "terminal-456:EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO",
		},
		{
			name: "valid named shipper cargo entity",
			entity: &ExposureEntity{
				Id:   "shipper-789",
				Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_NAMED_SHIPPER_CARGO,
			},
			expectedKey: "shipper-789:EXPOSURE_ENTITY_TYPE_NAMED_SHIPPER_CARGO",
		},
		{
			name: "invalid entity type",
			entity: &ExposureEntity{
				Id:   "entity-123",
				Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_INVALID,
			},
			expectedKey: "entity-123:EXPOSURE_ENTITY_TYPE_INVALID",
		},
		{
			name:        "nil entity",
			entity:      nil,
			expectedKey: "",
		},
		{
			name: "empty id",
			entity: &ExposureEntity{
				Id:   "",
				Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
			},
			expectedKey: ":EXPOSURE_ENTITY_TYPE_VEHICLE",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			key := tt.entity.Key()
			assert.Equal(t, tt.expectedKey, key)
		})
	}
}

func TestValidateExposureEntities(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		entities    []*ExposureEntity
		wantErr     bool
		expectedErr string
	}{
		{
			name:     "empty entities slice",
			entities: []*ExposureEntity{},
			wantErr:  false,
		},
		{
			name:     "nil entities slice",
			entities: nil,
			wantErr:  false,
		},
		{
			name: "valid single entity",
			entities: []*ExposureEntity{
				{
					Id:   "vehicle-123",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
				},
			},
			wantErr: false,
		},
		{
			name: "valid multiple entities",
			entities: []*ExposureEntity{
				{
					Id:   "vehicle-123",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
				},
				{
					Id:   "terminal-456",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
				},
			},
			wantErr: false,
		},
		{
			name: "invalid entity at index 0",
			entities: []*ExposureEntity{
				{
					Id:   "",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
				},
			},
			wantErr:     true,
			expectedErr: "invalid entity at index 0",
		},
		{
			name: "invalid entity at index 1",
			entities: []*ExposureEntity{
				{
					Id:   "vehicle-123",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
				},
				{
					Id:   "terminal-456",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_INVALID,
				},
			},
			wantErr:     true,
			expectedErr: "invalid entity at index 1",
		},
		{
			name: "nil entity at index 1",
			entities: []*ExposureEntity{
				{
					Id:   "vehicle-123",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
				},
				nil,
			},
			wantErr:     true,
			expectedErr: "invalid entity at index 1",
		},
		{
			name: "duplicate entities",
			entities: []*ExposureEntity{
				{
					Id:   "vehicle-123",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
				},
				{
					Id:   "vehicle-123",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
				},
			},
			wantErr:     true,
			expectedErr: "duplicate entity with key vehicle-123:EXPOSURE_ENTITY_TYPE_VEHICLE at index 1",
		},
		{
			name: "same id but different type is valid",
			entities: []*ExposureEntity{
				{
					Id:   "entity-123",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
				},
				{
					Id:   "entity-123",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
				},
			},
			wantErr: false,
		},
		{
			name: "valid entities with different types",
			entities: []*ExposureEntity{
				{
					Id:   "vehicle-123",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
				},
				{
					Id:   "terminal-456",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
				},
				{
					Id:   "shipper-789",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_NAMED_SHIPPER_CARGO,
				},
			},
			wantErr: false,
		},
		{
			name: "duplicate entity at later index",
			entities: []*ExposureEntity{
				{
					Id:   "vehicle-123",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
				},
				{
					Id:   "terminal-456",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
				},
				{
					Id:   "vehicle-123",
					Type: ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE,
				},
			},
			wantErr:     true,
			expectedErr: "duplicate entity with key vehicle-123:EXPOSURE_ENTITY_TYPE_VEHICLE at index 2",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateExposureEntities(tt.entities)
			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
