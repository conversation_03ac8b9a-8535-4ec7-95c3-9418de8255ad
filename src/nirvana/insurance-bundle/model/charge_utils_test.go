package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	appEnums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func TestCalculateBaseChargeDistributionAtAppSubCovLevel(t *testing.T) {
	t.Parallel()

	effectiveDate := timestamppb.New(time_utils.NewDate(2024, 4, 15).ToTime())

	tests := []struct {
		name                     string
		charges                  []*ptypes.Charge
		exposure                 *ptypes.Exposure
		expectedTotal            *float64
		expectedChargesBySubCov  map[appEnums.Coverage]*float64
		expectedChargesByVehicle map[string]map[appEnums.Coverage]*float64
		expectError              bool
		errorMsg                 string
	}{
		{
			name:                     "empty charges slice",
			charges:                  []*ptypes.Charge{},
			exposure:                 ptypes.NewNilExposure(),
			expectedTotal:            nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "single charge without distributions - should be skipped",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.50", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					Build(),
			},
			exposure:                 ptypes.NewNilExposure(),
			expectedTotal:            nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "single charge with vehicle distribution",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("200.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "0.6"),
							ptypes.NewChargeDistributionItem("vehicle2", "0.4"),
						},
					).
					Build(),
			},
			exposure:      ptypes.NewNilExposure(),
			expectedTotal: floatPtr(200.00),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoveragePropertyDamage: floatPtr(200.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoveragePropertyDamage: floatPtr(120.00),
				},
				"vehicle2": {
					appEnums.CoveragePropertyDamage: floatPtr(80.00),
				},
			},
			expectError: false,
		},
		{
			name: "multiple charges without distributions - should be skipped",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("150.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("300.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("250.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Cargo).
					Build(),
			},
			exposure:                 ptypes.NewNilExposure(),
			expectedTotal:            nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "multiple charges with distributions - different coverages",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("150.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
						},
					).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("300.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle2", "1.0"),
						},
					).
					Build(),
			},
			exposure:      ptypes.NewNilExposure(),
			expectedTotal: floatPtr(450.00),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageBodilyInjury:     floatPtr(150.00),
				appEnums.CoverageGeneralLiability: floatPtr(300.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoverageBodilyInjury: floatPtr(150.00),
				},
				"vehicle2": {
					appEnums.CoverageGeneralLiability: floatPtr(300.00),
				},
			},
			expectError: false,
		},
		{
			name: "charge with multiple sub-coverages mapping to primary coverage",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("400.00", effectiveDate).
					WithChargeableSubCoverageGroup(
						ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
						ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
					).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
						},
					).
					Build(),
			},
			exposure:      ptypes.NewNilExposure(),
			expectedTotal: floatPtr(400.00),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageAutoLiability: floatPtr(400.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoverageAutoLiability: floatPtr(400.00),
				},
			},
			expectError: false,
		},
		{
			name: "charge without sub-coverage group - should be skipped",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					WithChargedItem(ptypes.NewChargeChargedPolicy("POLICY123")).
					Build(),
			},
			exposure:                 ptypes.NewNilExposure(),
			expectedTotal:            nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "complex scenario with multiple vehicles and coverages",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("600.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "0.5"),
							ptypes.NewChargeDistributionItem("vehicle2", "0.3"),
							ptypes.NewChargeDistributionItem("vehicle3", "0.2"),
						},
					).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("400.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "0.7"),
							ptypes.NewChargeDistributionItem("vehicle2", "0.3"),
						},
					).
					Build(),
			},
			exposure:      ptypes.NewNilExposure(),
			expectedTotal: floatPtr(1000.00),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageBodilyInjury:     floatPtr(600.00),
				appEnums.CoverageGeneralLiability: floatPtr(400.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoverageBodilyInjury:     floatPtr(300.00),
					appEnums.CoverageGeneralLiability: floatPtr(280.00),
				},
				"vehicle2": {
					appEnums.CoverageBodilyInjury:     floatPtr(180.00),
					appEnums.CoverageGeneralLiability: floatPtr(120.00),
				},
				"vehicle3": {
					appEnums.CoverageBodilyInjury: floatPtr(120.00),
				},
			},
			expectError: false,
		},
		{
			name: "charge with invalid fraction - should return error",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "invalid_fraction"),
						},
					).
					Build(),
			},
			exposure:    ptypes.NewNilExposure(),
			expectError: true,
			errorMsg:    "invalid fraction",
		},
		{
			name: "charges without distributions - should be skipped",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("50.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
					Build(),
			},
			exposure:                 ptypes.NewNilExposure(),
			expectedTotal:            nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "accumulating vehicle charges for same coverage",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
						},
					).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("50.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
						},
					).
					Build(),
			},
			exposure:      ptypes.NewNilExposure(),
			expectedTotal: floatPtr(150.00),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageBodilyInjury:   floatPtr(100.00),
				appEnums.CoveragePropertyDamage: floatPtr(50.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoverageBodilyInjury:   floatPtr(100.00),
					appEnums.CoveragePropertyDamage: floatPtr(50.00),
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			totalCharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
				tt.charges,
				tt.exposure,
			)

			if tt.expectError {
				require.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
				return
			}

			require.NoError(t, err)
			assert.Equal(t, tt.expectedTotal, totalCharges)
			assert.Equal(t, tt.expectedChargesBySubCov, chargesBySubCov)
			assert.Equal(t, tt.expectedChargesByVehicle, chargesByVehicle)
		})
	}
}

func TestCalculateBaseChargeDistributionAtAppSubCovLevel_EdgeCases(t *testing.T) {
	t.Parallel()

	effectiveDate := timestamppb.New(time_utils.NewDate(2024, 4, 15).ToTime())

	t.Run("nil charges slice", func(t *testing.T) {
		t.Parallel()

		totalCharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
			nil,
			ptypes.NewNilExposure(),
		)

		require.NoError(t, err)
		assert.Nil(t, totalCharges)
		assert.Equal(t, map[appEnums.Coverage]*float64{}, chargesBySubCov)
		assert.Equal(t, map[string]map[appEnums.Coverage]*float64{}, chargesByVehicle)
	})

	t.Run("charge with zero amount and distribution", func(t *testing.T) {
		t.Parallel()

		charges := []*ptypes.Charge{
			ptypes.NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("0.00", effectiveDate).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
				WithDistribution(
					ptypes.Charge_DistributionType_Vehicle,
					[]*ptypes.Charge_DistributionItem{
						ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
					},
				).
				Build(),
		}

		totalCharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
			charges,
			ptypes.NewNilExposure(),
		)

		require.NoError(t, err)
		assert.Equal(t, floatPtr(0.00), totalCharges)
		assert.Equal(t, map[appEnums.Coverage]*float64{
			appEnums.CoverageBodilyInjury: floatPtr(0.00),
		}, chargesBySubCov)
		assert.Equal(t, map[string]map[appEnums.Coverage]*float64{
			"vehicle1": {
				appEnums.CoverageBodilyInjury: floatPtr(0.00),
			},
		}, chargesByVehicle)
	})

	t.Run("charge with fractional amounts", func(t *testing.T) {
		t.Parallel()

		charges := []*ptypes.Charge{
			ptypes.NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("100.00", effectiveDate).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
				WithDistribution(
					ptypes.Charge_DistributionType_Vehicle,
					[]*ptypes.Charge_DistributionItem{
						ptypes.NewChargeDistributionItem("vehicle1", "0.33"),
						ptypes.NewChargeDistributionItem("vehicle2", "0.67"),
					},
				).
				Build(),
		}

		totalCharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
			charges,
			ptypes.NewNilExposure(),
		)

		require.NoError(t, err)
		assert.Equal(t, floatPtr(100.00), totalCharges)
		assert.Equal(t, map[appEnums.Coverage]*float64{
			appEnums.CoverageBodilyInjury: floatPtr(100.00),
		}, chargesBySubCov)

		// Check that vehicle charges are properly calculated
		require.Contains(t, chargesByVehicle, "vehicle1")
		require.Contains(t, chargesByVehicle, "vehicle2")
		assert.NotNil(t, chargesByVehicle["vehicle1"][appEnums.CoverageBodilyInjury])
		assert.NotNil(t, chargesByVehicle["vehicle2"][appEnums.CoverageBodilyInjury])

		// Verify the approximate distribution (33% and 67%)
		vehicle1Amount := *chargesByVehicle["vehicle1"][appEnums.CoverageBodilyInjury]
		vehicle2Amount := *chargesByVehicle["vehicle2"][appEnums.CoverageBodilyInjury]
		assert.InDelta(t, 33.00, vehicle1Amount, 0.01) // 100.00 * 0.33 = 33.00
		assert.InDelta(t, 67.00, vehicle2Amount, 0.01) // 100.00 * 0.67 = 67.00
	})

	t.Run("charge with miles exposure and distribution", func(t *testing.T) {
		t.Parallel()

		charges := []*ptypes.Charge{
			ptypes.NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithRateBasedBillingDetails(
					"0.05",
					&proto.Interval{
						Start: effectiveDate,
						End:   effectiveDate,
					},
					ptypes.RateBasedBillingDetails_ExposureType_Miles,
				).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
				WithDistribution(
					ptypes.Charge_DistributionType_Vehicle,
					[]*ptypes.Charge_DistributionItem{
						ptypes.NewChargeDistributionItem("vehicle1", "0.6"),
						ptypes.NewChargeDistributionItem("vehicle2", "0.4"),
					},
				).
				Build(),
		}

		totalCharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
			charges,
			ptypes.NewMilesExposure(1000),
		)

		require.NoError(t, err)
		assert.Equal(t, floatPtr(50.00), totalCharges) // 0.05 * 1000 = 50.00
		assert.Equal(t, map[appEnums.Coverage]*float64{
			appEnums.CoverageBodilyInjury: floatPtr(50.00),
		}, chargesBySubCov)
		assert.Equal(t, map[string]map[appEnums.Coverage]*float64{
			"vehicle1": {
				appEnums.CoverageBodilyInjury: floatPtr(30.00), // 50.00 * 0.6
			},
			"vehicle2": {
				appEnums.CoverageBodilyInjury: floatPtr(20.00), // 50.00 * 0.4
			},
		}, chargesByVehicle)
	})

	t.Run("charge without distribution - should be skipped", func(t *testing.T) {
		t.Parallel()

		charges := []*ptypes.Charge{
			ptypes.NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("100.00", effectiveDate).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
				Build(),
		}

		totalCharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
			charges,
			ptypes.NewNilExposure(),
		)

		require.NoError(t, err)
		assert.Nil(t, totalCharges)
		assert.Equal(t, map[appEnums.Coverage]*float64{}, chargesBySubCov)
		assert.Equal(t, map[string]map[appEnums.Coverage]*float64{}, chargesByVehicle)
	})
}

// floatPtr is a helper function to create float64 pointers for test assertions
func floatPtr(f float64) *float64 {
	return &f
}
