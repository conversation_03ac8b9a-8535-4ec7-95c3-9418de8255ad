package model

import (
	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/proto"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func (i InsuranceBundleState) HumanReadableString() string {
	switch i {
	case InsuranceBundleState_InsuranceBundleState_Active:
		return "Active"
	case InsuranceBundleState_InsuranceBundleState_Inactive:
		return "Inactive"
	case InsuranceBundleState_InsuranceBundleState_Expired:
		return "Expired"
	case InsuranceBundleState_InsuranceBundleState_PendingCancellation:
		return "Pending Cancellation"
	case InsuranceBundleState_InsuranceBundleState_Cancelled:
		return "Cancelled"
	case InsuranceBundleState_InsuranceBundleState_Stale:
		return "Stale"
	case InsuranceBundleState_InsuranceBundleState_Invalid:
		return "Invalid"
	default:
		return "Invalid"
	}
}

func InsuranceBundleStateFromHumanReadableString(s string) InsuranceBundleState {
	switch s {
	case "Active":
		return InsuranceBundleState_InsuranceBundleState_Active
	case "Inactive":
		return InsuranceBundleState_InsuranceBundleState_Inactive
	case "Expired":
		return InsuranceBundleState_InsuranceBundleState_Expired
	case "Pending Cancellation":
		return InsuranceBundleState_InsuranceBundleState_PendingCancellation
	case "Cancelled":
		return InsuranceBundleState_InsuranceBundleState_Cancelled
	case "Stale":
		return InsuranceBundleState_InsuranceBundleState_Stale
	default:
		return InsuranceBundleState_InsuranceBundleState_Invalid
	}
}

func (i *InsuranceBundleSegment) ExtractAllPolicyNumbers() []string {
	policyNumbers := make([]string, 0)

	for policyNumber := range i.GetPolicies() {
		policyNumbers = append(policyNumbers, policyNumber)
	}

	return policyNumbers
}

func (i *InsuranceBundleSegment) Clone() *InsuranceBundleSegment {
	return proto.Clone(i).(*InsuranceBundleSegment)
}

func (i *InsuranceBundle) GetCondensed() *CondensedInsuranceBundle {
	return &CondensedInsuranceBundle{
		ExternalID:               i.ExternalId,
		InternalID:               i.InternalId,
		PrimaryInsured:           i.Segments[0].PrimaryInsured,
		DefaultEffectiveDuration: i.DefaultEffectiveDuration,
		State:                    i.State,
		CreatedAt:                i.CreatedAt,
		UpdatedAt:                i.UpdatedAt,
	}
}

func (i *InsuranceBundle) GetLastSegment() *InsuranceBundleSegment {
	if len(i.Segments) == 0 {
		return nil
	}
	return i.Segments[len(i.Segments)-1]
}

// GetChargesWithDistribution returns the charges data for the insurance bundle, i.e, the total charge of an insurance bundle,
// charges by sub-coverage and charges by policy chargeable item.
// The useChargeAdjustments parameter determines whether to include charge adjustments in the total.
// When true, any charge adjustments from legacy system pricing differences will be added
// to the original charges. When false, only the original charges are used.
func (i *InsuranceBundle) GetChargesWithDistribution(
	rateBasisValue *ptypes.RateBasisValue,
	useChargeAdjustments bool,
) (*ChargeDistribution, error) {
	totalCharge := decimal.Zero
	chargesBySubCov := make(map[ptypes.SubCoverageType]decimal.Decimal)
	surcharges, feeCharges := decimal.Zero, decimal.Zero
	for _, ibSegment := range i.Segments {
		for _, segmentPolicy := range ibSegment.Policies {
			charges := segmentPolicy.Charges.Charges

			// Add adjustments if enabled and they exist
			if useChargeAdjustments && segmentPolicy.ChargeAdjustments != nil {
				for _, ch := range segmentPolicy.ChargeAdjustments.Adjustments {
					charges = append(charges, ch.Charges.Charges...)
				}
			}

			subCovChargesForSegment, chargesBySubCovForSegment, err := CalculateBaseChargeDistributionAtSubCovLevel(
				charges, rateBasisValue)
			if err != nil {
				return nil, err
			}

			totalCharge = totalCharge.Add(*subCovChargesForSegment)
			for cov := range chargesBySubCovForSegment {
				chargesBySubCov[cov] = chargesBySubCov[cov].Add(chargesBySubCovForSegment[cov])
			}

			policyChargesForSegment, err := CalculateFeeChargeAndSurchargeDistribution(
				charges, rateBasisValue)
			if err != nil {
				return nil, err
			}

			totalCharge = totalCharge.Add(policyChargesForSegment.TotalCharge)
			surcharges = surcharges.Add(policyChargesForSegment.Surcharge)
			feeCharges = feeCharges.Add(policyChargesForSegment.FeeCharge)
		}
	}
	return &ChargeDistribution{
		TotalCharge:          pointer_utils.ToPointer(totalCharge),
		ChargesBySubCoverage: chargesBySubCov,
		Surcharges:           surcharges,
		FeeCharges:           feeCharges,
	}, nil
}

func (e *ExposureEntity) validate() error {
	if e == nil {
		return errors.New("nil ExposureEntity")
	}
	if e.GetId() == "" {
		return errors.New("missing ExposureEntity id")
	}
	if e.GetType() == ExposureEntityType_EXPOSURE_ENTITY_TYPE_INVALID {
		return errors.Newf("invalid ExposureEntity type %s", e.GetType())
	}
	return nil
}

func (e *ExposureEntity) Key() string {
	if e == nil {
		return ""
	}
	return e.GetId() + colonDelimiter + ExposureEntityType_name[int32(e.GetType())]
}

func validateExposureEntities(entities []*ExposureEntity) error {
	// It's acceptable to have no entities
	if len(entities) == 0 {
		return nil
	}

	seenEntities := map[string]struct{}{}
	for i, entity := range entities {
		if err := entity.validate(); err != nil {
			return errors.Wrapf(err, "invalid entity at index %d", i)
		}
		entityKey := entity.Key()
		if _, exists := seenEntities[entityKey]; exists {
			return errors.Newf("duplicate entity with key %s at index %d", entityKey, i)
		}
		seenEntities[entityKey] = struct{}{}
	}

	return nil
}
