package model

import (
	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/common-go/math_utils"
	appEnums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	coverageUtils "nirvanatech.com/nirvana/insurance-core/coverage"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func computeChargeForRateBasisValue(charge *ptypes.Charge, rateBasisValue *ptypes.RateBasisValue) (*decimal.Decimal, error) {
	amount, err := charge.Calculate(rateBasisValue)
	if err != nil {
		return nil, err
	}
	return pointer_utils.ToPointer(amount), nil
}

func decimalToFloatPtr(value decimal.Decimal, noOfDecimals int) (*float64, error) {
	floatVal, _ := value.Float64()
	return math_utils.RoundFloatPtr(&floatVal, noOfDecimals), nil
}

// CalculateBaseChargeDistributionAtAppSubCovLevel calculates the charge distribution at the app sub-coverage level.
// It returns the total charges at the app sub cov level, the "Base" charges for each app sub coverage,
// and the charges for each vehicle across all app sub coverages.
func CalculateBaseChargeDistributionAtAppSubCovLevel(
	charges []*ptypes.Charge,
	rateBasisValue *ptypes.RateBasisValue,
) (*float64, *float64, map[appEnums.Coverage]*float64, map[string]map[appEnums.Coverage]*float64, error) {
	calculator := &chargeCalculator{
		rateBasisValue:   rateBasisValue,
		chargesBySubCov:  make(map[appEnums.Coverage]*float64),
		chargesByVehicle: make(map[string]map[appEnums.Coverage]*float64),
	}

	return calculator.calculate(charges)
}

type chargeCalculator struct {
	rateBasisValue   *ptypes.RateBasisValue
	baseCharges      *float64
	surcharges       *float64
	chargesBySubCov  map[appEnums.Coverage]*float64
	chargesByVehicle map[string]map[appEnums.Coverage]*float64
}

func (c *chargeCalculator) calculate(charges []*ptypes.Charge) (*float64, *float64, map[appEnums.Coverage]*float64, map[string]map[appEnums.Coverage]*float64, error) {
	for _, charge := range charges {
		if err := c.processCharge(charge); err != nil {
			return nil, nil, nil, nil, err
		}
	}

	return c.baseCharges, c.surcharges, c.chargesBySubCov, c.chargesByVehicle, nil
}

func (c *chargeCalculator) processCharge(charge *ptypes.Charge) error {
	// Skip charges without distributions or chargeable sub-coverage groups
	if !isValidCharge(charge) {
		return nil
	}

	// Calculate charge amount
	chargeAmount, err := computeChargeForRateBasisValue(charge, c.rateBasisValue)
	if err != nil {
		return err
	}

	// Update totals
	if err := c.updateTotals(*chargeAmount, charge); err != nil {
		return err
	}

	// Process vehicle distributions
	if charge.IsBaseCharge() {
		if err := c.processVehicleDistributions(charge, *chargeAmount); err != nil {
			return err
		}
	}

	return nil
}

func isValidCharge(charge *ptypes.Charge) bool {
	if charge.IsBaseCharge() {
		if len(charge.GetDistributions()) == 0 {
			return false
		}

		chargeableSubCovGroup := charge.GetChargedSubCoverageGroup()
		return chargeableSubCovGroup != nil && chargeableSubCovGroup.GetGroup() != nil
	}
	return true
}

func getCoverageFromCharge(charge *ptypes.Charge) (appEnums.Coverage, error) {
	subCovGroup := charge.GetChargedSubCoverageGroup().GetGroup()
	pricingSubCovs := subCovGroup.GetSubCoverages()

	appCovs, err := mapPricingToAppCoverages(pricingSubCovs)
	if err != nil {
		return 0, err
	}

	if len(appCovs) > 1 {
		return coverageUtils.GetPrimaryCoverageFromSubCoverages(appCovs)
	}

	return appCovs[0], nil
}

func mapPricingToAppCoverages(pricingSubCovs []ptypes.SubCoverageType) ([]appEnums.Coverage, error) {
	var appCovs []appEnums.Coverage

	for _, subCov := range pricingSubCovs {
		appCov, err := coverageUtils.GetAppCoverageFromPricingSubCoverage(subCov)
		if err != nil {
			return nil, errors.Wrap(err, "failed to map pricing sub-coverage to app coverage")
		}
		appCovs = append(appCovs, *appCov)
	}

	return appCovs, nil
}

func (c *chargeCalculator) updateTotals(chargeAmount decimal.Decimal, charge *ptypes.Charge) error {
	chargeFloat, err := decimalToFloatPtr(chargeAmount, 3)
	if err != nil {
		return errors.Wrap(err, "failed to convert charge to float")
	}

	if charge.IsBaseCharge() {
		// Determine the coverage type
		coverage, err := getCoverageFromCharge(charge)
		if err != nil {
			return errors.Wrap(err, "failed to determine coverage")
		}
		// Update sub-coverage charges
		c.updateSubCoverageCharge(coverage, chargeFloat)
		// Update total charges
		c.baseCharges = math_utils.RoundedTotal(c.baseCharges, chargeFloat)
	}

	if charge.IsSurcharge() {
		c.updateSurchargeAndFeeCharge(charge, chargeFloat)
	}

	return nil
}

func (c *chargeCalculator) updateSurchargeAndFeeCharge(ch *ptypes.Charge, chargeFloat *float64) {
	// Get surcharges type
	surcharge := ch.GetSurcharge()
	if surcharge != nil {
		c.surcharges = math_utils.RoundedTotal(c.surcharges, chargeFloat)
	}
}

func (c *chargeCalculator) updateSubCoverageCharge(coverage appEnums.Coverage, chargeFloat *float64) {
	existing, found := c.chargesBySubCov[coverage]
	if found {
		c.chargesBySubCov[coverage] = math_utils.RoundedTotal(existing, chargeFloat)
	} else {
		c.chargesBySubCov[coverage] = chargeFloat
	}
}

func (c *chargeCalculator) processVehicleDistributions(charge *ptypes.Charge, chargeAmount decimal.Decimal) error {
	// Determine the coverage type
	coverage, err := getCoverageFromCharge(charge)
	if err != nil {
		return errors.Wrap(err, "failed to determine coverage")
	}
	for _, distribution := range charge.GetDistributions() {
		if distribution.GetType() != ptypes.Charge_DistributionType_Vehicle {
			continue
		}

		if err := c.processVehicleDistribution(distribution, coverage, chargeAmount); err != nil {
			return err
		}
	}

	return nil
}

func (c *chargeCalculator) processVehicleDistribution(distribution *ptypes.Charge_Distribution, coverage appEnums.Coverage, chargeAmount decimal.Decimal) error {
	for _, item := range distribution.GetItems() {
		if err := c.processVehicleItem(item, coverage, chargeAmount); err != nil {
			return err
		}
	}

	return nil
}

func (c *chargeCalculator) processVehicleItem(item *ptypes.Charge_DistributionItem, coverage appEnums.Coverage, chargeAmount decimal.Decimal) error {
	vehicleID := item.GetId()

	// Parse and calculate vehicle charge
	vehicleCharge, err := calculateVehicleCharge(item.GetFraction(), chargeAmount)
	if err != nil {
		return errors.Wrapf(err, "failed to calculate charge for vehicle %s", vehicleID)
	}

	// Initialize vehicle map if needed
	if c.chargesByVehicle[vehicleID] == nil {
		c.chargesByVehicle[vehicleID] = make(map[appEnums.Coverage]*float64)
	}

	// Update vehicle charge
	c.updateVehicleCharge(vehicleID, coverage, vehicleCharge)

	return nil
}

func calculateVehicleCharge(fractionStr string, baseCharge decimal.Decimal) (*float64, error) {
	fraction, err := decimal.NewFromString(fractionStr)
	if err != nil {
		return nil, errors.Wrapf(err, "invalid fraction: %s", fractionStr)
	}

	vehicleCharge := baseCharge.Mul(fraction)
	return decimalToFloatPtr(vehicleCharge, 3)
}

func (c *chargeCalculator) updateVehicleCharge(vehicleID string, coverage appEnums.Coverage, chargeFloat *float64) {
	existing, found := c.chargesByVehicle[vehicleID][coverage]
	if found {
		c.chargesByVehicle[vehicleID][coverage] = math_utils.RoundedTotal(existing, chargeFloat)
	} else {
		c.chargesByVehicle[vehicleID][coverage] = chargeFloat
	}
}

// CalculateBaseChargeDistributionAtSubCovLevel calculates the charge distribution at the sub-coverage level. It returns the
// total charges at the sub cov level and the "Base" charges for each sub coverage
func CalculateBaseChargeDistributionAtSubCovLevel(
	charges []*ptypes.Charge,
	rateBasisValue *ptypes.RateBasisValue,
) (*decimal.Decimal, map[ptypes.SubCoverageType]decimal.Decimal, error) {
	totalCharges := decimal.Zero
	chargesBySubCov := make(map[ptypes.SubCoverageType]decimal.Decimal)
	for _, c := range charges {
		baseChargeClass := c.GetBaseCharge()
		if baseChargeClass == nil {
			continue
		}
		chargeableSubCovGroup := c.GetChargedSubCoverageGroup()
		if chargeableSubCovGroup == nil {
			continue
		}
		subCovGroup := chargeableSubCovGroup.GetGroup()
		subCovs := subCovGroup.GetSubCoverages()
		if len(subCovs) != 1 {
			return nil, nil, errors.Newf("expected exactly one sub-coverage in the group, got %+v", subCovs)
		}
		charge, err := computeChargeForRateBasisValue(c, rateBasisValue)
		if err != nil {
			return nil, nil, err
		}
		totalCharges = totalCharges.Add(*charge)
		subCov := subCovs[0]
		subCovCharge, found := chargesBySubCov[subCov]
		if found {
			subCovCharge = subCovCharge.Add(*charge)
		} else {
			subCovCharge = *charge
		}
		chargesBySubCov[subCov] = subCovCharge
	}
	return pointer_utils.ToPointer(totalCharges), chargesBySubCov, nil
}

// CalculateFeeChargeAndSurchargeDistribution calculates the fee charge and surcharges distribution for the charges.
func CalculateFeeChargeAndSurchargeDistribution(
	charges []*ptypes.Charge,
	rateBasisValue *ptypes.RateBasisValue,
) (*PolicyCharge, error) {
	totalCharges := decimal.Zero
	var surcharge, feeCharge decimal.Decimal
	for _, c := range charges {
		charge, err := computeChargeForRateBasisValue(c, rateBasisValue)
		if err != nil {
			return nil, err
		}
		if c.IsFullyEarnedCharge() {
			feeCharge = feeCharge.Add(*charge)
			totalCharges = totalCharges.Add(*charge)
		} else if c.IsSurcharge() {
			surcharge = surcharge.Add(*charge)
			totalCharges = totalCharges.Add(*charge)
		}
	}
	return &PolicyCharge{
		TotalCharge: totalCharges,
		Surcharge:   surcharge,
		FeeCharge:   feeCharge,
	}, nil
}

type PolicyCharge struct {
	TotalCharge decimal.Decimal
	Surcharge   decimal.Decimal
	FeeCharge   decimal.Decimal
}

type ChargeDistribution struct {
	TotalCharge *decimal.Decimal
	// ChargesBySubCoverage is a map that holds the charges for each sub-coverage.
	// It does not include the surcharges/fee-charges per sub-coverage.
	// They are included in the Surcharges/FeeCharges fields.
	ChargesBySubCoverage map[ptypes.SubCoverageType]decimal.Decimal
	Surcharges           decimal.Decimal
	// FeeCharges is the total flat fee charges.
	FeeCharges decimal.Decimal
}

// FeeBreakdown is a struct that holds the fees for an endorsement broken down by coverage.
// It has three fields: PreEndorsement, PostEndorsement, and Endorsement.
// PreEndorsement is the fees before the endorsement, PostEndorsement is the fees after the endorsement,
// and Endorsement is the fees for the endorsement.
type FeeBreakdown struct {
	PreEndorsement  map[appEnums.Coverage]decimal.Decimal
	PostEndorsement map[appEnums.Coverage]decimal.Decimal
	Endorsement     map[appEnums.Coverage]decimal.Decimal
}
