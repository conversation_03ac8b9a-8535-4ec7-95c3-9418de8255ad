import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'cva';
import { CircularProgress } from '@material-ui/core';

import { cn } from '@nirvana/core/utils';

import Show from '../show';

export const buttonVariants = cva(
  'inline-flex items-center justify-center space-x-1 rounded-lg disabled:pointer-events-none aria-invalid:border aria-invalid:border-tw-red-600',
  {
    variants: {
      variant: {
        primary:
          'bg-tw-blue-700 text-white shadow-tw-sm hover:bg-tw-blue-1000 disabled:bg-tw-blue-500',
        secondary:
          'bg-white text-tw-gray-1000 shadow-tw-sm hover:bg-tw-gray-100 disabled:bg-tw-gray-100 disabled:text-tw-gray-300',
        danger:
          'bg-tw-red-600 text-white shadow-tw-sm hover:bg-tw-red-700 disabled:bg-tw-red-200',
        text: 'bg-transparent text-tw-gray-1000 hover:bg-tw-gray-200 disabled:text-tw-gray-400',
        link: 'bg-transparent text-tw-blue-700 hover:text-tw-blue-1000 disabled:text-tw-gray-400',
      },
      size: {
        default: 'h-9 px-4 py-2',
        icon: 'h-9 w-9 justify-center',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'default',
    },
  },
);

const linkClasses = 'p-0 h-auto';

const iconVariants = cva('flex-shrink-0', {
  variants: {
    type: {
      withChildren: 'h-4 w-4',
      withoutChildren: 'block h-4 w-4',
    },
  },
  defaultVariants: {
    type: 'withChildren',
  },
});

type Icon = React.ReactElement<{ className?: string }>;
export type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> &
  VariantProps<typeof buttonVariants> & {
    loading?: boolean;
    startIcon?: Icon;
    endIcon?: Icon;
    asChild?: boolean;
  };

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      size,
      variant,
      loading,
      className,
      startIcon,
      endIcon,
      children,
      asChild,
      ...props
    },
    ref,
  ) => {
    function getIconElement(icon?: Icon) {
      return icon
        ? React.cloneElement(icon, {
            className: cn(
              iconVariants({
                type: children ? 'withChildren' : 'withoutChildren',
                className: icon.props.className,
              }),
            ),
          })
        : null;
    }

    if (asChild) {
      return (
        <Slot
          ref={ref}
          className={cn(
            buttonVariants({ size, variant, className }),
            variant === 'link' ? linkClasses : '',
          )}
          {...props}
        >
          {children}
        </Slot>
      );
    }

    return (
      <button
        ref={ref}
        className={cn(
          buttonVariants({ size, variant, className }),
          variant === 'link' ? linkClasses : '',
        )}
        disabled={loading || props.disabled}
        {...props}
      >
        <Show when={loading} fallback={getIconElement(startIcon)}>
          <CircularProgress
            size={16}
            thickness={5}
            className={cn(
              iconVariants({
                type: children ? 'withChildren' : 'withoutChildren',
              }),
              'text-current',
            )}
          />
        </Show>
        {children ? <span>{children}</span> : null}
        {getIconElement(endIcon)}
      </button>
    );
  },
);

Button.displayName = 'Button';
export default Button;
