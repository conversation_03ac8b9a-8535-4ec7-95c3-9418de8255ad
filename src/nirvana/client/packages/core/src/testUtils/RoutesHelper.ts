import { CyHttpMessages } from '@nirvana/core/testUtils';
import {
  overrideGraphqlResponse,
  spyGraphqlRequest,
} from './graphql-test-utils';

type data = object | ((req: CyHttpMessages.IncomingHttpRequest) => object);
type method = 'GET' | 'POST' | 'PUT' | 'DELETE';

export class RoutesHelper {
  getBaseUrl() {
    return import.meta.env.VITE_WEB_URL;
  }

  getApiRestUrl() {
    return import.meta.env.VITE_API_URL;
  }

  getGraphqlUrl() {
    const graphqlEndpoint = new URL(import.meta.env.VITE_GRAPHQL_ENDPOINT);
    return {
      hostname: graphqlEndpoint.hostname,
      pathname: graphqlEndpoint.pathname,
    };
  }

  getGqlgenlUrl() {
    const gqlgenEndpoint = new URL(import.meta.env.VITE_GQLGEN_ENDPOINT);
    return {
      hostname: gqlgenEndpoint.hostname,
      pathname: gqlgenEndpoint.pathname,
    };
  }

  getClaimsLLMUrl() {
    return import.meta.env.VITE_CLAIMS_AGENT_API_URL;
  }

  overrideGraphqlResponse(operationName: string, data: object) {
    return overrideGraphqlResponse(operationName, data, this.getGraphqlUrl());
  }

  spyGraphqlRequest(
    operationName: string,
    data: data,
    onCalled: (req: CyHttpMessages.IncomingHttpRequest) => void,
  ) {
    return spyGraphqlRequest(
      operationName,
      data,
      onCalled,
      this.getGraphqlUrl(),
    );
  }

  overrideGqlgenlResponse(operationName: string, data: data) {
    return overrideGraphqlResponse(operationName, data, this.getGqlgenlUrl());
  }

  overrideRequest({
    method,
    url,
    response,
  }: {
    method: method;
    url: string;
    response: data;
  }) {
    return cy.intercept(method, url, response);
  }

  overrideClaimsLLMResponse(path: string, data: data, method: method = 'GET') {
    return this.overrideRequest({
      method,
      url: `${this.getClaimsLLMUrl()}${path}`,
      response: data,
    });
  }
}
