import {
  Box,
  FormControl,
  InputLabel,
  OutlinedInput,
  OutlinedInputProps,
} from '@material-ui/core';
import {
  LocalizationProvider,
  DatePicker as MuiDatePicker,
  DatePickerProps as MuiDatePickerProps,
} from '@material-ui/lab';
import AdapterDateFns from '@material-ui/lab/AdapterDateFns';

export type DatePickerProps = Omit<
  MuiDatePickerProps,
  'renderInput' | 'disabled'
> & {
  /** Current value of date */
  value: Date | null;
  /** Callback to handle change in selected date */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChange: (date: any) => void;
  /** Mui props for OutlinedInput component */
  InputProps?: OutlinedInputProps;
  /** Label for the DatePicker */
  label?: string;
  /** Disable the DatePicker */
  disabled?: boolean;
};

/**
 * Date Picker component with text input.
 * Note: All props except label and className are passed down to MuiDatePicker.
 * @component
 *
 * @example
 * const selectedDate = new Date();
 * const handleDateChange = (date) => {
 *  console.log('Date changed: ', date);
 * };
 * return <DatePicker value={selectedDate} onChange={handleDateChange} label="Select start time" />
 * @deprecated - Use `Calendar` + `Popover` component from `@nirvana/ui` instead. Refer date-picker.stories in the storybook for usage.
 */
export default function DatePicker({
  value,
  onChange,
  InputProps = {},
  label,
  disabled = false,
  ...rest
}: DatePickerProps) {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        <FormControl variant="outlined" fullWidth>
          {!!label && <InputLabel shrink>{label}</InputLabel>}
          <MuiDatePicker
            {...rest}
            value={value}
            onChange={onChange}
            disabled={disabled}
            renderInput={(params) => (
              <OutlinedInput
                {...params.InputProps}
                disabled={disabled}
                inputProps={params.inputProps}
                ref={params.inputRef}
                label={label}
                notched
                {...InputProps}
              />
            )}
          />
        </FormControl>
      </Box>
    </LocalizationProvider>
  );
}
