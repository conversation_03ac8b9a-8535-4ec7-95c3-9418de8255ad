import { createContext } from 'react';
import {
  FormState,
  UseFormGetValues,
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormResetField,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';
import { FileRejection } from 'react-dropzone';
import { z } from 'zod';
import { USState } from '@nirvana/api/forms';
import { ClaimsProvider } from '../../../../../../apps/support/src/types/graphql-types';

export const DESCRIPTION_MAX_LENGTH = 5_000;

const BYTES_PER_KB = 1024;
const MAX_TOTAL_ATTACHMENTS_SIZE_MB = 22.5;
const MAX_TOTAL_ATTACHMENTS_SIZE_BYTES =
  MAX_TOTAL_ATTACHMENTS_SIZE_MB * BYTES_PER_KB * BYTES_PER_KB;

const AttachmentSchema = z.object({
  file: z.instanceof(File),
  uploadUrl: z.string().url().optional(),
  downloadUrl: z.string().url().optional(),
  key: z.string().optional(),
  uploadStatus: z.enum(['pending', 'failed', 'success']),
});

export type Attachment = z.infer<typeof AttachmentSchema>;

const ReporterInformation = z.object({
  firstName: z.string().nonempty().default(''),
  lastName: z.string().nonempty().default(''),
  phone: z
    .union([
      z.string().regex(/^\(?\d{3}\)? ?\d{3}-?\d{4}$/), // Valid phone number
      z.string().length(0), // Empty string
    ])
    .optional(),
  email: z.string().email(),
});

export enum PolicyCoverageEnums {
  /** CoverageAutoLiability */
  CoverageAutoLiability = 'CoverageAutoLiability',
  /** CoverageAutoPhysicalDamage */
  CoverageAutoPhysicalDamage = 'CoverageAutoPhysicalDamage',
  /** CoverageGeneralLiability */
  CoverageGeneralLiability = 'CoverageGeneralLiability',
  /** CoverageMotorTruckCargo */
  CoverageMotorTruckCargo = 'CoverageMotorTruckCargo',
}

export enum ProgramType {
  /** BusinessAuto */
  BusinessAuto = 'BusinessAuto',
  /** Fleet */
  Fleet = 'Fleet',
  /** NonFleet */
  NonFleet = 'NonFleet',
  /** Unkown */
  Unkown = 'Unkown',
}

export enum FnolSourceEnum {
  Finola = 'finola',
  FinolaAutoSubmit = 'finolaAutoSubmit',
  Safety = 'safety',
  Support = 'support',
  Unknown = 'unknown',
}

export const ClaimSchema = z
  .object({
    reporter: ReporterInformation,
    insuredName: z.string().nonempty().default(''),
    insuredDOT: z.string().default(''),
    noticeType: z
      .enum(['initiateClaim', 'onlyReporting'])
      .default('initiateClaim'),
    lossDate: z.date().default(new Date()),
    lossState: z.nativeEnum(USState),
    lossLocation: z.string().default(''),
    isSandboxFlow: z.boolean().default(false),
    policyNumber: z
      .string()
      .refine(
        (value) => value === '' || /^[A-Z]{5}\d{7}-\d{2}$/.test(value),
        'If present, policy number must be in format: 5 letters, 7 digits, dash, 2 digits (e.g., NISTK1234567-24)',
      )
      .default(''),
    lineOfBusiness: z
      .custom<PolicyCoverageEnums>()
      .default(PolicyCoverageEnums.CoverageAutoLiability),
    ownVehiclesInvolved: z.string().default('yes'),
    insuredVehicleVins: z.string().array().default(['']),
    otherVehiclesInvolved: z.string().default('yes'),
    otherVehicleRegistrationNumbers: z.string().array().default(['']),
    police: z.object({
      onTheScene: z.string().default('no'),
      agencyName: z.string().default(''),
      reportNumber: z.string().default(''),
    }),
    injureds: z.string(),
    description: z.string().max(DESCRIPTION_MAX_LENGTH).default(''),
    attachments: z.array(AttachmentSchema).refine(
      (attachments) => {
        const totalAttachmentSize = attachments.reduce(
          (acc, { file }) => acc + file.size,
          0,
        );
        return totalAttachmentSize <= MAX_TOTAL_ATTACHMENTS_SIZE_BYTES;
      },
      {
        message: `Total size cannot be greater than ${MAX_TOTAL_ATTACHMENTS_SIZE_MB}MB`,
      },
    ),
    source: z.custom<FnolSourceEnum>().default(FnolSourceEnum.Unknown),
    overrideProvider: z
      .custom<ClaimsProvider>()
      .default(ClaimsProvider.Undetermined),
  })
  .required({
    lossDate: true,
    lossState: true,
    policyNumber: true,
  });

export type ClaimType = z.infer<typeof ClaimSchema>;

type PresignedUploadLink = {
  key: string;
  url: string;
};

export type Policies = {
  id: string;
  programType: ProgramType;
  policyNumber: string;
  coverages: PolicyCoverageEnums[];
  isTest: boolean;
}[];

export const NewClaimContext = createContext<{
  formState: FormState<ClaimType>;
  getValues: UseFormGetValues<ClaimType>;
  viewAllClaimsLink: string;
  handleSubmit: UseFormHandleSubmit<ClaimType>;
  register: UseFormRegister<ClaimType>;
  resetField: UseFormResetField<ClaimType>;
  setValue: UseFormSetValue<ClaimType>;
  watch: UseFormWatch<ClaimType>;
  canSubmit: boolean;
  isArchived?: boolean;
  isLoadingPolicies: boolean;
  isLoadingDraftFnol?: boolean;
  prefillingForm?: boolean;
  fnolId?: string | undefined;
  policies: Policies | undefined;
  signFiles: (files: File[]) => Promise<PresignedUploadLink[]>;
  rejectedAttachments?: FileRejection[];
}>({
  formState: {} as FormState<ClaimType>,
  getValues: (() => {}) as UseFormGetValues<ClaimType>,
  viewAllClaimsLink: '',
  handleSubmit: {} as UseFormHandleSubmit<ClaimType>,
  register: {} as UseFormRegister<ClaimType>,
  resetField: (() => {}) as UseFormResetField<ClaimType>,
  setValue: (() => {}) as UseFormSetValue<ClaimType>,
  watch: (() => {}) as UseFormWatch<ClaimType>,
  canSubmit: false,
  isArchived: false,
  isLoadingPolicies: false,
  isLoadingDraftFnol: false,
  prefillingForm: false,
  fnolId: undefined,
  policies: undefined,
  signFiles: (() => {}) as unknown as (files: File[]) => Promise<[]>,
  rejectedAttachments: [],
});
