import { TSP } from '@nirvana/api/quoting';

export const getLogoDimensions = (tsp: TSP) => {
  switch (tsp) {
    case TSP.TspGeotab:
      return { width: '180px' };

    default:
      return { height: '60px' };
  }
};

export const getCredentialsUsageHelperText = (tsp: TSP) => {
  switch (tsp) {
    case TSP.TspGeotab:
      return 'Please ensure appropriate user security clearance permissions (administrator, supervisor or view only) with no groups restrictions.';

    default:
      return '';
  }
};

export const getForgotPasswordLink = (tsp: TSP) => {
  switch (tsp) {
    case TSP.TspBigRoad:
      return 'https://bigroad.com/blog/your-top-support-questions-answered/#:~:text=Forgot%20Your%20Password?,and%20email%20it%20to%20me.%E2%80%9D';

    case TSP.TspZonar:
      return 'https://partners.zonarsystems.com/user/forgotpassword';

    case TSP.TspFleetComplete:
      return 'https://web.fleetcomplete.com/login/forgotpassword';

    case TSP.TspGeotab:
      return 'https://my.geotab.com/resetPassword.html';

    case TSP.TspVerizonConnect:
      return 'https://reveal.fleetmatics.com/login.aspx';

    case TSP.TspVerizonConnectFleet:
      return 'https://login.platform.telogis.com/';

    case TSP.TspVerizonConnectReveal:
      return 'https://reveal.fleetmatics.com/login.aspx';

    case TSP.TspMountainEld:
      return 'https://app.mountaineld.com/home/<USER>';

    case TSP.Tsptfmeld:
      return 'https://app.tfmeld.com/home/<USER>';

    case TSP.Tspkskeld:
      return 'https://app.kskeld.com/home/<USER>';

    case TSP.TspgpsTab:
      return 'https://app.gpstab.com/home/<USER>';

    case TSP.TspMasterEld:
      return 'https://app.mastereld.com/home/<USER>';

    case TSP.TspRightTruckingEld:
      return 'https://app.righttruckdeal.com/home/<USER>';

    case TSP.TspRoadStarEld:
      return 'https://app.brightroadstar.com/home/<USER>';
    case TSP.TspOptimaEld:
      return 'https://web.optimaeld.com/#/auth/password/forgot';

    case TSP.Tspxeld:
      return 'https://cloud.xeld.us/#/auth/password/forgot';

    case TSP.TspProRideEld:
      return 'https://web.prorideeld.com/#/auth/password/forgot';

    case TSP.TspRealEld:
      return 'https://app.realeld.com/home/<USER>';

    case TSP.Tsptteld:
      return 'https://dash.tteld.com/#/auth/reset_password';

    case TSP.TspOmnitracs:
    case TSP.TspOmnitracsXrs:
      return 'https://services.omnitracs.com/portalWeb/forgotPassword.do';

    case TSP.Tsperoad:
      return 'https://my.eroad.com/forgot-password';

    case TSP.TspTransflo:
      return 'https://one.transflo.com/auth/resetpassword?email=';

    case TSP.TspgoFleet:
      return 'https://gofleet.geotab.com/resetPassword.html';

    case TSP.TspgoGps:
      return 'https://gogps.geotab.com/resetPassword.html';

    case TSP.TspAgilisLinxup:
      return 'https://www.linxup.com/password.html';

    case TSP.TspatAndTFleet:
      return 'https://afmfe.att.com/resetPassword.html';

    case TSP.TspFleetistics:
      return 'https://fleetistics.geotab.com/resetPassword.html';

    case TSP.TspenVueTelematics:
      return 'https://envuetelematics.geotab.com/resetPassword.html';

    case TSP.TspContiGo:
      return 'https://app.conti-gotech.com/home/<USER>';

    case TSP.TspZippyEld:
      return 'https://dash.zippyeld.com/#/auth/reset_password';

    case TSP.Tspevoeld:
      return 'https://dash.evoeld.com/#/auth/reset_password';

    case TSP.Tspideld:
      return 'https://app.ideld.com/home/<USER>';

    case TSP.TspOneStepGps:
      return 'https://portal.onestepgps.com/forgot-password';

    case TSP.TspOntimeEld:
      return 'https://dash.ontime-logs.com/#/auth/reset_password';

    case TSP.TspAirEld:
      return 'https://api.aireld.com/password/reset';

    case TSP.Tspateld:
      return 'https://app.ateldservice.com/#/login?original_url=%2F';

    case TSP.TspFirstEld:
      return 'https://cloud.firsteld.com/reset-password';

    case TSP.Tspmgkeld:
      return 'https://app.mgkeld.com/#/login';

    case TSP.TspNoorEld:
      return 'https://app.nooreldsolutions.com/#/login?original_url=%2F';

    case TSP.TspOrientEld:
      return 'https://app.orienteld.com/#/login?original_url=%2F';

    case TSP.TspAdvantageOne:
    case TSP.TspAttriX:
    case TSP.TspEagleWireless:
    case TSP.TspGrayboxSolutions:
    case TSP.TspBlueArrowTelematics:
    case TSP.TspFleetProfitCenter:
    case TSP.TspGridline:
    case TSP.TspioTab:
    case TSP.TspgpsTrackingCanada:
    case TSP.TspArgosConnectedSolutions:
    case TSP.TspHighPointGps:
      return 'https://my.geotab.com/resetPassword.html';
    case TSP.TspClearPathGps:
      return 'https://portal.clearpathgps.com/web/auth/forgot-password';
    case TSP.TspeldBooks:
      return 'https://www.portal.eldbooks.com/';
    case TSP.TspApexUltima:
      return 'https://cloud.apexeld.us/#/auth/password/forgot';
    case TSP.TspBlueHorseEld:
      return 'https://portal.bluehorseeld.com/';
    case TSP.TspLightAndTravelEld:
      return 'https://app.lighteld.com/recovery-password';
    case TSP.TspMaxEld:
      return 'https://app.maxeld.com/recovery-password';
    case TSP.TspMotionEld:
      return 'https://www.portal.motioneld.com/';
    case TSP.TspRadicalEld:
      return 'https://www.portal.radicaleld.com/';
    case TSP.TspSmartelds:
      return 'https://www.portal.smartelds.com/';
    case TSP.TspVulcansols:
      return 'https://www.portal.vulcansols.com/';
    case TSP.TspBlueInkTechnology:
      return 'https://blueinktech.com/password_reset.php';
    default:
      return '';
  }
};

export const getLoginIdFieldHelperText = (tsp: TSP) => {
  switch (tsp) {
    case TSP.TspOmnitracs:
    case TSP.TspOmnitracsXrs:
      return 'This field is mandatory only for XRS customers, and optional for ES customers. Include it if you normally use a Company ID to log in.';

    default:
      return '';
  }
};

export const getLoginIdHelpUrl = (tsp: TSP) => {
  switch (tsp) {
    case TSP.TspOmnitracs:
    case TSP.TspOmnitracsXrs:
      return 'https://intercom.help/nirvana-insurance/en/articles/8955956-navigating-omnitracs-credentials';

    default:
      return '';
  }
};
