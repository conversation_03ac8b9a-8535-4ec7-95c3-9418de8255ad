import { FormControl, FormHelperText, Grid } from '@material-ui/core';
import { Controller, useFormContext } from 'react-hook-form-v6';
import { InputWithoutLabel as OutlinedInput } from 'src/components/input';

const RetailerInfo = () => {
  const { control, errors } = useFormContext();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const retailerInfoErrors = errors?.retailerInfo as any;

  return (
    <Grid container flexDirection="column" spacing={2} xs={8}>
      <Grid item container spacing={1} wrap="nowrap">
        <Grid item>
          <FormControl>
            <FormHelperText>First Name</FormHelperText>
            <Controller
              name="retailerInfo.firstName"
              control={control}
              rules={{
                required: 'Please enter first name',
              }}
              render={(props) => (
                <OutlinedInput
                  placeholder="e.g. John"
                  error={!!retailerInfoErrors?.firstName}
                  helperText={retailerInfoErrors?.firstName?.message}
                  value={props.value}
                  onChange={(e) => props.onChange(e.target.value)}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item>
          <FormControl>
            <FormHelperText>Last Name</FormHelperText>
            <Controller
              name="retailerInfo.lastName"
              control={control}
              rules={{
                required: 'Please enter last name',
              }}
              render={(props) => (
                <OutlinedInput
                  placeholder="e.g. Doe"
                  error={!!retailerInfoErrors?.lastName}
                  helperText={retailerInfoErrors?.lastName?.message}
                  value={props.value}
                  onChange={(e) => props.onChange(e.target.value)}
                />
              )}
            />
          </FormControl>
        </Grid>
      </Grid>
      <Grid item>
        <FormControl fullWidth>
          <FormHelperText>Email</FormHelperText>
          <Controller
            name="retailerInfo.email"
            control={control}
            rules={{
              required: 'Please enter email',
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: 'Please enter a valid email address',
              },
            }}
            render={(props) => (
              <OutlinedInput
                placeholder="e.g. <EMAIL>"
                error={!!retailerInfoErrors?.email}
                helperText={retailerInfoErrors?.email?.message}
                value={props.value}
                onChange={(e) => props.onChange(e.target.value)}
              />
            )}
          />
        </FormControl>
      </Grid>
      <Grid item>
        <FormControl fullWidth>
          <FormHelperText>Agency</FormHelperText>
          <Controller
            name="retailerInfo.agency"
            control={control}
            rules={{
              required: 'Please enter agency name',
            }}
            render={(props) => (
              <OutlinedInput
                placeholder="e.g. ABC Insurance Agency"
                error={!!retailerInfoErrors?.agency}
                helperText={retailerInfoErrors?.agency?.message}
                value={props.value}
                onChange={(e) => props.onChange(e.target.value)}
              />
            )}
          />
        </FormControl>
      </Grid>
    </Grid>
  );
};

export default RetailerInfo;
