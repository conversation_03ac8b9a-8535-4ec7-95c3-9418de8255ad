import {
  Box,
  Button,
  CircularProgress,
  FormControl,
  FormGroup,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Typography,
  makeStyles,
} from '@material-ui/core';
import {
  CoverageRecord,
  CoverageType,
  MTCVersion,
  Producer,
  TerminalLocation,
} from '@nirvana/api/quoting';
import {
  AttachmentView,
  InputNumeric,
  Tooltip,
  constants,
} from '@nirvana/ui-kit';
import { CheckboxRowDetailed } from '@nirvana/ui-kit/src/components/checkbox';
import clsx from 'clsx';
import * as React from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  UseFormMethods,
  useFormContext,
} from 'react-hook-form-v6';
import NumberFormat from 'react-number-format';
import { useSelector } from 'react-redux';
import IconPlus from 'src/assets/icons/plus-primary.svg';
import IconFileUpload from 'src/assets/icons/upload-primary.svg';
import { ITheme } from 'src/assets/themes';
import CheckboxRowDetailedWithAdditionalCoverages from 'src/components/coverageSelector/rowDetailedWithAdditionalCoverages';
import CheckboxRowDetailedWithoutBackground from 'src/components/coverageSelector/rowDetailedWithoutBackground';
import { SummaryView } from 'src/components/table';
import { LocationForm, LocationList } from 'src/components/terminalLocation';
import { IMPLER_INTRO_MODAL_OPEN } from 'src/constants/analytics';
import { OPERATIONS_PAGE_VIEW } from 'src/features/telematics/events';
import { application as ApplicationHelper } from 'src/helpers';
import { useAnalytics } from 'src/helpers/analytics';
import { Feature, useFeatureFlag } from 'src/helpers/featureFlags';
import { useDispatch } from 'src/redux';
import {
  downloadApplicationFile,
  fetchAvailableProducers,
  fetchDOTInformation,
  resetAvailableProducers,
  resetStatus,
} from '../../../actions';
import { applicationSelector } from '../../../slices';
import EquipmentListDialog from '../equipmentListUpload';
import ConnectCard from '../globalTelematics/connectCard';
import { getAdditionalCoveragesByPrimary } from '../utils';
import RetailerInfo from './retailer-info';

const { Coverages } = constants;

const RADIUS_RANGE_PADDING_HORIZONTAL = 2;
const RADIUS_RANGE_WIDTH = 140;
const RADIUS_FORM_CONTROL_WIDTH = 160;
const FORM_CONTROL_WIDTH = 220;
const FORM_CONTROL_MARGIN_RIGHT = 2;
const RADIO_LABEL_FONT_SIZE = 14;
const SUMMARY_CONTAINER_WIDTH = 380;
const DIALOG_PAPER_WIDTH = 600;
const DIALOG_CONTENT_PADDING_BOTTOM = 3;
const TOTAL_PERCENTAGE = 100;

interface ErrorRecord {
  type?: string;
  message?: string;
}

interface FormValues {
  coveragesRequired?: CoverageRequiredRecord[];
  numberOfPowerUnits?: string;
  projectedMileage?: string;
  radiusOfOperation?: Array<{ percentageOfFleet: number }>;
  terminalLocations?: unknown[];
  equipmentList?: { info?: Array<{ statedValue: number }> };
  ancillaryCoveragesRequired?: CoverageRecord[];
}

interface FormControlProps {
  childFormRef: React.RefObject<{
    submitForm: (onSuccess: () => void, onError: () => void) => void;
  }>;
  clearErrors: (name: string) => void;
  errors: FieldErrors;
  control: Control<Record<string, unknown>>;
  setValue: UseFormMethods['setValue'];
  watch: UseFormMethods['watch'];
  trigger: UseFormMethods['trigger'];
}

interface CoverageRequiredRecord {
  coverageType: string;
}

export const useStyles = makeStyles((theme: ITheme) => ({
  radiusRange: {
    alignItems: 'center',
    borderRadius: '5px',
    backgroundColor: theme.palette.primary.extraLight,
    color: theme.palette.text.secondary,
    display: 'flex',
    fontWeight: theme.typography.fontWeightRegular,
    height: '100%',
    padding: theme.spacing(1, RADIUS_RANGE_PADDING_HORIZONTAL),
    width: theme.typography.pxToRem(RADIUS_RANGE_WIDTH),
  },
  radiusFormControl: {
    width: theme.typography.pxToRem(RADIUS_FORM_CONTROL_WIDTH),
  },
  formControl: {
    width: FORM_CONTROL_WIDTH,
  },
  formControlLabel: {
    marginRight: theme.spacing(FORM_CONTROL_MARGIN_RIGHT),
    marginLeft: theme.spacing(-1),
  },
  radioLabel: {
    fontSize: theme.typography.pxToRem(RADIO_LABEL_FONT_SIZE),
  },
  summaryContainer: {
    width: SUMMARY_CONTAINER_WIDTH,
  },
  dialogPaper: {
    width: theme.typography.pxToRem(DIALOG_PAPER_WIDTH),
  },
  dialogContent: {
    paddingBottom: theme.spacing(DIALOG_CONTENT_PADDING_BOTTOM),
    position: 'relative',
    overflow: 'hidden',
    borderBottom: '1px solid #E6E7EF',
  },
  dialogCloseButton: {
    position: 'absolute',
    top: theme.spacing(0),
    right: theme.spacing(0),
  },
  radioGroup: {
    display: 'flex',
    flexDirection: 'row',
  },
}));

/**
 * Helper method to get readable helper text/error messages required during form validation.
 *
 * @param {string} fieldName - Name of form field.
 * @param {Object} errors - Errors provided by react-hook-form.
 */
const getHelperText = (fieldName: string, errors: FieldErrors): string => {
  const error = errors[fieldName] || {};

  switch (fieldName) {
    case 'numberOfPowerUnits':
      return error.message;

    case 'projectedMileage':
      return error.message;

    case 'radiusOfOperation':
      if (error.length) {
        // Check if a number error is available
        const numberError = (error as ErrorRecord[]).find(
          (record: ErrorRecord) => record && record.type === 'number',
        );
        if (numberError) {
          return numberError.message || '';
        }

        return (
          (error as ErrorRecord[]).find((e: ErrorRecord) => !!e?.message)
            ?.message || ''
        );
      }
      return '';

    case 'equipmentList':
      return error.message;

    case 'producerId':
      return error.message;

    case 'terminalLocations':
      return error.message;

    default:
      return '';
  }
};

const eventIdByCoverage = {
  [CoverageType.CoverageAutoLiability]: {
    add: 'operations_form_coverages_added (Auto Liability)',
    remove: 'operations_form_coverages_removed (Auto Liability)',
  },
  [CoverageType.CoverageAutoPhysicalDamage]: {
    add: 'operations_form_coverages_added (Physical Damage)',
    remove: 'operations_form_coverages_removed (Physical Damage)',
  },
  [CoverageType.CoverageMotorTruckCargo]: {
    add: 'operations_form_coverages_added (Motor Truck Cargo)',
    remove: 'operations_form_coverages_removed (Motor Truck Cargo)',
  },
  [CoverageType.CoverageGeneralLiability]: {
    add: 'operations_form_coverages_added (General Liability)',
    remove: 'operations_form_coverages_removed (General Liability)',
  },
};

/**
 * Helper function to select the appropriate checkbox component based on application version and coverage type
 */
const getCheckboxComponent = (
  mtcVersion: MTCVersion | undefined,
  coverageType: string,
) => {
  if (
    mtcVersion === MTCVersion.MtcVersionV2 &&
    coverageType === CoverageType.CoverageMotorTruckCargo
  ) {
    return CheckboxRowDetailedWithAdditionalCoverages;
  }

  if (mtcVersion === MTCVersion.MtcVersionV2) {
    return CheckboxRowDetailedWithoutBackground;
  }

  return CheckboxRowDetailed;
};

/**
 * Form to capture operations related application information.
 * Corresponds to Operations Landing (https://www.figma.com/proto/OwouvIq33I1CCIjUXIlrcn/NIrvana_Dev-Handoff?node-id=98%3A11195&scaling=min-zoom&page-id=98%3A10867)
 * @component
 */
const Operations = ({
  childFormRef,
  clearErrors,
  errors,
  control,
  setValue,
  watch,
  trigger,
}: FormControlProps) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const { capture } = useAnalytics();
  const getFeatureValue = useFeatureFlag();
  const isTelematicsWidgetEnabled = getFeatureValue(
    Feature.TELEMATICS_CONNECTION_WIDGET_OPERATIONS,
    false,
  );
  const isRetailerInfoCaptureEnabled = getFeatureValue(
    Feature.RETAILER_INFO_CAPTURE,
    false,
  );
  const isMtcRevampP1Enabled = getFeatureValue(Feature.MTC_REVAMP_P1, false);
  const [isFetchingAvailableProducers, setFetchingAvailableProducers] =
    React.useState<boolean>(false);
  const [openEquipmentListDialog, setOpenEquipmentListDialog] =
    React.useState<boolean>(false);
  const [isUploadingEquipmentList, setIsUploadingEquipmentList] =
    React.useState<boolean>(false);
  const [isAddTerminalLocationFormVisible, setAddTerminalLocationVisibility] =
    React.useState<boolean>(false);
  const [applicationUSState, setApplicationUSState] = React.useState<
    string | undefined
  >();
  const [additionalCoverages, setAdditionalCoverages] = React.useState<
    string[]
  >([]);
  const application = useSelector(applicationSelector);
  const { activeApplication, availableProducers = [] } = application;
  const applicationDotNumber = activeApplication?.summary?.dotNumber;
  const applicationId = activeApplication?.summary?.applicationID;
  const values: FormValues = watch([
    'coveragesRequired',
    'numberOfPowerUnits',
    'projectedMileage',
    'radiusOfOperation',
    'terminalLocations',
    'equipmentList',
    'ancillaryCoveragesRequired',
  ]);
  const CoveragesList = Coverages.getCoveragesList();

  React.useEffect(() => {
    capture(OPERATIONS_PAGE_VIEW, {
      applicationId,
    });
  }, []);

  const producersList = React.useMemo(() => {
    const list = [...availableProducers];

    if (activeApplication?.summary.renewalMetadata) {
      list.push({
        id: activeApplication.summary.producerName,
        name: activeApplication.summary.producerID || '',
      });
    }

    return list;
  }, [availableProducers, activeApplication]);

  const coverages =
    values.coveragesRequired?.map(
      (record: CoverageRequiredRecord) => record.coverageType,
    ) || [];
  const isApdCoverage = coverages.includes(
    CoverageType.CoverageAutoPhysicalDamage,
  );
  const isGlCoverage = coverages.includes(
    CoverageType.CoverageGeneralLiability,
  );

  // Check if Cargo at Terminals is selected as additional coverage
  const isCargoAtTerminalsSelected = additionalCoverages.includes(
    CoverageType.CoverageCargoAtScheduledTerminals,
  );

  // Show terminal locations if either GL is selected OR Cargo at Terminals is selected
  const shouldShowTerminalLocations =
    isGlCoverage || isCargoAtTerminalsSelected;

  // Re-validate terminal locations when cargo coverage selection changes
  React.useEffect(() => {
    trigger('terminalLocations');
  }, [isCargoAtTerminalsSelected, trigger]);

  const totalFleetPercentage: number = values.radiusOfOperation
    ? values.radiusOfOperation.reduce(
        (acc: number, record: { percentageOfFleet: number }) =>
          acc + (+record?.percentageOfFleet || 0),
        0,
      )
    : 0;

  const totalStatedValue: number = values.equipmentList?.info
    ? values.equipmentList.info.reduce(
        (acc: number, record: { statedValue: number }) =>
          acc + (+record?.statedValue || 0),
        0,
      )
    : 0;

  const mileageRadiusList = React.useMemo(() => {
    return ApplicationHelper.getMileageRadiusList();
  }, []);

  const fetchUSState = React.useCallback(
    (dotNumber: number) => {
      dispatch(fetchDOTInformation({ dotNumber })).then((response) => {
        if (fetchDOTInformation.fulfilled.match(response)) {
          setApplicationUSState(response.payload.usState);
        }
      });
    },
    [dispatch],
  );

  const handleFileDownload = (handleId?: string) => {
    if (!handleId) {
      return;
    }

    dispatch(downloadApplicationFile(handleId));
  };

  React.useEffect(() => {
    if (applicationDotNumber) {
      fetchUSState(applicationDotNumber);
    }
  }, [applicationDotNumber, fetchUSState]);

  React.useEffect(() => {
    // Fetch available producers
    setFetchingAvailableProducers(true);
    dispatch(fetchAvailableProducers());

    return () => {
      dispatch(resetAvailableProducers());
    };
  }, [dispatch]);

  React.useEffect(() => {
    if (isFetchingAvailableProducers && availableProducers) {
      setFetchingAvailableProducers(false);
      dispatch(resetStatus());
    }
  }, [dispatch, availableProducers, isFetchingAvailableProducers]);

  React.useEffect(() => {
    const ancillaryCoverages = values.ancillaryCoveragesRequired;
    if (ancillaryCoverages) {
      // Filter out any records without coverageType and ensure they are valid strings
      const validCoverageTypes = ancillaryCoverages
        .filter((record) => record && record.coverageType)
        .map((record) => record.coverageType as string)
        .filter(
          (coverageType) => coverageType && typeof coverageType === 'string',
        );

      setAdditionalCoverages(validCoverageTypes);
    }
  }, [values.ancillaryCoveragesRequired]);

  return (
    <Grid container direction="column" spacing={3}>
      <Grid item>
        <Typography variant="h4" fontWeight="fontWeightBold">
          Operations
        </Typography>
      </Grid>
      {isTelematicsWidgetEnabled && (
        <Grid item>
          <div className="max-w-[748px]">
            <ConnectCard
              applicationId={applicationId ?? ''}
              details={activeApplication}
              step="operations"
            />
          </div>
        </Grid>
      )}
      <Grid
        item
        container
        direction="row"
        wrap="nowrap"
        alignItems="flex-start"
        spacing={3}
      >
        <Grid item xs={5}>
          <Box paddingTop="10px">
            <InputLabel htmlFor="coverage-input">
              Coverages requested
            </InputLabel>
            <FormHelperText className="w-[288px]">
              Your limits and deductibles will be selected at the Indication
              step
            </FormHelperText>
          </Box>
        </Grid>
        <Grid item xs={7}>
          <Controller
            name="coveragesRequired"
            control={control}
            defaultValue={[
              {
                coverageType: CoverageType.CoverageAutoLiability,
              },
            ]}
            rules={{
              required: true,
            }}
            render={(props) => {
              const coverages =
                props.value?.map(
                  (record: CoverageRecord) => record.coverageType,
                ) || [];

              return (
                <FormGroup>
                  <div className="flex flex-col w-full gap-4">
                    {CoveragesList.filter((record) => {
                      if (
                        record.coverageType ===
                        CoverageType.CoverageGeneralLiability
                      ) {
                        if (
                          isGlCoverage ||
                          (applicationUSState &&
                            getFeatureValue(
                              Feature.GENERAL_LIABILITY_RELEASED_STATES,
                              '',
                            ).includes(applicationUSState)) ||
                          (applicationUSState &&
                            getFeatureValue(
                              Feature.GENERAL_LIABILITY_INTERNAL_STATES,
                              '',
                            ).includes(applicationUSState))
                        ) {
                          return true;
                        }

                        return false;
                      }

                      return true;
                    }).map((record) => {
                      const eventIds =
                        eventIdByCoverage[
                          record.coverageType as keyof typeof eventIdByCoverage
                        ] || {};

                      const Component = getCheckboxComponent(
                        application.activeApplication?.summary.mtcVersion,
                        record.coverageType,
                      );

                      return (
                        <React.Fragment key={record.coverageType}>
                          <Tooltip
                            title={
                              record.coverageType ===
                              CoverageType.CoverageAutoLiability
                                ? 'AL is a required coverage'
                                : ''
                            }
                          >
                            <Component
                              checked={
                                coverages.includes(record.coverageType) ||
                                record.coverageType ===
                                  CoverageType.CoverageAutoLiability
                              }
                              onChange={(_, checked) => {
                                if (
                                  record.coverageType !==
                                  CoverageType.CoverageAutoLiability
                                ) {
                                  const newCoverages = [...props.value];
                                  if (checked) {
                                    capture(eventIds.add, {
                                      applicationId,
                                    });
                                    newCoverages.push({
                                      coverageType: record.coverageType,
                                    });
                                  } else {
                                    capture(eventIds.remove, {
                                      applicationId,
                                    });
                                    newCoverages.splice(
                                      coverages.indexOf(record.coverageType),
                                      1,
                                    );
                                  }

                                  props.onChange(newCoverages);
                                }
                              }}
                              label={record.label}
                              description={record.description}
                              icon={
                                <img
                                  src={
                                    coverages.includes(record.coverageType)
                                      ? record.iconActive
                                      : record.iconInactive
                                  }
                                  alt={record.label}
                                />
                              }
                              className="min-h-[90px]"
                              additionalCoveragesOptions={getAdditionalCoveragesByPrimary(
                                record.coverageType,
                                isMtcRevampP1Enabled,
                              )}
                              selectedAdditionalCoverages={additionalCoverages}
                              selectedPrimaryCoverages={coverages}
                              onAdditionalCoveragesChange={(v) => {
                                // Filter out any invalid or undefined coverage types
                                const validCoverages = v.filter(
                                  (r) => r && typeof r === 'string',
                                );

                                // Update local state immediately
                                setAdditionalCoverages(validCoverages);

                                // Update form state - filter out empty objects
                                setValue(
                                  'ancillaryCoveragesRequired',
                                  validCoverages
                                    .map((r) => ({
                                      coverageType: r,
                                    }))
                                    .filter((record) => record.coverageType), // Remove any records without coverageType
                                  { shouldDirty: true },
                                );
                              }}
                            />
                          </Tooltip>
                        </React.Fragment>
                      );
                    })}
                  </div>
                </FormGroup>
              );
            }}
          />
          <Controller
            name="ancillaryCoveragesRequired"
            defaultValue={[]}
            control={control}
            render={() => <input type="hidden" />}
          />
        </Grid>
      </Grid>

      <Grid
        item
        container
        direction="row"
        wrap="nowrap"
        alignItems="center"
        spacing={3}
      >
        <Grid item xs={5}>
          <InputLabel htmlFor="producer-select">Producer</InputLabel>
        </Grid>
        <Grid item xs={7}>
          <Controller
            name="producerId"
            defaultValue=""
            rules={{
              required: 'Please select a producer',
            }}
            control={control}
            render={(props) => (
              <FormControl className={classes.formControl}>
                <Select
                  id="producer-select"
                  displayEmpty
                  variant="outlined"
                  value={props.value}
                  onChange={props.onChange}
                  error={!!errors.producerId}
                  disabled={!!activeApplication?.summary?.renewalMetadata}
                >
                  <MenuItem data-attr="operations-form-producer" value="">
                    Select
                  </MenuItem>
                  {producersList?.map((producer: Producer) => {
                    return (
                      <MenuItem
                        data-attr="operations-form-producer"
                        value={producer.id}
                        key={producer.id}
                      >
                        {producer.name}
                      </MenuItem>
                    );
                  })}
                </Select>
                {!!errors.producerId && (
                  <FormHelperText error>
                    {getHelperText('producerId', errors)}
                  </FormHelperText>
                )}
              </FormControl>
            )}
          />
        </Grid>
      </Grid>

      <Grid
        item
        container
        direction="row"
        wrap="nowrap"
        alignItems="center"
        spacing={3}
      >
        <Grid item xs={5}>
          <InputLabel htmlFor="numberOfPowerUnits-input">
            Projected number of power units
          </InputLabel>
        </Grid>
        <Grid item xs={7}>
          <Box display="flex" alignItems="center">
            <Controller
              name="numberOfPowerUnits"
              defaultValue=""
              control={control}
              rules={{
                required: 'Please enter a value',
                pattern: {
                  value: /^[1-9]\d*$/,
                  message: 'Please enter a valid number',
                },
              }}
              render={(props) => (
                <FormControl className={classes.formControl}>
                  <InputNumeric
                    id="numberOfPowerUnits-input"
                    placeholder="0"
                    decimalScale={0}
                    value={props.value}
                    onChange={(e) => props.onChange(e.target.value)}
                    error={!!errors.numberOfPowerUnits}
                  />
                  {!!errors.numberOfPowerUnits && (
                    <FormHelperText error>
                      {getHelperText('numberOfPowerUnits', errors)}
                    </FormHelperText>
                  )}
                </FormControl>
              )}
            />
          </Box>
        </Grid>
      </Grid>

      <Grid
        item
        container
        direction="row"
        wrap="nowrap"
        alignItems="center"
        spacing={3}
      >
        <Grid item xs={5}>
          <InputLabel htmlFor="projectedMileage-input">
            Projected mileage
          </InputLabel>
        </Grid>
        <Grid item xs={7}>
          <Box display="flex" alignItems="center">
            <Controller
              name="projectedMileage"
              defaultValue=""
              control={control}
              rules={{
                required: 'Please enter a value',
                pattern: {
                  value: /^[1-9]\d*$/,
                  message: 'Please enter a valid number',
                },
                validate: (value) =>
                  value > 0 || 'A non-zero value must be provided',
              }}
              render={(props) => (
                <FormControl className={classes.formControl}>
                  <InputNumeric
                    id="projectedMileage-input"
                    placeholder="0"
                    decimalScale={0}
                    value={props.value}
                    onChange={(e) => props.onChange(e.target.value)}
                    error={!!errors.projectedMileage}
                  />
                  {!!errors.projectedMileage && (
                    <FormHelperText error>
                      {getHelperText('projectedMileage', errors)}
                    </FormHelperText>
                  )}
                </FormControl>
              )}
            />
          </Box>
        </Grid>
      </Grid>

      <Grid
        item
        container
        direction="row"
        wrap="nowrap"
        alignItems="flex-start"
        spacing={3}
      >
        <Grid item xs={5}>
          <Box paddingTop="10px">
            <InputLabel htmlFor="radiusOfOperation-input">
              Radius of operation
            </InputLabel>
            <FormHelperText className="w-[288px]">
              Please specify percentage of operations in each radius bucket
            </FormHelperText>
          </Box>
        </Grid>
        <Grid item xs={7} container direction="column" spacing={1}>
          {mileageRadiusList.map((record, index) => {
            return (
              <Grid
                item
                container
                direction="row"
                wrap="nowrap"
                spacing={2}
                key={record.bucket}
              >
                <Grid item>
                  <Box className={classes.radiusRange}>{record.label}</Box>
                </Grid>
                <Grid item flexGrow={1}>
                  <Controller
                    name={`radiusOfOperation[${index}]`}
                    defaultValue={{
                      mileageRadiusBucket: record.bucket,
                      percentageOfFleet: undefined,
                    }}
                    control={control}
                    rules={{
                      validate: {
                        number: (record) =>
                          !record ||
                          !record.percentageOfFleet ||
                          (record &&
                            /^[0-9]+$/.test(record.percentageOfFleet)) ||
                          'Please enter a valid number',
                        total: () =>
                          totalFleetPercentage === TOTAL_PERCENTAGE ||
                          'Total must equal 100%',
                      },
                    }}
                    render={(props) => (
                      <FormControl className={classes.radiusFormControl}>
                        <InputNumeric
                          id="radiusOfOperation-input"
                          placeholder="0%"
                          decimalScale={0}
                          value={props.value.percentageOfFleet}
                          onChange={(event) => {
                            if (event.target.value) {
                              props.onChange({
                                ...props.value,
                                percentageOfFleet: +event.target.value,
                              });
                            } else {
                              props.onChange({
                                ...props.value,
                                percentageOfFleet: undefined,
                              });
                            }
                          }}
                          error={!!errors.radiusOfOperation}
                          suffix="%"
                        />
                      </FormControl>
                    )}
                  />
                </Grid>
              </Grid>
            );
          })}
          <Grid item container direction="row" wrap="nowrap" spacing={2}>
            <Grid item>
              <Box className={classes.radiusRange}>Total</Box>
            </Grid>
            <Grid item flexGrow={1}>
              <Box
                className={clsx(classes.radiusRange, classes.radiusFormControl)}
              >
                {totalFleetPercentage}%
              </Box>
            </Grid>
          </Grid>
          <Grid item container direction="row" wrap="nowrap" spacing={0}>
            <Grid item>
              {!!errors.radiusOfOperation && (
                <FormHelperText error>
                  {getHelperText('radiusOfOperation', errors)}
                </FormHelperText>
              )}
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      <Grid
        item
        container
        direction="row"
        wrap="nowrap"
        alignItems="flex-start"
        spacing={3}
      >
        <Grid item xs={5}>
          <Box paddingTop="10px">
            <InputLabel htmlFor="vehicleList-input">Equipment list</InputLabel>
          </Box>
        </Grid>
        <Grid item xs={7}>
          <Controller
            control={control}
            name="equipmentList"
            // Default value is set to undefined so that the required validation works
            defaultValue={undefined}
            rules={{
              required: 'Please upload a list of vehicles',
              validate: {
                total: (value) => {
                  if (value?.info?.length === 0) {
                    return 'Please upload a list of vehicles';
                  }

                  if (!isApdCoverage) {
                    return true;
                  } else {
                    return (
                      totalStatedValue > 0 ||
                      'Total Stated Value must be greater than 0'
                    );
                  }
                },
              },
            }}
            render={(props) => {
              const summaryValues = ApplicationHelper.getEquipmentSummary(
                props.value?.info,
              );
              const summaryRows = [
                {
                  label: 'Total # of trucks and trailers',
                  value: summaryValues.totalUnits,
                },
                {
                  label: 'Total value',
                  value: (
                    <NumberFormat
                      value={summaryValues.totalValue}
                      displayType="text"
                      thousandSeparator={true}
                      prefix="$"
                    />
                  ),
                },
              ];

              return (
                <Grid container direction="column" spacing={1}>
                  <Grid item>
                    {props.value?.implerMetadata?.fileMetadata?.name ? (
                      <AttachmentView
                        files={[
                          {
                            filename:
                              props.value?.implerMetadata?.fileMetadata?.name ||
                              'Equipment List.xlsx',
                          },
                        ]}
                        onRemove={() => {
                          capture('operations_form_equipment_list_removed', {
                            applicationId,
                          });
                          props.onChange(null);
                        }}
                        onDownload={() => {
                          handleFileDownload(
                            props.value.implerMetadata.fileMetadata.handle,
                          );
                        }}
                      />
                    ) : (
                      <Button
                        variant="outlined"
                        onClick={() => {
                          capture(IMPLER_INTRO_MODAL_OPEN, {
                            component: 'Fleet Equipment List',
                          });
                          setOpenEquipmentListDialog(true);
                        }}
                        startIcon={
                          isUploadingEquipmentList ? (
                            <CircularProgress size="16px" />
                          ) : (
                            <img src={IconFileUpload} alt="File Upload" />
                          )
                        }
                        disabled={isUploadingEquipmentList}
                      >
                        Upload
                      </Button>
                    )}
                    <EquipmentListDialog
                      open={openEquipmentListDialog}
                      onClose={() => setOpenEquipmentListDialog(false)}
                      isApdCoverage={isApdCoverage}
                      value={props.value}
                      onChange={props.onChange}
                      onUploadStatusChange={(status) => {
                        if (status === 'start') {
                          setOpenEquipmentListDialog(true);
                        } else if (status === 'progress') {
                          setIsUploadingEquipmentList(true);
                          capture(
                            'operations_form_equipment_list_upload_completed',
                            {
                              applicationId,
                            },
                          );
                        } else if (status === 'complete') {
                          setIsUploadingEquipmentList(false);
                        }
                      }}
                    />
                  </Grid>
                  {props.value?.info && props.value?.info?.length ? (
                    <Grid item>
                      <div className={classes.summaryContainer}>
                        <SummaryView
                          title="Document Summary"
                          rows={summaryRows}
                        />
                      </div>
                    </Grid>
                  ) : null}
                </Grid>
              );
            }}
          />

          {!!errors.equipmentList && (
            <FormHelperText error>
              {getHelperText('equipmentList', errors)}
            </FormHelperText>
          )}
        </Grid>
      </Grid>
      {shouldShowTerminalLocations && (
        <Grid
          item
          container
          direction="row"
          wrap="nowrap"
          alignItems="flex-start"
          spacing={3}
        >
          <Grid item xs={5}>
            <Box paddingTop="10px">
              <InputLabel htmlFor="vehicleList-input">
                Physical business addresses
              </InputLabel>
            </Box>
          </Grid>
          <Grid item xs={7}>
            <Grid container direction="column">
              <Grid item>
                <Controller
                  control={control}
                  name="terminalLocations"
                  defaultValue={[]}
                  rules={{
                    required: 'Please add a business address',
                    validate: {
                      minOne: (value) => {
                        if (value.length) {
                          return true;
                        }

                        return 'Please add at least 1 business address';
                      },
                      cargoTerminalSchedule: (terminalLocations) => {
                        if (!isCargoAtTerminalsSelected) {
                          return true;
                        }

                        // Simplified validation: check if all terminal locations have required cargo terminal schedule fields
                        if (
                          !terminalLocations ||
                          terminalLocations.length === 0
                        ) {
                          return true; // Let the main required validation handle empty locations
                        }

                        const hasIncompleteCargoSchedule =
                          terminalLocations.some(
                            (location: TerminalLocation) => {
                              if (!location?.cargoTerminalSchedule) {
                                return true;
                              }

                              // Check required fields (only the ones that are actually required)
                              const schedule = location.cargoTerminalSchedule;
                              return (
                                !schedule?.privateTheftProtection ||
                                !schedule?.privateFireProtection
                              );
                            },
                          );

                        if (hasIncompleteCargoSchedule) {
                          return 'Please complete the missing terminal info needed for Cargo at Terminals';
                        }

                        return true;
                      },
                    },
                  }}
                  render={(props) => {
                    return (
                      <>
                        <Box mb={2}>
                          <LocationList
                            list={props.value}
                            onChange={props.onChange}
                            selectedAdditionalCoverages={additionalCoverages}
                          />
                        </Box>

                        {isAddTerminalLocationFormVisible && (
                          <Box mb={2}>
                            <LocationForm
                              index={props.value.length}
                              ref={childFormRef}
                              selectedAdditionalCoverages={additionalCoverages}
                              onConfirm={(index: number, data) => {
                                const newLocations = [...props.value];
                                newLocations.splice(index, 0, data);

                                capture(
                                  'operations_form_terminal_location_added',
                                  {
                                    applicationId,
                                  },
                                );
                                props.onChange(newLocations);
                                setAddTerminalLocationVisibility(false);
                              }}
                              onRemove={(index: number) => {
                                const newLocations = [...props.value];
                                newLocations.splice(index, 1);

                                capture(
                                  'operations_form_terminal_location_removed',
                                  {
                                    applicationId,
                                  },
                                );

                                props.onChange(newLocations);
                                setAddTerminalLocationVisibility(false);
                              }}
                            />
                          </Box>
                        )}
                      </>
                    );
                  }}
                />
              </Grid>
              <Grid item>
                <Button
                  variant="outlined"
                  onClick={() => {
                    if (childFormRef.current?.submitForm) {
                      childFormRef.current.submitForm(
                        () => {
                          setAddTerminalLocationVisibility(true);
                        },
                        () => {},
                      );
                    } else {
                      setAddTerminalLocationVisibility(true);
                      clearErrors('terminalLocations');
                    }
                  }}
                  startIcon={
                    <img
                      src={IconPlus}
                      alt="Add terminal location"
                      width={11}
                    />
                  }
                >
                  Add New Location
                </Button>
              </Grid>
            </Grid>

            {!!errors.terminalLocations && (
              <FormHelperText error>
                {errors.terminalLocations.message}
              </FormHelperText>
            )}
          </Grid>
        </Grid>
      )}

      {isRetailerInfoCaptureEnabled && (
        <Grid
          item
          container
          direction="row"
          wrap="nowrap"
          alignItems="flex-start"
          spacing={3}
        >
          <Grid item xs={5}>
            <Box paddingTop="10px">
              <InputLabel htmlFor="vehicleList-input">
                Retailer information
              </InputLabel>
              <FormHelperText className="w-[288px]">
                Please enter the retailer's information
              </FormHelperText>
            </Box>
          </Grid>
          <Grid item xs={7}>
            <RetailerInfo />
          </Grid>
        </Grid>
      )}
    </Grid>
  );
};

export const OperationsContainer = (props: Record<string, unknown>) => {
  const methods = useFormContext();

  return (
    <Operations {...methods} {...props} childFormRef={React.useRef(null)} />
  );
};

export default OperationsContainer;
