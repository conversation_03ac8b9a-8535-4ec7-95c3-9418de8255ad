import { UserProfileResponse } from '@nirvana/api/auth';
import {
  useLDClient as useFeatureFlagClient,
  useFlags,
  withLDProvider as withFeatureFlagProvider,
} from 'launchdarkly-react-client-sdk';

export type FleetType = 'fleet' | 'retail-non-fleet' | 'wholesale-non-fleet';
export type AccessType = 'fleet' | 'non-fleet' | 'all';
export const ANONYMOUS_USER_KEY = '00000000-0000-0000-0000-000000000000';

export const Feature = {
  GENERAL_LIABILITY_INTERNAL_STATES: 'generalLiabilityInternalStates',
  GENERAL_LIABILITY_RELEASED_STATES: 'generalLiabilityReleasedStates',
  FLEET_TYPE: 'fleetType',
  FEATURE_RENEWALS: 'renewalApplication',
  FEATURE_WORKRAMP_TAB: 'featureWorkrampTab',
  TELEMATICS_RESOURCE: 'telematicsResource',
  NEW_RENEWALS_FLOW: 'renewalFlow',
  FEATURE_POLICIES_LIST: 'policiesList',
  APPLICATION_TYPE_ACCESS: 'applicationTypeAccess',
  PRICELESS_INDICATION: 'pricelessIndication',
  DISABLE_POSTHOG_ANALYTICS: 'disablePosthogAnalytics',
  INDICATION_RANGES: 'indicationRanges',
  CLAIMS_PORTAL: 'quotingClaims',
  NF_UIIA: 'nfUiia',
  ENHANCED_TOWING_LIMITS: 'enhancedTowingLimits',
  TELEMATICS_CONSENT_SMS: 'telematicsConsentSms',
  UIIA_SUPPORTED_STATES: 'uiiaSupportedStates',
  FEATURE_AGENT_DASHBOARD: 'agentDashboard',
  EDIT_NON_FLEET_RENEWALS: 'editNonFleetRenewals',
  NO_CREDIT_HIT: 'noCreditHit',
  NON_FLEET_RENEWALS_FLOW: 'nonFleetRenewalsEnabled',
  CREATE_NON_FLEET_RENEWALS: 'createNonFleetRenewals',
  GLOBAL_TELEMATICS_VARIANT_EVENT: 'globalTelematicsVariantEvent',
  TELEMATICS_CONNECTION_WIDGET_OPERATIONS:
    'telematicsConnectionWidgetOperations',
  TELEMATICS_CONSENT_NEW_UX: 'telematicsConsentNewUx',
  PRE_QUOTE_TELEMATICS: 'preTelematicsNfEnabled',
  NF_TELEMATICS_CONSENT_NEW_UX: 'nfTelematicsConsentNewUx',
  ELD_PROVIDER_FLOW: 'eldProviderFlow',
  NF_TELEMATICS_CONSENT_NEW_UX_SMS: 'nfTelematicsConsentNewUxSms',
  PRE_TELEMATICS_EXPERIMENT: 'preQuoteTelematicsV1Experiment',
  QUOTE_TELEMATICS_ENABLED: 'quoteTelematicsEnabled',
  MTC_REVAMP_P1: 'mtcRevampP1',
  CONTRACTOR_FORM_AGENCY_MARKETER: 'contractorFormAgencyMarketer',
  RETAILER_INFO_CAPTURE: 'retailerInfoCapture',
};

export const useFeatureFlag = () => {
  const flags = useFlags();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (flag: string, fallback: any) => flags[flag] ?? fallback;
};

export const getUserIdentity = (user: UserProfileResponse) => {
  return {
    kind: 'user',
    key: user.id,
    email: user.email.toLowerCase(),
    name: user.name,
    agencyId: user.defaultAgencyId || '',
    agencyRoles:
      user.roles?.agencyRoles
        ?.filter((record) => record.agency.id === user.defaultAgencyId)
        .map((record) => record.role) ?? '',
    nirvanaRoles: user.roles?.nirvanaRoles?.map((record) => record.role) ?? '',
  };
};

export { useFeatureFlagClient, withFeatureFlagProvider };
