import { RoutesHelper, CyHttpMessages } from '@nirvana/core/testUtils';

import { fleetDetails } from '../../../fixtures/fleetDetails';
import { DEFAULT_DOT_NUMBER } from '../../../support/factories/fleetDetails';
import {
  claimById,
  ClaimIdForClosedClaim,
  ClaimIdForOpenClaim,
  claimIdForOpenWithoutNewNotes,
  ClaimIdForSnapsheet,
  generateClaimSummary,
  upsertClaimSummaryFeedback,
} from '../../../fixtures/claims';
import { mockPermissions } from '../../../fixtures/permissions';

const routesHelper = new RoutesHelper();
const safetyUrl = routesHelper.getBaseUrl();

describe('Claim Details Page', () => {
  const claimId = '639aa811-32e9-4a1a-8ce6-19205b5dd973';
  const ClaimIdWithoutAdjuster = 'ab75234b-9fb8-4684-b56f-451f26329836';

  beforeEach(() => {
    routesHelper
      .overrideGqlgenlResponse(
        'getUIPermissions',
        mockPermissions({ ClaimsList: true }),
      )
      .as('permissions');
    routesHelper.overrideGraphqlResponse('dotNumber', fleetDetails);
    routesHelper
      .overrideGraphqlResponse('ClaimById', claimById)
      .as('claimById');
    routesHelper
      .overrideGraphqlResponse(
        'UpsertClaimSummaryFeedback',
        upsertClaimSummaryFeedback,
      )
      .as('upsertClaimSummaryFeedback');
    routesHelper
      .overrideGraphqlResponse('generateClaimSummary', generateClaimSummary)
      .as('generateClaimSummary');
    cy.login();
  });

  describe('Always', () => {
    beforeEach(() => {
      cy.visit(`${safetyUrl}/${DEFAULT_DOT_NUMBER}/claims/${claimId}`);
    });

    it('should render claim details page', () => {
      cy.get('[data-testid="claim-details"]').should('be.visible');
    });

    it('Sets the browser title', () => {
      cy.title().should('include', 'Claim #NIT');
    });

    it('shows up/down button for last update', () => {
      cy.get('[data-testid="item-feedback"]').should('be.visible');
    });

    it('Trigggers an update when the up/down button is clicked', () => {
      cy.get(
        '[data-testid="weekly-updates"] [data-testid="item-feedback"] > button:first',
      ).click();
      cy.contains('Thank you for your feedback!').should('be.visible');
    });

    it('shows the adjuster name with mailto link when adjuster email exists', () => {
      cy.contains('Adjuster')
        .parent()
        .find('a[href^="mailto:"]')
        .should('be.visible');
    });
  });

  describe('When the claim is closed', () => {
    beforeEach(() => {
      cy.visit(
        `${safetyUrl}/${DEFAULT_DOT_NUMBER}/claims/${ClaimIdForClosedClaim}`,
      );
    });

    it('does not show the generate summary button', () => {
      cy.get('[data-testid="claim-details"]').should('be.visible');
      cy.get('[data-testid="summary-generation"]').should('not.exist');
    });
  });

  describe('when the claim is Open', () => {
    beforeEach(() => {
      cy.visit(
        `${safetyUrl}/${DEFAULT_DOT_NUMBER}/claims/${ClaimIdForOpenClaim}`,
      );
    });

    it('shows the generate summary button', () => {
      cy.get('[data-testid="summary-generation"]').should('exist');
    });

    it('Generates a summary', () => {
      cy.get('[data-testid="summary-generation"] button').click();
      cy.wait('@generateClaimSummary');
      cy.get(
        '[data-testid="summary-generation"] [data-testid="summary"]',
      ).should('exist');
    });

    describe('when the claim has no new updates since the last summary was generated', () => {
      beforeEach(() => {
        cy.visit(
          `${safetyUrl}/${DEFAULT_DOT_NUMBER}/claims/${claimIdForOpenWithoutNewNotes}`,
        );
      });

      it("Doesn't allow to generate a summary", () => {
        cy.get('[data-testid="summary-generation"] button').should(
          'be.disabled',
        );
      });
    });
  });

  describe('When the claim has no adjuster email', () => {
    beforeEach(() => {
      routesHelper
        .overrideGraphqlResponse(
          'ClaimById',
          (req: CyHttpMessages.IncomingHttpRequest) => {
            const response = claimById(req);
            response.data.claimById!.adjusterEmail = '';
            return response;
          },
        )
        .as('claimByIdNoAdjuster');

      cy.visit(
        `${safetyUrl}/${DEFAULT_DOT_NUMBER}/claims/${ClaimIdWithoutAdjuster}`,
      );
    });

    it('does not render the Adjuster field', () => {
      cy.contains('Adjuster').should('not.exist');
    });
  });

  describe('When the user does not have permission to view claims', () => {
    beforeEach(() => {
      routesHelper
        .overrideGqlgenlResponse(
          'getUIPermissions',
          mockPermissions({ ClaimsDetails: false }),
        )
        .as('permissions');

      cy.visit(`${safetyUrl}/${DEFAULT_DOT_NUMBER}/claims/${claimId}`);
    });

    it('redirects to home page with a message', () => {
      cy.get('#claim-details-permission-denied-snackbar')
        .should('be.visible')
        .and('contain', 'You do not have permissions to view this claim.');
      cy.url().should('include', '/');
    });
  });

  describe('When the claim is from Nars', () => {
    beforeEach(() => {
      cy.visit(`${safetyUrl}/${DEFAULT_DOT_NUMBER}/claims/${claimId}`);
    });

    it('does not show the `no updates message available`', () => {
      cy.get('[data-testid="no-updates-message-for-snapsheet"]').should(
        'not.exist',
      );
    });
  });

  describe('When the claim is from Snapsheet', () => {
    beforeEach(() => {
      cy.visit(
        `${safetyUrl}/${DEFAULT_DOT_NUMBER}/claims/${ClaimIdForSnapsheet}`,
      );
    });

    it('shows the `no updates message available`', () => {
      cy.get('[data-testid="no-updates-message-for-snapsheet"]').should(
        'be.visible',
        'contain',
        'No updates available for this claim while our systems are being updated. Thank you for your patience.',
      );
    });
  });
});
