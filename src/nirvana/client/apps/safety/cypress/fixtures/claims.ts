import { CyHttpMessages, getVariable } from '@nirvana/core/testUtils';
import {
  ClaimByIdQuery,
  ClaimStatus,
  ClaimsProvider,
} from '../../src/types/graphql-types';
import {
  claimByIdFactory,
  claimsByDOTNumberFactory,
  generateClaimSummaryFactory,
  mutateClaimFeedbackFactory,
  mutateFNOLFactory,
  upsertClaimSummaryFeedbackFactory,
} from '../support/factories/claims';
import { NO_CLAIMS_DOT_NUMBER } from '../support/factories/fleetDetails';
import { fnolsFactory } from '../support/factories/fnols';

export const ClaimIdForClosedClaim = 'ab75234b-9fb8-4684-b56f-451f26329833';
export const ClaimIdForOpenClaim = 'ab75234b-9fb8-4684-b56f-451f26329834';
export const claimIdForOpenWithoutNewNotes =
  'ab75234b-9fb8-4684-b56f-451f26329835';
export const ClaimIdForSnapsheet = '7f8a9b2c-4d5e-6f70-8192-a3b4c5d6e7f8';

export const claims = (req: CyHttpMessages.IncomingHttpRequest) => {
  const isNoResultsUUID =
    getVariable(req, 'dotNumber') === NO_CLAIMS_DOT_NUMBER;

  if (isNoResultsUUID) {
    return claimsByDOTNumberFactory.build({ data: undefined });
  }

  return claimsByDOTNumberFactory.build();
};

export const mutateFeedback = () => {
  return mutateClaimFeedbackFactory.build();
};

export const fnols = () => {
  return fnolsFactory.build();
};

export const mutateFNOL = () => {
  return mutateFNOLFactory.build();
};

export const claimById = (req: CyHttpMessages.IncomingHttpRequest) => {
  const claimId = getVariable(req, 'id');
  const transientParams: Partial<ClaimByIdQuery['claimById']> = {};
  if (claimId === ClaimIdForClosedClaim) {
    transientParams.status = ClaimStatus.Closed;
  }
  if ([ClaimIdForOpenClaim, claimIdForOpenWithoutNewNotes].includes(claimId)) {
    transientParams.status = ClaimStatus.Open;
  }
  if (claimId === claimIdForOpenWithoutNewNotes) {
    transientParams.hasNotesSinceLastScheduleSummary = false;
  }
  if (claimId === ClaimIdForSnapsheet) {
    transientParams.status = ClaimStatus.Open;
    transientParams.source = ClaimsProvider.Snapsheet;
    transientParams.hasNotesSinceLastScheduleSummary = false;
  }
  return claimByIdFactory.build({}, { transient: transientParams });
};

export const upsertClaimSummaryFeedback = (
  req: CyHttpMessages.IncomingHttpRequest,
) => {
  const claimSummaryId = getVariable(req, 'claimSummaryId');
  return upsertClaimSummaryFeedbackFactory.build(
    {},
    {
      transient: { claimSummaryId },
    },
  );
};

export const generateClaimSummary = () => {
  return generateClaimSummaryFactory.build();
};
