import React, { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { FileRejection } from 'react-dropzone';
import { zodResolver } from '@hookform/resolvers/zod';
import useAuthContext from 'hooks/use-auth-context';
import { useSnackbar } from 'notistack';

import {
  DraftFnolByIdQuery,
  DraftFnolVehicle,
  FnolNoticeType,
  FnolSource,
  PolicyState,
  useClaimsPresignedUploadLinksLazyQuery,
  useDraftFnolByIdQuery,
  usePoliciesQuery,
} from 'types/graphql-types';
import {
  Attachment,
  ClaimSchema,
  ClaimType,
  FnolSourceEnum,
  NewClaimContext,
} from '@nirvana/ui-kit/src/components/NewFnolForm/contexts/newClaim';
import { acceptedFiles } from '@nirvana/ui-kit/src/components/NewFnolForm/steps/FileUploadDialog';
import { USState } from '@nirvana/api/forms';

const acceptedExtensions = Object.values(acceptedFiles).flat().join(', ');

const SUBMITTED_FROM_MAPPER: Record<FnolSource, FnolSourceEnum> = {
  [FnolSource.FinolaEmail]: FnolSourceEnum.Finola,
  [FnolSource.FinolaAutoSubmit]: FnolSourceEnum.FinolaAutoSubmit,
  [FnolSource.SafetyApp]: FnolSourceEnum.Safety,
  [FnolSource.SupportApp]: FnolSourceEnum.Support,
  [FnolSource.Unknown]: FnolSourceEnum.Unknown,
};

export const NewClaimProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const {
    formState,
    getValues,
    handleSubmit,
    register,
    resetField,
    setValue,
    watch,
  } = useForm<ClaimType>({
    mode: 'onChange',
    resolver: zodResolver(ClaimSchema),
    defaultValues: {
      reporter: {
        firstName: '',
        lastName: '',
        phone: '',
        email: '',
      },
      lossDate: new Date(),
      police: {
        onTheScene: 'no',
      },
      otherVehiclesInvolved: 'yes',
      ownVehiclesInvolved: 'yes',
      noticeType: 'initiateClaim',
      insuredVehicleVins: [''],
      otherVehicleRegistrationNumbers: [''],
      attachments: [],
      source: FnolSourceEnum.Support,
    },
  });
  const [prefillingForm, setPrefillingForm] = useState<boolean>(false);
  const [rejectedAttachments, setRejectedAttachments] = useState<
    FileRejection[]
  >([]);
  const { draftFnolId } = useParams<{ draftFnolId: string }>();
  const { enqueueSnackbar } = useSnackbar();

  const { user } = useAuthContext();

  useEffect(() => {
    if (!user) {
      return;
    }
    if (user.name) {
      const [firstName, lastName] = user.name.split(' ');
      setValue('reporter.firstName', firstName);
      setValue('reporter.lastName', lastName);
    }
    if (user.email) {
      setValue('reporter.email', user.email);
    }
  }, [user, setValue]);

  const lossDate = watch('lossDate');

  const { data: policiesData, loading: isLoadingPolicies } = usePoliciesQuery({
    skip: !lossDate,
    variables: {
      activeDateIn: lossDate.toISOString(),
      policyStates: [
        PolicyState.Active,
        PolicyState.Expired,
        PolicyState.CancellationFiled,
      ],
    },
  });

  const [getPresignedUploadLinks] = useClaimsPresignedUploadLinksLazyQuery();

  const signFiles = useCallback(
    async (files: File[]) => {
      const { data, error } = await getPresignedUploadLinks({
        variables: {
          fileNames: files.map(({ name }) => name),
        },
      });
      if (error) {
        throw error;
      }
      return (data?.claimsPresignedUploadLinks ?? []).map(({ url, key }) => ({
        url,
        key,
      }));
    },
    [getPresignedUploadLinks],
  );

  const fetchFile = async (url: string, key: string): Promise<File> => {
    const response = await fetch(url);
    const blob = await response.blob();
    const filename = key.split('/').pop() || 'unknown';
    return new File([blob], filename, { type: blob.type });
  };

  const initForm = async (data: DraftFnolByIdQuery) => {
    setPrefillingForm(true);
    if (!data?.draftFnolById) {
      enqueueSnackbar('Draft FNOL not found', {
        variant: 'error',
      });
      return;
    }

    const draftFnol = data.draftFnolById;
    if (draftFnol.contacts?.length) {
      const { firstName, lastName, phone, email } = draftFnol.contacts[0];
      setValue('reporter.firstName', firstName ?? '');
      setValue('reporter.lastName', lastName ?? '');
      setValue('reporter.phone', phone ?? '');
      setValue('reporter.email', email ?? '');
    }
    if (draftFnol.noticeType) {
      if (draftFnol.noticeType === FnolNoticeType.Claim) {
        setValue('noticeType', 'initiateClaim');
      }
      if (draftFnol.noticeType === FnolNoticeType.Report) {
        setValue('noticeType', 'onlyReporting');
      }
    }
    if (draftFnol.lossDatetime) {
      setValue('lossDate', new Date(draftFnol.lossDatetime));
    }
    if (draftFnol.lossState) {
      const lossState = draftFnol.lossState as USState;
      setValue('lossState', lossState);
    }
    if (draftFnol.lossLocation) {
      setValue('lossLocation', draftFnol.lossLocation);
    }
    if (draftFnol.policyNumber) {
      setValue('policyNumber', draftFnol.policyNumber);

      if (draftFnol.isTestPolicy) {
        setValue('isSandboxFlow', draftFnol.isTestPolicy);
      }
    }
    if (draftFnol.insuredName) {
      setValue('insuredName', draftFnol.insuredName);
    }
    const insuredVehicleVins: string[] = (draftFnol.vehicles ?? [])
      .filter(
        (vehicle): vehicle is DraftFnolVehicle =>
          !!vehicle && (vehicle as DraftFnolVehicle).isInsuredVehicle === true,
      )
      .map(
        (vehicle: DraftFnolVehicle) =>
          vehicle.vin || vehicle.registrationNumber || '',
      );
    if (insuredVehicleVins.length) {
      setValue('ownVehiclesInvolved', 'yes');
      setValue('insuredVehicleVins', insuredVehicleVins);
    } else {
      setValue('ownVehiclesInvolved', 'no');
    }
    const otherVehicleRegistrationNumbers = draftFnol.vehicles
      ?.filter(
        (vehicle): vehicle is DraftFnolVehicle =>
          !!vehicle && (vehicle as DraftFnolVehicle).isInsuredVehicle === false,
      )
      .map(
        (vehicle: DraftFnolVehicle) =>
          vehicle.registrationNumber || vehicle.vin || '',
      );
    if (otherVehicleRegistrationNumbers.length) {
      setValue('otherVehiclesInvolved', 'yes');
      setValue(
        'otherVehicleRegistrationNumbers',
        otherVehicleRegistrationNumbers,
      );
    } else {
      setValue('otherVehiclesInvolved', 'no');
    }
    if (draftFnol.policeAgencyName || draftFnol.policeReportNumber) {
      setValue('police.onTheScene', 'yes');
      if (draftFnol.policeAgencyName) {
        setValue('police.agencyName', draftFnol.policeAgencyName ?? '');
      }
      if (draftFnol.policeReportNumber) {
        setValue('police.reportNumber', draftFnol.policeReportNumber ?? '');
      }
    } else {
      setValue('police.onTheScene', 'no');
    }
    if (draftFnol.injuriesInvolved === true) {
      setValue('injureds', 'yes');
    } else if (draftFnol.injuriesInvolved === false) {
      setValue('injureds', 'no');
    }
    if (draftFnol.incidentDescription) {
      setValue('description', draftFnol.incidentDescription);
    }
    if (draftFnol.attachments?.length) {
      const attachments = [];
      const rejected = [];
      for (let i = 0; i < draftFnol.attachments.length; i++) {
        const { key, url } = draftFnol.attachments[i];
        const file = await fetchFile(url, key);
        const attachment: Attachment = {
          downloadUrl: url,
          file: file,
          key: key,
          uploadStatus: 'success',
        };
        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        if (fileExtension && acceptedExtensions.includes(fileExtension)) {
          attachments.push(attachment);
        } else {
          const rejection: FileRejection = {
            file,
            errors: [
              { code: 'file-invalid-type', message: 'Invalid file type' },
            ],
          };
          rejected.push(rejection);
        }
      }
      setValue('attachments', attachments);
      setRejectedAttachments(rejected);
    }
    if (draftFnol.submittedFrom) {
      setValue('source', SUBMITTED_FROM_MAPPER[draftFnol.submittedFrom]);
    }
    setPrefillingForm(false);
  };

  const { data, loading: isLoadingDraftFnol } = useDraftFnolByIdQuery({
    skip: !draftFnolId,
    variables: { id: draftFnolId ?? '' },
    onCompleted: initForm,
    onError: () => {
      enqueueSnackbar('There was an error retrieving the draft FNOL', {
        variant: 'error',
      });
    },
  });

  const viewAllClaimsLink = '/claims/fnol';
  const noErrors = Object.keys(formState.errors).length === 0;

  return (
    <NewClaimContext.Provider
      value={{
        formState,
        getValues,
        handleSubmit,
        register,
        resetField,
        setValue,
        watch,
        canSubmit: noErrors && !data?.draftFnolById?.archivedAt,
        isArchived: Boolean(data?.draftFnolById?.archivedAt),
        isLoadingDraftFnol,
        prefillingForm,
        isLoadingPolicies,
        fnolId: data?.draftFnolById?.fnolId ?? undefined,
        policies: policiesData?.policies,
        signFiles,
        viewAllClaimsLink,
        rejectedAttachments,
      }}
    >
      {children}
    </NewClaimContext.Provider>
  );
};
