mutation submitFnol(
  $policyNumber: string!
  $description: string
  $noticeType: FnolNoticeType!
  $lossLocation: string
  $lossState: string!
  $lossDate: Time!
  $injuriesInvolved: bool
  $police: Police_InputObject
  $reporter: Reporter_InputObject!
  $insuredVehicles: [ClaimVehicle_InputObject!]
  $otherVehicles: [ClaimVehicle_InputObject!]
  $insuredName: string
  $attachmentKeys: [string!]
  $draftFnolId: string
  $source: FnolSource
  $provider: ClaimsProvider
) {
  submitFnol(
    policyNumber: $policyNumber
    description: $description
    noticeType: $noticeType
    lossLocation: $lossLocation
    lossState: $lossState
    lossDate: $lossDate
    injuriesInvolved: $injuriesInvolved
    police: $police
    reporter: $reporter
    insuredVehicles: $insuredVehicles
    otherVehicles: $otherVehicles
    insuredName: $insuredName
    attachmentKeys: $attachmentKeys
    draftFnolId: $draftFnolId
    source: $source
    provider: $provider
  ) {
    id
    externalId
    url
  }
}
