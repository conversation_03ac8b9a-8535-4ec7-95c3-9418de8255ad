import { useCallback } from 'react';
import { Attachment, ClaimType } from '@nirvana/ui-kit';
import { format } from 'date-fns';
import { useSnackbar } from 'notistack';
import { useParams } from 'react-router-dom';
import { ApolloError } from '@apollo/client';
import {
  ClaimsProvider,
  FnolNoticeType,
  FnolSource,
  MutationSubmitFnolArgs,
  useCreateFnolMutation,
  useGetSubmittableProviderOptionsLazyQuery,
  useSubmitFnolMutation,
} from 'types/graphql-types';
import { getGqlErrorMessages } from 'utils/apollo';
import { Feature, useFeatureEnabled } from 'utils/feature-flags';
import { FnolSourceEnum } from '@nirvana/ui-kit/src/components/NewFnolForm/contexts/newClaim';

const noticeTypes: Record<ClaimType['noticeType'], FnolNoticeType> = {
  initiateClaim: FnolNoticeType.Claim,
  onlyReporting: FnolNoticeType.Report,
};

export const sources: Record<FnolSourceEnum, FnolSource> = {
  [FnolSourceEnum.Finola]: FnolSource.FinolaEmail,
  [FnolSourceEnum.FinolaAutoSubmit]: FnolSource.FinolaAutoSubmit,
  [FnolSourceEnum.Safety]: FnolSource.SafetyApp,
  [FnolSourceEnum.Support]: FnolSource.SupportApp,
  [FnolSourceEnum.Unknown]: FnolSource.Unknown,
};

function isPolicyNotFoundError(error: ApolloError): boolean {
  return getGqlErrorMessages(error)?.some((message) =>
    message.includes('failed to fetch policy'),
  );
}

export function formDataToMutationVariables(
  data: ClaimType,
): MutationSubmitFnolArgs {
  const variables: MutationSubmitFnolArgs = {
    policyNumber: data.policyNumber,
    description: data.description,
    insuredName: data.insuredName,
    noticeType: noticeTypes[data.noticeType],
    lossLocation: data.lossLocation,
    lossState: data.lossState,
    lossDate: format(data.lossDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
    police: {
      agencyName: data.police.agencyName,
      reportNumber: data.police.reportNumber,
    },
    reporter: {
      firstName: data.reporter.firstName,
      lastName: data.reporter.lastName,
      phone: data.reporter.phone,
      email: data.reporter.email,
    },
    insuredVehicles: data.insuredVehicleVins
      .filter((vin: string) => vin)
      .map((vin: string) => ({
        vin: vin,
      })),
    otherVehicles: data.otherVehicleRegistrationNumbers
      .filter((registrationNumber: string) => registrationNumber)
      .map((registrationNumber: string) => ({
        registrationNumber,
      })),
    attachmentKeys: data.attachments.map((att: Attachment) => att.key || ''),
    source:
      data.source === FnolSourceEnum.Unknown
        ? FnolSource.SupportApp
        : sources[data.source],
  };
  if (data.injureds) {
    variables.injuriesInvolved = data.injureds === 'yes';
  }
  return variables;
}

interface Props {
  onCompleted: () => void;
}

export function useProvider(): {
  getProvider: (
    policyNumber: string,
    isSandboxFlow: boolean,
  ) => Promise<ClaimsProvider>;
} {
  const isSnapsheetEnabled = useFeatureEnabled(Feature.SNAPSHEET_CLAIMS);
  const [getSubmittableProviderOptions] =
    useGetSubmittableProviderOptionsLazyQuery();

  const getProvider = useCallback(
    async (
      policyNumber: string,
      isSandboxFlow: boolean,
    ): Promise<ClaimsProvider> => {
      // Every test policy will be sent to Snapsheet, regardless of the feature flag's value.
      if (isSandboxFlow) {
        return ClaimsProvider.Snapsheet;
      }

      // If the feature flag is disabled, use previous behavior: always send to NARS.
      if (!isSnapsheetEnabled) {
        return ClaimsProvider.Nars;
      }

      const submittableProviderOptions = await getSubmittableProviderOptions({
        variables: {
          policyNumber: policyNumber,
        },
      });

      const availableProviders =
        submittableProviderOptions.data?.getSubmittableProviderOptions;

      // Ensure Snapsheet is chosen if both Snapsheet and NARS are enabled (FF on)
      return availableProviders?.snapsheet
        ? ClaimsProvider.Snapsheet
        : ClaimsProvider.Nars;
    },
    [isSnapsheetEnabled, getSubmittableProviderOptions],
  );

  return { getProvider };
}

export function useSubmitFnol({ onCompleted }: Props) {
  const { enqueueSnackbar } = useSnackbar();
  const { draftFnolId } = useParams<{ draftFnolId: string }>();
  const { getProvider } = useProvider();

  const onCreateError = (error: ApolloError) => {
    const message = isPolicyNotFoundError(error)
      ? 'Policy Number is not valid or does not exist'
      : 'Error submitting claim';
    enqueueSnackbar(message, {
      variant: 'error',
    });
  };

  const [submitFnol, { loading: isSubmittingFnol }] = useSubmitFnolMutation({
    onCompleted: ({ submitFnol }) => {
      if (submitFnol?.url) {
        enqueueSnackbar(
          `Successfully created on Snapsheet (${submitFnol?.url})`,
          {
            variant: 'success',
          },
        );
      }
      onCompleted();
    },
    onError: onCreateError,
  });

  const [legacyCreateFNOL, { loading: isSubmittingLegacyFnol }] =
    useCreateFnolMutation({
      onCompleted,
      onError: onCreateError,
    });

  async function handleSubmit(data: ClaimType): Promise<void> {
    let provider = data.overrideProvider;

    if (provider === ClaimsProvider.Undetermined) {
      provider = await getProvider(data.policyNumber, data.isSandboxFlow);
    }

    const submitFn =
      provider === ClaimsProvider.Snapsheet ? submitFnol : legacyCreateFNOL;

    await submitFn({
      variables: {
        ...formDataToMutationVariables(data),
        draftFnolId,
        provider,
      },
    });
  }

  return {
    handleSubmit,
    isSubmitting: isSubmittingFnol || isSubmittingLegacyFnol,
  };
}
