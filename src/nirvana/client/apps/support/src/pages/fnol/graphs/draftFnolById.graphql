query draftFnolById($id: string!) {
    draftFnolById(id: $id) {
        id
        dotNumber
        policyNumber
        isTestPolicy
        insuredName
        lossDatetime
        lossLocation
        lossState
        policeAgencyName
        policeReportNumber
        incidentDescription
        noticeType
        injuriesInvolved
        archivedAt
        submittedFrom
        attachments {
            handleId
            key
            url
        }
        contacts {
            firstName
            lastName
            phone
            email
        }
        vehicles {
            isInsuredVehicle
            vin
            registrationNumber
        }
        fnolId
    }
}
