import { CoverageRunWithNotes, NoteFeedback } from '@nirvana/api/claims_agent';
import { claimsAgentClient } from 'utils';

// TODO(AII-55): remove this exclusion after rush<PERSON> finishes doing the demos
const CLAIMS_TO_SKIP_REFETCH = ['NITSP25070016', 'NITSP25070017'];

export async function fetchVerificationData(
  externalClaimId: string,
  forceRefresh: boolean,
): Promise<CoverageRunWithNotes> {
  const shouldForceRefetch =
    forceRefresh && !CLAIMS_TO_SKIP_REFETCH.includes(externalClaimId);

  if (CLAIMS_TO_SKIP_REFETCH.includes(externalClaimId)) {
    const { data } =
      await claimsAgentClient.getCoverageDeterminationApiClaimsClaimIdCoverageDeterminationGet(
        externalClaimId,
        undefined,
        shouldForceRefetch,
      );

    return {
      created_at: new Date().toISOString(),
      created_by: 'old-endpoint',
      coverage_notes: data.verifications?.map((verification) => ({
        note_id: verification.name,
        original_content: verification,
      })),
    };
  }

  if (shouldForceRefetch) {
    const { data } =
      await claimsAgentClient.getCoverageDeterminationV2V2ClaimsClaimIdCoverageDeterminationGet(
        externalClaimId,
      );

    return data;
  }

  const { data } =
    await claimsAgentClient.getCachedCoverageDeterminationsV2ClaimsClaimIdCoverageDeterminationCachedGet(
      externalClaimId,
    );

  if (data.length === 0) {
    const { data } =
      await claimsAgentClient.getCoverageDeterminationV2V2ClaimsClaimIdCoverageDeterminationGet(
        externalClaimId,
      );

    return data;
  }

  return data[0];
}

export async function upsertCoverageFeedback(noteFeedback: NoteFeedback) {
  const { data } =
    await claimsAgentClient.upsertCoverageFeedbackClaimsCoverageFeedbackPost({
      feedback: [noteFeedback],
    });
  return data;
}
