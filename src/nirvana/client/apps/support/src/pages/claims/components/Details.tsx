import clsx from 'clsx';
import { useEffect } from 'react';
import {
  Chip,
  CopyToClipboard,
  getProviderInfoFromSafety,
  Show,
  Switch,
  TableTabs,
} from '@nirvana/ui-kit';
import { formatDateAgo, getFormattedDate } from '@nirvana/core/utils';
import { IconType } from 'react-icons';
import { FaArrowLeft } from 'react-icons/fa';
import { CircularProgress, IconButton } from '@material-ui/core';
import { Link, Navigate, useNavigate, useParams } from 'react-router-dom';
import {
  ClaimStatus,
  RoleGroupEnum,
  Tsp,
  useClaimByIdQuery,
  useFleetSafetyReportQuery,
} from 'types/graphql-types';
import {
  MdNumbers,
  MdOutlineCalendarMonth,
  MdOutlineWatchLater,
  MdPersonOutline,
} from 'react-icons/md';
import { useSessionStorage } from 'usehooks-ts';
import { HiLog<PERSON>, HiLogout, HiOutlineDocumentSearch } from 'react-icons/hi';

import { useIncludesRole } from 'hooks/useIncludesRoles';
import { Feature, useFeatureEnabled } from 'utils/feature-flags';
import { FaRegBuilding } from 'react-icons/fa6';
import { useClaimsContext } from '../hooks/useClaims';
import { useClaimsIQAccesibility } from '../hooks/useClaimsIQAccesibility';
import { StatusChip } from './StatusChip';
import { CameraEvents } from './CameraEvents/CameraEvents';
import { ClaimActivity } from './ClaimActivity';
import { Policy } from './Policy';
import ClaimIQ from './ClaimIQ';
import Timeline from './Timeline';

enum ClaimActivityTab {
  ClaimIQ = 'claimIQ',
  ClaimsActivity = 'activity',
  TelematicsData = 'telematics',
  Policy = 'policy',
}

type ClaimDetailProps = {
  Icon?: IconType;
  name: string;
  value?: string;
  href?: string;
  className?: string;
  helper?: string;
  fastCopy?: boolean;
};

const ClaimDetail = ({
  Icon,
  name,
  value,
  href,
  className,
  helper,
  fastCopy,
}: ClaimDetailProps) => {
  if (!value) {
    return null;
  }

  return (
    <div className="flex items-center gap-1 py-1">
      {Icon ? (
        <div>
          <Icon className=" text-text-hint" />
        </div>
      ) : null}
      <h4>{name}</h4>
      <span
        className={clsx('text-secondary-main flex', className)}
        title={helper}
      >
        <Show when={href} fallback={value}>
          <a href={href} target="_blank" rel="noreferrer">
            {value}
          </a>
        </Show>
        <Show when={fastCopy}>
          <CopyToClipboard
            copyText={value}
            snackBarMessage="Copied to clipboard"
          />
        </Show>
      </span>
    </div>
  );
};

const getActiveTab = (tab: string | undefined) => {
  if (
    [
      ClaimActivityTab.ClaimIQ,
      ClaimActivityTab.Policy,
      ClaimActivityTab.TelematicsData,
      ClaimActivityTab.ClaimsActivity,
    ].includes(tab as ClaimActivityTab)
  ) {
    return tab as ClaimActivityTab;
  }
  return ClaimActivityTab.ClaimsActivity;
};

const getTelematicsDataParams = (
  isSafetyReportLoading: boolean,
  tspProvider: Tsp | undefined,
) => {
  const label = 'Telematics & Camera Data';

  const isTspSupported =
    isSafetyReportLoading || !tspProvider
      ? true
      : tspProvider
        ? ['Samsara', 'Motive'].includes(
            getProviderInfoFromSafety(tspProvider)?.name,
          )
        : false;
  return {
    label: (
      <div
        className="flex items-center gap-2"
        data-telematics-support-loading={isSafetyReportLoading}
      >
        {label}{' '}
        <Show when={isSafetyReportLoading}>
          <CircularProgress size={16} />
        </Show>
      </div>
    ),
    isTspSupported,
  };
};

export const Details = () => {
  const navigate = useNavigate();
  const {
    isDocumentViewerOpen,
    isClaimListOpen,
    setIsClaimListOpen,
    setIsDocumentViewerOpen,
  } = useClaimsContext();

  const { claimId, tab } = useParams<{ claimId: string; tab: string }>();

  const [, setSidebarOpen] = useSessionStorage<boolean | null>(
    'sidebar-open',
    null,
  );
  const showPolicyTab = useFeatureEnabled(Feature.CLAIMS_POLICY_VIEWER);

  const { data, loading: isLoading } = useClaimByIdQuery({
    variables: { id: claimId as string },
  });

  const { isEnabledForClaim } = useClaimsIQAccesibility();

  const { data: safetyReportData, loading: isSafetyReportLoading } =
    useFleetSafetyReportQuery({
      variables: {
        dotNumber: data?.claimById?.policy?.insuredDOTNumber ?? '',
      },
      skip: !data?.claimById?.policy?.insuredDOTNumber,
    });

  const telematicsDataParams = getTelematicsDataParams(
    isSafetyReportLoading,
    safetyReportData?.fleetSafetyReport?.TspProvider,
  );

  const claimDetails = data?.claimById;

  const hasPermissionToSeeFootage = useIncludesRole(
    RoleGroupEnum.SuperuserRole,
    RoleGroupEnum.ClaimsAdminRole,
  );

  const activeTab = getActiveTab(tab);

  useEffect(() => {
    if (activeTab === ClaimActivityTab.Policy) {
      setSidebarOpen(false);
    }
  }, [activeTab, setSidebarOpen]);

  if (
    // TODO(IE-967): rely on telematicsDataParams.isTspSupported once we fix permissions to fetch
    // TSPs for claims adjusters.
    activeTab === ClaimActivityTab.TelematicsData &&
    !hasPermissionToSeeFootage
  ) {
    return (
      <Navigate
        to={window.location.pathname.replace('telematics', 'activity')}
      />
    );
  }

  function toggleClaimList() {
    if (isClaimListOpen) {
      setIsClaimListOpen(false);
    } else {
      setIsClaimListOpen(true);
      setIsDocumentViewerOpen(false);
    }
  }

  return (
    <section
      data-testid="claims-details-container"
      className={clsx(
        'absolute top-0 left-0 w-full h-screen pb-6 px-6 bg-white lg:px-24 lg:h-full lg:relative lg:flex lg:flex-col lg:w-full overflow-y-auto',
        isDocumentViewerOpen || isClaimListOpen
          ? 'lg:col-span-2'
          : 'lg:col-span-3',
      )}
    >
      <Show when={claimDetails}>
        <div className="hidden my-4 md:block">
          <Chip color="tint" label={claimDetails?.lineOfBusiness} />
        </div>
      </Show>
      <nav className="sticky top-0 z-50 flex items-center w-full py-4 text-base bg-white">
        <Link
          to="/claims/list"
          className="mr-2 lg:hidden"
          data-testid="back-to-claims-list"
        >
          <FaArrowLeft />
        </Link>
        <div className="absolute hidden top-2 -left-16 lg:block">
          <IconButton
            onClick={toggleClaimList}
            aria-label={`${isClaimListOpen ? 'Hide' : 'Show'} sidebar`}
            data-testid="claim-list-toggle"
            aria-expanded={isClaimListOpen}
          >
            <Show when={!isClaimListOpen} fallback={<HiLogin aria-hidden />}>
              <HiLogout aria-hidden />
            </Show>
          </IconButton>
        </div>
        <Show when={claimDetails}>
          <h1 className="lg:text-xl">
            <strong>Claim</strong> #{claimDetails?.claimNumber}{' '}
            <StatusChip status={claimDetails?.status || ClaimStatus.Open} />
          </h1>
        </Show>
      </nav>
      <section className="flex-1 h-full">
        <Switch>
          <Switch.Match when={isLoading}>
            <div className="flex items-center justify-center h-full">
              <CircularProgress />
            </div>
          </Switch.Match>
          <Switch.Match when={!claimDetails}>
            <div className="flex flex-col items-center justify-center h-full p-4 text-center">
              <h2 className="text-xl font-bold">
                The claim you are looking for does not exist
              </h2>
              <p> Try selecting another claim</p>
            </div>
          </Switch.Match>
          <Switch.Match when={claimDetails}>
            <section data-testid="claim-details" className="pt-3 pb-7">
              <div className="flex flex-wrap gap-x-8">
                <ClaimDetail
                  Icon={MdOutlineWatchLater}
                  name="Updated"
                  helper={
                    claimDetails?.modifiedAt
                      ? getFormattedDate(
                          claimDetails?.modifiedAt,
                          'MMM dd yyyy',
                        )
                      : ''
                  }
                  value={
                    claimDetails?.modifiedAt
                      ? formatDateAgo(claimDetails?.modifiedAt)
                      : 'Unknown'
                  }
                />
                <ClaimDetail
                  Icon={MdOutlineCalendarMonth}
                  name="Created"
                  value={
                    claimDetails?.reportedAt
                      ? getFormattedDate(
                          claimDetails?.reportedAt,
                          'MMM dd yyyy',
                        )
                      : 'Unknown'
                  }
                />
                <ClaimDetail
                  Icon={FaRegBuilding}
                  name="Insured"
                  key={`${claimDetails?.policy?.insuredName} #${claimDetails?.policy?.insuredDOTNumber}`}
                  value={`${claimDetails?.policy?.insuredName} #${claimDetails?.policy?.insuredDOTNumber}`}
                />
              </div>
              <div className="flex flex-wrap gap-x-8">
                <ClaimDetail
                  Icon={MdPersonOutline}
                  name="Submitted By"
                  className="capitalize"
                  value={claimDetails?.reportedBy?.toLocaleLowerCase() || '-'}
                />
                <ClaimDetail
                  Icon={MdNumbers}
                  name="Policy ID"
                  fastCopy
                  value={claimDetails?.policyNumber}
                />
                <ClaimDetail
                  Icon={HiOutlineDocumentSearch}
                  name="Adjuster"
                  href={`mailto:${claimDetails?.adjusterEmail}`}
                  value={claimDetails?.adjusterName}
                />
              </div>
            </section>
            <Timeline />
            <nav>
              <TableTabs
                value={activeTab}
                onChange={(newTab) => {
                  navigate(
                    `/claims/list/${claimId}/${newTab}${location.search ?? ''}`,
                    { preventScrollReset: true },
                  );
                }}
                tabs={[
                  ...(isEnabledForClaim(claimDetails ?? undefined)
                    ? [
                        {
                          label: 'ClaimIQ',
                          value: ClaimActivityTab.ClaimIQ,
                          id: 'claimiq',
                        },
                      ]
                    : []),
                  {
                    label: 'Activity',
                    value: ClaimActivityTab.ClaimsActivity,
                    id: 'claims-activity',
                  },
                  ...(hasPermissionToSeeFootage
                    ? [
                        {
                          label: telematicsDataParams.label,
                          value: ClaimActivityTab.TelematicsData,
                          id: 'telematics',
                        },
                      ]
                    : []),
                  ...(showPolicyTab
                    ? [
                        {
                          label: 'Policy',
                          value: ClaimActivityTab.Policy,
                          id: 'policy',
                        },
                      ]
                    : []),
                ]}
              />
            </nav>
            <Switch>
              <Switch.Match when={activeTab === ClaimActivityTab.ClaimIQ}>
                {claimDetails?.externalId && (
                  <ClaimIQ externalClaimId={claimDetails.externalId} />
                )}
              </Switch.Match>
              <Switch.Match
                when={activeTab === ClaimActivityTab.ClaimsActivity}
              >
                <ClaimActivity notes={claimDetails?.notes ?? []} />
              </Switch.Match>
              <Switch.Match
                when={activeTab === ClaimActivityTab.TelematicsData}
              >
                <CameraEvents
                  claim={{
                    policyNumber: claimDetails?.policyNumber ?? '',
                    reportedAt: claimDetails?.reportedAt ?? '',
                  }}
                />
              </Switch.Match>
              <Switch.Match when={activeTab === ClaimActivityTab.Policy}>
                <Policy policyNumber={claimDetails?.policyNumber} />
              </Switch.Match>
            </Switch>
          </Switch.Match>
        </Switch>
      </section>
    </section>
  );
};
