import {
  NoteFeedback,
  VerificationItemStatusEnum,
} from '@nirvana/api/claims_agent';
import { Button, Tooltip } from '@nirvana/ui';
import { queryClient } from 'Layout';
import {
  fetchVerificationData,
  upsertCoverageFeedback,
} from 'pages/claims/queries';
import React from 'react';
import { AiOutlineLoading } from 'react-icons/ai';
import {
  HiOutlineCheckCircle,
  HiOutlineArchive,
  HiOutlinePencil,
  HiCheckCircle,
} from 'react-icons/hi';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useClaimsContext } from 'pages/claims/hooks/useClaims';

const getUpsertCoverageFeedbackPayload = (
  noteId: string,
  status: VerificationItemStatusEnum,
  name: string,
  summary: string,
): NoteFeedback => {
  return {
    note_id: noteId,
    modified_content: {
      status,
      name,
      summary,
    },
  };
};

const Icon = ({
  isLoading,
  EmptyIcon,
  FilledIcon,
  isFilled,
}: {
  isLoading: boolean;
  EmptyIcon: React.ElementType;
  FilledIcon: React.ReactNode;
  isFilled: boolean;
}) => {
  if (isLoading) {
    return <AiOutlineLoading className="animate-spin" />;
  }

  return isFilled ? FilledIcon : <EmptyIcon />;
};

export const Actions = ({
  noteId,
  externalClaimId,
}: {
  noteId: string;
  externalClaimId: string;
}) => {
  const { isUpdatingNote } = useClaimsContext();
  const { mutate: updateNote, isLoading } = useMutation({
    mutationFn: upsertCoverageFeedback,
    onSuccess: () => {
      isUpdatingNote.current = noteId;
      queryClient.invalidateQueries(
        ['verificationData', externalClaimId],
        undefined,
        {},
      );
    },
  });

  const { isRefetching, data } = useQuery(
    ['verificationData', externalClaimId],
    () => fetchVerificationData(externalClaimId, false),
    {
      enabled: false,
    },
  );
  const { original_content, modified_content } =
    data?.coverage_notes?.find((note) => note.note_id === noteId) || {};
  const { name, summary, status } = modified_content ||
    original_content || {
      name: '',
      summary: '',
      status: VerificationItemStatusEnum.Undefined,
    };

  const toggleAcceptNote = () => {
    updateNote(
      getUpsertCoverageFeedbackPayload(
        noteId,
        status === VerificationItemStatusEnum.Accept
          ? VerificationItemStatusEnum.Undefined
          : VerificationItemStatusEnum.Accept,
        name,
        summary,
      ),
    );
  };

  const isProcessing =
    isLoading || (isRefetching && isUpdatingNote.current === noteId);

  return (
    <Tooltip>
      <div className="ml-8">
        <div className="flex items-center gap-2">
          <Tooltip>
            <Tooltip.Trigger asChild>
              <Button
                variant="text"
                size="icon"
                data-testid="accept-note-button"
                data-cy-status={status}
                onClick={toggleAcceptNote}
                disabled={isProcessing}
                startIcon={
                  <Icon
                    isLoading={isProcessing}
                    EmptyIcon={HiOutlineCheckCircle}
                    FilledIcon={<HiCheckCircle className="text-green-600" />}
                    isFilled={status === VerificationItemStatusEnum.Accept}
                  />
                }
              />
            </Tooltip.Trigger>
            <Tooltip.Content>
              <p>
                {status === VerificationItemStatusEnum.Accept
                  ? 'Undo Accept'
                  : 'Accept'}
              </p>
            </Tooltip.Content>
          </Tooltip>
          <Tooltip.Trigger asChild>
            <div className="flex items-center cursor-not-allowed">
              <div className="border-r border-tw-border-secondary h-4" />
              <Button
                disabled
                variant="text"
                size="icon"
                startIcon={<HiOutlinePencil />}
              />
              <div className="border-r border-tw-border-secondary h-4" />
              <Button
                disabled
                variant="text"
                size="icon"
                startIcon={<HiOutlineArchive />}
              />
            </div>
          </Tooltip.Trigger>
        </div>
      </div>

      <Tooltip.Content>
        <p>Coming soon</p>
      </Tooltip.Content>
    </Tooltip>
  );
};
