import { Button, Show } from '@nirvana/ui';
import { useNavigate } from 'react-router-dom';
import { ReactNode } from 'react';
import { useClaimsContext } from '../hooks/useClaims';

const Unauthorized = () => {
  const navigate = useNavigate();
  return (
    <div className="flex flex-col items-center justify-center w-full h-full text-text-secondary gap-4">
      <p>You don't have permissions to access this page</p>
      <Button
        variant="primary"
        onClick={() => {
          navigate('/');
        }}
      >
        Go to home
      </Button>
    </div>
  );
};
export const PermissionChecker = ({ children }: { children: ReactNode }) => {
  const { hasPermissions } = useClaimsContext();
  return (
    <Show when={hasPermissions} fallback={<Unauthorized />}>
      {children}
    </Show>
  );
};
