import clsx from 'clsx';
import { useParams } from 'react-router-dom';
import { useEffect } from 'react';
import { Panel } from 'react-resizable-panels';
import { useClaimsContext } from '../hooks/useClaims';
import { Claims } from './Claims/Claims';
import { ClaimsControls } from './Claims/Controls';

export const List = () => {
  const { isClaimListOpen } = useClaimsContext();
  const { claimId } = useParams<{ claimId: string }>();
  const { setIsClaimListOpen } = useClaimsContext();

  useEffect(() => {
    if (!claimId) {
      setIsClaimListOpen(true);
    }
  }, [claimId, setIsClaimListOpen]);

  return (
    <Panel
      id="claim-list"
      order={1}
      defaultSize={25}
      className={clsx('flex flex-col border-r border-text-disabled', {
        'lg:hidden': !isClaimListOpen,
      })}
    >
      <div data-testid="claims-list">
        <ClaimsControls />
        <Claims />
      </div>
    </Panel>
  );
};
