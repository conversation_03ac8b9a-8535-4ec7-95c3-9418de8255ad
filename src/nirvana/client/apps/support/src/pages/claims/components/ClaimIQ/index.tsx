import { useRef, useState } from 'react';

import { Switch } from '@nirvana/ui-kit';
import { fetchVerificationData } from 'pages/claims/queries';
import { useQuery } from '@tanstack/react-query';
import { useClaimsContext } from 'pages/claims/hooks/useClaims';
import { useSnackbar } from 'notistack';
import Header from './header';
import EmptyState from './empty-state';
import ErrorState from './error-state';
import Determination from './determination';
import { Loading } from './Loading';

type AIOverviewProps = {
  externalClaimId: string;
};

export default function ClaimIQ({ externalClaimId }: AIOverviewProps) {
  const [enableClaimIQ, setEnableClaimIQ] = useState(false);
  const refetchData = useRef(false);
  const { enqueueSnackbar } = useSnackbar();
  const { loadingClaimIQs, setLoadingClaimIQs, isUpdatingNote } =
    useClaimsContext();

  // We use useQuery to persist the data in the cache
  // This is to avoid the loading state from being shown when the user
  // navigates back to the claim details page
  const {
    refetch,
    isInitialLoading: isLoading,
    isRefetching,
    isError,
    data,
  } = useQuery(
    ['verificationData', externalClaimId],
    () => fetchVerificationData(externalClaimId, refetchData.current),
    {
      retry: false,
      enabled: enableClaimIQ,
      onSuccess: () => {
        isUpdatingNote.current = false;
        setLoadingClaimIQs((prev) => {
          const newState = { ...prev };
          newState[externalClaimId].succeded = {
            startTime: newState[externalClaimId].current.startTime,
            finishTime:
              newState[externalClaimId].current.finishTime || new Date(),
          };
          return newState;
        });
      },
      onError: () => {
        if (data?.coverage_notes) {
          enqueueSnackbar('Error updating coverage notes', {
            variant: 'error',
          });
        }
      },
    },
  );

  const refetchAndResetLoadingState = async () => {
    setLoadingClaimIQs((prev) => {
      const newState = { ...prev };
      newState[externalClaimId].current = {
        startTime: new Date(),
        finishTime: undefined,
      };
      return newState;
    });
    refetchData.current = true;
    await refetch();
    refetchData.current = false;
  };

  const isSomethingLoading =
    isLoading || (isRefetching && !isUpdatingNote.current);

  return (
    <>
      <div className="py-8" data-testid="claim-iq-container">
        <Switch
          fallback={
            <EmptyState onTriggerClaimIQ={() => setEnableClaimIQ(true)} />
          }
        >
          <Switch.Match
            when={!data?.coverage_notes && isError && !isSomethingLoading}
          >
            <ErrorState onRetry={refetchAndResetLoadingState} />
          </Switch.Match>
          <Switch.Match
            when={enableClaimIQ || isSomethingLoading || data?.coverage_notes}
          >
            <Loading
              loading={isSomethingLoading}
              refetchError={isError}
              externalClaimId={externalClaimId}
            >
              <Header
                refetch={refetchAndResetLoadingState}
                lastRefreshedAt={data?.created_at}
                startTime={loadingClaimIQs[externalClaimId]?.succeded.startTime}
                finishTime={
                  loadingClaimIQs[externalClaimId]?.succeded.finishTime
                }
              />
              <Determination
                externalClaimId={externalClaimId}
                verificationData={data?.coverage_notes}
              />
            </Loading>
          </Switch.Match>
        </Switch>
      </div>
    </>
  );
}
