import { useParams } from 'react-router-dom';

import { Show } from '@nirvana/ui-kit';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';
import AppShell from 'components/app-shell';
import { ClaimsSectionTitle } from 'components/ClaimsSectionTitle';

import { List } from './components/List';
import { Details } from './components/Details';
import DocumentViewer from './components/ClaimIQ/document-viewer';
import { ClaimsProvider } from './hooks/useClaims';
import { PermissionChecker } from './components/PermissionChecker';

export const Claims = () => {
  const { claimId } = useParams<{ claimId: string }>();

  return (
    <ClaimsProvider>
      <AppShell usePadding={false}>
        <ClaimsSectionTitle title="Claims" />
        <PermissionChecker>
          <div className="h-full overflow-y-hidden">
            <PanelGroup direction="horizontal" autoSaveId="claim-details">
              <List />
              <PanelResizeHandle />

              <Panel id="claim-details" order={2}>
                <Show
                  when={claimId}
                  fallback={
                    <div className="flex items-center justify-center w-full h-full text-text-secondary">
                      Select a claim to view its details
                    </div>
                  }
                >
                  <Details />
                </Show>
              </Panel>
              <DocumentViewer />
            </PanelGroup>
          </div>
        </PermissionChecker>
      </AppShell>
    </ClaimsProvider>
  );
};
