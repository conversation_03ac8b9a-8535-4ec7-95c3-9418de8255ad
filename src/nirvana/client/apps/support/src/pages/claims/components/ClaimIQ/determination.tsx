import { HiExclamation, HiXCircle } from 'react-icons/hi';

import { HoverCard, Tag, Tooltip } from '@nirvana/ui';
import { Show, Switch } from '@nirvana/ui-kit';
import { Citation, CoverageNote } from '@nirvana/api/claims_agent';
import { useClaimsContext } from 'pages/claims/hooks/useClaims';
import clsx from 'clsx';
import { Actions } from './Actions';
import { Copy } from './Copy';

type DeterminationProps = {
  verificationData: CoverageNote[] | undefined;
  externalClaimId: string;
};

const mapDocumentToCitation = (coverage_notes: CoverageNote[]) => {
  let lastIndex = 1;
  return coverage_notes.reduce(
    (acc, coverage_note) => {
      const { original_content, note_id } = coverage_note;
      const { citation } = original_content;
      const { document } = citation || {};
      const key = document?.document_id || note_id;
      if (!acc[key]) {
        acc[key] = lastIndex;
        lastIndex++;
      }
      return acc;
    },
    {} as Record<string, number>,
  );
};

const Icon = ({
  assessment_score,
}: {
  assessment_score: number | null | undefined;
}) => {
  const PARTIAL_SCORE = 0.5;
  const FAILED_SCORE = 0.0;

  switch (assessment_score) {
    case PARTIAL_SCORE:
    case null:
      return (
        <Tooltip>
          <Tooltip.Trigger asChild>
            <span className="cursor-help">
              <HiExclamation className="mt-0.5 text-lg text-warning-main shrink-0" />
            </span>
          </Tooltip.Trigger>
          <Tooltip.Content>
            <p>Needs Review</p>
          </Tooltip.Content>
        </Tooltip>
      );
    case FAILED_SCORE:
      return (
        <Tooltip>
          <Tooltip.Trigger asChild>
            <span className="cursor-help">
              <HiXCircle className="mt-0.5 text-lg text-error-main shrink-0" />
            </span>
          </Tooltip.Trigger>
          <Tooltip.Content>
            <p>Coverage Issue</p>
          </Tooltip.Content>
        </Tooltip>
      );
    default:
      return null;
  }
};

export default function Determination({
  externalClaimId,
  verificationData,
}: DeterminationProps) {
  const { setIsDocumentViewerOpen, setIsClaimListOpen, setDocumentData } =
    useClaimsContext();

  function openDocumentViewer(citation: Citation) {
    setIsDocumentViewerOpen(true);
    setIsClaimListOpen(false);

    setDocumentData({
      fileName: citation.filename,
      documentSummary: citation.excerpt,
      pageNumber: citation.pages?.[0] || 1,
      documentID: citation.document?.document_id,
    });
  }
  const documentToCitation = mapDocumentToCitation(verificationData || []);

  return (
    <div className="overflow-y-auto mt-4" data-testid="determination-container">
      <div className="flex justify-between text-tw-primary font-semibold py-4">
        <h2 className="flex items-center">
          Coverage Notes <Copy verificationData={verificationData || []} />
        </h2>
        <span>Actions</span>
      </div>
      <div className="space-y-6">
        <Switch>
          <Switch.Match when={verificationData?.length === 0}>
            <div className="text-center text-gray-500">
              No verification data available.
            </div>
          </Switch.Match>
          <Switch.Match when={verificationData}>
            {verificationData?.map(({ original_content, note_id }) => (
              <div
                key={note_id}
                className="flex mt-1 border-t border-tw-border-secondary pt-4"
              >
                <div className="flex-1 text-sm">
                  <h3 className="font-semibold text-tw-primary flex items-center">
                    <span className="mr-2">{original_content.name}</span>
                    <Icon
                      assessment_score={original_content.assessment_score}
                    />
                  </h3>
                  <p className="mt-1 text-tw-primary font-medium">
                    {original_content.summary}{' '}
                    <Show when={original_content.citation}>
                      {(citation) => (
                        <HoverCard openDelay={100}>
                          <HoverCard.Trigger>
                            <button
                              type="button"
                              className={clsx({
                                'cursor-default': !citation.document,
                              })}
                              onClick={
                                citation.document
                                  ? () => openDocumentViewer(citation)
                                  : undefined
                              }
                            >
                              <Tag
                                color={citation.document ? 'teal' : 'gray'}
                                className="text-xs font-semibold"
                              >
                                {
                                  documentToCitation[
                                    citation?.document?.document_id || note_id
                                  ]
                                }
                              </Tag>
                            </button>
                          </HoverCard.Trigger>
                          <HoverCard.Content>
                            <div className="max-w-xs">
                              <div className="mb-2 text-xs font-medium">
                                {citation.filename}
                              </div>
                              <div className="mt-1 text-xs text-gray-600">
                                "{citation.excerpt}"
                              </div>
                            </div>
                          </HoverCard.Content>
                        </HoverCard>
                      )}
                    </Show>
                  </p>
                </div>
                <Actions externalClaimId={externalClaimId} noteId={note_id} />
              </div>
            ))}
          </Switch.Match>
        </Switch>
      </div>
    </div>
  );
}
