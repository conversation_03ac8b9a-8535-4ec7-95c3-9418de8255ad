import { TableV8WithNavigation } from '@nirvana/ui-kit';
import { useTableQueryParams } from '@nirvana/core/hooks';
import { matchSorter } from 'match-sorter';
import { useEffect, useMemo, useState } from 'react';
import { useClaimsQuery } from 'types/graphql-types';
import { OnChangeFn, RowSelectionState, Updater } from '@tanstack/react-table';
import { useNavigate, useParams } from 'react-router-dom';
import { getGqlErrorMessages } from 'utils/apollo';
import { useClaimsColumns } from '../../constants/columns';
import { useClaimsContext } from '../../hooks/useClaims';
import { useClaimsIQAccesibility } from '../../hooks/useClaimsIQAccesibility';

export const Claims = () => {
  const { globalFilter } = useTableQueryParams();
  const { data: claims, loading: claimsLoading, error } = useClaimsQuery();
  const { claimId } = useParams<{ claimId: string }>();
  const { isEnabledForClaim } = useClaimsIQAccesibility();
  const { setHasPermissions } = useClaimsContext();

  const navigate = useNavigate();

  const claimsColumns = useClaimsColumns();

  const filteredClaims = useMemo(() => {
    const filtered = matchSorter(claims?.claims ?? [], globalFilter ?? '', {
      keys: ['claimNumber', 'externalId', 'policy.insuredName', 'policyNumber'],
      sorter: (ranked) => ranked, // NOTE: This allows to disable sorting
    });
    return filtered;
  }, [claims, globalFilter]);

  const [rowSelection, setRowSelectionState] = useState<RowSelectionState>(
    claimId ? { [claimId]: true } : {},
  );

  useEffect(() => {
    if (
      getGqlErrorMessages(error)?.some((message) =>
        message.includes('unauthorized'),
      )
    ) {
      setHasPermissions(false);
    }
  }, [error, setHasPermissions]);

  const setRowSelection: OnChangeFn<RowSelectionState> = (
    cb: Updater<RowSelectionState>,
  ) => {
    const old: RowSelectionState = {};
    const updatedParams = typeof cb === 'function' ? cb(old) : cb;
    const id = Object.keys(updatedParams)[0];

    const claim = filteredClaims.find((claim) => claim.id === id);

    navigate(
      `/claims/list/${id ?? ''}/${isEnabledForClaim(claim) ? 'claimIQ' : ''}${location.search ?? ''}`,
      {
        preventScrollReset: true,
      },
    );

    setRowSelectionState(cb);
  };

  useEffect(() => {
    if (!claimId && Object.values(rowSelection).length > 0) {
      setRowSelectionState({});
    }
  }, [claimId, rowSelection]);
  return (
    <div className="h-full bg-white">
      <TableV8WithNavigation
        enableQueryParamsFilters
        data={filteredClaims}
        columns={claimsColumns}
        getRowId={(row) => row.id}
        enableRowSelection
        enableMultiRowSelection={false}
        onRowSelectionChange={setRowSelection}
        isLoading={claimsLoading}
        onCell={() => ({
          className: 'px-0 py-0',
        })}
        className="table-fixed"
        disableHeader
        rowSelection={rowSelection}
        virtualization
        virtualizationRowEstimatedSize={80}
        columnVisibility={{
          status: false,
          lineOfBusiness: false,
          adjusterName: false,
          adjusterEmail: false,
          modifiedAt: false,
          reportedAt: false,
          show_test_policy: false,
        }}
      />
    </div>
  );
};
