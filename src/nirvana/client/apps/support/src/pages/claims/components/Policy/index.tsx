import { TableV8 } from '@nirvana/ui-kit';
import { getFormattedDate } from '@nirvana/core/utils';
import { useMemo, useState } from 'react';
import {
  ColumnFiltersState,
  OnChangeFn,
  RowSelectionState,
  Updater,
} from '@tanstack/react-table';
import { usePolicyAndEndorsementsQuery } from 'types/graphql-types';
import { parseISO } from 'date-fns';
import { Alert } from '@nirvana/ui';
import { Link, useNavigate, useParams } from 'react-router-dom';
import clsx from 'clsx';
import { useClaimsContext } from 'pages/claims/hooks/useClaims';
import { FaArrowLeft } from 'react-icons/fa';
import {
  DocumentType,
  Policy as PolicyType,
  usePolicyColumns,
} from './columns';
import { Controls } from './Controls';
import { PdfViewer } from './PdfViewer';
import { Coverage } from './Coverage';
import { VehicleList } from './VehicleList';
import { DriverList } from './DriverList';
import { ExpiringLinkMonitor } from './ExpiringLinkMonitor';

export const Policy = ({ policyNumber }: { policyNumber?: string }) => {
  const columns = usePolicyColumns();
  const navigate = useNavigate();
  const { documentId, claimId } = useParams<{
    documentId?: string;
    claimId?: string;
  }>();

  const [search, setSearch] = useState<string>('');
  const [filters, setFilters] = useState<ColumnFiltersState>([]);
  const { setIsClaimListOpen } = useClaimsContext();

  const [rowSelectionState, setRowSelectionState] = useState<RowSelectionState>(
    documentId ? { [documentId]: true } : {},
  );

  const { data: policyAndEndorsements, error } = usePolicyAndEndorsementsQuery({
    variables: { claimId: claimId as string },
    skip: !claimId,
  });

  const setRowSelection: OnChangeFn<RowSelectionState> = (
    cb: Updater<RowSelectionState>,
  ) => {
    const old: RowSelectionState = {};
    const updatedParams = typeof cb === 'function' ? cb(old) : cb;
    const id = Object.keys(updatedParams)[0];
    navigate(
      `/claims/list/${claimId}/policy/${id ?? ''}${location.search ?? ''}`,
      {
        preventScrollReset: true,
      },
    );

    setRowSelectionState(cb);
    setIsClaimListOpen(false);
  };

  const typedData: PolicyType[] = useMemo(() => {
    if (!policyAndEndorsements?.policy) {
      return [];
    }
    const formattedEndorsements = policyAndEndorsements.policy.endorsements
      ?.map((endorsement) => ({
        ...endorsement,
        type: DocumentType.Endorsement,
      }))
      .toSorted(
        (a, b) =>
          parseISO(a.approvedAt).getTime() - parseISO(b.approvedAt).getTime(),
      );

    const formattedPolicy: PolicyType = {
      ...policyAndEndorsements.policy,
      type: DocumentType.Policy,
      approvedAt: policyAndEndorsements.policy.startDate,
      changeTypes: ['Policy'],
      effectiveInterval: {
        effectiveDate: policyAndEndorsements.policy.startDate,
        expirationDate: policyAndEndorsements.policy.endDate,
      },
    };

    return [formattedPolicy, ...formattedEndorsements];
  }, [policyAndEndorsements?.policy]);

  const expiringDateForLinks: string | undefined =
    typedData[0]?.signedLink.expiration;

  const filteredData: PolicyType[] = useMemo(() => {
    return typedData.filter((policy) => {
      return (
        policy.signedLink.link.toLowerCase().includes(search.toLowerCase()) ||
        getFormattedDate(policy.approvedAt)
          .toLowerCase()
          .includes(search.toLowerCase()) ||
        policy.underwriter.name.toLowerCase().includes(search.toLowerCase())
      );
    });
  }, [search, typedData]);

  if (!policyNumber) {
    return null;
  }
  if (error) {
    return (
      <div className="mt-4">
        <Alert severity="error">
          <Alert.Description>
            An error occurred while fetching the data.
          </Alert.Description>
        </Alert>
      </div>
    );
  }
  const selectedFile = typedData.find(
    (row) => row.id === Object.keys(rowSelectionState)[0],
  );
  return (
    <>
      <ExpiringLinkMonitor expiringDate={expiringDateForLinks} />
      <div className="flex h-full" data-testid="policy-section">
        <div
          className={clsx('mr-2 w-60', {
            'hidden md:block': documentId,
          })}
        >
          <Coverage
            coverages={policyAndEndorsements?.policy?.coverages ?? []}
            subCoverages={policyAndEndorsements?.policy?.subCoverages ?? []}
          />
          <VehicleList
            vehicles={policyAndEndorsements?.policy?.vehicles ?? []}
          />
          <DriverList drivers={policyAndEndorsements?.policy?.drivers ?? []} />

          <Controls
            search={search}
            setSearch={setSearch}
            filters={filters}
            setFilters={setFilters}
            data={typedData}
          />

          <TableV8
            disableHeader
            data-testid="policy-table"
            data={filteredData}
            columns={columns}
            getRowId={(row) => row.id}
            virtualization
            virtualizationRowEstimatedSize={140}
            onCell={() => ({
              className: 'px-0 py-0',
            })}
            enableRowSelection
            rowSelection={rowSelectionState}
            onRowSelectionChange={setRowSelection}
            enableMultiRowSelection={false}
            columnFilters={filters}
            columnVisibility={{
              underwriterName: false,
              approvedAt: false,
              effectiveInterval_effectiveDate: false,
              effectiveInterval_expirationDate: false,
            }}
          />
        </div>
        {selectedFile?.signedLink.link && (
          <div
            className={clsx('flex-1 flex flex-col', {
              'hidden md:block': !documentId,
            })}
          >
            <div className="md:hidden">
              <Link to={window.location.pathname.replace(selectedFile.id, '')}>
                <p className="py-2">
                  <FaArrowLeft className="inline-block" /> List of files
                </p>
              </Link>
            </div>
            <PdfViewer
              pdfUrl={selectedFile.signedLink.link}
              fileId={selectedFile.id}
            />
          </div>
        )}
      </div>
    </>
  );
};
