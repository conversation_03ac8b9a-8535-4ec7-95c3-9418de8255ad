import {
  Hi<PERSON>rrowR<PERSON>,
  HiOutlineDocumentText,
  HiOutlineFolderOpen,
  HiOutlineLogin,
} from 'react-icons/hi';
import { useCallback, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns/esm';
import { HiOutlineExclamationTriangle } from 'react-icons/hi2';

import { Button, HoverCard, Show } from '@nirvana/ui';
import { getFormattedDate } from '@nirvana/core/utils';
import {
  ClaimStatus,
  useClaimByIdQuery,
  usePolicyAndEndorsementsQuery,
} from 'types/graphql-types';
import { useClaimsContext } from '../hooks/useClaims';
import { extractFileName } from './Policy/utils';

const MAX_PERCENTAGE = 100;

// 8% overlap threshold
// Came up with this value by visually inspecting the timeline
// and ensuring that icons do not overlap.
const OVERLAP_PERCENT_THRESHOLD = 8;

const ICON_LEFT_MARGIN = 30; // Margin for overlapping icons

export default function Timeline() {
  const { claimId = '' } = useParams();
  const { setIsClaimListOpen, setIsDocumentViewerOpen, setDocumentData } =
    useClaimsContext();

  const { data: claimData } = useClaimByIdQuery({
    variables: { id: claimId },
    skip: !claimId,
  });

  const { data: policyData } = usePolicyAndEndorsementsQuery({
    variables: { claimId },
    skip: !claimId,
  });

  const events = useMemo(() => {
    if (!claimData?.claimById || !policyData?.policy) {
      return [];
    }

    const timeline = [
      {
        name: policyData.policy.policyNumber,
        id: policyData.policy.id,
        date: policyData.policy.startDate,
        documentId: policyData.policy.documentID,
        icon: <HiOutlineFolderOpen className="text-tw-blue-700" />,
      },
      ...(claimData.claimById.lossDatetime
        ? [
            {
              name: 'Incident',
              id: claimData.claimById.id,
              date: claimData.claimById?.lossDatetime,
              icon: (
                <HiOutlineExclamationTriangle className="text-tw-red-700" />
              ),
            },
          ]
        : []),
      {
        name: 'FNOL Submitted',
        id: `fnol-${claimData.claimById.id}`,
        date: claimData.claimById.reportedAt,
        icon: <HiOutlineLogin className="-rotate-90 text-tw-green-600" />,
      },
      {
        name: 'Today',
        id: 'today-marker',
        date: new Date().toISOString(),
        icon: (
          <div className="relative rounded-full bg-tw-blue-700 size-2">
            <div className="absolute w-0.5 h-7 -translate-x-1/2 top-1 bg-tw-blue-700 left-1/2">
              <p className="absolute text-xs font-semibold -translate-x-1/2 -bottom-6 left-1/2">
                Today
              </p>
            </div>
          </div>
        ),
      },
    ];

    if (policyData.policy.endorsements?.length) {
      policyData.policy.endorsements.forEach((endorsement) => {
        if (endorsement.approvedAt) {
          timeline.push({
            id: endorsement.id,
            date: endorsement.approvedAt,
            documentId: endorsement.documentID,
            name: extractFileName(endorsement.signedLink.link),
            icon: <HiOutlineDocumentText className="text-tw-blue-700" />,
          });
        }
      });
    }

    return timeline.sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
    );
  }, [claimData, policyData]);

  const handleOpenDocument = useCallback(
    (fileName: string, documentID?: string) => {
      setIsClaimListOpen(false);
      setIsDocumentViewerOpen(true);

      setDocumentData({ fileName, documentSummary: '', documentID });
    },
    [setDocumentData, setIsClaimListOpen, setIsDocumentViewerOpen],
  );

  if (!policyData?.policy || !claimData?.claimById) {
    return null;
  }

  if (claimData.claimById.status === ClaimStatus.Closed) {
    return null;
  }

  return (
    <Show when={policyData.policy}>
      {({ startDate, endDate }) => (
        <>
          <p>
            <span className="mr-2 text-xl font-semibold">
              Incident Timeline
            </span>
            <Show when={claimData.claimById?.reportedAt}>
              {(date) => (
                <span className="text-tw-gray-700">
                  {formatDistanceToNow(new Date(date))} since claim opened
                </span>
              )}
            </Show>
          </p>

          <div className="relative h-20 mb-10">
            <div
              aria-label="base-timeline"
              className="absolute inset-x-0 h-0.5 -translate-y-1/2 bg-tw-gray-200 top-1/2"
            />

            <div
              aria-label="policy-end-marker"
              className="absolute right-0 w-0.5 h-5 -translate-y-1/2 top-1/2 bg-tw-gray-200"
            />

            <p
              aria-label="Policy Start Date"
              className="absolute bottom-0 font-semibold text-tw-gray-700"
            >
              {getFormattedDate(startDate, 'MMM d yyyy')}
            </p>

            <p
              aria-label="Policy End Date"
              className="absolute bottom-0 right-0 font-semibold text-tw-gray-700"
            >
              {getFormattedDate(endDate, 'MMM d yyyy')}
            </p>

            {events.map((event, idx) => {
              // Calculate base position as percentage
              const basePercent =
                ((new Date(event.date).getTime() -
                  new Date(startDate).getTime()) /
                  (new Date(endDate).getTime() -
                    new Date(startDate).getTime())) *
                MAX_PERCENTAGE;

              // Find all previous events with nearly the same position
              const prevSamePositionIndexes = events
                .slice(0, idx)
                .map((e, i) => ({
                  idx: i,
                  percent:
                    ((new Date(e.date).getTime() -
                      new Date(startDate).getTime()) /
                      (new Date(endDate).getTime() -
                        new Date(startDate).getTime())) *
                    MAX_PERCENTAGE,
                }))
                .filter(
                  (e) =>
                    Math.abs(e.percent - basePercent) <
                    OVERLAP_PERCENT_THRESHOLD,
                );

              // Offset each overlapping icon horizontally by 50px to the right, maintaining event order
              const overlapIdx = prevSamePositionIndexes.length;
              let marginLeft = 0;
              if (overlapIdx > 0) {
                marginLeft = ICON_LEFT_MARGIN * overlapIdx;
              }

              return (
                <div
                  key={event.id}
                  className="absolute p-1 -translate-y-1/2 bg-white top-1/2 hover:z-10"
                  style={{ marginLeft, left: `calc(${basePercent}% - 0.5rem)` }}
                >
                  <Show when={event.name !== 'Today'} fallback={event.icon}>
                    <HoverCard openDelay={100}>
                      <HoverCard.Trigger asChild>
                        <div className="text-lg rounded-full p-1.5 bg-white shadow-tw-sm cursor-pointer">
                          {event.icon}
                        </div>
                      </HoverCard.Trigger>
                      <HoverCard.Content
                        align="start"
                        className="w-48 p-3 break-words"
                      >
                        <p className="mb-1 font-medium">
                          {getFormattedDate(event.date, 'MMM d yyyy')}
                        </p>
                        <p className="mb-3">{event.name}</p>
                        <Show when={event.documentId}>
                          <Button
                            variant="link"
                            className="font-medium"
                            endIcon={<HiArrowRight />}
                            onClick={() => {
                              handleOpenDocument(event.name, event.documentId);
                            }}
                          >
                            View Source
                          </Button>
                        </Show>
                      </HoverCard.Content>
                    </HoverCard>
                  </Show>
                </div>
              );
            })}
          </div>
        </>
      )}
    </Show>
  );
}
