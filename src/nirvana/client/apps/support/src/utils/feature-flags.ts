import { useFlags } from 'launchdarkly-react-client-sdk';

export type AccessType = 'fleet' | 'non-fleet' | 'all';
export type FleetType = 'fleet' | 'retail-non-fleet' | 'wholesale-non-fleet';
export const ANONYMOUS_USER_KEY = '00000000-0000-0000-0000-000000000000';

export enum Feature {
  AGENTS_INVITE = 'agentsInvite',
  APPLICATION_TYPE_ACCESS = 'applicationTypeAccess',
  CLAIMS_AI_OVERVIEW = 'claimsAiOverview',
  CLAIMS_POLICIY_VIEWER_VEHICLE_LIST = 'claimsPolicyViewerVehicleList',
  CLAIMS_POLICY_VIEWER = 'claimsPolicyViewer',
  PRE_TELEMATICS_EXPERIMENT = 'preQuoteTelematicsV1Experiment',
  PRICING_SIMULATION_DASHBOARD = 'pricingSimulationDashboard',
  SAVE_DRAFT_FNOL = 'saveDraftFnol',
  SHOW_EDIT_DRAFT_FNOL_BUTTON = 'showEditDraftFnolButton',
  SNAPSHEET_CLAIMS = 'snapsheetClaims',
}

export function useFeatureEnabled(flag: string) {
  const flags = useFlags();

  return flags[flag];
}
