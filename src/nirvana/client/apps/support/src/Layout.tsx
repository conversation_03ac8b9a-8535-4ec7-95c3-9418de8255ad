import { ApolloProvider } from '@apollo/client';
import {
  QueryClient as LegacyQueryClient,
  QueryClientProvider as LegacyQueryClientProvider,
} from 'react-query';
import { LocalizationProvider } from '@material-ui/lab';
import { ReactQueryDevtools } from 'react-query/devtools';
import AdapterDateFns from '@material-ui/lab/AdapterDateFns';
import {
  QueryClient,
  QueryClientProvider,
  QueryCache,
  MutationCache,
} from '@tanstack/react-query';
import axios from 'axios';

import LaunchDarkly from 'components/launch-darkly';
import Auth from 'components/auth';
import { apolloClient } from 'utils/apollo';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';
import { Analytics } from 'components/Analytics';
import {
  captureException,
  withScope,
  init,
  browserTracingIntegration,
  addIntegration,
} from '@sentry/react';
import { useLightTheme } from '@nirvana/ui-kit';

import { SnackbarProvider } from 'notistack';
import { ThemeProvider } from '@material-ui/core/styles';
import CssBaseline from '@material-ui/core/CssBaseline';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import React from 'react';
import { Outlet } from 'react-router-dom';
import '@nirvana/ui/global.css';

function handleAPIError(error: unknown) {
  if (!axios.isAxiosError(error) || !error.config.url) {
    captureException(error);
    return;
  }

  withScope((scope) => {
    const url = new URL(error.config.url!);

    const pathnamePieces = url.pathname.split('/');
    if (pathnamePieces.length > 1) {
      scope.setTag('basePath', pathnamePieces[1]);
    }

    captureException(error);
  });
}

export const legacyQueryClient = new LegacyQueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      onError: handleAPIError,
    },
  },
});

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
  queryCache: new QueryCache({
    onError: handleAPIError,
  }),
  mutationCache: new MutationCache({
    onError: handleAPIError,
  }),
});

posthog.init(import.meta.env.VITE_POSTHOG_API_KEY, {
  api_host: 'https://us.i.posthog.com',
  capture_pageview: false,
  session_recording: {
    maskAllInputs: false,
    maskInputOptions: {
      password: true,
    },
  },
});

init({
  dsn: import.meta.env.VITE_SENTRY_DSN,
  environment: import.meta.env.MODE,
  integrations: [browserTracingIntegration()],
  tracesSampleRate: 1.0, //  Capture 100% of the transactions
  // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
  tracePropagationTargets: [/^\//, /^https:\/\/.+\.prod\.nirvanatech./],

  replaysSessionSampleRate: 0,
  replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
});

import('@sentry/react').then((lazyLoadedSentry) => {
  addIntegration(
    lazyLoadedSentry.replayIntegration({
      maskAllText: false,
      blockAllMedia: false,
    }),
  );
});

const cache = createCache({
  key: 'css',
  prepend: true,
});

export const Layout: React.FC = () => {
  const theme = useLightTheme();
  return (
    <LegacyQueryClientProvider client={legacyQueryClient}>
      <QueryClientProvider client={queryClient}>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <PostHogProvider client={posthog}>
            <ApolloProvider client={apolloClient}>
              <Auth>
                <LaunchDarkly />
                <Analytics />
                <ThemeProvider theme={theme}>
                  <CssBaseline />
                  <CacheProvider value={cache}>
                    <SnackbarProvider
                      anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
                    >
                      <Outlet />
                    </SnackbarProvider>
                  </CacheProvider>
                </ThemeProvider>
              </Auth>
            </ApolloProvider>
          </PostHogProvider>
        </LocalizationProvider>

        {import.meta.env.DEV && <ReactQueryDevtools position="bottom-right" />}
      </QueryClientProvider>
    </LegacyQueryClientProvider>
  );
};
