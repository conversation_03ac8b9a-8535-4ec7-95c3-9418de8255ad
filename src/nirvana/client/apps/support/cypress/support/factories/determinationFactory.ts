import { Factory } from 'fishery';
import {
  CoverageRunWithNotes,
  FeedbackResponse,
  VerificationItemStatusEnum,
} from '@nirvana/api/claims_agent/api';

export const determinationFactory = Factory.define<
  CoverageRunWithNotes[],
  { status: VerificationItemStatusEnum }
>(({ transientParams }) => [
  {
    created_at: '2025-01-01',
    created_by: 'test',
    coverage_notes: [
      {
        note_id: '123',
        original_content: {
          name: 'Coverage',
          assessment_score: 0.5,
          summary: 'Coverage is 50%',
          status:
            transientParams.status || VerificationItemStatusEnum.Undefined,
          citation: {
            excerpt: 'Coverage is 50%',
            filename: 'policy.pdf',
            presigned_url: 'https://example.com/policy.pdf',
            pages: [1, 2, 3],
          },
        },
      },
      {
        note_id: '124',
        original_content: {
          name: 'Coverage 2',
          assessment_score: 0.5,
          summary: 'Coverage is 100%',
        },
      },
    ],
  },
]);

export const upsertCoverageFeedbackFactory = Factory.define<FeedbackResponse>(
  () => ({
    updated_notes: ['123'],
  }),
);
