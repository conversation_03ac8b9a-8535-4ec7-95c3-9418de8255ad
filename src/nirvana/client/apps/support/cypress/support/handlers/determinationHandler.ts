import { CyHttpMessages } from '@nirvana/core/testUtils';
import { VerificationItemStatusEnum } from '@nirvana/api/claims_agent/api';
import {
  determinationFactory,
  upsertCoverageFeedbackFactory,
} from '../factories/determinationFactory';

const BAD_REQUEST_STATUS = 400;

// State management for test handlers
const handlerState = {
  lastUpsertValue: VerificationItemStatusEnum.Undefined,
  reset() {
    this.lastUpsertValue = VerificationItemStatusEnum.Undefined;
  },
};

export const determinationCachedHandler = (
  req: CyHttpMessages.IncomingHttpRequest,
) => {
  req.on('response', (res) => {
    res.setDelay(1000);
  });
  if (req.url.includes('FAILING')) {
    return req.reply(BAD_REQUEST_STATUS);
  }
  req.reply(
    determinationFactory.build(undefined, {
      transient: {
        status: handlerState.lastUpsertValue,
      },
    }),
  );
};

export const forcedFailureHandler = (
  req: CyHttpMessages.IncomingHttpRequest,
) => {
  req.reply(BAD_REQUEST_STATUS);
};

export const determinationHandler = (
  req: CyHttpMessages.IncomingHttpRequest,
) => {
  req.reply(determinationFactory.build()[0]);
};

export const upsertCoverageFeedbackHandler = (
  req: CyHttpMessages.IncomingHttpRequest,
) => {
  handlerState.lastUpsertValue = req.body.feedback[0].modified_content.status;
  req.reply(
    upsertCoverageFeedbackFactory.build({
      updated_notes: [req.body.feedback[0].note_id],
    }),
  );
};

// Export reset function for test cleanup
export const resetDeterminationHandlerState = () => handlerState.reset();
