import clsx from 'clsx';
import { useMemo } from 'react';
import { format, parseISO } from 'date-fns';
import { useParams } from 'react-router-dom';
import {
  HiExclamationCircle,
  HiExternalLink,
  HiOutlineCalendar,
  HiOutlineInformationCircle,
} from 'react-icons/hi';
import { useMutation, useQuery, useQueryClient } from 'react-query';

import { TSP } from '@nirvana/api/quoting';
import { CopyToClipboard, Switch } from '@nirvana/ui-kit';
import {
  Button,
  Calendar,
  Popover,
  Show,
  Tag,
  Textarea,
  Tooltip,
} from '@nirvana/ui';

import SFDCIcon from 'src/assets/icons/sfdc_logo.svg?react';
import { Feature, useFeatureEnabled } from 'src/utils/feature-flags';
import UpdatedValueIcon from 'src/assets/icons/updated-value.svg?react';

import {
  fetchApplicationReviewSummary,
  reassignUnderwriter,
  updateApplicationReviewSummary,
} from 'src/queries/applications';
import { formatDate } from 'src/utils/date';
import { getTSPLabel } from 'src/helpers/tsp';
import formatNumber from 'src/utils/format-number';
import { isTSPGeoTab } from 'src/pages/applications/utils';
import { fetchTelematicsStatus } from 'src/queries/telematics';
import { usePanelReviewContext } from 'src/hooks/use-panel-review';

import UWAssign from '../uw-assign';
import PricingTooltip from '../pricing/pricing-tooltip';
import CameraProgramForm from './camera-program-form';
import { FactorSummary } from './factor-summary';

type SummaryProps = {
  isReviewReadiness?: boolean;
};

export default function Summary({ isReviewReadiness = false }: SummaryProps) {
  const { appReviewId = '' } = useParams();
  const isTelematicsStatusEnabled =
    useFeatureEnabled(Feature.TELEMATICS_STATUS) || false;

  const isCameraProgramEnabled = useFeatureEnabled(Feature.CAMERA_PROGRAM);

  const isUwRankingButtonEnabled = useFeatureEnabled(
    Feature.PRICING_EXPLAINABILITY_UW_RANKING_BUTTON,
  );

  const { data, isLoading: isLoadingSummary } = useQuery(
    ['summary', appReviewId],
    () => fetchApplicationReviewSummary(appReviewId),
  );
  const applicationId = data?.applicationId || '';

  const { data: tspConnectionInfo, isLoading: isLoadingTelematicsStatus } =
    useQuery(
      ['telematics-status', applicationId],
      () => fetchTelematicsStatus(applicationId),
      { enabled: !!applicationId },
    );
  const isLoading = isLoadingSummary || isLoadingTelematicsStatus;

  const { data: appReview } = usePanelReviewContext();
  const isTspGeoTab = useMemo(() => isTSPGeoTab(data?.tspDetails?.tsp), [data]);

  const queryClient = useQueryClient();
  const { mutate } = useMutation(updateApplicationReviewSummary, {
    onSuccess: () => queryClient.invalidateQueries(['summary', appReviewId]),
  });

  const { mutate: updateUW } = useMutation(reassignUnderwriter, {
    // Always refetch after error or success
    onSettled: () => queryClient.invalidateQueries(['applications']),
  });

  const handleUWChange = (underwriterId: string) => {
    updateUW({ appReviewId, underwriterId });
  };

  const isRenewalApplication = data?.isRenewal;

  const premiumValues = useMemo(
    () => ({
      alPremium:
        data?.agentSelectedIndicationOption?.autoLiabilityPremium
          ?.traditionalPremium,
      apdPremium:
        data?.agentSelectedIndicationOption?.autoPhysicalDamagePremium
          ?.traditionalPremium,
      glPremium:
        data?.agentSelectedIndicationOption?.generalLiabilityPremium?.premium,
      mtcPremium:
        data?.agentSelectedIndicationOption?.motorTruckCargoPremium?.premium,
      flatCharges: data?.agentSelectedIndicationOption?.flatCharges,
      totalSurchargePremium:
        data?.agentSelectedIndicationOption?.totalSurchargePremium,
      premiumAmount: data?.agentSelectedIndicationOption?.totalPremium,
      discount: data?.agentSelectedIndicationOption?.safetyDiscount,
      discountPercentage:
        data?.agentSelectedIndicationOption?.safetyDiscountPercentage,
      preDiscountTotalPremium:
        data?.agentSelectedIndicationOption?.preDiscountTotalPremium,
    }),
    [data],
  );

  const fleetSummaryData = [
    {
      title: 'Underwriter',
      value: (
        <div className="flex-1 mt-3">
          <UWAssign
            onChange={handleUWChange}
            disabled={isReviewReadiness}
            options={appReview?.summary.assignees.underwriter?.options}
            value={appReview?.summary.assignees.underwriter?.current}
          />
        </div>
      ),
      className: 'col-span-2',
    },
    {
      title: 'Effective date',
      className: 'col-span-2',
      value: (
        <Show when={data}>
          {({ effectiveDate, originalEffectiveDate }) => (
            <>
              <Popover>
                <Popover.Trigger asChild>
                  <Button
                    variant="secondary"
                    disabled={isReviewReadiness}
                    className="justify-start w-full"
                    startIcon={<HiOutlineCalendar />}
                  >
                    {effectiveDate ? formatDate(effectiveDate) : 'Select date'}
                  </Button>
                </Popover.Trigger>
                <Popover.Content align="start" className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={
                      effectiveDate ? parseISO(effectiveDate) : undefined
                    }
                    onSelect={(date) => {
                      if (date) {
                        const value = format(date, 'yyyy-MM-dd');
                        mutate({ appReviewId, form: { effectiveDate: value } });
                      }
                    }}
                  />
                </Popover.Content>
              </Popover>
              <Show when={originalEffectiveDate}>
                {(originalDate) => (
                  <Tooltip>
                    <Tooltip.Trigger>
                      <UpdatedValueIcon className="w-3 h-3" />
                    </Tooltip.Trigger>
                    <Tooltip.Content>
                      {formatDate(originalDate)}
                    </Tooltip.Content>
                  </Tooltip>
                )}
              </Show>
            </>
          )}
        </Show>
      ),
    },
    {
      title: 'Company',
      className: 'col-span-2',
      value: (
        <div className="flex items-center space-x-1">
          <div>{data?.accountName}</div>
          <div>
            <a
              target="_blank"
              rel="noreferrer"
              className="text-primary-main"
              href={`https://nirvanatech.lightning.force.com/apex/navigateToAccount?dotnumber=${data?.dotNumber}`}
            >
              <SFDCIcon className="w-6 h-6" />
            </a>
          </div>
        </div>
      ),
    },
    {
      title: 'DOT Number',
      value: (
        <Button variant="link" asChild>
          <a
            target="_blank"
            rel="noreferrer"
            className="gap-1"
            href={`https://safety.nirvanatech.com/${data?.dotNumber}/overview`}
          >
            {data?.dotNumber}

            <HiExternalLink />
          </a>
        </Button>
      ),
    },
    { title: 'Unit counts', value: data?.unitCount },
    {
      title: 'Address',
      value: (
        <Button variant="link" asChild>
          <a
            target="_blank"
            rel="noreferrer"
            href={`https://www.google.com/maps/search/?api=1&query=${data?.address}`}
          >
            {data?.address}
          </a>
        </Button>
      ),
      className: 'col-span-2',
    },
    ...(data?.isRenewal
      ? []
      : [
          {
            title: 'Indication Pricing',
            value: (
              <div className="flex items-center space-x-2">
                <span>
                  $
                  {formatNumber(
                    data?.agentSelectedIndicationOption?.totalPremium ?? 0,
                  )}
                </span>
                <Tooltip>
                  <Tooltip.Trigger>
                    <HiOutlineInformationCircle className="text-primary-main" />
                  </Tooltip.Trigger>
                  <Tooltip.Content>
                    <PricingTooltip premiumValues={premiumValues} />
                  </Tooltip.Content>
                </Tooltip>
              </div>
            ),
          },
        ]),
    { title: 'AL Incumbent', value: data?.isALIncumbent ? 'Yes' : 'No' },
    ...(data?.tspDetails?.tsp
      ? [
          { type: 'divider' },
          {
            title: 'TSP',
            value: (
              <div className="w-full space-y-1">
                <div className={clsx('flex', isTspGeoTab ? 'flex-col' : '')}>
                  {getTSPLabel(data?.tspDetails?.tsp as TSP)}

                  <Show when={data?.tspDetails?.isException && !isTspGeoTab}>
                    <Tag
                      color="orange"
                      className="text-xs"
                      icon={<HiExclamationCircle />}
                    >
                      Exception
                    </Tag>
                  </Show>
                  <Show when={isTspGeoTab}>
                    <Tooltip>
                      <Tooltip.Trigger className="inline-flex">
                        <Tag
                          color="orange"
                          className="mt-1 text-xs"
                          icon={<HiExclamationCircle />}
                        >
                          Telematics Updates Offline
                        </Tag>
                      </Tooltip.Trigger>
                      <Tooltip.Content className="w-96">
                        Support is processing historical data offline for
                        Geotab. You will be notified when the data is available.
                        You can also follow-up by searching for &quot;
                        {data?.accountName}&quot; in #telematics-connection-help
                        in slack
                      </Tooltip.Content>
                    </Tooltip>
                  </Show>
                </div>
              </div>
            ),
            className:
              data?.tspDetails?.isException && !isTspGeoTab ? 'col-span-2' : '',
          },
          ...(tspConnectionInfo?.TelematicsPipelineStatus?.processingDate &&
          isTelematicsStatusEnabled
            ? [
                {
                  title: 'Processing Started',
                  value: formatDate(
                    tspConnectionInfo?.TelematicsPipelineStatus?.processingDate,
                  ),
                },
              ]
            : []),
        ]
      : []),
    ...(isCameraProgramEnabled
      ? [
          {
            className: 'col-span-2',
            value: (
              <CameraProgramForm
                disabled={isReviewReadiness}
                data={data?.cameraSubsidyDetails}
              />
            ),
          },
        ]
      : []),
    { type: 'divider' },
    { title: 'Producer', value: data?.producerName },
    { title: 'Agency', value: data?.agencyName },
    ...(isTelematicsStatusEnabled
      ? [
          {
            title: 'Agent Contact',
            value: (
              <div className="flex flex-col space-y-2">
                <div className="flex items-center justify-between">
                  <span>
                    {tspConnectionInfo?.CompanyInfo?.phoneNumber ||
                      'Not available'}
                  </span>
                </div>
                {tspConnectionInfo?.CompanyInfo.producerEmail && (
                  <div className="flex items-center space-x-1 w-72">
                    <a
                      href={`mailto:${
                        tspConnectionInfo?.CompanyInfo.producerEmail
                      }?subject=Telematics%20Connection%20for%20${
                        tspConnectionInfo?.CompanyInfo.companyName
                      }&body=Hi%20${
                        tspConnectionInfo?.CompanyInfo.producerName.split(
                          ' ',
                        )?.[0] || tspConnectionInfo?.CompanyInfo.producerName
                      }%2C%0A`}
                      className="truncate"
                    >
                      {tspConnectionInfo?.CompanyInfo.producerEmail}
                    </a>
                    <CopyToClipboard
                      snackBarMessage="Email copied to clipboard"
                      copyText={tspConnectionInfo?.CompanyInfo.producerEmail}
                    />
                  </div>
                )}
              </div>
            ),
          },
        ]
      : []),
    {
      title: 'Marketer Info',
      className: 'col-span-2',
      value: (
        <div className="flex ">
          <div className="items-center justify-between space-y-2">
            <span>{data?.marketerName || 'Not available'}</span>

            <div className="flex items-center space-x-1 w-72">
              <a href={`mailto:${data?.marketerEmail}`} className="truncate">
                {data?.marketerEmail || 'Not available'}
              </a>
              <CopyToClipboard
                snackBarMessage="Email copied to clipboard"
                copyText={data?.marketerEmail || ''}
              />
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'BD',
      value: data?.assignedBD,
    },
    {
      title: 'Fronter',
      value: data?.fronter,
    },
    {
      title: 'Agent Application',
      value: (
        <Button variant="link">
          <a
            className="flex items-center gap-1"
            href={`${import.meta.env.VITE_AGENTS_APP_URL}/applications/${data?.applicationId}/${isRenewalApplication ? 'renew' : 'create'}`}
            target="_blank"
            rel="noreferrer"
          >
            Open Link <HiExternalLink />
          </a>
        </Button>
      ),
    },
    {
      title: 'Salesforce Opportunity',
      value: data?.salesforceLink ? (
        <Button variant="link">
          <a
            href={data?.salesforceLink}
            target="_blank"
            rel="noreferrer"
            className="flex items-center gap-1"
          >
            Open Link <HiExternalLink className="inline" />
          </a>
        </Button>
      ) : (
        <p>Not available</p>
      ),
    },
    ...(isUwRankingButtonEnabled
      ? [
          {
            title: 'Factor Summary',
            value: <FactorSummary appReviewId={appReviewId} />,
          },
        ]
      : []),
  ];

  return (
    <div className="grid grid-cols-2 p-6 text-sm gap-y-6">
      {fleetSummaryData?.map(({ title, value, className, type }, index) => {
        if (type === 'divider') {
          return <hr key={index} className="col-span-2 border-solid" />;
        }
        return (
          <div key={index} className={className}>
            <Switch>
              <Switch.Match when={isLoading}>
                <div className="text-xs font-light text-gray-600">{title}</div>
                <div className="h-6 bg-gray-100 rounded-lg animate-pulse" />
              </Switch.Match>

              <Switch.Match when={value}>
                <div className="text-xs font-light text-gray-600">{title}</div>
                <div className="flex items-center font-semibold whitespace-pre-line text-text-primary gap-x-2">
                  {value}
                </div>
              </Switch.Match>
            </Switch>
          </div>
        );
      })}

      <Switch>
        <Switch.Match when={isLoading}>
          <div className="col-span-2">
            <p className="mb-1 text-xs font-light text-gray-600">
              Additional Information
            </p>
            <div className="h-6 bg-gray-100 rounded-lg w-36 animate-pulse" />
          </div>
        </Switch.Match>

        <Switch.Match when={data?.additionalInfo}>
          <div className="col-span-2 mb-4">
            <p className="mb-1 text-xs font-light text-gray-600">
              Additional Information
            </p>
            <Textarea rows={8} readOnly value={data?.additionalInfo} />
          </div>
        </Switch.Match>
      </Switch>
    </div>
  );
}
