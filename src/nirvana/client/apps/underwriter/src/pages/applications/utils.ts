import {
  add,
  endOfMonth,
  startOfMonth,
  sub,
  isAfter,
  isBefore,
  isValid,
  isWithinInterval,
  formatISO,
} from 'date-fns';

import { TSP } from '@nirvana/api/quoting';
import { RecommendedAction } from '@nirvana/api/uw';

export const isDateInRange = (
  date: Date,
  range: { start?: Date | null; end?: Date | null },
) => {
  // Check if range is valid
  if (
    range.start &&
    range.end &&
    isValid(range.start) &&
    isValid(range.end) &&
    isBefore(range.end, range.start)
  ) {
    return false;
  }

  if (range.start && !range.end) {
    return isAfter(date, range.start);
  }

  if (!range.start && range.end) {
    return isBefore(date, range.end);
  }

  if (range.start && range.end) {
    return isWithinInterval(date, { start: range.start, end: range.end });
  }

  return true;
};

export type InitialFilters = {
  selectedUnderwriter?: string;
  selectedEffectiveDateRange: {
    start?: string;
    end?: string;
  };
  selectedRecommendedAction: RecommendedAction[];
};

export const getInitialFilters = () => {
  const initialFilters: InitialFilters = {
    selectedUnderwriter: undefined,
    selectedEffectiveDateRange: {
      start: formatISO(startOfMonth(sub(new Date(), { months: 1 }))),
      end: formatISO(endOfMonth(add(new Date(), { months: 3 }))),
    },
    selectedRecommendedAction: [],
  };

  return initialFilters;
};

export function isTSPGeoTab(tsp: string = ''): boolean {
  return tsp === TSP.TspGeotab;
}

export function getRecommendedActionProps(value: RecommendedAction) {
  switch (value) {
    case RecommendedAction.StronglyQuote:
      return {
        className:
          'bg-success-extraLight text-success-dark border border-success-main/50',
        children: 'Strong Quote',
        showIcon: true,
      };
    case RecommendedAction.Quote:
      return {
        className:
          'bg-success-extraLight text-success-dark border border-success-main/50',
        children: 'Quote',
        showIcon: true,
      };
    case RecommendedAction.Decline:
      return {
        className: 'bg-error-tint2 text-error-main',
        children: 'Decline',
      };
    case RecommendedAction.Neutral:
      return {
        className: 'bg-gold-tint text-warning-main',
        children: 'Further Review',
      };
    case RecommendedAction.NotApplicableForNonFleet:
      return {
        className: 'bg-text-darkGrey text-text-primary',
        children: 'Non Fleet',
      };
    case RecommendedAction.NotApplicableForRenewal:
      return {
        className: 'bg-success-light text-success-dark',
        children: 'Renewal',
      };
    case RecommendedAction.Pending:
      return {
        className: 'bg-text-darkGrey text-text-primary',
        children: 'Pending',
      };
  }
}
