import { z } from 'zod';
import { useState } from 'react';
import { HiPencil } from 'react-icons/hi';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';

import { Button, Dialog, Form, InputNumber } from '@nirvana/ui';
import { lossData } from '../constants/loss-data';

const lossSummarySchema = z.object({
  lossSummary: z.array(
    z.object({
      policyPeriod: z.string().min(1, 'Policy Period is required'),
      numberOfPUs: z.number().min(0, 'Number of PUs must be at least 0'),
      numberOfClaims: z.number().min(0, 'Number of Claims must be at least 0'),
      grossLoss: z.number().min(0, 'Gross Loss must be at least 0'),
    }),
  ),
});

export default function EditLossSummaryDialog() {
  const [open, setOpen] = useState(false);
  const form = useForm<z.infer<typeof lossSummarySchema>>({
    defaultValues: {
      lossSummary: lossData.map((i) => ({
        policyPeriod: i.period,
        numberOfPUs: i.pus,
        numberOfClaims: i.claims,
        grossLoss: parseFloat(i.grossLoss.replace(/[$,]/g, '')),
      })),
    },
    resolver: zodResolver(lossSummarySchema),
  });

  const { fields } = useFieldArray({
    name: 'lossSummary',
    control: form.control,
  });

  function onSubmit() {}

  function handleOpenChange(open: boolean) {
    setOpen(open);
    if (!open) {
      form.reset();
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <Dialog.Trigger asChild>
        <Button variant="secondary" startIcon={<HiPencil />}>
          Edit Rows
        </Button>
      </Dialog.Trigger>

      <Dialog.Content className="max-w-2xl">
        <Dialog.Header>
          <Dialog.Title>Edit AL Loss Data</Dialog.Title>
        </Dialog.Header>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-5 gap-4 py-3 text-xs font-semibold border-b bg-tw-gray-50 border-tw-gray-200">
              <div className="col-span-2 px-2">Policy Period</div>
              <div className="text-right"># of PUs</div>
              <div className="text-right"># of Claims</div>
              <div className="text-right">Gross Loss</div>
            </div>

            <div className="grid grid-cols-5 gap-4 py-4">
              {fields.map((field, index) => (
                <>
                  <p className="col-span-2 px-2">{field.policyPeriod}</p>
                  <Form.Field
                    control={form.control}
                    name={`lossSummary.${index}.numberOfPUs`}
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Control>
                          <InputNumber
                            value={field.value}
                            className="text-right"
                            onChange={field.onChange}
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                  <Form.Field
                    control={form.control}
                    name={`lossSummary.${index}.numberOfClaims`}
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Control>
                          <InputNumber
                            value={field.value}
                            className="text-right"
                            onChange={field.onChange}
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                  <Form.Field
                    control={form.control}
                    name={`lossSummary.${index}.grossLoss`}
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Control>
                          <InputNumber
                            prefix="$"
                            thousandSeparator
                            value={field.value}
                            className="text-right"
                            onChange={field.onChange}
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                </>
              ))}
            </div>

            <Dialog.Footer className="mt-4">
              <Dialog.Close asChild>
                <Button variant="text">Cancel</Button>
              </Dialog.Close>
              <Button type="submit">Save</Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
}
