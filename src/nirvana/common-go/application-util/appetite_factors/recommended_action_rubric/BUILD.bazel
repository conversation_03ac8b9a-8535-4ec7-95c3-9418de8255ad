load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "recommended_action_rubric",
    srcs = [
        "recommended_action_rubric.go",
        "types.go",
    ],
    importpath = "nirvanatech.com/nirvana/common-go/application-util/appetite_factors/recommended_action_rubric",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/telematics",
        "//nirvana/underwriting/appetite_guidelines/models",
        "//nirvana/underwriting/rule-engine/appetite_factors/appetite_factor",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_volatiletech_null_v8//:null",
    ],
)

go_test(
    name = "recommended_action_rubric_test",
    srcs = ["recommended_action_rubric_test.go"],
    embed = [":recommended_action_rubric"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/telematics",
        "//nirvana/underwriting/appetite_guidelines/models",
        "//nirvana/underwriting/rule-engine/appetite_factors/appetite_factor",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_volatiletech_null_v8//:null",
    ],
)
