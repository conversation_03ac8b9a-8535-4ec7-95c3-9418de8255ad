package recommended_action_rubric

import (
	"time"

	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"

	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/appetite_factor"
)

var (
	invalidAction = appetite_factor.RecommendedAction(0)
	invalidReason = appetite_factor.RecommendedActionReason(0)
)

// allowedExceptionTSPs is a list of TSPs that are allowed to follow the exception case in the UW recommended action.
// If a TSP is not in this list, it will be treated as a Recommended Action 'Decline' case.
//
//nolint:exhaustive
var allowedExceptionTSPs = map[telematics.TSP]bool{
	telematics.TSPLynx:                    true,
	telematics.TSPTrimble:                 true,
	telematics.TSPTeletracNavman:          true,
	telematics.TSPOrbcomm:                 true,
	telematics.TSPAzuga:                   true,
	telematics.TSPELDMandate:              true,
	telematics.TSPELDMandatePlus:          true,
	telematics.TSPELDMandatePro:           true,
	telematics.TSPAgilisLinxup:            true,
	telematics.TSPMonarchGPS:              true,
	telematics.TSPELDRider:                true,
	telematics.TSPOptimaELD:               true,
	telematics.TSPOmnitracs:               true,
	telematics.TSPOmnitracsES:             true,
	telematics.TSPOmnitracsXRS:            true,
	telematics.TSPATAndTFleet:             true,
	telematics.TSPContiGO:                 true,
	telematics.TSPEnVueTelematics:         true,
	telematics.TSPFleetistics:             true,
	telematics.TSPEVOELD:                  true,
	telematics.TSPZippyELD:                true,
	telematics.TSPATAndTFleetComplete:     true,
	telematics.TSPOntimeELD:               true,
	telematics.TSPAdvantageOne:            true,
	telematics.TSPFleetProfitCenter:       true,
	telematics.TSPBlueArrowTelematics:     true,
	telematics.TSPGridline:                true,
	telematics.TSPIoTab:                   true,
	telematics.TSPGPSTrackingCanada:       true,
	telematics.TSPArgosConnectedSolutions: true,
	telematics.TSPHighPointGPS:            true,
	telematics.TSPAttriX:                  true,
	telematics.TSPEagleWireless:           true,
	telematics.TSPGrayboxSolutions:        true,
	telematics.TSPIDELD:                   true,
	telematics.TSPApexUltima:              true,
	telematics.TSPBlueHorseELD:            true,
	telematics.TSPBlueInkTechnology:       true,
	telematics.TSPELDBooks:                true,
	telematics.TSPLightAndTravelELD:       true,
	telematics.TSPMaxELD:                  true,
	telematics.TSPMotionELD:               true,
	telematics.TSPRadicalELD:              true,
	telematics.TSPSmartelds:               true,
	telematics.TSPVulcansols:              true,
	telematics.TSPOneStepGPS:              true,
	telematics.TSPAirELD:                  true,
	telematics.TSPATELD:                   true,
	telematics.TSPFirstELD:                true,
	telematics.TSPMGKELD:                  true,
	telematics.TSPNoorELD:                 true,
	telematics.TSPOrientELD:               true,
}

type CalculateRecommendationActionRequest struct {
	AppetiteScore  appetite_factor.AppetiteScore
	MarketCategory *string
	IsException    bool
	TSPKind        *telematics.TSP
	// Deprecated
	IsCRatingRecent              bool // if the conditional rating is < 2 years w.r.t app review's effective date
	IsShortHaul                  *bool
	EffectiveDate                time.Time
	ReviewID                     string
	HazardZone                   HazardZone
	NumOfPU                      null.Int32
	IsRiskScoreElementNotPresent bool
	// Deprecated
	IsLossesBurnRateGreaterThan20K bool
	AppetiteGuidelinesInput        AppetiteGuidelinesInput
}

type HazardZone struct {
	DistancePercentage *float32
	DurationPercentage *float32
}

type AppetiteGuidelinesInput struct {
	IsDecline bool
	RuleName  *models.Rule
	Variant   *models.Variant
}
