package applicationutil

import (
	"testing"

	"github.com/stretchr/testify/require"
	"nirvanatech.com/nirvana/common-go/slice_utils"
)

func TestGetAvailableUW(t *testing.T) {
	tests := []struct {
		name           string
		isDevApp       bool
		expectedEmails []string
	}{
		{
			name:     "All underwriters with details",
			isDevApp: false,
			expectedEmails: []string{
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
			},
		},
		{
			name:           "<PERSON> <PERSON> underwriter in dev app",
			isDevApp:       true,
			expectedEmails: []string{"<EMAIL>"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			uwDetails, err := GetAllUnderWriterDetails(tt.isDevApp)
			require.NoError(t, err)

			emailsSeen := map[string]bool{}
			for _, uw := range uwDetails {
				require.NotNil(t, uw)
				require.NotNil(t, uw.Designation)
				require.NotNil(t, uw.PhoneNumber)

				emailsSeen[uw.Email] = true

				if slice_utils.Contains(tt.expectedEmails, uw.Email) {
					require.NotNil(t, uw.AssignmentCriteria, "Expected AssignmentCriteria for %s", uw.Email)
				}
			}

			for _, expectedEmail := range tt.expectedEmails {
				require.True(t, emailsSeen[expectedEmail], "Expected to find underwriter: %s", expectedEmail)
			}
		})
	}
}
