package slice_utils

import (
	"fmt"
	"maps"
	"math/rand"
	"slices"
	"sort"
	"time"

	"github.com/cockroachdb/errors"
	"golang.org/x/exp/constraints"
)

// Contains returns true iff slice contains elem
func Contains[T comparable](slice []T, elem T) bool {
	for _, v := range slice {
		if v == elem {
			return true
		}
	}
	return false
}

// ContainsCurried takes slice and returns a function such that
// ContainsCurried(slice)(elem) == Contains(slice, elem)
func ContainsCurried[T comparable](slice []T) func(T) bool {
	return func(elem T) bool {
		return Contains(slice, elem)
	}
}

// DoesNotContain returns true iff slice does not contain elem
func DoesNotContain[T comparable](slice []T, elem T) bool {
	return !Contains(slice, elem)
}

// DoesNotContainCurried takes slice and returns a function such that
// DoesNotContainCurried(slice)(elem) == DoesNotContain(slice, elem)
func DoesNotContainCurried[T comparable](slice []T) func(T) bool {
	return func(elem T) bool {
		return DoesNotContain(slice, elem)
	}
}

// ContainsAny returns true if any element of `elems` exists in `slice`.
func ContainsAny[T comparable](slice []T, elems ...T) bool {
	if len(slice) == 0 || len(elems) == 0 {
		return false
	}
	set := make(map[T]struct{}, len(slice))
	for _, val := range slice {
		set[val] = struct{}{}
	}
	for _, elem := range elems {
		if _, found := set[elem]; found {
			return true
		}
	}
	return false
}

// Remove removes all occurrences of elems from slice and returns
// the modified slice.
// NOTE: original slice should not be used after this function is called.
func Remove[T comparable](slice []T, elems ...T) []T {
	n := len(slice)
	for i := 0; i < n; {
		if Contains(elems, slice[i]) {
			slice[i], slice[n-1] = slice[n-1], slice[i]
			n = n - 1
		} else {
			i++
		}
	}
	return slice[:n]
}

// RemovePreserveOrder removes all occurrences of elems from slice and returns
// the modified slice. The order of elements in the slice is preserved.
func RemovePreserveOrder[T comparable](slice []T, elems ...T) []T {
	elemMap := make(map[T]struct{}, len(elems))
	for _, elem := range elems {
		elemMap[elem] = struct{}{}
	}

	// Preallocate result slice with same capacity
	result := make([]T, 0, len(slice))

	// Single pass through slice with O(1) lookups
	for _, item := range slice {
		// Check if item exists in map (constant time)
		if _, exists := elemMap[item]; !exists {
			result = append(result, item)
		}
	}

	return result
}

// Dedup takes a slice of items, and returns a new slice containing only the
// unique values from the input slice.
// Values are added to the return slice in the same order that they appear
// in the input slice, modulo any duplicates.
func Dedup[T comparable](items []T) []T {
	dedupMap := make(map[T]bool)
	var retval []T
	for _, s := range items {
		if dedupMap[s] {
			continue
		}
		dedupMap[s] = true
		retval = append(retval, s)
	}
	return retval
}

// DedupFunc is same as Dedup except it takes a fn to identify the property of element on which to dedup the slice.
func DedupFunc[T any, E comparable](items []T, fn func(a T) E) []T {
	dedupMap := make(map[E]bool)
	var retval []T
	for _, s := range items {
		key := fn(s)
		if dedupMap[key] {
			continue
		}
		dedupMap[key] = true
		retval = append(retval, s)
	}
	return retval
}

// Difference returns the elements in `a` that aren't in `b`.
func Difference[T comparable](a, b []T) []T {
	if len(a) == 0 {
		return a
	}

	if len(b) == 0 {
		return a
	}

	mb := make(map[T]bool, len(b))
	for _, x := range b {
		mb[x] = true
	}
	var diff []T
	for _, x := range a {
		if _, found := mb[x]; !found {
			diff = append(diff, x)
		}
	}
	return diff
}

// ToSliceOfPointers receives a slice of elements of type `T`, and
// returns another slice of pointers to type `T` ([]*T)
func ToSliceOfPointers[T any](in []T) []*T {
	items := make([]*T, 0, len(in))
	for i := range in {
		items = append(items, &in[i])
	}
	return items
}

// FromSliceOfPointers receives a slice of elements of type `*T`, and
// returns another slice of pointers to type `T` ([]T)
func FromSliceOfPointers[T any](in []*T) []T {
	items := make([]T, 0, len(in))
	for i := range in {
		if in[i] == nil {
			continue
		}
		items = append(items, *in[i])
	}
	return items
}

func ToExistsMap[T comparable](in []T) map[T]bool {
	if len(in) == 0 {
		return nil
	}
	ret := make(map[T]bool, len(in))
	for _, k := range in {
		ret[k] = true
	}
	return ret
}

// Map applies the given function (fn) to every element in the src slice,
// returning the slice of fn outputs
func Map[K, V any](src []K, fn func(K) V) []V {
	resp := make([]V, 0, len(src))
	for i := range src {
		resp = append(resp, fn(src[i]))
	}
	return resp
}

// MapErr applies the given function (fn) to every element in the src slice,
// returning the slice of fn outputs, or err
func MapErr[K, V any](src []K, fn func(K) (V, error)) ([]V, error) {
	resp := make([]V, 0, len(src))
	for i := range src {
		val, err := fn(src[i])
		if err != nil {
			return nil, err
		}
		resp = append(resp, val)
	}
	return resp, nil
}

// Split splits the slice provided (src) in to chunks such that all (returned)
// chunks have an upper bound on size = maxChunkSize.
func Split[K any](src []K, maxChunkSize int) [][]K {
	var resp [][]K
	for i := 0; i < len(src); i += maxChunkSize {
		j := i + maxChunkSize
		if j > len(src) {
			j = len(src)
		}
		resp = append(resp, src[i:j])
	}
	return resp
}

func Filter[K any](src []K, predicateFn func(K) bool) []K {
	var resp []K
	for i := range src {
		if val := src[i]; predicateFn(val) {
			resp = append(resp, val)
		}
	}
	return resp
}

// Find returns the first element in the slice that satisfies the predicateFn.
// If no element satisfies the predicateFn, it returns nil.
func Find[K any](src []K, predicateFn func(K) bool) *K {
	for i := range src {
		if val := src[i]; predicateFn(val) {
			return &val
		}
	}
	return nil
}

func Max[K constraints.Ordered](src []K) K {
	var maxVal K
	if len(src) == 0 {
		return maxVal
	}
	for i := range src {
		if val := src[i]; val > maxVal {
			maxVal = val
		}
	}
	return maxVal
}

func From[T any](vals ...T) []T {
	return vals
}

func Merge[T any](slices ...[]T) (res []T) {
	for _, s := range slices {
		res = append(res, s...)
	}
	return res
}

func GroupBy[K any, C comparable](items []K, keyFunc func(item K) C) map[C][]K {
	return GroupByAndReduce(items, keyFunc, func(slice []K, newitem K) []K {
		return append(slice, newitem)
	})
}

// GroupByAndReduce is similar to GroupBy except that it also reduces all the items in a group into a single
// value using the specified reduceFn.
func GroupByAndReduce[K any, C comparable, E any](
	items []K,
	keyFunc func(item K) C,
	reduceFn func(E, K) E,
) map[C]E {
	groupedItems := make(map[C]E)
	for _, item := range items {
		key := keyFunc(item)
		groupedItems[key] = reduceFn(groupedItems[key], item)
	}
	return groupedItems
}

func UseReplaceReducer[E any](_, new E) E {
	return new
}

func UseCountReducer[E any](count int, _ E) int {
	return count + 1
}

// Intersection returns the elements common between `a` and `b`.
// If `a` or `b` are empty, this function will return a `nil` slice.
func Intersection[T comparable](a, b []T) []T {
	return Filter(a, ContainsCurried(b))
}

func IntersectionExists[T comparable](a, b []T) bool {
	return len(Intersection(a, b)) > 0
}

// Union returns the combined element of `a` and `b` after which it does a dedupe
// If `a` or `b` are empty, this function will return a `nil` slice.
func Union[T comparable](a, b []T) []T {
	return Dedup(Merge(a, b))
}

func ToString[T fmt.Stringer](arr []T) (retVal []string) {
	retVal = make([]string, 0, len(arr))
	for _, item := range arr {
		retVal = append(retVal, item.String())
	}
	return
}

func AsString[T ~string](arr []T) (retVal []string) {
	retVal = make([]string, 0, len(arr))
	for _, item := range arr {
		retVal = append(retVal, string(item))
	}
	return
}

func ResolvePointer[T any](arr *[]T) []T {
	if arr == nil {
		return nil
	}
	return *arr
}

func Exists[T comparable](x T, arr []T) bool {
	for _, element := range arr {
		if x == element {
			return true
		}
	}
	return false
}

// RemoveIndex removes the element at `index` from the slice s.
func RemoveIndex[T any](s []T, index int) []T {
	if index < 0 || index >= len(s) {
		return s
	}
	ret := make([]T, 0)
	ret = append(ret, s[:index]...)
	return append(ret, s[index+1:]...)
}

// EqualUnorderedSlicesOfPointers compares two unordered slices of pointers for equality.
func EqualUnorderedSlicesOfPointers[T comparable](a, b []*T) bool {
	if len(a) != len(b) {
		return false
	}

	// Helper function to count number of occurrences of an item in the slice
	toCountMap := func(slice []*T) (map[T]int, int) {
		// variable to count nil pointers in the slice
		nilCounter := 0
		countMap := make(map[T]int)
		for _, item := range slice {
			if item != nil {
				countMap[*item]++
				continue
			}
			// Increase counter for nil pointers
			nilCounter++
		}
		return countMap, nilCounter
	}

	mapA, nilCounterA := toCountMap(a)
	mapB, nilCounterB := toCountMap(b)

	if nilCounterA != nilCounterB {
		return false
	}

	return maps.Equal(mapA, mapB)
}

// EqualSorted compares sorted slice_utils `a` and `b` and returns true if they are equal.
func EqualSorted[T constraints.Ordered](a, b []T) bool {
	if len(a) != len(b) {
		return false
	}

	aTemp := createSortedCopy(a)
	bTemp := createSortedCopy(b)
	for i := range a {
		if aTemp[i] != bTemp[i] {
			return false
		}
	}

	return true
}

func createSortedCopy[T constraints.Ordered](a []T) []T {
	aCopy := CreateCopy(a)
	slices.Sort(aCopy)
	return aCopy
}

func CreateCopy[T any](src []T) []T {
	dest := make([]T, len(src))
	copy(dest, src)
	return dest
}

func NonNil[T any](slice []T) []T {
	if slice == nil {
		return make([]T, 0)
	}
	return slice
}

// HasDuplicates returns true if the given slice contains duplicates, false otherwise.
// If the slice contains duplicates, the second return value is the first duplicate value found.
func HasDuplicates[T comparable](slice []T) (bool, T) {
	seen := make(map[T]bool)
	for _, val := range slice {
		if seen[val] {
			return true, val
		}
		seen[val] = true
	}
	var zeroValue T
	return false, zeroValue
}

func ChainSortFuncs[E any](sortFuncs ...func(a, b E) int) func(a, b E) int {
	return func(a, b E) int {
		for _, fn := range sortFuncs {
			ans := fn(a, b)
			switch ans {
			case 0:
				continue
			default:
				return ans
			}
		}
		return 0
	}
}

func Reverse[T any](s []T) {
	for i, j := 0, len(s)-1; i < j; i, j = i+1, j-1 {
		s[i], s[j] = s[j], s[i]
	}
}

// ToMap converts a slice to a map using the keyMapper and valueMapper functions to generate the key and value for each element.
// in case of multiple elements with the same key, the last value will be reflected in the map.
func ToMap[T any, K comparable, V any](slice []T, keyMapper func(T) K, valueMapper func(T) V) map[K]V {
	ret := make(map[K]V)
	for _, item := range slice {
		ret[keyMapper(item)] = valueMapper(item)
	}
	return ret
}

// SortPermutation returns an array of subscripts (indexes) so that if slice were to be sorted:
//
//	sorted[i] = slice_utils[subscripts[i]]
//
// would be true for all valid i
func SortPermutation[T any](slice []T, sortFunc func(a, b T) bool) (subscripts []int) {
	subscripts = make([]int, len(slice))
	for i := range slice {
		subscripts[i] = i
	}
	sort.SliceStable(subscripts, func(i, j int) bool {
		return sortFunc(slice[subscripts[i]], slice[subscripts[j]])
	})
	return
}

// ApplyPermutation returns a new slice res so that
//
//	res[i] = slice[subscripts[i]]
func ApplyPermutation[T any](slice []T, subscripts []int) (res []T) {
	res = make([]T, len(slice))
	for i := range slice {
		res[i] = slice[subscripts[i]]
	}
	return res
}

// ApplyInversePermutation returns a new slice res so that
//
// slice[i] = res[subscripts[i]]
func ApplyInversePermutation[T any](slice []T, subscripts []int) (res []T) {
	res = make([]T, len(slice))
	for i := range slice {
		res[subscripts[i]] = slice[i]
	}
	return res
}

// Overlap returns the elements common between `a` and `b`.
func Overlap[T comparable](a, b []T) []T {
	var res []T
	for _, x := range a {
		if Contains(b, x) {
			res = append(res, x)
		}
	}
	return res
}

// ToSliceOfAnys converts a slice of type T to a slice of type any.
func ToSliceOfAnys[T any](slice []T) []any {
	ret := make([]any, len(slice))
	for i, val := range slice {
		ret[i] = val
	}
	return ret
}

// Shuffle shuffles the elements of the slice in place.
func Shuffle[T any](slice []T) {
	// Create a new random source with current time as seed
	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	// Fisher-Yates shuffle algorithm
	for i := len(slice) - 1; i > 0; i-- {
		j := r.Intn(i + 1)
		slice[i], slice[j] = slice[j], slice[i]
	}
}

// ConvertType is a generic function that converts a slice of type T1 to a slice of type T2
// using the provided conversion function. If an error occurs, it returns the index in the error.
func ConvertType[T1, T2 any](input []T1, convertFunc func(T1) (T2, error)) ([]T2, error) {
	var result []T2
	for i, item := range input {
		convertedItem, err := convertFunc(item)
		if err != nil {
			return nil, errors.Wrapf(err, "error converting types at index %d", i)
		}
		result = append(result, convertedItem)
	}
	return result, nil
}

func StringersToAnys[T fmt.Stringer](slice []T) []any {
	ret := make([]any, len(slice))
	for i, val := range slice {
		ret[i] = val.String()
	}
	return ret
}

func Reduce[T1, T2 any](slice []T1, reducer func(T2, T1) T2) T2 {
	var acc T2
	for _, item := range slice {
		acc = reducer(acc, item)
	}
	return acc
}
