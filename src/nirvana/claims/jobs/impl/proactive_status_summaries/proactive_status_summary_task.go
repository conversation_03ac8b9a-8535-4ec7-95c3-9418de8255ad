package proactive_status_summaries

import (
	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/messages"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

type proactiveStatusSummaryTask struct {
	job_utils.NonRetryableTask[*messages.ProactiveStatusSummariesMessage]
	job_utils.NoopUndoTask[*messages.ProactiveStatusSummariesMessage]

	deps *impl.Deps
}

// the id of user <NAME_EMAIL>
const emailUserId = "2378c09d-d268-492a-b784-8f551a74f319"

func (t *proactiveStatusSummaryTask) ID() string {
	return "ClaimProactiveStatusSummaryTask"
}

func (t *proactiveStatusSummaryTask) Run(jCtx jtypes.Context, msg *messages.ProactiveStatusSummariesMessage) error {
	user, err := t.deps.AuthWrapper.FetchAuthzUser(jCtx, msg.UserId)
	if err != nil {
		return err
	}

	if user.ID.String() != emailUserId {
		return errors.New("only the PCU Email user can run this job")
	}

	ctx := authz.WithUser(jCtx, *user)
	err = t.deps.ClaimSummaryClient.MakeAndSendSummaries(ctx, msg.ClaimIds)
	if err != nil {
		log.Error(ctx, "Error while making and sending claim summaries", log.Err(err))
		return err
	}
	return nil
}
