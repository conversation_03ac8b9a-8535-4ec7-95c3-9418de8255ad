package messages

import (
	"encoding/json"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/jobber/jtypes"
)

type ProactiveStatusSummariesMessage struct {
	UserId   uuid.UUID
	ClaimIds []string
}

func (m *ProactiveStatusSummariesMessage) Validate() error {
	if m.UserId == uuid.Nil {
		return errors.New("UserId can't be zero-value UUID")
	}
	return nil
}

func (m *ProactiveStatusSummariesMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*m)
}

func (m *ProactiveStatusSummariesMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func ProactiveStatusSummariesMessageMessageUnmarshalFn(
	data []byte,
	version jtypes.MessageVersion,
) (*ProactiveStatusSummariesMessage, error) {
	var m ProactiveStatusSummariesMessage
	if version != 0 {
		return nil, errors.New("unsupported message version")
	}
	if err := json.Unmarshal(data, &m); err != nil {
		return nil, err
	}
	return &m, nil
}
