package gql

import (
	"time"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	gql_models "nirvanatech.com/nirvana/gqlschema/models"
)

type Claim struct {
	Id             uuid.UUID `graphql:"id,key"`
	ClaimNumber    string
	ExternalId     string
	LineOfBusiness string
	// TODO: (Rafa) Remove PolicyNumber field once it is fully replaced by Policy
	PolicyNumber string
	// TODO: (Rafa) Policy field should no longer be a pointer once it is fully implemented
	Policy        *gql_models.Policy
	Status        claim_enums.ClaimStatus
	AdjusterName  string
	AdjusterEmail string
	// Source identifies the origin system/provider for the claim (e.g. NARS, Snapsheet).
	Source       claim_enums.ClaimsProvider
	ReportedBy   *string
	ReportedAt   time.Time
	ModifiedAt   time.Time
	LossDatetime *time.Time
}

// ClaimFromBL converts a db.Claim to a Claim for GraphQL.
func ClaimFromBL(claim db.Claim) *Claim {
	// ExternalId is used as 'claimNumber' for NARS claims.
	claimNumber := claim.GetClaimNumber()

	return &Claim{
		Id:             claim.Id,
		ClaimNumber:    claimNumber,
		ExternalId:     claim.ExternalId,
		LineOfBusiness: claim.LineOfBusiness,
		PolicyNumber:   claim.PolicyNumber,
		Status:         claim.Status,
		AdjusterName:   claim.AdjusterName,
		AdjusterEmail:  claim.AdjusterEmail,
		Source:         claim.Source,
		ReportedBy:     claim.ReportedBy,
		ReportedAt:     claim.ReportedAt,
		ModifiedAt:     claim.ModifiedAt,
		LossDatetime:   claim.LossDatetime,
	}
}

func (c *Claim) WithPolicy(p policy.Policy, isTest bool) *Claim {
	c.Policy = gql_models.PolicyFromDB(p, isTest)
	return c
}

type ClaimStatusChange struct {
	Id              uuid.UUID `graphql:"id,key"`
	ClaimExternalId string
	Value           claim_enums.ClaimStatus
	CreatedAt       time.Time
}

func StatusChangeToGQL(status db.ClaimStatus) ClaimStatusChange {
	return ClaimStatusChange{
		Id:              status.Id,
		ClaimExternalId: status.ClaimExternalId,
		Value:           status.Value,
		CreatedAt:       status.CreatedAt,
	}
}
