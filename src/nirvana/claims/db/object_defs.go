package db

import (
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
)

// Claim standardizes claims across the system regardless of origin.
// Source field identifies the provider via ClaimsProvider enum.
type Claim struct {
	Id uuid.UUID
	// ExternalId is the unique identifier for the claim in the provider's system.
	ExternalId     string
	ClaimNumber    string
	Status         claim_enums.ClaimStatus
	PolicyNumber   string
	LineOfBusiness string
	AdjusterName   string
	AdjusterEmail  string

	// ReportedAt represents the timestamp for when the incident was reported.
	ReportedAt      time.Time
	ReportedBy      *string
	LossDatetime    *time.Time
	LossState       *string
	LossDescription *string

	Source claim_enums.ClaimsProvider

	// ModifiedAt represents the timestamp for when a claim was last updated in the provider's system.
	ModifiedAt time.Time
	// CreatedAt represents the timestamp for when the record was created.
	CreatedAt time.Time
	// UpdateAt represents the timestamp for when the record was last updated.
	UpdatedAt time.Time
}

// GetClaimNumber returns the representation of the claim number that should be used for the claim.
// For Snapsheet claims, the actual 'ClaimNumber' is used.
// For NARS claims, the 'ExternalId' is used instead.
func (c *Claim) GetClaimNumber() string {
	claimNumber := c.ClaimNumber
	if c.Source == claim_enums.ClaimsProviderNars {
		claimNumber = c.ExternalId
	}
	return claimNumber
}

type Builder struct {
	object *Claim
	err    error
}

func New(source claim_enums.ClaimsProvider) *Builder {
	b := &Builder{object: &Claim{}}
	if !source.IsAClaimsProvider() {
		b.err = errors.Wrapf(b.err, "invalid claims provider %s", source)
		return b
	}
	b.object.Source = source
	return b
}

func (b *Builder) Build() (*Claim, error) {
	if b.err != nil {
		return nil, b.err
	}
	return b.object, nil
}

func (b *Builder) WithDefaultMockData() *Builder {
	now := time.Now()

	b.object.Id = uuid.New()
	b.object.ExternalId = uuid.New().String()
	b.object.Status = claim_enums.ClaimStatusOpen
	b.object.PolicyNumber = "NISTK1000001-24"
	b.object.ClaimNumber = "ID-876321"
	b.object.LineOfBusiness = "AUTO"
	b.object.LossDatetime = pointer_utils.Time(now)
	b.object.LossState = pointer_utils.String("IL")
	b.object.LossDescription = pointer_utils.String("Car accident")
	b.object.AdjusterName = "J Doe"
	b.object.AdjusterEmail = "<EMAIL>"
	b.object.ReportedBy = pointer_utils.String("J Doe")
	b.object.ReportedAt = now
	b.object.ModifiedAt = now
	b.object.CreatedAt = now
	b.object.UpdatedAt = now
	return b
}

func (b *Builder) WithId(id uuid.UUID) *Builder {
	b.object.Id = id
	return b
}

func (b *Builder) WithExternalId(externalId string) *Builder {
	b.object.ExternalId = externalId
	return b
}

func (b *Builder) WithClaimNumber(claimNumber string) *Builder {
	b.object.ClaimNumber = claimNumber
	return b
}

func (b *Builder) WithStatus(status claim_enums.ClaimStatus) *Builder {
	if !status.IsAClaimStatus() {
		b.err = errors.Wrapf(b.err, "invalid claim status %s", status)
		return b
	}
	b.object.Status = status
	return b
}

func (b *Builder) WithPolicyNumber(policyNumber string) *Builder {
	b.object.PolicyNumber = policyNumber
	return b
}

func (b *Builder) WithLineOfBusiness(lob string) *Builder {
	b.object.LineOfBusiness = lob
	return b
}

func (b *Builder) WithAdjusterName(adjusterName string) *Builder {
	b.object.AdjusterName = adjusterName
	return b
}

func (b *Builder) WithAdjusterEmail(adjusterEmail string) *Builder {
	b.object.AdjusterEmail = adjusterEmail
	return b
}

func (b *Builder) WithReportedAt(reportedAt time.Time) *Builder {
	b.object.ReportedAt = reportedAt
	return b
}

func (b *Builder) WithReportedBy(reportedBy *string) *Builder {
	b.object.ReportedBy = reportedBy
	return b
}

func (b *Builder) WithLossDatetime(lossDatetime *time.Time) *Builder {
	b.object.LossDatetime = lossDatetime
	return b
}

func (b *Builder) WithCreatedAt(createdAt time.Time) *Builder {
	b.object.CreatedAt = createdAt
	return b
}

func (b *Builder) WithModifiedAt(modifiedAt time.Time) *Builder {
	b.object.ModifiedAt = modifiedAt
	return b
}

func (b *Builder) WithLossState(lossState *string) *Builder {
	b.object.LossState = lossState
	return b
}

func (b *Builder) WithLossDescription(lossDescription *string) *Builder {
	b.object.LossDescription = lossDescription
	return b
}

func (b *Builder) WithUpdatedAt(updatedAt time.Time) *Builder {
	b.object.UpdatedAt = updatedAt
	return b
}

type ClaimStatus struct {
	Id              uuid.UUID
	ClaimId         uuid.UUID
	ClaimExternalId string
	Value           claim_enums.ClaimStatus
	CreatedAt       time.Time
}

func NewClaimStatus(
	claimId uuid.UUID,
	claimExternalId string,
	value claim_enums.ClaimStatus,
	createdAt time.Time,
) ClaimStatus {
	return ClaimStatus{
		Id:              uuid.New(),
		ClaimId:         claimId,
		ClaimExternalId: claimExternalId,
		Value:           value,
		CreatedAt:       createdAt,
	}
}
