package gql_test

import (
	"context"
	"net/url"

	"go.uber.org/mock/gomock"
	"nirvanatech.com/nirvana/claims/reporting/enums"
	"nirvanatech.com/nirvana/claims/reporting/gql"
	brdrxdb "nirvanatech.com/nirvana/claims/reporting/internal/db/postgres/bordereaux_reports"
	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/infra/authz"
)

func (s *resolverTestSuite) TestGenerateBordereauxReport() {
	ctx := context.Background()

	wantBordereauxClaims := []snowflake.BordereauxClaimRow{
		{
			POLICY_NUMBER: "TINCA0100010-24",
			CLAIM_NUMBER:  "CLAIM-111",
		},
		{
			POLICY_NUMBER: "TINCA0200020-25",
			CLAIM_NUMBER:  "CLAIM-222",
		},
	}
	s.SnowflakeWrapper.
		EXPECT().
		GetBordereauxClaimsRows(gomock.Any(), gomock.Any()).
		Return(wantBordereauxClaims, nil).
		AnyTimes()

	wantBordereauxReserveSummaries := []snowflake.BordereauxReserveSummaryRow{
		{
			CLAIM_NUMBER:  "CLAIM-111",
			AOE_PAID:      1_000,
			VIN:           pointer_utils.ToPointer("12345678901234567"),
			VEHICLE_MAKE:  pointer_utils.ToPointer("Toyota"),
			VEHICLE_MODEL: pointer_utils.ToPointer("Camry"),
		},
		{
			CLAIM_NUMBER:  "CLAIM-222",
			AOE_PAID:      2_000,
			VIN:           pointer_utils.ToPointer("12345678901234568"),
			VEHICLE_MAKE:  pointer_utils.ToPointer("Honda"),
			VEHICLE_MODEL: pointer_utils.ToPointer("Civic"),
		},
	}
	s.SnowflakeWrapper.
		EXPECT().
		GetBordereauxReserveSummariesRows(gomock.Any(), gomock.Any()).
		Return(wantBordereauxReserveSummaries, nil).
		AnyTimes()

	testCases := []struct {
		name    string
		carrier enums.Carrier
		user    authz.User
		wantErr bool
	}{
		{
			name:    "allowed user with valid carrier",
			carrier: enums.CarrierMSTransverse,
			user:    s.ClaimsAdmin.AuthzUser(),
			wantErr: false,
		},
		{
			name:    "allowed user with invalid carrier",
			carrier: 0,
			user:    s.ClaimsAdmin.AuthzUser(),
			wantErr: true,
		},
		{
			name:    "disallowed user",
			carrier: enums.CarrierMSTransverse,
			user:    s.FleetAdmin.AuthzUser(),
			wantErr: true,
		},
	}
	for _, tc := range testCases {
		s.Run(tc.name, func() {
			response, err := s.generateBordereauxReport(ctx, tc.user, tc.carrier)
			if tc.wantErr {
				s.Require().Error(err)
				return
			}

			s.Require().NoError(err)
			s.Require().NotNil(response)

			storedReports, err := s.BordereauxDataWrapper.Get(ctx)
			s.Require().NoError(err)

			got := response.GenerateBordereauxReport
			gotReport := slice_utils.Find(storedReports, func(r brdrxdb.BordereauxReport) bool {
				return r.Id == got.Id
			})
			s.Require().NotNil(gotReport)
			s.Equal(tc.carrier, gotReport.Carrier)
			s.Equal(tc.user.ID, gotReport.GeneratedBy)
			s.True(isValidURL(got.DownloadURL))
		})
	}
}

type generateBordereauxReportResult struct {
	GenerateBordereauxReport gql.GenerateBordereauxReportResponse
}

func (s *resolverTestSuite) generateBordereauxReport(
	ctx context.Context,
	user authz.User,
	carrier enums.Carrier,
) (*generateBordereauxReportResult, error) {
	mutation := `mutation($carrier: Carrier!) {
		generateBordereauxReport(carrier: $carrier) {
			id
			downloadURL
		}
	}`

	mutationVars := map[string]any{
		"carrier": carrier,
	}

	var result generateBordereauxReportResult
	if err := s.QueryClient.Mutation(ctx, user, mutation, mutationVars).ResultAs(&result); err != nil {
		return nil, err
	}
	return &result, nil
}

func isValidURL(str string) bool {
	u, err := url.ParseRequestURI(str)
	return err == nil && u.Scheme != "" && u.Host != ""
}
