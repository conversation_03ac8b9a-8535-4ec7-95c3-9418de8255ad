load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "builders",
    srcs = ["lossrunsreport.go"],
    importpath = "nirvanatech.com/nirvana/claims/reporting/loss_runs/builders",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/reporting/loss_runs/db",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/jobber/jtypes",
        "@com_github_google_uuid//:uuid",
    ],
)
