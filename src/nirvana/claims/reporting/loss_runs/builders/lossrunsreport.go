package builders

import (
	"time"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/claims/reporting/loss_runs/db"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

type LossRunsReportBuilder struct {
	object *db.LossRunsReport
}

func NewLossRunsReportBuilder() *LossRunsReportBuilder {
	return &LossRunsReportBuilder{object: &db.LossRunsReport{}}
}

func (b *LossRunsReportBuilder) Build() *db.LossRunsReport {
	return b.object
}

func (b *LossRunsReportBuilder) WithDefaultMockData() *LossRunsReportBuilder {
	return &LossRunsReportBuilder{
		&db.LossRunsReport{
			Id:           uuid.New(),
			PolicyNumber: "NISTK1000001-24",
			GeneratedAt:  time.Now().Round(time.Millisecond).UTC(),
			RequestedBy:  uuid.New(),
			FileHandleId: pointer_utils.ToPointer(uuid.New()),
		},
	}
}

func (b *LossRunsReportBuilder) WithDefaultJobTriggeredMockData() *LossRunsReportBuilder {
	return &LossRunsReportBuilder{
		&db.LossRunsReport{
			Id:           uuid.New(),
			PolicyNumber: "NISTK1000001-24",
			GeneratedAt:  time.Now().Round(time.Millisecond).UTC(),
			RequestedBy:  uuid.New(),
			JobRunId: pointer_utils.ToPointer(
				jtypes.NewJobRunId(jtypes.JobId(uuid.New().String())+"::", 1),
			),
		},
	}
}

func (b *LossRunsReportBuilder) WithId(id uuid.UUID) *LossRunsReportBuilder {
	b.object.Id = id
	return b
}

func (b *LossRunsReportBuilder) WithPolicyNumber(policyNumber string) *LossRunsReportBuilder {
	b.object.PolicyNumber = policyNumber
	return b
}

func (b *LossRunsReportBuilder) WithGeneratedAt(generatedAt time.Time) *LossRunsReportBuilder {
	b.object.GeneratedAt = generatedAt
	return b
}

func (b *LossRunsReportBuilder) WithRequestedBy(requestedBy uuid.UUID) *LossRunsReportBuilder {
	b.object.RequestedBy = requestedBy
	return b
}

func (b *LossRunsReportBuilder) WithFileHandleId(fileHandleId uuid.UUID) *LossRunsReportBuilder {
	b.object.FileHandleId = &fileHandleId
	return b
}
