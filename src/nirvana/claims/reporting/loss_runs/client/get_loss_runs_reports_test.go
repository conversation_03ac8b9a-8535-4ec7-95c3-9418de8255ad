package client_test

import (
	"context"
	"time"

	"github.com/google/uuid"
	"nirvanatech.com/nirvana/claims/reporting/loss_runs/db"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
)

func (s *lossRunsRenewalsClientTestSuite) TestGetLossRunsReports() {
	ctx := context.Background()

	s.Run("no loss runs reports", func() {
		reports, err := s.client.GetLossRunsReports(ctx)
		s.Require().NoError(err)
		s.Require().Len(reports, 0)
	})

	s.seedLossRunsReports(ctx)

	s.Run("get loss runs reports", func() {
		reports, err := s.client.GetLossRunsReports(ctx)
		s.Require().NoError(err)
		s.Require().Len(reports, 2)
	})
}

func (s *lossRunsRenewalsClientTestSuite) seedLossRunsReports(ctx context.Context) {
	lossRunsReports := []db.LossRunsReport{
		{
			Id:           uuid.New(),
			PolicyNumber: s.expiredPolicyNumberWithNarsClaimsOnly,
			GeneratedAt:  time_utils.NewDate(2021, 12, 31).ToTime().Round(time.Millisecond).UTC(),
			RequestedBy:  uuid.New(),
			FileHandleId: pointer_utils.ToPointer(uuid.New()),
			CreatedAt:    time_utils.NewDate(2021, 12, 31).ToTime().Round(time.Millisecond).UTC(),
		},
		{
			Id:           uuid.New(),
			PolicyNumber: s.activePolicyNumberWithNoClaims,
			GeneratedAt:  time_utils.NewDate(2022, 1, 1).ToTime().Round(time.Millisecond).UTC(),
			RequestedBy:  uuid.New(),
			FileHandleId: pointer_utils.ToPointer(uuid.New()),
			CreatedAt:    time_utils.NewDate(2022, 1, 1).ToTime().Round(time.Millisecond).UTC(),
		},
	}

	for _, report := range lossRunsReports {
		s.Require().NoError(s.insuredWrapper.InsertLossRunsReport(ctx, report))
	}
}
