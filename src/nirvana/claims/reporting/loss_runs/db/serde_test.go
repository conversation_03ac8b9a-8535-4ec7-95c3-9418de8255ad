package db

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_models/insured"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

func TestLossRunsSerdeUtils(t *testing.T) {
	suite.Run(t, new(lossRunsSerdeUtilsTestSuite))
}

type lossRunsSerdeUtilsTestSuite struct {
	suite.Suite
}

func (s *lossRunsSerdeUtilsTestSuite) Test_LossRunsReportRoundTrip() {
	testCases := []struct {
		name    string
		lrr     *LossRunsReport
		wantErr bool
	}{
		{
			name: "valid LossRunsReport",
			lrr: NewLossRunsReport("NISTK100001-24", time.Now(), uuid.New()).
				WithFileHandleId(uuid.New()),
			wantErr: false,
		},
		{
			name: "valid LossRunsReport (job-triggered)",
			lrr: NewLossRunsReport("NISTK100001-24", time.Now(), uuid.New()).
				WithFileHandleId(uuid.New()).
				WithJobRunId(
					jtypes.NewJobRunId(
						jtypes.JobId(uuid.New().String())+"::",
						1,
					)),
			wantErr: false,
		},
		{
			name: "valid no-file LossRunsReport (job-triggered)", // For when report generation is pending or failed
			lrr: NewLossRunsReport("NISTK100001-24", time.Now(), uuid.New()).
				WithJobRunId(
					jtypes.NewJobRunId(
						jtypes.JobId(uuid.New().String())+"::",
						1,
					)),
			wantErr: false,
		},
		{
			name: "missing Id",
			lrr: &LossRunsReport{
				PolicyNumber: "NISTK100001-24",
				GeneratedAt:  time.Now(),
				RequestedBy:  uuid.New(),
				FileHandleId: pointer_utils.ToPointer(uuid.New()),
				JobRunId: pointer_utils.ToPointer(
					jtypes.NewJobRunId(
						jtypes.JobId(uuid.New().String())+"::",
						1,
					)),
			},
			wantErr: true,
		},
		{
			name: "missing PolicyNumber",
			lrr: NewLossRunsReport("", time.Now(), uuid.New()).
				WithFileHandleId(uuid.New()).
				WithJobRunId(
					jtypes.NewJobRunId(
						jtypes.JobId(uuid.New().String())+"::",
						1,
					)),
			wantErr: true,
		},
		{
			name: "missing GeneratedAt",
			lrr: NewLossRunsReport("NISTK100001-24", time.Time{}, uuid.New()).
				WithFileHandleId(uuid.New()).
				WithJobRunId(
					jtypes.NewJobRunId(
						jtypes.JobId(uuid.New().String())+"::",
						1,
					)),
			wantErr: true,
		},
		{
			name: "missing RequestedBy",
			lrr: NewLossRunsReport("NISTK100001-24", time.Now(), uuid.Nil).
				WithFileHandleId(uuid.New()).
				WithJobRunId(
					jtypes.NewJobRunId(
						jtypes.JobId(uuid.New().String())+"::",
						1,
					)),
			wantErr: true,
		},
		{
			name: "invalid FileHandleId",
			lrr: NewLossRunsReport("NISTK100001-24", time.Now(), uuid.New()).
				WithFileHandleId(uuid.Nil).
				WithJobRunId(
					jtypes.NewJobRunId(
						jtypes.JobId(uuid.New().String())+"::",
						1,
					)),
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			dbLossRunsReport, err := lossRunsReportToDb(tc.lrr)
			if tc.wantErr {
				s.Require().Error(err)
				s.Require().Nil(dbLossRunsReport)
				return
			}

			s.Require().NoError(err)

			result, err := lossRunsReportFromDb(dbLossRunsReport)
			s.Require().NoError(err)

			s.Require().Equal(tc.lrr, result)
		})
	}
}

func (s *lossRunsSerdeUtilsTestSuite) Test_LossRunsReportFromDbErrors() {
	testCases := []struct {
		name             string
		dbLossRunsReport *insured.LossRunsReport
		wantErr          bool
	}{
		{
			name: "invalid ID",
			dbLossRunsReport: &insured.LossRunsReport{
				ID:           "invalid",
				PolicyNumber: "NISTK100001-24",
				GeneratedAt:  time.Now(),
				RequestedBy:  uuid.NewString(),
				FileHandleID: null.StringFrom(uuid.NewString()),
				JobRunID: null.StringFrom(
					jtypes.NewJobRunId(
						jtypes.JobId(
							uuid.New().String())+"::",
						1,
					).String()),
				CreatedAt: time.Now(),
			},
			wantErr: true,
		},
		{
			name: "invalid RequestedBy",
			dbLossRunsReport: &insured.LossRunsReport{
				ID:           uuid.NewString(),
				PolicyNumber: "NISTK100001-24",
				GeneratedAt:  time.Now(),
				RequestedBy:  "invalid",
				FileHandleID: null.StringFrom(uuid.NewString()),
				JobRunID: null.StringFrom(
					jtypes.NewJobRunId(
						jtypes.JobId(
							uuid.New().String())+"::",
						1,
					).String()),
				CreatedAt: time.Now(),
			},
			wantErr: true,
		},
		{
			name: "invalid FileHandleID",
			dbLossRunsReport: &insured.LossRunsReport{
				ID:           uuid.NewString(),
				PolicyNumber: "NISTK100001-24",
				GeneratedAt:  time.Now(),
				RequestedBy:  uuid.NewString(),
				FileHandleID: null.StringFrom("invalid"),
				JobRunID: null.StringFrom(
					jtypes.NewJobRunId(
						jtypes.JobId(
							uuid.New().String())+"::",
						1,
					).String()),
				CreatedAt: time.Now(),
			},
			wantErr: true,
		},
		{
			name: "invalid JobRunID",
			dbLossRunsReport: &insured.LossRunsReport{
				ID:           uuid.NewString(),
				PolicyNumber: "NISTK100001-24",
				GeneratedAt:  time.Now(),
				RequestedBy:  uuid.NewString(),
				FileHandleID: null.StringFrom(uuid.NewString()),
				JobRunID: null.StringFrom(
					"invalid",
				),
				CreatedAt: time.Now(),
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			_, err := lossRunsReportFromDb(tc.dbLossRunsReport)
			s.Require().Error(err)
		})
	}
}
