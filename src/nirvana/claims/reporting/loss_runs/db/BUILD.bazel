load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "db",
    srcs = [
        "fx.go",
        "interface.go",
        "object_defs.go",
        "serde.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/reporting/loss_runs/db",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/insured",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/jobber/jtypes",
        "@com_github_benb<PERSON><PERSON><PERSON>_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "db_test",
    srcs = [
        "serde_test.go",
        "wrapper_test.go",
    ],
    embed = [":db"],
    deps = [
        "//nirvana/claims/reporting/loss_runs/builders",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/insured",
        "//nirvana/infra/fx/testloader",
        "//nirvana/jobber/jtypes",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
