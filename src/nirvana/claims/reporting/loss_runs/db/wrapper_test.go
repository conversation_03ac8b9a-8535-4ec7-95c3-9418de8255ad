package db_test

import (
	"context"
	"testing"

	"github.com/benb<PERSON><PERSON>son/clock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/claims/reporting/loss_runs/builders"
	"nirvanatech.com/nirvana/claims/reporting/loss_runs/db"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	db_api "nirvanatech.com/nirvana/db-api"
	models_insured "nirvanatech.com/nirvana/db-api/db_models/insured"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func TestLossRunsWrapper(t *testing.T) {
	suite.Run(t, new(lossRunsWrapperTestSuite))
}

type lossRunsWrapperTestSuite struct {
	suite.Suite

	fxapp *fxtest.App

	db      db_api.NirvanaRW
	wrapper db.Wrapper

	ctx context.Context
	clk clock.Clock
}

func (s *lossRunsWrapperTestSuite) SetupTest() {
	var env struct {
		fx.In

		Clk clock.Clock

		DB      db_api.NirvanaRW
		Wrapper db.Wrapper
	}

	s.fxapp = testloader.RequireStart(s.T(), &env)

	s.db = env.DB
	s.wrapper = env.Wrapper

	s.ctx = context.Background()
	s.clk = env.Clk
}

func (s *lossRunsWrapperTestSuite) TearDownTest() {
	s.fxapp.RequireStop()
}

func (s *lossRunsWrapperTestSuite) Test_GetLossRunsReports() {
	s.Run("No reports", func() {
		reports, err := s.wrapper.GetLossRunsReports(s.ctx)
		s.Require().NoError(err)
		s.Require().Empty(reports)
	})

	s.seedLossRunsReports(s.ctx, s.clk, s.db)

	s.Run("Retrieve all reports", func() {
		reports, err := s.wrapper.GetLossRunsReports(s.ctx)
		s.Require().NoError(err)
		s.Require().Len(reports, 2)
	})
}

func (s *lossRunsWrapperTestSuite) seedLossRunsReports(ctx context.Context, clk clock.Clock, db db_api.NirvanaRW) {
	dbReports := []models_insured.LossRunsReport{
		{
			ID:           uuid.NewString(),
			PolicyNumber: "NISTK4932942-25",
			GeneratedAt:  clk.Now(),
			RequestedBy:  uuid.NewString(),
			FileHandleID: null.StringFrom(uuid.NewString()),
		},
		{
			ID:           uuid.NewString(),
			PolicyNumber: "NISTK4932942-26",
			GeneratedAt:  clk.Now(),
			RequestedBy:  uuid.NewString(),
			FileHandleID: null.StringFrom(uuid.NewString()),
		},
	}
	for _, report := range dbReports {
		s.Require().NoError(report.Insert(ctx, db, boil.Infer()))
	}
}

func (s *lossRunsWrapperTestSuite) Test_InsertLossRunsReport() {
	s.Run("Successful insertion", func() {
		l := builders.NewLossRunsReportBuilder().
			WithDefaultMockData().
			WithPolicyNumber("NISTK4932942-27").
			Build()

		err := s.wrapper.InsertLossRunsReport(s.ctx, *l)
		s.Require().NoError(err)

		// Verify the report was inserted
		dbReports, err := models_insured.LossRunsReports(
			models_insured.LossRunsReportWhere.ID.EQ(l.Id.String()),
		).All(s.ctx, s.db.DB)
		s.Require().NoError(err)
		s.Require().Len(dbReports, 1)

		dbReport := dbReports[0]
		s.assertLossRunsReportsEqual(l, dbReport)
	})

	s.Run("Successful insertion (job-triggered)", func() {
		l := builders.NewLossRunsReportBuilder().
			WithDefaultJobTriggeredMockData().
			WithPolicyNumber("NISTK4932942-27").
			Build()

		err := s.wrapper.InsertLossRunsReport(s.ctx, *l)
		s.Require().NoError(err)

		// Verify the report was inserted
		dbReports, err := models_insured.LossRunsReports(
			models_insured.LossRunsReportWhere.ID.EQ(l.Id.String()),
		).All(s.ctx, s.db.DB)
		s.Require().NoError(err)
		s.Require().Len(dbReports, 1)

		dbReport := dbReports[0]
		s.assertLossRunsReportsEqual(l, dbReport)
	})

	s.Run("Fail to insert report because of missing GeneratedAt", func() {
		l := builders.NewLossRunsReportBuilder().
			WithId(uuid.New()).
			WithPolicyNumber("NISTK4932942-27").
			WithRequestedBy(uuid.New()).
			WithFileHandleId(uuid.New()).
			Build()
		s.Require().Error(s.wrapper.InsertLossRunsReport(s.ctx, *l))
	})

	s.Run("Failed insertion due to duplicate ID", func() {
		l := builders.NewLossRunsReportBuilder().
			WithDefaultMockData().
			WithPolicyNumber("NISTK4932942-28").
			Build()

		// Insert the report for the first time
		err := s.wrapper.InsertLossRunsReport(s.ctx, *l)
		s.Require().NoError(err)

		// Attempt to insert the same report again
		err = s.wrapper.InsertLossRunsReport(s.ctx, *l)
		s.Require().Error(err)
		s.Require().Contains(err.Error(), "could not insert loss runs report")
	})

	s.Run("Failed insertion due to duplicate job run id", func() {
		l1 := builders.NewLossRunsReportBuilder().
			WithDefaultJobTriggeredMockData().
			WithPolicyNumber("NISTK4932942-28").
			Build()

		// First insertion should succeed
		err := s.wrapper.InsertLossRunsReport(s.ctx, *l1)
		s.Require().NoError(err)

		// Duplicate Report with different ID
		l2 := *l1
		l2.Id = uuid.New()

		// Attempt to insert report with duplicate job run id
		err = s.wrapper.InsertLossRunsReport(s.ctx, l2)
		s.Require().Error(err)
		s.Require().Contains(err.Error(), "could not insert loss runs report")
	})
}

func (s *lossRunsWrapperTestSuite) assertLossRunsReportsEqual(expected *db.LossRunsReport, actual *models_insured.LossRunsReport) {
	var jobRunId *string
	if expected.JobRunId != nil {
		jobRunId = pointer_utils.ToPointer(expected.JobRunId.String())
	}

	s.Equal(expected.Id.String(), actual.ID)
	s.Equal(expected.PolicyNumber, actual.PolicyNumber)
	s.Equal(expected.GeneratedAt, actual.GeneratedAt)
	s.Equal(expected.RequestedBy.String(), actual.RequestedBy)
	s.Equal(pointer_utils.UUIDStringOrNil(expected.FileHandleId), actual.FileHandleID.Ptr())
	s.Equal(jobRunId, actual.JobRunID.Ptr())
}
