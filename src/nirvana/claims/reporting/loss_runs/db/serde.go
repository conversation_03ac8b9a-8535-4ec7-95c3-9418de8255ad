package db

import (
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_models/insured"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

func lossRunsReportFromDb(dbLossRunsReport *insured.LossRunsReport) (*LossRunsReport, error) {
	id, err := uuid.Parse(dbLossRunsReport.ID)
	if err != nil {
		return nil, errors.Wrap(err, "could not parse loss runs report id")
	}

	requestedBy, err := uuid.Parse(dbLossRunsReport.RequestedBy)
	if err != nil {
		return nil, errors.Wrap(err, "could not parse requestedBy")
	}

	fileHandleId, err := pointer_utils.UUIDParseOrNil(
		dbLossRunsReport.FileHandleID.Ptr(),
	)
	if err != nil {
		return nil, errors.Wrap(err, "could not parse fileHandleId")
	}

	var jobRunId *jtypes.JobRunId
	if !dbLossRunsReport.JobRunID.IsZero() {
		parsedJobRunId, err := jtypes.JobRunIdFromString(dbLossRunsReport.JobRunID.String)
		if err != nil {
			return nil, errors.Wrap(err, "could not parse jobRunId")
		}

		jobRunId = &parsedJobRunId
	}

	return &LossRunsReport{
		Id:           id,
		PolicyNumber: dbLossRunsReport.PolicyNumber,
		GeneratedAt:  dbLossRunsReport.GeneratedAt,
		RequestedBy:  requestedBy,
		FileHandleId: fileHandleId,
		JobRunId:     jobRunId,
		CreatedAt:    dbLossRunsReport.CreatedAt,
	}, nil
}

func lossRunsReportsFromDb(dbLossRunsReports []*insured.LossRunsReport) ([]LossRunsReport, error) {
	var result []LossRunsReport
	for _, dbLossRunsReport := range dbLossRunsReports {
		lossRunsReport, err := lossRunsReportFromDb(dbLossRunsReport)
		if err != nil {
			return nil, errors.Wrap(err, "could not convert loss runs report from db")
		}
		result = append(result, *lossRunsReport)
	}
	return result, nil
}

func lossRunsReportToDb(lossRunsReport *LossRunsReport) (*insured.LossRunsReport, error) {
	if err := lossRunsReport.Validate(); err != nil {
		return nil, errors.Wrap(err, "invalid loss runs report")
	}

	handleId := pointer_utils.UUIDStringOrNil(lossRunsReport.FileHandleId)

	var jobRunId *string
	if lossRunsReport.JobRunId != nil {
		jobRunId = pointer_utils.ToPointer(
			lossRunsReport.JobRunId.String(),
		)
	}

	return &insured.LossRunsReport{
		ID:           lossRunsReport.Id.String(),
		PolicyNumber: lossRunsReport.PolicyNumber,
		GeneratedAt:  lossRunsReport.GeneratedAt,
		RequestedBy:  lossRunsReport.RequestedBy.String(),
		FileHandleID: null.StringFromPtr(handleId),
		JobRunID:     null.StringFromPtr(jobRunId),
		CreatedAt:    lossRunsReport.CreatedAt,
	}, nil
}
