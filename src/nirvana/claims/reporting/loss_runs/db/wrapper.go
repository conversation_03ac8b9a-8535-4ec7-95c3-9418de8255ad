package db

import (
	"context"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"

	db_api "nirvanatech.com/nirvana/db-api"
	"nirvanatech.com/nirvana/db-api/db_models/insured"
)

func NewWrapper(db db_api.NirvanaRW, clk clock.Clock) Wrapper {
	return &dataWrapperImpl{
		db:  db,
		clk: clk,
	}
}

type dataWrapperImpl struct {
	db  db_api.NirvanaRW
	clk clock.Clock
}

func (d *dataWrapperImpl) GetLossRunsReports(ctx context.Context) ([]LossRunsReport, error) {
	dbReports, err := insured.LossRunsReports().All(ctx, d.db.DB)
	if err != nil {
		return nil, errors.Wrap(err, "could not get loss runs reports")
	}
	return lossRunsReportsFromDb(dbReports)
}

func (d *dataWrapperImpl) InsertLossRunsReport(ctx context.Context, l LossRunsReport) error {
	dbLossRunsReport, err := lossRunsReportToDb(&l)
	if err != nil {
		return errors.Wrap(err, "could not convert loss runs report to DB representation")
	}
	if err := dbLossRunsReport.Insert(ctx, d.db.DB, boil.Infer()); err != nil {
		return errors.Wrap(err, "could not insert loss runs report")
	}
	return nil
}
