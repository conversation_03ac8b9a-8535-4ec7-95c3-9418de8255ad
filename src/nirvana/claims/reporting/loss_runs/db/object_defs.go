package db

import (
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/jobber/jtypes"
)

type LossRunsReport struct {
	Id           uuid.UUID
	PolicyNumber string
	GeneratedAt  time.Time
	RequestedBy  uuid.UUID
	FileHandleId *uuid.UUID
	JobRunId     *jtypes.JobRunId
	CreatedAt    time.Time
}

func NewLossRunsReport(
	policyNumber string, generatedAt time.Time, requestedBy uuid.UUID,
) *LossRunsReport {
	return &LossRunsReport{
		Id:           uuid.New(),
		PolicyNumber: policyNumber,
		GeneratedAt:  generatedAt,
		RequestedBy:  requestedBy,
		CreatedAt:    time.Now(),
	}
}

func (l *LossRunsReport) WithFileHandleId(fileHandleId uuid.UUID) *LossRunsReport {
	l.FileHandleId = &fileHandleId
	return l
}

func (l *LossRunsReport) WithJobRunId(jobRunId jtypes.JobRunId) *LossRunsReport {
	l.JobRunId = &jobRunId
	return l
}

func (l *LossRunsReport) Validate() error {
	if l.Id == uuid.Nil {
		return errors.New("Id is required")
	}
	if l.PolicyNumber == "" {
		return errors.New("PolicyNumber is required")
	}
	if l.GeneratedAt.IsZero() {
		return errors.New("GeneratedAt is required")
	}
	if l.RequestedBy == uuid.Nil {
		return errors.New("RequestedBy is required")
	}
	if l.FileHandleId != nil && *l.FileHandleId == uuid.Nil {
		return errors.New("FileHandleId is not valid")
	}

	return nil
}
