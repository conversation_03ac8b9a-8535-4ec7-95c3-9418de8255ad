package snowflake

import (
	"context"

	"nirvanatech.com/nirvana/claims/reporting/enums"
	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake/primitives"
)

const bordereauxReserveSummariesTableName = "REPORTING_BORDEREAUX_RESERVE_SUMMARIES"

type BordereauxReserveSummaryRow struct {
	CLIENT_NAME                *string              `csv:"Client Name"`
	CLIENT_LOCATION            *string              `csv:"Client Location"`
	ORG_LOCATION_NAME          *string              `csv:"Org Location Name"`
	LOB                        *string              `csv:"LOB"`
	AGENCY_NAME                *string              `csv:"Retail Broker/Agent"`
	INSURED_NAME               *string              `csv:"Insured"`
	INSURED_STATE              *string              `csv:"Insured State"`
	INSURER_DBA_NAME           *string              `csv:"Insurer DBA Name"`
	DRIVER                     *string              `csv:"Driver"`
	VIN                        *string              `csv:"Vehicle VIN"`
	INSURED_VEHICLE_TOTAL_LOSS primitives.YesNoBool `csv:"Insured Vehicle Total Loss (Y/N)"`
	VEHICLE_MAKE               *string              `csv:"Vehicle Make"`
	VEHICLE_MODEL              *string              `csv:"Vehicle Model"`
	POLICY_NUMBER              *string              `csv:"Policy Nbr"`
	RISK_STATE                 *string              `csv:"Risk St"`
	POLICY_EFFECTIVE_DATE      primitives.UsDate    `csv:"Eff Date"`
	POLICY_EXPIRATION_DATE     primitives.UsDate    `csv:"Exp Date"`
	POLICY_CANCELLED_AT        primitives.UsDate    `csv:"Policy Cancel Date"`
	NUMBER                     float64              `csv:"Reserve Nbr"`
	STATUS                     string               `csv:"Reserve Status"`
	CLAIM_NUMBER               string               `csv:"Claim Nbr"`
	REFERENCE_NUMBER           string               `csv:"Reserve Reference Nbr"`
	CLAIM_TYPE                 *string              `csv:"Claim Type"`
	PREV_CLAIM_NUMBER          *string              `csv:"Prev Claim Nbr"` // Left empty on purpose - do not read from Snowflake.
	IS_DENIED                  primitives.YesNoBool `csv:"Is Denied"`
	DENIAL_REASON              *string              `csv:"Denial Reason"`
	IS_CONTESTED               primitives.YesNoBool `csv:"Contested (Y/N)"`
	IN_LITIGATION              primitives.YesNoBool `csv:"Litigation (Y/N)"`
	LOSS_DATE                  primitives.UsDate    `csv:"Acc Date"`
	LOSS_DESCRIPTION           *string              `csv:"Acc Desc"`
	LOSS_TYPE                  string               `csv:"Acc Code"`
	LOSS_LOCATION              *string              `csv:"Acc Location"`
	LOSS_CITY                  *string              `csv:"Acc City"`
	LOSS_COUNTY                *string              `csv:"Acc County"`
	LOSS_STATE                 string               `csv:"Acc State"`
	JURISDICTION_STATE         *string              `csv:"Jurisdiction State"` // Left empty on purpose - do not read from Snowflake.
	CLAIM_STATUS               string               `csv:"Claim Status"`
	LINE_CODE                  *string              `csv:"Line Code"`
	COVERAGE_CODE              *string              `csv:"Coverage Code"`
	COVERAGE_TYPE              string               `csv:"Coverage Type"`
	CLAIMANT_NAME              *string              `csv:"Claimant"`
	ADJUSTER_NAME              *string              `csv:"Reserve Adjuster"`
	SUPERVISOR_NAME            *string              `csv:"Reserve Supervisor"`
	CLAIM_REPORTED_AT          primitives.UsDate    `csv:"Dt Reported"`
	CLAIM_REPORTED_METHOD      *string              `csv:"Reported Method"`
	CLAIM_LAG_TIME             float64              `csv:"Lag Time"`
	CLAIM_OPENED_AT            primitives.UsDate    `csv:"Dt Open"`
	CLAIM_CLOSED_AT            primitives.UsDate    `csv:"Dt Closed"`
	CLAIM_REOPENED_AT          primitives.UsDate    `csv:"Dt Reopen"`
	OPENED_AT                  primitives.UsDate    `csv:"Reserve Dt Open"`
	CLOSED_AT                  primitives.UsDate    `csv:"Reserve Dt Closed"`
	REOPENED_AT                primitives.UsDate    `csv:"Reserve Dt Reopen"`
	DAYS_OPEN                  float64              `csv:"Days Reserve Open"`
	CATASTROPHE_NUMBER         *string              `csv:"Catastrophe Nbr"`
	EXCEEDS_CLIENT_AUTHORITY   primitives.YesNoBool `csv:"Exceeds Client Authority (Y/N)"`
	LOSS_PAID                  float64              `csv:"Ind Paid"`
	MEDICAL_PAID               float64              `csv:"Med Paid"`
	EXPENSES_PAID              float64              `csv:"Exp Paid"`
	DCC_PAID                   float64              `csv:"DCC Paid"`
	AOE_PAID                   float64              `csv:"AOE Paid"`
	LOSS_RESERVES              float64              `csv:"Ind Res"`
	MEDICAL_RESERVES           float64              `csv:"Med Res"`
	EXPENSES_RESERVES          float64              `csv:"Exp Res"`
	DCC_RESERVES               float64              `csv:"DCC Res"`
	AOE_RESERVES               float64              `csv:"AOE Res"`
	GROSS_INCURRED             float64              `csv:"Gross Incurred"`
	TOTAL_RECOVERIES           float64              `csv:"Total Recoveries"`
	SUBROGATION_RECOVERIES     float64              `csv:"Subro Rec"`
	SALVAGE_RECOVERIES         float64              `csv:"Salvage Rec"`
	DEDUCTIBLE_RECOVERIES      float64              `csv:"Deduct Rec"`
	OTHER_RECOVERIES           float64              `csv:"Other Recoveries"`
	NET_INCURRED               float64              `csv:"Net Incurred"`
}

func (c *DataWrapperImpl) GetBordereauxReserveSummariesRows(
	ctx context.Context,
	carrier enums.Carrier,
) ([]BordereauxReserveSummaryRow, error) {
	cols := []string{
		"CLIENT_NAME",
		"CLIENT_LOCATION",
		"ORG_LOCATION_NAME",
		"LOB",
		"AGENCY_NAME",
		"INSURED_NAME",
		"INSURED_STATE",
		"INSURER_DBA_NAME",
		"DRIVER",
		"VIN",
		"INSURED_VEHICLE_TOTAL_LOSS",
		"VEHICLE_MAKE",
		"VEHICLE_MODEL",
		"POLICY_NUMBER",
		"RISK_STATE",
		"POLICY_EFFECTIVE_DATE",
		"POLICY_EXPIRATION_DATE",
		"POLICY_CANCELLED_AT",
		"NUMBER",
		"STATUS",
		"CLAIM_NUMBER",
		"REFERENCE_NUMBER",
		"CLAIM_TYPE",
		// "PREV_CLAIM_NUMBER" keep to match the column order in csv file.
		"IS_DENIED",
		"DENIAL_REASON",
		"IS_CONTESTED",
		"IN_LITIGATION",
		"LOSS_DATE",
		"LOSS_DESCRIPTION",
		"LOSS_TYPE",
		"LOSS_LOCATION",
		"LOSS_CITY",
		"LOSS_COUNTY",
		"LOSS_STATE",
		// "JURISDICTION_STATE" keep to match the column order in csv file.
		"CLAIM_STATUS",
		"LINE_CODE",
		"COVERAGE_CODE",
		"COVERAGE_TYPE",
		"CLAIMANT_NAME",
		"ADJUSTER_NAME",
		"SUPERVISOR_NAME",
		"CLAIM_REPORTED_AT",
		"CLAIM_REPORTED_METHOD",
		"CLAIM_LAG_TIME",
		"CLAIM_OPENED_AT",
		"CLAIM_CLOSED_AT",
		"CLAIM_REOPENED_AT",
		"OPENED_AT",
		"CLOSED_AT",
		"REOPENED_AT",
		"DAYS_OPEN",
		"CATASTROPHE_NUMBER",
		"EXCEEDS_CLIENT_AUTHORITY",
		"LOSS_PAID",
		"MEDICAL_PAID",
		"EXPENSES_PAID",
		"DCC_PAID",
		"AOE_PAID",
		"LOSS_RESERVES",
		"MEDICAL_RESERVES",
		"EXPENSES_RESERVES",
		"DCC_RESERVES",
		"AOE_RESERVES",
		"GROSS_INCURRED",
		"TOTAL_RECOVERIES",
		"SUBROGATION_RECOVERIES",
		"SALVAGE_RECOVERIES",
		"DEDUCTIBLE_RECOVERIES",
		"OTHER_RECOVERIES",
		"NET_INCURRED",
	}

	scanTargets := func(r *BordereauxReserveSummaryRow) []any {
		return []any{
			&r.CLIENT_NAME,
			&r.CLIENT_LOCATION,
			&r.ORG_LOCATION_NAME,
			&r.LOB,
			&r.AGENCY_NAME,
			&r.INSURED_NAME,
			&r.INSURED_STATE,
			&r.INSURER_DBA_NAME,
			&r.DRIVER,
			&r.VIN,
			&r.INSURED_VEHICLE_TOTAL_LOSS,
			&r.VEHICLE_MAKE,
			&r.VEHICLE_MODEL,
			&r.POLICY_NUMBER,
			&r.RISK_STATE,
			&r.POLICY_EFFECTIVE_DATE,
			&r.POLICY_EXPIRATION_DATE,
			&r.POLICY_CANCELLED_AT,
			&r.NUMBER,
			&r.STATUS,
			&r.CLAIM_NUMBER,
			&r.REFERENCE_NUMBER,
			&r.CLAIM_TYPE,
			&r.IS_DENIED,
			&r.DENIAL_REASON,
			&r.IS_CONTESTED,
			&r.IN_LITIGATION,
			&r.LOSS_DATE,
			&r.LOSS_DESCRIPTION,
			&r.LOSS_TYPE,
			&r.LOSS_LOCATION,
			&r.LOSS_CITY,
			&r.LOSS_COUNTY,
			&r.LOSS_STATE,
			&r.CLAIM_STATUS,
			&r.LINE_CODE,
			&r.COVERAGE_CODE,
			&r.COVERAGE_TYPE,
			&r.CLAIMANT_NAME,
			&r.ADJUSTER_NAME,
			&r.SUPERVISOR_NAME,
			&r.CLAIM_REPORTED_AT,
			&r.CLAIM_REPORTED_METHOD,
			&r.CLAIM_LAG_TIME,
			&r.CLAIM_OPENED_AT,
			&r.CLAIM_CLOSED_AT,
			&r.CLAIM_REOPENED_AT,
			&r.OPENED_AT,
			&r.CLOSED_AT,
			&r.REOPENED_AT,
			&r.DAYS_OPEN,
			&r.CATASTROPHE_NUMBER,
			&r.EXCEEDS_CLIENT_AUTHORITY,
			&r.LOSS_PAID,
			&r.MEDICAL_PAID,
			&r.EXPENSES_PAID,
			&r.DCC_PAID,
			&r.AOE_PAID,
			&r.LOSS_RESERVES,
			&r.MEDICAL_RESERVES,
			&r.EXPENSES_RESERVES,
			&r.DCC_RESERVES,
			&r.AOE_RESERVES,
			&r.GROSS_INCURRED,
			&r.TOTAL_RECOVERIES,
			&r.SUBROGATION_RECOVERIES,
			&r.SALVAGE_RECOVERIES,
			&r.DEDUCTIBLE_RECOVERIES,
			&r.OTHER_RECOVERIES,
			&r.NET_INCURRED,
		}
	}

	return selectAllFrom(
		ctx,
		c.snapsheetDB,
		bordereauxReserveSummariesTableName,
		cols,
		scanTargets,
	)
}
