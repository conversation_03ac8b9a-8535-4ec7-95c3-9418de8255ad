package snowflake

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/claims/reporting/enums"
	"nirvanatech.com/nirvana/common-go/tracing"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/pricing/explainability/snowflake_utils"
)

//go:generate go run go.uber.org/mock/mockgen -destination mock.go -package snowflake nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake DataWrapper
type DataWrapper interface {
	GetBordereauxClaimsRows(ctx context.Context, carrier enums.Carrier) ([]BordereauxClaimRow, error)
	GetBordereauxReserveSummariesRows(ctx context.Context, carrier enums.Carrier) ([]BordereauxReserveSummaryRow, error)
	GetLossRunsRows(ctx context.Context, policyNumber string) ([]LossRunsRow, error)
}

type DataWrapperImpl struct {
	snapsheetDB *snapsheetDB
}

var _ DataWrapper = (*DataWrapperImpl)(nil)

func newDataWrapper(cfg *config.Config, lc fx.Lifecycle) (*DataWrapperImpl, error) {
	snapsheetDB, err := newSnapsheetDbConnection(cfg, lc)
	if err != nil {
		return nil, err
	}
	return &DataWrapperImpl{
		snapsheetDB: snapsheetDB,
	}, nil
}

// selectAllFrom is a generic helper function for querying Snowflake tables and scanning the results
// into a slice of structs. It handles the common pattern of selecting specific columns from a table
// and mapping each row to a struct instance.
//
// Example usage:
//
//	type MyRow struct {
//	    PolicyNumber string
//	    ClaimNumber  string
//	}
//
//	cols := []string{"POLICY_NUMBER", "CLAIM_NUMBER"}
//	scanTargets := func(r *MyRow) []any {
//	    return []any{&r.PolicyNumber, &r.ClaimNumber}
//	}
//
//	rows, err := selectAllFrom(ctx, db, "MY_TABLE", cols, scanTargets)
func selectAllFrom[R any](
	ctx context.Context,
	db boil.ContextExecutor,
	tableName string,
	columns []string,
	scanTargets func(*R) []any,
	additionalMods ...qm.QueryMod,
) ([]R, error) {
	ctx, span := tracing.Start(ctx, "snowflake.selectAllFrom")
	defer span.End()

	q := new(queries.Query)
	queries.SetDialect(q, &snowflake_utils.SnowFlakeSQLDialect)
	mods := append(additionalMods, qm.Select(columns...), qm.From(tableName))
	qm.Apply(q, mods...)

	rows, err := q.QueryContext(ctx, db)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to query %s", tableName)
	}
	defer rows.Close()

	var results []R
	for rows.Next() {
		obj := new(R)
		pointersToScan := scanTargets(obj)
		if err := rows.Scan(pointersToScan...); err != nil {
			return nil, errors.Wrap(err, "failed to scan row")
		}
		results = append(results, *obj)
	}
	if err := rows.Err(); err != nil {
		return nil, errors.Wrap(err, "row iteration error")
	}
	return results, nil
}
