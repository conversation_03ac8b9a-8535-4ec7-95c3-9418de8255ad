// Code generated by MockGen. DO NOT EDIT.
// Source: nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake (interfaces: DataWrapper)
//
// Generated by this command:
//
//	mockgen -destination mock.go -package snowflake nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake DataWrapper
//

// Package snowflake is a generated GoMock package.
package snowflake

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	enums "nirvanatech.com/nirvana/claims/reporting/enums"
)

// MockDataWrapper is a mock of DataWrapper interface.
type MockDataWrapper struct {
	ctrl     *gomock.Controller
	recorder *MockDataWrapperMockRecorder
	isgomock struct{}
}

// MockDataWrapperMockRecorder is the mock recorder for MockDataWrapper.
type MockDataWrapperMockRecorder struct {
	mock *MockDataWrapper
}

// NewMockDataWrapper creates a new mock instance.
func NewMockDataWrapper(ctrl *gomock.Controller) *MockDataWrapper {
	mock := &MockDataWrapper{ctrl: ctrl}
	mock.recorder = &MockDataWrapperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDataWrapper) EXPECT() *MockDataWrapperMockRecorder {
	return m.recorder
}

// GetBordereauxClaimsRows mocks base method.
func (m *MockDataWrapper) GetBordereauxClaimsRows(ctx context.Context, carrier enums.Carrier) ([]BordereauxClaimRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBordereauxClaimsRows", ctx, carrier)
	ret0, _ := ret[0].([]BordereauxClaimRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBordereauxClaimsRows indicates an expected call of GetBordereauxClaimsRows.
func (mr *MockDataWrapperMockRecorder) GetBordereauxClaimsRows(ctx, carrier any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBordereauxClaimsRows", reflect.TypeOf((*MockDataWrapper)(nil).GetBordereauxClaimsRows), ctx, carrier)
}

// GetBordereauxReserveSummariesRows mocks base method.
func (m *MockDataWrapper) GetBordereauxReserveSummariesRows(ctx context.Context, carrier enums.Carrier) ([]BordereauxReserveSummaryRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBordereauxReserveSummariesRows", ctx, carrier)
	ret0, _ := ret[0].([]BordereauxReserveSummaryRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBordereauxReserveSummariesRows indicates an expected call of GetBordereauxReserveSummariesRows.
func (mr *MockDataWrapperMockRecorder) GetBordereauxReserveSummariesRows(ctx, carrier any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBordereauxReserveSummariesRows", reflect.TypeOf((*MockDataWrapper)(nil).GetBordereauxReserveSummariesRows), ctx, carrier)
}

// GetLossRunsRows mocks base method.
func (m *MockDataWrapper) GetLossRunsRows(ctx context.Context, policyNumber string) ([]LossRunsRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLossRunsRows", ctx, policyNumber)
	ret0, _ := ret[0].([]LossRunsRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLossRunsRows indicates an expected call of GetLossRunsRows.
func (mr *MockDataWrapperMockRecorder) GetLossRunsRows(ctx, policyNumber any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLossRunsRows", reflect.TypeOf((*MockDataWrapper)(nil).GetLossRunsRows), ctx, policyNumber)
}
