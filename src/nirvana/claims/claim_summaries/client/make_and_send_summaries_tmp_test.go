package client_test

import (
	"context"
	"slices"
	"time"

	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/claims/claim_summaries/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/emailer/models"
	"nirvanatech.com/nirvana/infra/authz"
	llmops "nirvanatech.com/nirvana/llmops/src/go-client/generated"
)

// This test is a temporary test to check if the logic for sending emails to internal contacts is working as expected.
func (s *claimSummariesClientTestSuite) TestMakeAndSendSummaries_Tmp() {
	ctx := context.Background()
	policy1 := s.seedPolicy(ctx, "2011021", s.dotNumber)
	narsClaim := s.seedClaim(
		ctx,
		policy1.PolicyNumber.String(),
		4321,
		claim_enums.ClaimStatusOpen,
		claim_enums.ClaimsProviderNars,
	)
	snapsheetClaim := s.seedClaim(
		ctx,
		policy1.PolicyNumber.String(),
		1432,
		claim_enums.ClaimStatusOpen,
		claim_enums.ClaimsProviderSnapsheet,
	)

	now := time_utils.NewDate(2020, time.April, 5)

	s.seedClaimNotes(
		ctx,
		narsClaim.ExternalId,
		now.ToTime(),
		"We received a letter from the insured's lawyer",
		claim_enums.ClaimsProviderNars,
	)

	s.seedClaimNotes(
		ctx,
		snapsheetClaim.ExternalId,
		now.ToTime(),
		"We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.",
		claim_enums.ClaimsProviderSnapsheet,
	)

	testCases := []struct {
		name              string
		user              authz.User
		time              time.Time
		claimExternalIds  []string
		wantSummariesInDb int
		wantSummaries     []struct {
			storedInDb bool
			callLLMOps bool
			db         db.ClaimSummary
		}
		wantEmails []models.SendRequest
	}{
		{
			name:              "With Nars and Snapsheet claims",
			user:              test_utils.Superuser(),
			time:              now.ToTime(),
			claimExternalIds:  []string{narsClaim.ExternalId, snapsheetClaim.ExternalId},
			wantSummariesInDb: 1,
			wantSummaries: []struct {
				storedInDb bool
				callLLMOps bool
				db         db.ClaimSummary
			}{
				{
					storedInDb: true,
					callLLMOps: true,
					db: db.ClaimSummary{
						ClaimId: narsClaim.Id,
						Title:   "CORRESPONDENCE IN: We received a le",
						Interval: time_utils.Interval{
							Start: time_utils.StartOfDayFor(now.ToTime().AddDate(0, 0, -7)),
							End:   time_utils.StartOfDayFor(now.ToTime()),
						},
						Summary: "CORRESPONDENCE IN: We received a letter from the insured's lawyer;",
					},
				},
				{
					storedInDb: false,
					callLLMOps: true,
					db: db.ClaimSummary{
						ClaimId: snapsheetClaim.Id,
						Title:   "",
						Interval: time_utils.Interval{
							Start: time_utils.StartOfDayFor(now.ToTime().AddDate(0, 0, -7)),
							End:   time_utils.StartOfDayFor(now.ToTime()),
						},
						Summary: "No updates available for this claim while our systems are being updated. Thank you for your patience.",
					},
				},
			},
			wantEmails: []models.SendRequest{
				{
					Subject: "Last week's open claims update - Nirvana Insurance",
					Sender: models.Contact{
						EmailAddress: models.ClaimsContact.EmailAddress,
						Name:         "Claire Swift",
					},
					BCC: []models.Contact{
						{
							Name:         "Rafaela Karachon",
							EmailAddress: "<EMAIL>",
						},
						{
							Name:         "Akhilesh Koppineni",
							EmailAddress: "<EMAIL>",
						},
						{
							EmailAddress: s.deps.FleetAdmin.Email,
							Name:         s.deps.FleetAdmin.FirstName,
						},
					},
					TemplateData: map[string]any{
						"date_range": "Mar 29-Apr 5, 2020",
						"dot_number": s.dotNumber,
					},
				},
				{
					Subject: "Last week's open claims update - Nirvana Insurance",
					Sender: models.Contact{
						EmailAddress: models.ClaimsContact.EmailAddress,
						Name:         "Claire Swift",
					},
					BCC: []models.Contact{
						{
							Name:         "Rafaela Karachon",
							EmailAddress: "<EMAIL>",
						},
						{
							Name:         "Akhilesh Koppineni",
							EmailAddress: "<EMAIL>",
						},
						{
							Name:         "David Halman",
							EmailAddress: "<EMAIL>",
						},
						{
							EmailAddress: "<EMAIL>",
							Name:         "Joaquin Moreira",
						},
						{
							EmailAddress: "<EMAIL>",
							Name:         "Nicolas Leyton",
						},
						{
							EmailAddress: "<EMAIL>",
							Name:         "Chris Xue",
						},
						{
							EmailAddress: "<EMAIL>",
							Name:         "Stephanie Chau",
						},
					},
					TemplateData: map[string]any{
						"date_range": "Mar 29-Apr 5, 2020",
						"dot_number": s.dotNumber,
					},
				},
			},
		},
		{
			name:              "With Nars claims",
			user:              test_utils.Superuser(),
			time:              now.ToTime(),
			claimExternalIds:  []string{narsClaim.ExternalId},
			wantSummariesInDb: 2, // 1 from last execution, 1 from this one
			wantSummaries: []struct {
				storedInDb bool
				callLLMOps bool
				db         db.ClaimSummary
			}{
				{
					storedInDb: true,
					callLLMOps: true,
					db: db.ClaimSummary{
						ClaimId: narsClaim.Id,
						Title:   "CORRESPONDENCE IN: We received a le",
						Interval: time_utils.Interval{
							Start: time_utils.StartOfDayFor(now.ToTime().AddDate(0, 0, -7)),
							End:   time_utils.StartOfDayFor(now.ToTime()),
						},
						Summary: "CORRESPONDENCE IN: We received a letter from the insured's lawyer;",
					},
				},
			},
			wantEmails: []models.SendRequest{
				{
					Subject: "Last week's open claims update - Nirvana Insurance",
					Sender: models.Contact{
						EmailAddress: models.ClaimsContact.EmailAddress,
						Name:         "Claire Swift",
					},
					BCC: []models.Contact{
						{
							Name:         "Rafaela Karachon",
							EmailAddress: "<EMAIL>",
						},
						{
							Name:         "Akhilesh Koppineni",
							EmailAddress: "<EMAIL>",
						},
						{
							EmailAddress: s.deps.FleetAdmin.Email,
							Name:         s.deps.FleetAdmin.FirstName,
						},
					},
					TemplateData: map[string]any{
						"date_range": "Mar 29-Apr 5, 2020",
						"dot_number": s.dotNumber,
					},
				},
			},
		},
		{
			name:              "With Snapsheet claims",
			user:              test_utils.Superuser(),
			time:              now.ToTime(),
			wantSummariesInDb: 0, // since this is a Snapsheet claim, it won't be stored in the database
			claimExternalIds:  []string{snapsheetClaim.ExternalId},
			wantSummaries: []struct {
				storedInDb bool
				callLLMOps bool
				db         db.ClaimSummary
			}{
				{
					storedInDb: false,
					callLLMOps: true,
					db: db.ClaimSummary{
						ClaimId: snapsheetClaim.Id,
						Title:   "",
						Interval: time_utils.Interval{
							Start: time_utils.StartOfDayFor(now.ToTime().AddDate(0, 0, -7)),
							End:   time_utils.StartOfDayFor(now.ToTime()),
						},
						Summary: "No updates available for this claim while our systems are being updated. Thank you for your patience.",
					},
				},
			},
			wantEmails: []models.SendRequest{
				{
					Subject: "Last week's open claims update - Nirvana Insurance",
					Sender: models.Contact{
						EmailAddress: models.ClaimsContact.EmailAddress,
						Name:         "Claire Swift",
					},
					BCC: []models.Contact{
						{
							Name:         "Rafaela Karachon",
							EmailAddress: "<EMAIL>",
						},
						{
							Name:         "Akhilesh Koppineni",
							EmailAddress: "<EMAIL>",
						},
						{
							EmailAddress: s.deps.FleetAdmin.Email,
							Name:         s.deps.FleetAdmin.FirstName,
						},
					},
					TemplateData: map[string]any{
						"date_range": "Mar 29-Apr 5, 2020",
						"dot_number": s.dotNumber,
					},
				},
				{
					Subject: "Last week's open claims update - Nirvana Insurance",
					Sender: models.Contact{
						EmailAddress: models.ClaimsContact.EmailAddress,
						Name:         "Claire Swift",
					},
					BCC: []models.Contact{
						{
							Name:         "Rafaela Karachon",
							EmailAddress: "<EMAIL>",
						},
						{
							Name:         "Akhilesh Koppineni",
							EmailAddress: "<EMAIL>",
						},
						{
							Name:         "David Halman",
							EmailAddress: "<EMAIL>",
						},
						{
							EmailAddress: "<EMAIL>",
							Name:         "Joaquin Moreira",
						},
						{
							EmailAddress: "<EMAIL>",
							Name:         "Nicolas Leyton",
						},
						{
							EmailAddress: "<EMAIL>",
							Name:         "Chris Xue",
						},
						{
							EmailAddress: "<EMAIL>",
							Name:         "Stephanie Chau",
						},
					},
					TemplateData: map[string]any{
						"date_range": "Mar 29-Apr 5, 2020",
						"dot_number": s.dotNumber,
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			ctx := context.Background()
			ctx = authz.WithUser(ctx, tc.user)

			s.deps.Clk.Set(tc.time)

			llmOpsCalls := len(slice_utils.Filter(tc.wantSummaries, func(s struct {
				storedInDb bool
				callLLMOps bool
				db         db.ClaimSummary
			},
			) bool {
				return s.callLLMOps
			}))

			s.deps.MockLLMOpsClient.
				EXPECT().
				GenerateClaimUpdateSummary(gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, notes []string, source claim_enums.ClaimsProvider) (*llmops.ClaimSummary, error) {
					title := "title for no notes"
					summary := "summary for no notes"
					// we shouldn't have a case without notes, as those are explicitly skipping the
					// call to the LLMOpsClient. However, if something breaks/changes, is better
					// to fail an assertion than panic
					if len(notes) > 0 {
						note := notes[0]
						title = note[0:35]
						summary = note
					}
					return &llmops.ClaimSummary{
							Title:   title,
							Summary: summary,
						},
						nil
				}).
				Times(llmOpsCalls)

			var sentEmailRequests []models.SendRequest
			s.deps.MockEmailer.
				EXPECT().
				Send(gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, req *models.SendRequest) (*models.SendResponse, error) {
					sentEmailRequests = append(sentEmailRequests, *req)
					return &models.SendResponse{}, nil
				}).
				Times(len(tc.wantEmails))

			err := s.deps.Client.MakeAndSendSummaries(ctx, tc.claimExternalIds)
			s.Require().NoError(err)

			for _, summary := range tc.wantSummaries {
				dbSummaries, err := s.deps.ClaimSummaryWrapper.GetClaimSummaries(
					ctx,
					db.ClaimIdIs(summary.db.ClaimId),
					db.IntervalEndIsOnDay(tc.time),
				)
				s.Require().NoError(err)

				if summary.storedInDb {
					s.Require().Len(dbSummaries, tc.wantSummariesInDb)
					gotSummary := dbSummaries[0]
					s.Require().Equal(summary.db.ClaimId.String(), gotSummary.ClaimId.String())
					s.Require().Equal(summary.db.Title, gotSummary.Title)
					s.Require().Equal(summary.db.Interval, gotSummary.Interval)
					s.Require().Equal(summary.db.Summary, gotSummary.Summary)
				} else {
					s.Require().Len(dbSummaries, 0)
				}
			}

			s.Require().Len(sentEmailRequests, len(tc.wantEmails))

			sortFn := func(req1, req2 models.SendRequest) int {
				if len(req1.BCC) > len(req2.BCC) {
					return 1
				}
				if len(req1.BCC) < len(req2.BCC) {
					return -1
				}
				return 0
			}

			slices.SortFunc(tc.wantEmails, sortFn)
			slices.SortFunc(sentEmailRequests, sortFn)

			for i, expectedEmailRequest := range tc.wantEmails {
				s.Equal(expectedEmailRequest.TemplateData["date_range"], sentEmailRequests[i].TemplateData["date_range"])
				s.Equal(expectedEmailRequest.TemplateData["dot_number"], sentEmailRequests[i].TemplateData["dot_number"])
				s.Equal(expectedEmailRequest.Subject, sentEmailRequests[i].Subject)
				s.Require().Equal(len(expectedEmailRequest.BCC), len(sentEmailRequests[i].BCC))
			}
		})
	}
}
