package client_test

import (
	"context"
	"slices"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/claims/claim_summaries/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/emailer/models"
	"nirvanatech.com/nirvana/infra/authz"
	llmops "nirvanatech.com/nirvana/llmops/src/go-client/generated"
)

func (s *claimSummariesClientTestSuite) TestMakeAndSendSummaries() {
	// We seed the necessary data
	ctx := context.Background()
	policy1 := s.seedPolicy(ctx, "1001010", s.dotNumber)
	policy2 := s.seedPolicy(ctx, "2002020", 222222)
	claimWithNotes1 := s.seedClaim(ctx, policy1.PolicyNumber.String(), 1, claim_enums.ClaimStatusOpen, claim_enums.ClaimsProviderNars)
	claimWithoutNotes := s.seedClaim(ctx, policy1.PolicyNumber.String(), 2, claim_enums.ClaimStatusOpen, claim_enums.ClaimsProviderNars)
	claimWithNotes2 := s.seedClaim(ctx, policy2.PolicyNumber.String(), 1, claim_enums.ClaimStatusOpen, claim_enums.ClaimsProviderNars)
	closedClaim := s.seedClaim(ctx, policy1.PolicyNumber.String(), 3, claim_enums.ClaimStatusClosed, claim_enums.ClaimsProviderNars)

	s.seedClaimNotes(
		ctx,
		claimWithNotes1.ExternalId,
		s.deps.Clk.Now(),
		"We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.",
		claim_enums.ClaimsProviderNars,
	)
	s.seedClaimNotes(
		ctx,
		claimWithNotes1.ExternalId,
		time_utils.NewDate(2022, time.April, 5).ToTime(),
		"We've started looking into what happened. We need the names of the other drivers.",
		claim_enums.ClaimsProviderNars,
	)
	s.seedClaimNotes(
		ctx,
		claimWithNotes1.ExternalId,
		time_utils.NewDate(2023, time.January, 6).ToTime(),
		"We're waiting for the quote from the repair shop.",
		claim_enums.ClaimsProviderNars,
	)
	s.seedClaimNotes(
		ctx,
		claimWithNotes1.ExternalId,
		s.deps.Clk.Now().AddDate(0, -1, -1),
		"We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.",
		claim_enums.ClaimsProviderNars,
	)
	s.seedClaimNotes(
		ctx,
		claimWithNotes2.ExternalId,
		s.deps.Clk.Now(),
		"We're ready with your liability determination and payments.",
		claim_enums.ClaimsProviderNars,
	)
	s.seedClaimNotes(
		ctx,
		claimWithNotes2.ExternalId,
		time_utils.NewDate(2022, time.April, 5).ToTime(),
		"We are waiting for the camera footage to come, so that we can assess it.",
		claim_enums.ClaimsProviderNars,
	)

	testCases := []struct {
		name               string
		user               authz.User
		time               time.Time
		claimExternalIds   []string
		wantClaimSummaries []db.ClaimSummary
		// this field is <= len(wantClaimSummaries), as some claim summaries might
		// skip the call to the LLMOps service, since they don't have notes
		wantGeneratedClaimSummaries int
		wantEmailRequests           []models.SendRequest
		wantErr                     bool
	}{
		{
			name:             "Valid execution for a superuser",
			user:             test_utils.Superuser(),
			time:             s.deps.Clk.Now(),
			claimExternalIds: nil,
			wantClaimSummaries: []db.ClaimSummary{
				{
					ClaimId: claimWithNotes1.Id,
					// truncated notes seeded for this claim
					Title: "CORRESPONDENCE IN: We received a le",
					Interval: time_utils.Interval{
						Start: time_utils.StartOfDayFor(s.deps.Clk.Now().AddDate(0, 0, -7)),
						End:   time_utils.StartOfDayFor(s.deps.Clk.Now()),
					},
					// longer, but still truncated notes seeded for this claim
					Summary: "CORRESPONDENCE IN: We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.;",
				},
				{
					// title and summary come from the default for claims without notes during the week
					ClaimId: claimWithoutNotes.Id,
					Title:   "Actively monitoring, with no new developments",
					Interval: time_utils.Interval{
						Start: time_utils.StartOfDayFor(s.deps.Clk.Now().AddDate(0, 0, -7)),
						End:   time_utils.StartOfDayFor(s.deps.Clk.Now()),
					},
					Summary: "Last week, there were no new developments regarding your claim. Please rest assured that we are actively monitoring the situation, and we will keep you informed of any changes. Thank you for your patience and understanding.",
				},
				{
					ClaimId: claimWithNotes2.Id,
					// truncated notes seeded for this claim
					Title: "CORRESPONDENCE IN: We're ready with",
					Interval: time_utils.Interval{
						Start: time_utils.StartOfDayFor(s.deps.Clk.Now().AddDate(0, 0, -7)),
						End:   time_utils.StartOfDayFor(s.deps.Clk.Now()),
					},
					// longer, but still truncated notes seeded for this claim
					Summary: "CORRESPONDENCE IN: We're ready with your liability determination and payments.;",
				},
			},
			// Only two are generated, the other one isn't, as the claim has no notes for the week
			wantGeneratedClaimSummaries: 2,
			wantEmailRequests: []models.SendRequest{
				{
					Subject: "Last week's open claims update - Nirvana Insurance",
					Sender: models.Contact{
						EmailAddress: models.ClaimsContact.EmailAddress,
						Name:         "Claire Swift",
					},
					Recipients: []models.Contact{
						{
							EmailAddress: s.deps.FleetAdmin.Email,
							Name:         s.deps.FleetAdmin.FirstName,
						},
					},
					TemplateData: map[string]any{
						"date_range": "Mar 8-15, 2022",
						"dot_number": s.dotNumber,
					},
				},
			},
			wantErr: false,
		},
		{
			name:             "Valid execution for change of month",
			user:             test_utils.Superuser(),
			time:             time_utils.NewDate(2022, time.April, 5).ToTime(),
			claimExternalIds: nil,
			wantClaimSummaries: []db.ClaimSummary{
				{
					ClaimId: claimWithNotes1.Id,
					// truncated notes seeded for this claim
					Title: "CORRESPONDENCE IN: We've started lo",
					Interval: time_utils.Interval{
						Start: time_utils.NewDate(2022, time.April, 5).AddDate(0, 0, -7).StartOfDay(),
						End:   time_utils.NewDate(2022, time.April, 5).StartOfDay(),
					},
					// longer, but still truncated notes seeded for this claim
					Summary: "CORRESPONDENCE IN: We've started looking into what happened. We need the names of the other drivers.;",
				},
				{
					// title and summary come from the default for claims without notes during the week
					ClaimId: claimWithoutNotes.Id,
					Title:   "Actively monitoring, with no new developments",
					Interval: time_utils.Interval{
						Start: time_utils.NewDate(2022, time.April, 5).AddDate(0, 0, -7).StartOfDay(),
						End:   time_utils.NewDate(2022, time.April, 5).StartOfDay(),
					},
					Summary: "Last week, there were no new developments regarding your claim. Please rest assured that we are actively monitoring the situation, and we will keep you informed of any changes. Thank you for your patience and understanding.",
				},
				{
					ClaimId: claimWithNotes2.Id,
					// truncated notes seeded for this claim
					Title: "CORRESPONDENCE IN: We are waiting f",
					Interval: time_utils.Interval{
						Start: time_utils.NewDate(2022, time.April, 5).AddDate(0, 0, -7).StartOfDay(),
						End:   time_utils.NewDate(2022, time.April, 5).StartOfDay(),
					},
					// longer, but still truncated notes seeded for this claim
					Summary: "CORRESPONDENCE IN: We are waiting for the camera footage to come, so that we can assess it.;",
				},
			},
			// Only two are generated, the other one isn't, as the claim has no notes for the week
			wantGeneratedClaimSummaries: 2,
			wantEmailRequests: []models.SendRequest{
				{
					Subject: "Last week's open claims update - Nirvana Insurance",
					Sender: models.Contact{
						EmailAddress: models.ClaimsContact.EmailAddress,
						Name:         "Claire Swift",
					},
					Recipients: []models.Contact{
						{
							EmailAddress: s.deps.FleetAdmin.Email,
							Name:         s.deps.FleetAdmin.FirstName,
						},
					},
					TemplateData: map[string]any{
						"date_range": "Mar 29-Apr 5, 2022",
						"dot_number": s.dotNumber,
					},
				},
			},
			wantErr: false,
		},
		{
			name:             "Valid execution for change of year",
			user:             test_utils.Superuser(),
			time:             time_utils.NewDate(2023, time.January, 6).ToTime(),
			claimExternalIds: nil,
			wantClaimSummaries: []db.ClaimSummary{
				{
					ClaimId: claimWithNotes1.Id,
					// truncated notes seeded for this claim
					Title: "CORRESPONDENCE IN: We're waiting fo",
					Interval: time_utils.Interval{
						Start: time_utils.NewDate(2023, time.January, 6).AddDate(0, 0, -7).StartOfDay(),
						End:   time_utils.NewDate(2023, time.January, 6).StartOfDay(),
					},
					// longer, but still truncated notes seeded for this claim
					Summary: "CORRESPONDENCE IN: We're waiting for the quote from the repair shop.;",
				},
				{
					// title and summary come from the default for claims without notes during the week
					ClaimId: claimWithoutNotes.Id,
					Title:   "Actively monitoring, with no new developments",
					Interval: time_utils.Interval{
						Start: time_utils.NewDate(2023, time.January, 6).AddDate(0, 0, -7).StartOfDay(),
						End:   time_utils.NewDate(2023, time.January, 6).StartOfDay(),
					},
					Summary: "Last week, there were no new developments regarding your claim. Please rest assured that we are actively monitoring the situation, and we will keep you informed of any changes. Thank you for your patience and understanding.",
				},
				{
					ClaimId: claimWithNotes2.Id,
					Title:   "Actively monitoring, with no new developments",
					Interval: time_utils.Interval{
						Start: time_utils.NewDate(2023, time.January, 6).AddDate(0, 0, -7).StartOfDay(),
						End:   time_utils.NewDate(2023, time.January, 6).StartOfDay(),
					},
					Summary: "Last week, there were no new developments regarding your claim. Please rest assured that we are actively monitoring the situation, and we will keep you informed of any changes. Thank you for your patience and understanding.",
				},
			},
			// Only one is generated, the other two aren't, as the claims have no notes for the week
			wantGeneratedClaimSummaries: 1,
			wantEmailRequests: []models.SendRequest{
				{
					Subject: "Last week's open claims update - Nirvana Insurance",
					Sender: models.Contact{
						EmailAddress: models.ClaimsContact.EmailAddress,
						Name:         "Claire Swift",
					},
					Recipients: []models.Contact{
						{
							EmailAddress: s.deps.FleetAdmin.Email,
							Name:         s.deps.FleetAdmin.FirstName,
						},
					},
					TemplateData: map[string]any{
						"date_range": "Dec 30, 2022-Jan 6, 2023",
						"dot_number": s.dotNumber,
					},
				},
			},
			wantErr: false,
		},
		{
			name:               "Truncated execution for a non-superuser",
			time:               s.deps.Clk.Now(),
			user:               test_utils.AgencyAdminUser(uuid.New()),
			claimExternalIds:   nil,
			wantClaimSummaries: []db.ClaimSummary{},
			wantErr:            true,
		},
		{
			name:             "Should return claim summary for closed claims when requested by external ID",
			user:             test_utils.Superuser(),
			time:             time_utils.NewDate(2024, time.February, 1).ToTime(),
			claimExternalIds: []string{closedClaim.ExternalId},
			wantClaimSummaries: []db.ClaimSummary{
				{
					ClaimId: closedClaim.Id,
					Title:   "Actively monitoring, with no new developments",
					Interval: time_utils.Interval{
						Start: time_utils.NewDate(2024, time.February, 1).AddDate(0, 0, -7).StartOfDay(),
						End:   time_utils.NewDate(2024, time.February, 1).StartOfDay(),
					},
					Summary: "Last week, there were no new developments regarding your claim. Please rest assured that we are actively monitoring the situation, and we will keep you informed of any changes. Thank you for your patience and understanding.",
				},
			},
			wantGeneratedClaimSummaries: 0,
			wantEmailRequests: []models.SendRequest{
				{
					Subject: "Last week's open claims update - Nirvana Insurance",
					Sender: models.Contact{
						EmailAddress: models.ClaimsContact.EmailAddress,
						Name:         "Claire Swift",
					},
					Recipients: []models.Contact{
						{
							EmailAddress: s.deps.FleetAdmin.Email,
							Name:         s.deps.FleetAdmin.FirstName,
						},
					},
					TemplateData: map[string]any{
						"date_range": "Jan 25-Feb 1, 2024",
						"dot_number": s.dotNumber,
					},
				},
			},
			wantErr: false,
		},
		{
			name:             "Should return claim summary only specific claim by external ID",
			user:             test_utils.Superuser(),
			time:             time_utils.NewDate(2024, time.March, 1).ToTime(),
			claimExternalIds: []string{claimWithNotes1.ExternalId},
			wantClaimSummaries: []db.ClaimSummary{
				{
					ClaimId: claimWithNotes1.Id,
					Title:   "Actively monitoring, with no new developments",
					Interval: time_utils.Interval{
						Start: time_utils.NewDate(2024, time.March, 1).AddDate(0, 0, -7).StartOfDay(),
						End:   time_utils.NewDate(2024, time.March, 1).StartOfDay(),
					},
					Summary: "Last week, there were no new developments regarding your claim. Please rest assured that we are actively monitoring the situation, and we will keep you informed of any changes. Thank you for your patience and understanding.",
				},
			},
			wantGeneratedClaimSummaries: 0,
			wantEmailRequests: []models.SendRequest{
				{
					Subject: "Last week's open claims update - Nirvana Insurance",
					Sender: models.Contact{
						EmailAddress: models.ClaimsContact.EmailAddress,
						Name:         "Claire Swift",
					},
					Recipients: []models.Contact{
						{
							EmailAddress: s.deps.FleetAdmin.Email,
							Name:         s.deps.FleetAdmin.FirstName,
						},
					},
					TemplateData: map[string]any{
						"date_range": "Feb 23-Mar 1, 2024",
						"dot_number": s.dotNumber,
					},
				},
			},
			wantErr: false,
		},
	}

	msg := func(cId uuid.UUID) string {
		if cId == claimWithNotes1.Id {
			return "claimWithNotes1"
		}
		if cId == claimWithoutNotes.Id {
			return "claimWithoutNotes"
		}
		if cId == claimWithNotes2.Id {
			return "claimWithNotes2"
		}
		if cId == closedClaim.Id {
			return "closedClaim"
		}
		return "otherClaim"
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			ctx := context.Background()
			ctx = authz.WithUser(ctx, tc.user)

			s.deps.Clk.Set(tc.time)

			s.deps.MockLLMOpsClient.
				EXPECT().
				GenerateClaimUpdateSummary(gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, notes []string, source claim_enums.ClaimsProvider) (*llmops.ClaimSummary, error) {
					title := "title for no notes"
					summary := "summary for no notes"
					// we shouldn't have a case without notes, as those are explicitly skipping the
					// call to the LLMOpsClient. However, if something breaks/changes, is better
					// to fail an assertion than panic
					if len(notes) > 0 {
						note := notes[0]
						title = note[0:35]
						summary = note
					}
					return &llmops.ClaimSummary{
							Title:   title,
							Summary: summary,
						},
						nil
				}).
				Times(tc.wantGeneratedClaimSummaries)

			var sentEmailRequests []models.SendRequest
			s.deps.MockEmailer.
				EXPECT().
				Send(gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, req *models.SendRequest) (*models.SendResponse, error) {
					sentEmailRequests = append(sentEmailRequests, *req)
					return &models.SendResponse{}, nil
				}).
				Times(len(tc.wantEmailRequests))

			err := s.deps.Client.MakeAndSendSummaries(ctx, tc.claimExternalIds)
			if tc.wantErr {
				s.Require().Error(err)
				return
			}
			s.Require().NoError(err)

			claimsToCheck := []uuid.UUID{claimWithNotes1.Id, claimWithoutNotes.Id, claimWithNotes2.Id, closedClaim.Id}

			for _, claimId := range claimsToCheck {
				claimSummaries, err := s.deps.ClaimSummaryWrapper.GetClaimSummaries(
					ctx,
					db.ClaimIdIs(claimId),
					db.IntervalEndIsOnDay(tc.time),
				)
				s.Require().NoError(err)

				wantClaimSummaries := slice_utils.Filter(
					tc.wantClaimSummaries,
					func(claimSummary db.ClaimSummary) bool {
						return claimSummary.ClaimId == claimId
					},
				)

				s.Require().Len(claimSummaries, len(wantClaimSummaries), msg(claimId))
				if len(wantClaimSummaries) == 0 {
					continue
				}

				sortSummariesFn := func(summ1, summ2 db.ClaimSummary) int {
					if summ1.Interval.End.After(summ2.Interval.End) {
						return 1
					}
					if summ1.Interval.End.Before(summ2.Interval.End) {
						return -1
					}

					return strings.Compare(summ1.Title, summ2.Title)
				}
				slices.SortFunc(wantClaimSummaries, sortSummariesFn)
				slices.SortFunc(claimSummaries, sortSummariesFn)

				for i, expectedClaimSummary := range wantClaimSummaries {
					s.Equal(
						expectedClaimSummary.ClaimId.String(),
						claimSummaries[i].ClaimId.String(),
						msg(claimId),
					)
					s.Equal(expectedClaimSummary.Title, claimSummaries[i].Title, msg(claimId))
					s.Equal(expectedClaimSummary.Summary, claimSummaries[i].Summary, msg(claimId))

					s.Require().NotNil(claimSummaries[i].Interval, msg(claimId))
					s.Equal(
						expectedClaimSummary.Interval.Start.Round(time.Second).UTC(),
						claimSummaries[i].Interval.Start.Round(time.Second).UTC(),
						msg(claimId),
					)
					s.Equal(
						expectedClaimSummary.Interval.End.Round(time.Second).UTC(),
						claimSummaries[i].Interval.End.Round(time.Second).UTC(),
						msg(claimId),
					)
				}
			}

			s.Require().Len(sentEmailRequests, len(tc.wantEmailRequests))

			sortEmailRequestsFn := func(req1, req2 models.SendRequest) int {
				if len(req1.Recipients) > len(req2.Recipients) {
					return 1
				}

				if len(req1.Recipients) < len(req2.Recipients) {
					return -1
				}

				for i, recipient := range req1.Recipients {
					if recipient.EmailAddress < req2.Recipients[i].EmailAddress {
						return 1
					}

					if recipient.EmailAddress > req2.Recipients[i].EmailAddress {
						return -1
					}
				}

				return 0
			}
			slices.SortFunc(tc.wantEmailRequests, sortEmailRequestsFn)
			slices.SortFunc(sentEmailRequests, sortEmailRequestsFn)

			for i, expectedEmailRequest := range tc.wantEmailRequests {
				s.Equal(expectedEmailRequest.Sender.EmailAddress, sentEmailRequests[i].Sender.EmailAddress)
				s.Equal(expectedEmailRequest.Sender.Name, sentEmailRequests[i].Sender.Name)
				s.Equal(expectedEmailRequest.TemplateData["date_range"], sentEmailRequests[i].TemplateData["date_range"])
				s.Equal(expectedEmailRequest.TemplateData["dot_number"], sentEmailRequests[i].TemplateData["dot_number"])
				s.Len(sentEmailRequests[i].Recipients, len(expectedEmailRequest.Recipients))
				s.Equal(expectedEmailRequest.Subject, sentEmailRequests[i].Subject)
			}
		})
	}
}
