package client_test

import (
	"context"
	"fmt"
	"slices"
	"strconv"
	"testing"
	"time"

	"github.com/benb<PERSON><PERSON>son/clock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	notes_client "nirvanatech.com/nirvana/claims/claim_notes/client"
	claim_notes_db "nirvanatech.com/nirvana/claims/claim_notes/db"
	"nirvanatech.com/nirvana/claims/claim_summaries/client"
	"nirvanatech.com/nirvana/claims/claim_summaries/db"
	claims_client "nirvanatech.com/nirvana/claims/client"
	claim_builder "nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/emailer"
	"nirvanatech.com/nirvana/emailer/models"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fleet_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	llmops_client "nirvanatech.com/nirvana/llmops/src/go-client"
	llmops "nirvanatech.com/nirvana/llmops/src/go-client/generated"
	policy_constants "nirvanatech.com/nirvana/policy/constants"
)

func TestClaimSummariesClientTestSuite(t *testing.T) {
	suite.Run(t, new(claimSummariesClientTestSuite))
}

type deps struct {
	fx.In

	ClaimsClient *claims_client.Client

	ClaimSummaryWrapper *db.DataWrapper
	Client              client.Client
	ClaimNotesClient    *notes_client.Client
	Clk                 *clock.Mock
	MockLLMOpsClient    *llmops_client.MockLLMOpsClient
	MockEmailer         *emailer.MockEmailer
	PolicyWrapper       policy.DataWrapper

	*fleet_fixture.FleetFixture
	*users_fixture.UsersFixture
}

type claimSummariesClientTestSuite struct {
	test_utils.StatsHandler
	suite.Suite
	fxapp *fxtest.App

	deps         deps
	mockFFClient *feature_flag_lib.MockClient
	dotNumber    int64
}

func (s *claimSummariesClientTestSuite) SetupTest() {
	deps := new(deps)
	s.fxapp = testloader.RequireStart(s.T(), deps)
	s.deps = *deps

	var err error
	s.dotNumber, err = strconv.ParseInt(s.deps.Fleet.DotNumber, 10, 64)
	s.Require().NoError(err)
}

func (s *claimSummariesClientTestSuite) seedPolicy(
	ctx context.Context,
	identifier string,
	dot int64,
) policy.Policy {
	policyNumberInFF, err := policy.NewNirvanaPolicyNumber(
		policy_constants.NirvanaPolicyCarrierALPrefix, identifier, s.deps.Clk.Now(),
	)
	s.Require().NoError(err)
	p, err := policy.MockPolicyImpl(policy.MockPolicyArgs{
		PolicyNumber: policyNumberInFF,
		ProgramType:  policy_enums.ProgramTypeFleet,
	})
	s.Require().NoError(err)
	p.EffectiveDate = time_utils.NewDate(2024, time.January, 14).ToTime()
	p.EffectiveDateTo = time_utils.NewDate(2025, time.January, 14).ToTime()
	p.CompanyInfo.DOTNumber = dot
	err = s.deps.PolicyWrapper.InsertPolicy(ctx, p)
	s.Require().NoError(err)
	return *p
}

// seedClaim inserts a claim and returns the external ID, which is useful for associated entities,
// such as notes.
func (s *claimSummariesClientTestSuite) seedClaim(
	ctx context.Context,
	pn string,
	id int,
	status claim_enums.ClaimStatus,
	provider claim_enums.ClaimsProvider,
) *claim_builder.Claim {
	externalId := fmt.Sprintf("external-id-for-%s-%d", pn, id)
	claim, err := claim_builder.New(provider).
		WithDefaultMockData().
		WithExternalId(externalId).
		WithPolicyNumber(pn).
		WithStatus(status).
		Build()
	s.Require().NoError(err)
	s.Require().NoError(s.deps.ClaimsClient.InsertClaim(ctx, claim))
	return claim
}

func (s *claimSummariesClientTestSuite) seedClaimNotes(
	ctx context.Context,
	externalClaimId string,
	createdAt time.Time,
	value string,
	provider claim_enums.ClaimsProvider,
) {
	note, err := claim_notes_db.NewNoteBuilder(provider).
		WithDefaultMockData().
		WithClaimExternalId(externalClaimId).
		WithValue(value).
		WithCreatedAt(createdAt).
		Build()
	s.Require().NoError(err)
	err = s.deps.ClaimNotesClient.InsertNote(ctx, *note)
	s.Require().NoError(err)
}

func (s *claimSummariesClientTestSuite) TearDownTest() {
	s.fxapp.RequireStop()
}

func (s *claimSummariesClientTestSuite) TestMakeSummary() {
	// We seed the necessary entities
	ctx := context.Background()
	p := s.seedPolicy(ctx, "1100101", s.dotNumber)
	claimWithNotes := s.seedClaim(ctx, p.PolicyNumber.String(), 3, claim_enums.ClaimStatusOpen, claim_enums.ClaimsProviderNars)

	s.seedClaimNotes(
		ctx,
		claimWithNotes.ExternalId,
		s.deps.Clk.Now(),
		"We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.",
		claim_enums.ClaimsProviderNars,
	)
	s.seedClaimNotes(
		ctx,
		claimWithNotes.ExternalId,
		time_utils.NewDate(2022, time.April, 5).ToTime(),
		"We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.",
		claim_enums.ClaimsProviderNars,
	)
	s.seedClaimNotes(
		ctx,
		claimWithNotes.ExternalId,
		time_utils.NewDate(2023, time.January, 6).ToTime(),
		"We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.",
		claim_enums.ClaimsProviderNars,
	)
	s.seedClaimNotes(
		ctx,
		claimWithNotes.ExternalId,
		s.deps.Clk.Now().AddDate(0, -1, -1),
		"We received a letter from the insured's lawyer, claiming it was the other vehicle's fault.",
		claim_enums.ClaimsProviderNars,
	)

	testCases := []struct {
		name                 string
		time                 time.Time
		claimId              uuid.UUID
		interval             time_utils.Interval
		makeSummaryOptions   client.MakeSummaryOptions
		expectedClaimSummary db.ClaimSummary
		expectedCallToLLMOps bool
		wantErr              bool
	}{
		{
			name:    "Valid execution without notes",
			time:    s.deps.Clk.Now().AddDate(0, -1, -7),
			claimId: claimWithNotes.Id,
			interval: time_utils.Interval{
				Start: time_utils.DateFromTime(s.deps.Clk.Now().AddDate(0, -1, -10)).ToTime(),
				End:   time_utils.DateFromTime(s.deps.Clk.Now().AddDate(0, -1, -7)).ToTime(),
			},
			makeSummaryOptions: client.MakeSummaryOptions{
				IsScheduled: false,
			},
			expectedCallToLLMOps: false,
			expectedClaimSummary: db.ClaimSummary{
				ClaimId: claimWithNotes.Id,
				Title:   "Actively monitoring, with no new developments",
				Summary: "Last week, there were no new developments regarding your claim. Please rest assured that we are actively monitoring the situation, and we will keep you informed of any changes. Thank you for your patience and understanding.",
			},
			wantErr: false,
		},
		{
			name:    "Valid execution with notes",
			time:    s.deps.Clk.Now().AddDate(0, -1, 0),
			claimId: claimWithNotes.Id,
			interval: time_utils.Interval{
				Start: time_utils.DateFromTime(s.deps.Clk.Now().AddDate(0, -1, -3)).ToTime(),
				End:   time_utils.DateFromTime(s.deps.Clk.Now().AddDate(0, -1, 0)).ToTime(),
			},
			makeSummaryOptions: client.MakeSummaryOptions{
				IsScheduled: false,
			},
			expectedCallToLLMOps: true,
			expectedClaimSummary: db.ClaimSummary{
				ClaimId: claimWithNotes.Id,
				Title:   "this is the title 1",
				Summary: "and this is the summary 1",
			},
			wantErr: false,
		},
	}

	superuser := test_utils.Superuser()
	for _, tc := range testCases {
		s.Run(tc.name, func() {
			ctx := context.Background()
			ctx = authz.WithUser(ctx, superuser)

			s.deps.Clk.Set(tc.time)

			counter := 0
			timesCall := 0
			if tc.expectedCallToLLMOps {
				timesCall = 1
			}
			s.deps.MockLLMOpsClient.
				EXPECT().
				GenerateClaimUpdateSummary(gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, req []string, source claim_enums.ClaimsProvider) (*llmops.ClaimSummary, error) {
					counter += 1
					return &llmops.ClaimSummary{
							Title:   fmt.Sprintf("this is the title %d", counter),
							Summary: fmt.Sprintf("and this is the summary %d", counter),
						},
						nil
				}).
				Times(timesCall)

			summary, err := s.deps.Client.MakeSummary(ctx, tc.claimId, tc.interval, tc.makeSummaryOptions)
			if tc.wantErr {
				s.Require().Error(err)
				return
			}
			s.Require().NoError(err)

			s.Equal(tc.expectedClaimSummary.ClaimId.String(), summary.ClaimId.String())
			s.Equal(tc.expectedClaimSummary.Title, summary.Title)
			s.Equal(tc.expectedClaimSummary.Summary, summary.Summary)
		})
	}
}

func (s *claimSummariesClientTestSuite) TestMakeAndSendSummariesEmailRecipients() {
	ctx := context.Background()

	policyNars := s.seedPolicy(ctx, "1001010", s.dotNumber)
	policySnapsheet := s.seedPolicy(ctx, "2002020", 222222)

	claimNars := s.seedClaim(ctx, policyNars.PolicyNumber.String(), 1, claim_enums.ClaimStatusOpen, claim_enums.ClaimsProviderNars)
	claimSnapsheet := s.seedClaim(ctx, policySnapsheet.PolicyNumber.String(), 1, claim_enums.ClaimStatusOpen, claim_enums.ClaimsProviderSnapsheet)

	s.seedClaimNotes(
		ctx,
		claimNars.ExternalId,
		s.deps.Clk.Now(),
		"NARS claim update.",
		claim_enums.ClaimsProviderNars,
	)

	s.seedClaimNotes(
		ctx,
		claimSnapsheet.ExternalId,
		s.deps.Clk.Now(),
		"Snapsheet claim update.",
		claim_enums.ClaimsProviderSnapsheet,
	)

	s.deps.Clk.Set(s.deps.Clk.Now())
	ctx = authz.WithUser(ctx, test_utils.Superuser())

	s.deps.MockLLMOpsClient.
		EXPECT().
		GenerateClaimUpdateSummary(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, notes []string, source claim_enums.ClaimsProvider) (*llmops.ClaimSummary, error) {
			return &llmops.ClaimSummary{
				Title:   "Test Summary Title",
				Summary: "Test Summary Content",
			}, nil
		}).
		Times(2)

	var sentEmailRequests []models.SendRequest
	s.deps.MockEmailer.
		EXPECT().
		Send(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, req *models.SendRequest) (*models.SendResponse, error) {
			sentEmailRequests = append(sentEmailRequests, *req)
			return &models.SendResponse{}, nil
		}).
		Times(2) // Expect 2 emails: one for NARS (external), one for Snapsheet (internal)

	err := s.deps.Client.MakeAndSendSummaries(ctx, nil)
	s.Require().NoError(err)

	s.Require().Len(sentEmailRequests, 2)

	slices.SortFunc(sentEmailRequests, func(req1, req2 models.SendRequest) int {
		return len(req1.BCC) - len(req2.BCC)
	})

	for _, email := range sentEmailRequests {
		s.Require().Len(email.Recipients, 1)
		s.Equal("<EMAIL>", email.Recipients[0].EmailAddress)
		s.Equal("Claims Nirvana", email.Recipients[0].Name)
	}

	// First email should be for NARS (external BCC: fleet admin + 2 hardcoded internal contacts)
	narsEmail := sentEmailRequests[0]
	s.Require().Len(narsEmail.BCC, 3)

	// Second email should be for Snapsheet (internal BCC only: 7 internal contacts)
	snapsheetEmail := sentEmailRequests[1]
	s.Require().Len(snapsheetEmail.BCC, 7)

	// Verify NARS email includes fleet admin in BCC
	fleetAdminFound := false
	for _, bcc := range narsEmail.BCC {
		if bcc.EmailAddress == s.deps.FleetAdmin.Email {
			fleetAdminFound = true
			break
		}
	}
	s.True(fleetAdminFound, "Fleet admin should be in BCC for NARS email")

	internalContacts := []string{
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
	}
	expectedInternalEmailsFound := []int{2, 7}
	// Verify emails internal contacts in BCC
	for i, email := range sentEmailRequests {
		internalEmailsFound := 0
		for _, bcc := range email.BCC {
			if slices.Contains(internalContacts, bcc.EmailAddress) {
				internalEmailsFound++
			}
		}
		s.Equal(expectedInternalEmailsFound[i], internalEmailsFound, "Email %d should have 2 internal contacts in BCC", i)
	}
}
