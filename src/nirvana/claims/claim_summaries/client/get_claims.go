package client

import (
	"context"

	claims_db "nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
)

// getClaims fetches claims by external Ids if provided, otherwise returns all open/reopened claims.
func (c *Client) getClaims(
	ctx context.Context,
	claimExternalIds []string,
) ([]claims_db.Claim, error) {
	var openClaims []claims_db.Claim
	filters := []claims_db.Filter{}

	if len(claimExternalIds) > 0 {
		filters = append(
			filters,
			claims_db.ClaimExternalIdIn(claimExternalIds),
		)
	} else {
		filters = append(
			filters,
			claims_db.ClaimStatusIn(
				claim_enums.ClaimStatusOpen,
				claim_enums.ClaimStatusReopen,
			),
		)
	}

	claims, err := c.deps.ClaimsClient.GetClaimsBySource(
		ctx,
		claim_enums.ClaimsProviderNars,
		filters...,
	)
	if err != nil {
		return nil, err
	}
	openClaims = append(openClaims, claims...)

	claims, err = c.deps.ClaimsClient.GetClaimsBySource(
		ctx,
		claim_enums.ClaimsProviderSnapsheet,
		filters...,
	)
	if err != nil {
		return nil, err
	}
	openClaims = append(openClaims, claims...)
	return openClaims, nil
}
