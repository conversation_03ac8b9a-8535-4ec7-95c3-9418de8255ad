package client

import (
	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	notes_client "nirvanatech.com/nirvana/claims/claim_notes/client"
	"nirvanatech.com/nirvana/claims/claim_summaries/db"
	claims_client "nirvanatech.com/nirvana/claims/client"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/metrics"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fleet"
	"nirvanatech.com/nirvana/emailer"
	llmops "nirvanatech.com/nirvana/llmops/src/go-client"
	"nirvanatech.com/nirvana/policy"
)

type deps struct {
	fx.In

	Clk     clock.Clock
	Emailer emailer.Emailer

	ClaimsClient  *claims_client.Client
	NotesClient   *notes_client.Client
	LLMOpsClient  llmops.LLMOpsClient
	MetricsClient metrics.Client
	PolicyClient  policy.Client

	AuthWrapper         auth.DataWrapper
	ClaimSummaryWrapper *db.DataWrapper
	FleetWrapper        fleet.DataWrapper
}

type Client struct {
	deps deps
}

type ClaimSummary struct {
	Number  string
	Title   string
	Summary string
	ClaimId uuid.UUID
	Source  claim_enums.ClaimsProvider
}

type MakeSummaryOptions struct {
	// whether it's a weekly-scheduled summary or an ad-hoc one. This option is open only
	// because of the backfill. In the future, we should only have non-scheduled summaries here
	IsScheduled bool
}

const (
	noNotesSummary = "Last week, there were no new developments regarding your claim. Please rest assured that we are actively monitoring the situation, and we will keep you informed of any changes. Thank you for your patience and understanding."
	noNotesTitle   = "Actively monitoring, with no new developments"
)

var ExistingSummaryError = errors.New("Summary is already in DB for that claim and day")

// these are the only notes categories we use for summaries. The others are either delicate in nature
// (e.g.: medical), or unhelpful (e.g.: managerial notes from claims leaders to adjusters).
var eligibleNotesCategories = []string{
	"CORRESPONDENCE OUT",
	"CORRESPONDENCE IN",
	"CONTACT EFFORT",
	"COVERAGE",
	"POLICY DOCUMENTS REQUESTED",
	"STATUS REPORT",
	"COVERAGE ANALYSIS",
	"INITIAL STATUS REPORT",
	"INVESTIGATION",
	"PHONE CALL",
	"INTERIM STATUS REPORT",
	"CONTACT ACHIEVED",
	"CLAIM STATUS CHANGE",
	"CLOSING NOTE",
	"LIABILITY ANALYSIS",
	"TRANSACTION",
	"INVOICE PAYMENT STAGED",
	"SETTLEMENT",
	"CLOSING STATUS REPORT",
	"LIABILITY POSITION",
	"RESOLUTION PLAN",
	"LITIGATION",
	"Cargo Coverage Template",
	"Claim Closure",
	"Cargo Coverage Template",
	"Coverage Approval Template",
	"Coverage Question Review",
	"Denial Review",
	"Estimate / Supplement Status",
	"Fleet Coverage Template",
	"Initial Claim Review",
	"Initial Lawsuit Template",
	"Injury Demand Received",
	"Liability Note",
	"Other than Fleet Coverage Template",
	"Referral to Counsel",
	"Settlement Offer(s)",
	"Subrogation Transfer Template",
	"", // if they don't use a note template
}

func newClient(deps deps) Client {
	return Client{
		deps: deps,
	}
}
