package client

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	claim_notes_db "nirvanatech.com/nirvana/claims/claim_notes/db"
	"nirvanatech.com/nirvana/claims/claim_summaries/db"
	claims_db "nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/metrics"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fleet"
	policy_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/emailer/models"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/policy"
)

// The scheduled summaries span a full week
const defaultIntervalInDays = 7

// MakeAndSendSummaries constructs summaries for all open claims from the notes that ajustors have
// inputted in the last week. It then sends an email (but only to launched accounts!) to the fleet
// manager with one summary per each open claim for the account.
func (c Client) MakeAndSendSummaries(ctx context.Context, claimExternalIds []string) error {
	user := authz.UserFromContext(ctx)
	if !user.IsSuperuser() {
		return errors.Newf("User not authorized to send Claims Summary email")
	}

	openClaims, err := c.getClaims(ctx, claimExternalIds)
	if err != nil {
		return err
	}

	policyNumbersWithOpenClaims := slice_utils.Map(openClaims, func(c claims_db.Claim) string {
		return c.PolicyNumber
	})

	policiesWithOpenClaims, err := c.deps.PolicyClient.GetPolicies(ctx, policy.GetPoliciesFilter{
		PolicyNumbers: policyNumbersWithOpenClaims,
	})
	if err != nil {
		return err
	}

	// we get emails for the accounts of said open claims
	fleetManagerEmailsByDOT, err := c.getFleetManagerEmailsByDOT(
		ctx,
		policiesWithOpenClaims,
	)
	if err != nil {
		return err
	}

	// If we're asked to run a scheduled summary on Monday at 9AM, what we do is run it for
	// the whole last week. The interval spans a week from previous Monday at 00:00:00, to the next
	endOfInterval := time_utils.StartOfDayFor(c.deps.Clk.Now())
	interval := time_utils.Interval{
		Start: time_utils.AddDays(endOfInterval, -defaultIntervalInDays),
		End:   endOfInterval,
	}
	// we get notes from last week for those open claims
	lastWeekNotesPerClaimNumber, err := c.getLastWeekNotesForOpenClaims(ctx, interval, openClaims)
	if err != nil {
		return err
	}

	openClaimsForDOT := make(map[int64][]claims_db.Claim)
	for _, p := range policiesWithOpenClaims {
		openClaimsForPolicy := slice_utils.Filter(
			openClaims,
			func(c claims_db.Claim) bool {
				return p.PolicyNumber.String() == c.PolicyNumber
			},
		)

		openClaimsForDOT[p.CompanyInfo.DOTNumber] = append(
			openClaimsForDOT[p.CompanyInfo.DOTNumber],
			openClaimsForPolicy...,
		)
	}

	claimNumberToId := make(map[string]uuid.UUID)
	for _, claim := range openClaims {
		claimNumberToId[claim.ExternalId] = claim.Id
	}

	for dotNumber, openClaimsDOT := range openClaimsForDOT {
		err := c.makeAndSendSummariesForDOT(
			ctx,
			dotNumber,
			openClaimsDOT,
			lastWeekNotesPerClaimNumber,
			fleetManagerEmailsByDOT[dotNumber],
			claimNumberToId,
			interval,
		)
		var operationErr error
		if err != nil {
			operationErr = errors.Wrapf(err, "unable to send claim summaries for dot=%d", dotNumber)
		}
		c.deps.MetricsClient.IncOperation(ctx, metrics.OperationMakeClaimSummary, operationErr)
	}

	return nil
}

func (c *Client) makeAndSendSummariesForDOT(
	ctx context.Context,
	dotNumber int64,
	openClaims []claims_db.Claim,
	lastWeekNotesPerClaimNumber map[string][]claim_notes_db.Note,
	fleetManagerEmailsForDOT []string,
	claimNumberToId map[string]uuid.UUID,
	interval time_utils.Interval,
) error {
	if len(openClaims) == 0 {
		return errors.Newf("no open claims for DOT Number=%d", dotNumber)
	}
	llmSummariesForDOT, err := c.summarizeNotesForDOT(
		ctx,
		lastWeekNotesPerClaimNumber,
		openClaims,
	)
	if err != nil {
		return err
	}

	return c.storeAndSendForDOT(
		ctx,
		dotNumber,
		llmSummariesForDOT,
		fleetManagerEmailsForDOT,
		claimNumberToId,
		interval,
	)
}

func (c *Client) getFleetManagerEmailsByDOT(
	ctx context.Context,
	policies []policy_wrapper.Policy,
) (map[int64][]string, error) {
	dotNumbers := slice_utils.Map(policies, func(p policy_wrapper.Policy) string {
		return strconv.Itoa(int(p.CompanyInfo.DOTNumber))
	})

	fleets, err := c.deps.FleetWrapper.FetchFleets(
		ctx,
		fleet.WhereDOTIn(dotNumbers...),
	)
	if err != nil {
		return nil, err
	}

	fleetManagerEmailsByDOT := make(map[int64][]string)
	for _, f := range fleets {
		users, err := c.deps.AuthWrapper.FetchFleetAuthzUsers(ctx, f.ID)
		if err != nil {
			return nil, err
		}

		for _, u := range users {
			if u.IsFleetAdmin() {
				parsedDotNumber, err := strconv.Atoi(f.DotNumber)
				if err != nil {
					return nil, errors.Wrapf(err, "unable to parse DOT Number=%s as int", f.DotNumber)
				}
				fleetManagerEmailsByDOT[int64(parsedDotNumber)] = slice_utils.Dedup(
					append(fleetManagerEmailsByDOT[int64(parsedDotNumber)], u.LowerEmail),
				)
			}
		}
	}

	return fleetManagerEmailsByDOT, nil
}

// storeAndSendForDOT stores the summaries in the database and sends an email to the fleet manager with
// the summaries for a specific account.
func (c Client) storeAndSendForDOT(
	ctx context.Context,
	dotNumber int64,
	llmSummariesForDOT []ClaimSummary,
	fleetManagerEmailsForDOT []string,
	claimNumberToId map[string]uuid.UUID,
	interval time_utils.Interval,
) error {
	var claimSummaries []db.ClaimSummary
	for _, s := range llmSummariesForDOT {
		if s.Source == claim_enums.ClaimsProviderSnapsheet {
			continue
		}
		claimSummaries = append(claimSummaries, *db.
			NewClaimSummary().
			WithClaimId(claimNumberToId[s.Number]).
			WithTitle(s.Title).
			WithInterval(interval).
			WithSummary(s.Summary).
			WithSource(s.Source).
			WithScheduled(true))
	}
	err := c.deps.ClaimSummaryWrapper.InsertManyClaimSummaries(ctx, claimSummaries)
	if err != nil {
		return errors.Wrapf(
			err,
			"unable to insert claim summaries for DOT = %d",
			dotNumber,
		)
	}
	contacts := make([]models.Contact, 0, len(fleetManagerEmailsForDOT))
	for _, email := range fleetManagerEmailsForDOT {
		contacts = append(contacts, models.Contact{
			EmailAddress: email,
		})
	}
	internalContacts := []models.Contact{
		{
			Name:         "David Halman",
			EmailAddress: "<EMAIL>",
		},
		{
			EmailAddress: "<EMAIL>",
			Name:         "Joaquin Moreira",
		},
		{
			EmailAddress: "<EMAIL>",
			Name:         "Nicolas Leyton",
		},
		{
			EmailAddress: "<EMAIL>",
			Name:         "Chris Xue",
		},
		{
			EmailAddress: "<EMAIL>",
			Name:         "Stephanie Chau",
		},
	}

	// For logging and errors
	internalContactsEmails := slice_utils.Map(internalContacts, func(c models.Contact) string {
		return c.EmailAddress
	})

	// Send Snapsheet summaries to internal contacts for QA phase
	snapsheetClaimsSummariesForDOT := slice_utils.Filter(llmSummariesForDOT, func(s ClaimSummary) bool {
		return s.Source == claim_enums.ClaimsProviderSnapsheet
	})
	if len(snapsheetClaimsSummariesForDOT) > 0 {
		err = c.sendSummariesEmail(ctx, emailArgs{
			DotNumber: dotNumber,
			Interval:  interval,
			Summaries: snapsheetClaimsSummariesForDOT,
			Contacts:  internalContacts,
		})
		if err != nil {
			return errors.Wrapf(
				err,
				"unable to send emails to emails=%s, DOT=%d",
				strings.Join(internalContactsEmails, ","),
				dotNumber,
			)
		}
		log.Info(
			ctx,
			"sent emails to internal contacts",
			log.Int64("dotNumber", dotNumber),
			log.Strings("emails", internalContactsEmails),
		)
	}

	// Send placeholder summaries to Fleet Manager during QA phase
	placeholderSnapsheetSummaries := make([]ClaimSummary, 0, len(snapsheetClaimsSummariesForDOT))
	for _, s := range snapsheetClaimsSummariesForDOT {
		placeholderSnapsheetSummaries = append(placeholderSnapsheetSummaries, ClaimSummary{
			Number:  s.Number,
			Title:   "",
			Summary: "No updates available for this claim while our systems are being updated. Thank you for your patience.",
			ClaimId: s.ClaimId,
			Source:  claim_enums.ClaimsProviderSnapsheet,
		})
	}

	narsClaimsSummariesForDOT := slice_utils.Filter(llmSummariesForDOT, func(s ClaimSummary) bool {
		return s.Source == claim_enums.ClaimsProviderNars
	})
	narsClaimsSummariesForDOT = append(narsClaimsSummariesForDOT, placeholderSnapsheetSummaries...)

	if len(narsClaimsSummariesForDOT) > 0 {
		err = c.sendSummariesEmail(ctx, emailArgs{
			DotNumber: dotNumber,
			Interval:  interval,
			Summaries: narsClaimsSummariesForDOT,
			Contacts:  contacts,
		})
		if err != nil {
			return errors.Wrapf(
				err,
				"unable to send emails to emails=%s, DOT=%d",
				strings.Join(fleetManagerEmailsForDOT, ","),
				dotNumber,
			)
		}
		log.Info(
			ctx,
			"sent emails to fleet managers",
			log.Int64("dotNumber", dotNumber),
			log.Strings("emails", fleetManagerEmailsForDOT),
		)
	}
	return nil
}

func (c *Client) getLastWeekNotesForOpenClaims(
	ctx context.Context,
	interval time_utils.Interval,
	openClaims []claims_db.Claim,
) (map[string][]claim_notes_db.Note, error) {
	openClaimsExternalIds := slice_utils.Map(openClaims, func(c claims_db.Claim) string {
		return c.ExternalId
	})

	lastWeekNotesForOpenClaims, err := c.deps.NotesClient.GetNotes(
		ctx,
		claim_notes_db.ClaimExternalIdIn(openClaimsExternalIds),
		claim_notes_db.CreatedAtGTE(interval.Start),
		// we only use some categories to construct the summaries. The others are either not useful, or
		// contain delicate information.
		claim_notes_db.CategoryIn(eligibleNotesCategories...),
	)
	if err != nil {
		return nil, err
	}

	lastWeekNotesPerClaimNumber := make(map[string][]claim_notes_db.Note)
	for _, note := range lastWeekNotesForOpenClaims {
		lastWeekNotesPerClaimNumber[note.ClaimExternalId] = append(
			lastWeekNotesPerClaimNumber[note.ClaimExternalId],
			note,
		)
	}

	return lastWeekNotesPerClaimNumber, nil
}

func (c *Client) summarizeNotesForDOT(
	ctx context.Context,
	lastWeekNotesPerClaimNumber map[string][]claim_notes_db.Note,
	openClaimsForDOT []claims_db.Claim,
) ([]ClaimSummary, error) {
	claimsByExternalId := make(map[string]claims_db.Claim)
	for _, claim := range openClaimsForDOT {
		claimsByExternalId[claim.ExternalId] = claim
	}

	summaries := make([]ClaimSummary, 0, len(openClaimsForDOT))
	for _, claim := range openClaimsForDOT {
		notes := lastWeekNotesPerClaimNumber[claim.ExternalId]

		if len(notes) == 0 {
			summaries = append(
				summaries,
				ClaimSummary{
					Number:  claim.ExternalId,
					Title:   noNotesTitle,
					Summary: noNotesSummary,
					ClaimId: claim.Id,
					Source:  claim.Source,
				},
			)
			continue
		}

		parsedNotes := parseNotes(notes)
		claimSummary, err := c.deps.LLMOpsClient.GenerateClaimUpdateSummary(
			ctx,
			parsedNotes,
			claim.Source,
		)
		if err != nil {
			return nil,
				errors.Wrapf(
					err,
					"unable to get summary from LLMOps Service for claim's ExternalId=%s",
					claim.ExternalId,
				)
		}

		summaries = append(
			summaries,
			ClaimSummary{
				Number:  claim.ExternalId,
				Title:   claimSummary.Title,
				Summary: claimSummary.Summary,
				ClaimId: claim.Id,
				Source:  claim.Source,
			},
		)
	}

	return summaries, nil
}

// parseNotes joins the category and text of the notes to leave one string per note with the full
// context. These will be joined by the LLMOps service to generate the summary.
func parseNotes(notes []claim_notes_db.Note) []string {
	return slice_utils.Map(notes, func(n claim_notes_db.Note) string {
		var category string
		if n.Category != nil {
			category = *n.Category
		}
		return fmt.Sprintf("%s: %s;", category, n.GetValue())
	})
}
