load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "client",
    srcs = [
        "client.go",
        "fx.go",
        "get_claims.go",
        "get_summaries_for_claim.go",
        "get_summary.go",
        "has_eligible_notes_for_summarization.go",
        "insert_feedback.go",
        "make_and_send_summaries.go",
        "make_summary.go",
        "send_summaries_email.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/claim_summaries/client",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/claim_notes/client",
        "//nirvana/claims/claim_notes/db",
        "//nirvana/claims/claim_summaries/db",
        "//nirvana/claims/client",
        "//nirvana/claims/db",
        "//nirvana/claims/enums",
        "//nirvana/claims/metrics",
        "//nirvana/common-go/log",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/str_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/fleet",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/emailer",
        "//nirvana/emailer/models",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/llmops/src/go-client",
        "//nirvana/llmops/src/go-client/generated:llmops",
        "//nirvana/policy",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "client_test",
    srcs = [
        "client_test.go",
        "has_eligible_notes_for_summarization_test.go",
        "make_and_send_summaries_test.go",
        "make_and_send_summaries_tmp_test.go",
    ],
    deps = [
        ":client",
        "//nirvana/claims/claim_notes/client",
        "//nirvana/claims/claim_notes/db",
        "//nirvana/claims/claim_summaries/db",
        "//nirvana/claims/client",
        "//nirvana/claims/db",
        "//nirvana/claims/enums",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/test_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/emailer",
        "//nirvana/emailer/models",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/testfixtures/fleet_fixture",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/llmops/src/go-client",
        "//nirvana/llmops/src/go-client/generated:llmops",
        "//nirvana/policy/constants",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
