package gql

import (
	"context"

	"github.com/google/uuid"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/gqlschema/models"
)

type fetchDraftFNOLByIdArgs struct {
	Id string
}

func (r *Resolver) fetchDraftFNOLById(
	ctx context.Context, args fetchDraftFNOLByIdArgs,
) (*models.DraftFnol, error) {
	if err := r.deps.AuthzClient.CanReadDraftFnols(ctx); err != nil {
		return nil, err
	}

	id, err := uuid.Parse(args.Id)
	if err != nil {
		return nil, err
	}
	draftFnol, err := r.deps.FnolWrapper.GetFnol(ctx, id)
	if err != nil {
		return nil, err
	}

	df := models.FnolDraftFromDb(ctx, *draftFnol)
	if df.PolicyNumber != nil {
		policy, err := r.deps.PolicyClient.GetLatestPolicy(ctx, *df.PolicyNumber, false)
		if err != nil {
			// If no policy is found, we don't want the whole query to fail, as this is only a draft
			// FNOL, so we log the error, but we still return the draft FNOL.
			log.Warn(
				ctx,
				"Failed to get policy for draft fnol",
				log.String("policyNumber", *df.PolicyNumber),
				log.Err(err),
			)
			return df, nil
		}

		appIdToIsTestMap, err := r.deps.ApplicationClient.GetAppIDsToIsTestMap(ctx, policy.ApplicationId)
		if err != nil {
			// If we can't get the app Ids to is test map, we log the error, but we still return the
			// draft FNOL.
			log.Warn(
				ctx,
				"Failed to get app Ids to is test map for draft fnol's policy",
				log.String("policyNumber", *df.PolicyNumber),
				log.Err(err),
			)
			return df, nil
		}

		if appIdToIsTestMap[policy.ApplicationId] {
			return df.WithIsTestPolicy(true), nil
		}
		return df.WithIsTestPolicy(false), nil
	}
	return df, nil
}
