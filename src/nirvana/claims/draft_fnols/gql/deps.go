package gql

import (
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/application/normalizer"
	"nirvanatech.com/nirvana/claims/authz"
	"nirvanatech.com/nirvana/claims/fnols/client"
	"nirvanatech.com/nirvana/claims/fnols/db"
	"nirvanatech.com/nirvana/claims/metrics"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/db-api/db_wrappers/file_upload"
	"nirvanatech.com/nirvana/policy"
)

type deps struct {
	fx.In

	AuthzClient       *authz.Client
	MetricsClient     metrics.Client
	FnolClient        *client.Client
	FnolWrapper       *db.DataWrapper
	FileUploadWrapper file_upload.DataWrapper
	ApplicationClient *normalizer.Client
	PolicyClient      policy.Client

	FileUploadManager file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
}
