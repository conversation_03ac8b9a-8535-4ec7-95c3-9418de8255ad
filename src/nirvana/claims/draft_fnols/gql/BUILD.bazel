load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "gql",
    srcs = [
        "archive_draft_fnols.go",
        "deps.go",
        "fetch_draft_fnol_attachments.go",
        "fetch_draft_fnol_by_id.go",
        "fetch_draft_fnols.go",
        "resolver.go",
        "upsert_draft_fnol.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/draft_fnols/gql",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/application/normalizer",
        "//nirvana/claims/authz",
        "//nirvana/claims/enums",
        "//nirvana/claims/fnols/client",
        "//nirvana/claims/fnols/db",
        "//nirvana/claims/fnols/enums",
        "//nirvana/claims/fnols/gql",
        "//nirvana/claims/metrics",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api/db_wrappers/file_upload",
        "//nirvana/gqlschema/models",
        "//nirvana/gqlschema/resolver",
        "//nirvana/infra/authz",
        "//nirvana/policy",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_samsarahq_thunder//graphql/schemabuilder",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "gql_test",
    srcs = [
        "archive_draft_fnols_test.go",
        "fetch_draft_fnol_attachments_test.go",
        "fetch_draft_fnol_by_id_test.go",
        "fetch_draft_fnols_test.go",
        "resolver_suite_test.go",
        "upsert_draft_fnol_test.go",
    ],
    deps = [
        ":gql",
        "//nirvana/claims/enums",
        "//nirvana/claims/fnols/db",
        "//nirvana/claims/fnols/enums",
        "//nirvana/claims/fnols/gql",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/s3_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/test_utils",
        "//nirvana/gqlschema/models",
        "//nirvana/graphql-server/queryclient",
        "//nirvana/infra/authz",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/testfixtures/agency_fixture",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "@com_github_aws_aws_sdk_go//aws",
        "@com_github_aws_aws_sdk_go//service/s3/s3manager",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
