package gql_test

import (
	"context"
	"time"

	"github.com/google/uuid"

	claim_enums "nirvanatech.com/nirvana/claims/enums"
	fnols_db "nirvanatech.com/nirvana/claims/fnols/db"
	"nirvanatech.com/nirvana/claims/fnols/enums"
	fnol_enums "nirvanatech.com/nirvana/claims/fnols/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/gqlschema/models"
	"nirvanatech.com/nirvana/graphql-server/queryclient"
	"nirvanatech.com/nirvana/infra/authz"
)

func (d *draftFnolsResolverTestSuite) TestFetchDraftFNOLById() {
	ctx := context.Background()

	fnolId := uuid.New()

	contact, err := fnols_db.NewFnolContactBuilder().
		WithDefaultMockData().
		WithFirstName("<PERSON>").
		WithLastName("Smith").
		WithEmail(pointer_utils.String("<EMAIL>")).
		WithPhone("0987654321").
		WithFnolId(fnolId).
		WithContactType(enums.FnolContactTypeReporter).
		Build()
	d.Require().NoError(err)

	vehicle, err := fnols_db.NewFnolVehicleBuilder().
		WithDefaultMockData().
		WithFnolId(fnolId).
		WithIsInsuredVehicle(true).
		WithRegistrationNumber(pointer_utils.String("ABC123")).
		WithVIN(pointer_utils.String("1HGBH41JXMN109186")).
		Build()
	d.Require().NoError(err)

	otherVehicle, err := fnols_db.NewFnolVehicleBuilder().
		WithDefaultMockData().
		WithFnolId(fnolId).
		WithIsInsuredVehicle(false).
		WithRegistrationNumber(pointer_utils.String("XYZ987")).
		WithVIN(nil).
		Build()
	d.Require().NoError(err)

	fnol, err := fnols_db.NewClaimFnolBuilder(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithID(fnolId).
		WithPolicyNumber(pointer_utils.String("NISTK1004500-24")).
		WithNoticeType(pointer_utils.ToPointer(enums.FnolNoticeTypeClaim)).
		WithLossDatetime(pointer_utils.Time(time.Now().UTC())).
		WithLossLocation(pointer_utils.String("123 Main St, Springfield, IL 62701")).
		WithLossState(pointer_utils.String("IL")).
		WithPoliceAgencyName(pointer_utils.String("Springfield Police Department")).
		WithPoliceReportNumber(pointer_utils.String("2021-12345")).
		WithIncidentDescription(pointer_utils.String("Car accident on Main St")).
		WithInjuriesInvolved(pointer_utils.Bool(true)).
		WithArchivedAt(pointer_utils.Time(time.Now().UTC())).
		WithStatus(fnol_enums.FnolStatusDraft).
		WithContacts([]fnols_db.FnolContact{*contact}).
		WithVehicles([]fnols_db.FnolVehicle{*vehicle, *otherVehicle}).
		Build()
	d.Require().NoError(err)
	d.Require().NoError(d.fnolWrapper.UpsertFnol(ctx, *fnol))

	testCases := []struct {
		name    string
		user    authz.User
		id      uuid.UUID
		wantErr bool
	}{
		{
			name:    "With SuperUser",
			user:    d.usersFixture.Superuser.AuthzUser(),
			id:      fnol.Id,
			wantErr: false,
		},
		{
			name:    "With UnprivilegedUser",
			user:    d.usersFixture.FleetAdmin.AuthzUser(),
			id:      fnol.Id,
			wantErr: true,
		},
		{
			name:    "Non existent ID",
			user:    d.usersFixture.Superuser.AuthzUser(),
			id:      uuid.New(),
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		d.Run(tc.name, func() {
			draftFnol, err := getDraftFnolById(ctx, tc.user, d.gqlClient, tc.id.String())
			if tc.wantErr {
				d.Require().Error(err)
				return
			}
			d.Require().NoError(err)
			d.Require().NotNil(draftFnol)
			d.Require().Equal(tc.id.String(), draftFnol.Id.String())
		})
	}
}

func getDraftFnolById(
	ctx context.Context,
	user authz.User,
	gqlClient *queryclient.QueryClient,
	id string,
) (models.DraftFnol, error) {
	query := `query DraftFnolById($id: string!) {
		draftFnolById(id: $id) {
			id
			dotNumber
			policyNumber
			isTestPolicy
			lossDatetime
			lossLocation
			lossState
			policeAgencyName
			policeReportNumber
			incidentDescription
			noticeType
			injuriesInvolved
			createdBy
			archivedAt
			fnolId
			attachments {
				handleId
			}
			contacts {
				firstName
				lastName
				phone
				email
			}
			vehicles {
				isInsuredVehicle
				registrationNumber
				vin
			}
		}
	}`

	var queryDraftFnolByIdOutput struct {
		DraftFnolById models.DraftFnol
	}

	vars := map[string]interface{}{
		"id": id,
	}
	err := gqlClient.Query(ctx, user, query, vars).ResultAs(&queryDraftFnolByIdOutput)
	return queryDraftFnolByIdOutput.DraftFnolById, err
}
