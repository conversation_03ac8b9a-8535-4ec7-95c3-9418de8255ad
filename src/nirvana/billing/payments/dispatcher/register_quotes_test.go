package dispatcher_test

import (
	"cmp"
	"context"
	"net/url"
	"slices"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/billing/payments/dispatcher"
	"nirvanatech.com/nirvana/billing/payments/enums"
	"nirvanatech.com/nirvana/billing/payments/internal/application"
	"nirvanatech.com/nirvana/billing/payments/internal/integrations/ascend"
	"nirvanatech.com/nirvana/billing/payments/internal/integrations/ascend/openapi"
	"nirvanatech.com/nirvana/billing/payments/internal/primitives"
	file_upload_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/file_upload"
	ibmodel "nirvanatech.com/nirvana/insurance-bundle/model"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func (s *dispatcherClientTestSuite) TestRegisterQuotes_Ascend() {
	ctx := context.Background()

	s.seedAscendEntityMapping(ctx, enums.NirvanaEntityTypeApplication, s.nfApp.ID.String(), "program-prod")
	s.seedAscendSandboxEntityMapping(ctx, enums.NirvanaEntityTypeApplication, s.nfTestApp1.ID.String(), "program-sandbox")

	s.seedAscendEntityMapping(ctx, enums.NirvanaEntityTypeAgency, s.nfApp.AgencyID.String(), "agency-prod")

	siriusPoint := insurancecoreproto.InsuranceCarrier_InsuranceCarrier_SiriusPoint.String()
	s.seedAscendEntityMapping(ctx, enums.NirvanaEntityTypeCarrier, siriusPoint, "carrier-prod")
	s.seedAscendSandboxEntityMapping(ctx, enums.NirvanaEntityTypeCarrier, siriusPoint, "carrier-sandbox")

	mockQuotePdfURL, err := url.Parse("https://s3.amazonaws.com/quote-link")
	s.Require().NoError(err)
	mockAscendAttachmentId := uuid.New().String()

	var createAttachmentRequestOpts *ascend.ReqOptions
	s.ascendClient.
		EXPECT().
		CreateAttachment(gomock.Any(), mockQuotePdfURL.String(), gomock.Any(), gomock.Any(), gomock.Any()).
		AnyTimes().
		DoAndReturn(func(ctx context.Context, fileURL string, title string, visibility openapi.AttachmentVisibility, opts *ascend.ReqOptions) (
			*openapi.Attachment,
			error,
		) {
			createAttachmentRequestOpts = opts
			return &openapi.Attachment{
				Id:         mockAscendAttachmentId,
				Title:      &title,
				Visibility: visibility,
			}, nil
		})

	var createBillableRequests []*openapi.CreateBillableRequest
	var createBillableRequestOpts *ascend.ReqOptions
	s.ascendClient.
		EXPECT().
		CreateBillable(gomock.Any(), gomock.Any(), gomock.Any()).
		AnyTimes().
		DoAndReturn(func(ctx context.Context, req openapi.CreateBillableRequest, opts *ascend.ReqOptions) (
			*openapi.Billable,
			error,
		) {
			createBillableRequests = append(createBillableRequests, &req)
			createBillableRequestOpts = opts
			return &openapi.Billable{
				Id: uuid.New().String(),
			}, nil
		})

	signaturePacketId := uuid.New()
	effectiveDate := time.Now()
	expirationDate := effectiveDate.AddDate(1, 0, 0)
	alQuoteNumber, err := primitives.NewQuoteNumber(appenums.CoverageAutoLiability, signaturePacketId)
	s.Require().NoError(err)
	glQuoteNumber, err := primitives.NewQuoteNumber(appenums.CoverageGeneralLiability, signaturePacketId)
	s.Require().NoError(err)

	validNonFleetProdPayload := dispatcher.RegisterQuotesPayload{
		ApplicationId: s.nfApp.ID,
		CarrierId:     insurancecoreproto.InsuranceCarrier_InsuranceCarrier_SiriusPoint,
		Quotes: []dispatcher.QuotePayload{
			{
				PolicyNumber:   "NNFTK0012345-24",
				QuoteNumber:    *alQuoteNumber,
				EffectiveDate:  effectiveDate,
				ExpirationDate: expirationDate,
				Premium:        decimal.NewFromInt(500),
				Fees:           decimal.NewFromInt(100),
				Surcharges:     decimal.NewFromInt(200),
				SurplusLines:   decimal.NewFromInt(300 + 50), // SL Taxes + Stamping Fees,
			},
			{
				PolicyNumber:   "NNFGL0012345-24",
				QuoteNumber:    *glQuoteNumber,
				EffectiveDate:  effectiveDate,
				ExpirationDate: expirationDate,
				Premium:        decimal.NewFromInt(1000),
			},
		},
		CommissionRate: decimal.NewFromFloat(0.1),
		PdfURL:         mockQuotePdfURL,
	}

	testCases := []struct {
		name                string
		payloadFactory      func() dispatcher.RegisterQuotesPayload
		wantSandboxed       bool
		wantAscendAgencyId  string
		wantProgramId       string
		wantAscendCarrierId string
		wantErr             error
	}{
		{
			name: "Fleet app",
			payloadFactory: func() dispatcher.RegisterQuotesPayload {
				payload := validNonFleetProdPayload
				payload.ApplicationId = uuid.MustParse(s.fleetApp.ID)
				return payload
			},
			wantErr: application.ErrUnsupportedProgramType,
		},
		{
			name: "No quotes",
			payloadFactory: func() dispatcher.RegisterQuotesPayload {
				req := validNonFleetProdPayload
				req.Quotes = []dispatcher.QuotePayload{}
				return req
			},
			wantErr: dispatcher.ErrInvalidPayload,
		},
		{
			name: "NF prod app",
			payloadFactory: func() dispatcher.RegisterQuotesPayload {
				return validNonFleetProdPayload
			},
			wantSandboxed:       false,
			wantAscendAgencyId:  "agency-prod",
			wantProgramId:       "program-prod",
			wantAscendCarrierId: "carrier-prod",
			wantErr:             nil,
		},
		{
			name: "NF test app",
			payloadFactory: func() dispatcher.RegisterQuotesPayload {
				payload := validNonFleetProdPayload
				payload.ApplicationId = s.nfTestApp1.ID
				return payload
			},
			wantSandboxed:       true,
			wantAscendAgencyId:  "6d6dbdc5-8454-4bda-855d-87b2145b1fa9", // Custom (fixed) sandbox agency ID
			wantProgramId:       "program-sandbox",
			wantAscendCarrierId: "carrier-sandbox",
			wantErr:             nil,
		},
	}
	for _, tc := range testCases {
		s.Run(tc.name, func() {
			// Reset all spied variables
			createBillableRequests = nil
			createBillableRequestOpts = nil
			createAttachmentRequestOpts = nil

			payload := tc.payloadFactory()

			err := s.client.RegisterQuotes(ctx, payload)
			if tc.wantErr != nil {
				s.Require().ErrorIs(err, tc.wantErr)
				s.Empty(createBillableRequests)
				s.Nil(createBillableRequestOpts)
				s.Nil(createAttachmentRequestOpts)
				return
			}
			s.Require().NoError(err)
			s.Equal(tc.wantSandboxed, createBillableRequestOpts.Sandboxed)
			s.Equal(tc.wantSandboxed, createAttachmentRequestOpts.Sandboxed)
			s.Len(createBillableRequests, len(payload.Quotes))

			for i, quote := range payload.Quotes {
				req := createBillableRequests[i]

				s.Equal(tc.wantProgramId, req.ProgramId)
				s.Equal(quote.PolicyNumber, *req.PolicyNumber)
				s.Equal(quote.QuoteNumber.String(), req.BillableIdentifier)
				s.Equal(tc.wantAscendCarrierId, req.CarrierIdentifier)
				s.Equal(dispatcher.NirvanaCoverageToAscendCoverage[quote.QuoteNumber.Coverage()], req.CoverageIdentifier)
				s.Equal(quote.EffectiveDate, req.EffectiveDate.Time)
				s.Equal(quote.ExpirationDate, req.ExpirationDate.Time)

				toCents := func(d decimal.Decimal) int {
					return int(d.Mul(decimal.NewFromInt(100)).IntPart())
				}
				s.Equal(toCents(quote.Premium), req.PremiumCents)
				s.Equal(
					toCents(quote.Fees.Mul(decimal.NewFromInt(1).Sub(payload.CommissionRate))),
					*req.PolicyFeeCents,
				)
				s.Equal(toCents(quote.Surcharges), *req.TaxesAndFeesCents)
				s.Equal(toCents(quote.SurplusLines), *req.SurplusLinesTaxCents)

				wantCommissionRate, _ := payload.CommissionRate.Float64()
				wantCommissionTerms := openapi.CommissionTerms{
					AgencyFeesCents:       pointer_utils.ToPointer(toCents(quote.Fees.Mul(payload.CommissionRate))),
					CommissionRate:        wantCommissionRate,
					OrganizationAccountId: &tc.wantAscendAgencyId,
				}
				gotCommissionTerms := req.CommissionTerms
				s.Require().Len(*gotCommissionTerms, 1)
				s.Equal(wantCommissionTerms, (*gotCommissionTerms)[0])

				if quote.QuoteNumber.Coverage() == appenums.CoverageAutoLiability {
					s.NotEmpty(req.Attachments)
					s.Require().Len(*req.Attachments, 1)
					attachment := (*req.Attachments)[0]
					s.Equal(mockAscendAttachmentId, attachment.Id)
				} else {
					s.Empty(req.Attachments)
				}

				wrapper := s.getAscendDataWrapper(tc.wantSandboxed)
				mapping, err := wrapper.GetEntityMappingByNirvanaId(ctx, enums.NirvanaEntityTypeQuote, quote.QuoteNumber.NirvanaId())
				s.Require().NoError(err)
				s.NotEmpty(mapping.AscendId)
			}
		})
	}
}

func (s *dispatcherClientTestSuite) TestGetDefaultQuotesPayload() {
	ctx := context.Background()

	alPolicyNumber := "NNFTK0012345-24"
	glPolicyNumber := "NNFGL0012345-24"
	mtcPolicyNumber := "NNMTC0012345-24"
	signaturePacketId := uuid.New()
	quotePdfHandleId := uuid.New()

	programType := insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted
	effectiveDate := timestamppb.New(time_utils.NewDate(2024, time.April, 15).ToTime())
	expirationDate := timestamppb.New(time_utils.NewDate(2025, time.April, 15).ToTime())

	// TrailerInterchange maps to APD
	trailerInterchangeCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("100", effectiveDate).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_TrailerInterchange).
		Build()

	// PropertyDamage maps to AL
	propertyDamageCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("200", effectiveDate).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
		Build()

	// BodilyInjury maps to AL
	bodilyInjuryCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("300", effectiveDate).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
		Build()

	// GeneralLiability maps to GL
	glCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("400", effectiveDate).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
		Build()

	// ReeferWithoutHumanError maps to MTC
	reeferCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("500", effectiveDate).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError).
		Build()

	defaultSurcharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("750", effectiveDate).
		WithDefaultSurchargeType_TestOnly().
		Build()

	alSurplusTaxSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(alPolicyNumber).
		WithSurplusTaxSurchargeType().
		WithAmountBasedBillingDetails("200", effectiveDate).
		Build()

	alFeeChargeSurplusTaxSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(alPolicyNumber).
		WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
		WithAmountBasedBillingDetails("250", effectiveDate).
		Build()

	glSurplusTaxSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(glPolicyNumber).
		WithSurplusTaxSurchargeType().
		WithAmountBasedBillingDetails("300", effectiveDate).
		Build()

	mtcFeeChargeSurplusTaxSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(mtcPolicyNumber).
		WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
		WithAmountBasedBillingDetails("350", effectiveDate).
		Build()

	alStampingFeeSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(alPolicyNumber).
		WithStampingFeeSurchargeType().
		WithAmountBasedBillingDetails("50", effectiveDate).
		Build()

	alFeeChargeStampingFeeSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(alPolicyNumber).
		WithStampingFeeFromFullyEarnedPremiumSurchargeType().
		WithAmountBasedBillingDetails("75", effectiveDate).
		Build()

	// Fee
	additionalInsuredCharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(mtcPolicyNumber).
		WithBaseChargeTypeWithBlanketRegularAdditionalInsured().
		WithAmountBasedBillingDetails("1000", effectiveDate).
		Build()

	rateBasedCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithDefaultRateBasedBillingDetails_TestOnly().
		Build()

	alPolicy := ibmodel.NewPolicyBuilder(programType).
		WithPolicyNumber(alPolicyNumber).
		WithCoverages([]*ibmodel.Coverage{
			{Id: appenums.CoverageAutoLiability.String()},
			{Id: appenums.CoverageAutoPhysicalDamage.String()},
		}).
		WithEffectiveInterval(&proto.Interval{Start: effectiveDate, End: expirationDate}).
		WithCharges([]*ptypes.Charge{
			trailerInterchangeCharge,
			propertyDamageCharge,
			bodilyInjuryCharge,
			alSurplusTaxSurcharge,
			alFeeChargeSurplusTaxSurcharge,
			alStampingFeeSurcharge,
			alFeeChargeStampingFeeSurcharge,
		}).
		Build()

	glPolicy := ibmodel.NewPolicyBuilder(programType).
		WithPolicyNumber(glPolicyNumber).
		WithCoverages([]*ibmodel.Coverage{{Id: appenums.CoverageGeneralLiability.String()}}).
		WithEffectiveInterval(&proto.Interval{Start: effectiveDate, End: expirationDate}).
		WithCharges([]*ptypes.Charge{
			glCharge,
			defaultSurcharge,
			glSurplusTaxSurcharge,
		}).
		Build()

	mtcPolicy := ibmodel.NewPolicyBuilder(programType).
		WithPolicyNumber(mtcPolicyNumber).
		WithCoverages([]*ibmodel.Coverage{{Id: appenums.CoverageMotorTruckCargo.String()}}).
		WithEffectiveInterval(&proto.Interval{Start: effectiveDate, End: expirationDate}).
		WithCharges([]*ptypes.Charge{
			reeferCharge,
			additionalInsuredCharge,
			rateBasedCharge,
			mtcFeeChargeSurplusTaxSurcharge,
		}).
		Build()

	segment := ibmodel.NewInsuranceBundleSegmentBuilder(programType).
		WithPolicies(
			map[string]*ibmodel.Policy{
				alPolicy.GetPolicyNumber():  alPolicy,
				glPolicy.GetPolicyNumber():  glPolicy,
				mtcPolicy.GetPolicyNumber(): mtcPolicy,
			},
		).
		Build()

	inMemIB := ibmodel.NewInsuranceBundleBuilder(programType).
		WithSegments([]*ibmodel.InsuranceBundleSegment{segment}).
		WithRootApplicationId(s.nfApp.ID).
		WithCoreForm(&insurancecoreproto.FormCore{
			DocumentHandleId:    quotePdfHandleId.String(),
			FormCompilationId:   signaturePacketId.String(),
			FormCompilationType: insurancecoreproto.FormCompilationType_FormCompilationTypeSignaturePacket,
		}).
		Build()

	s.Require().NoError(
		s.fileUploadWrapper.InsertFile(
			ctx,
			file_upload.NewFile(quotePdfHandleId, uuid.Nil, "key/quote-pdf", file_upload_enums.FileDestinationGroupForms),
		),
	)

	s.insuranceBundleClient.GetInMemoryInsuranceBundleFn = func(
		ctx context.Context,
		applicationId uuid.UUID,
	) (*ibmodel.InsuranceBundle, error) {
		if applicationId != s.nfApp.ID {
			return nil, application.ErrUnsupportedProgramType
		}
		return inMemIB, nil
	}

	alQuoteNumber, err := primitives.NewQuoteNumber(appenums.CoverageAutoLiability, signaturePacketId)
	s.Require().NoError(err)

	apdQuoteNumber, err := primitives.NewQuoteNumber(appenums.CoverageAutoPhysicalDamage, signaturePacketId)
	s.Require().NoError(err)

	glQuoteNumber, err := primitives.NewQuoteNumber(appenums.CoverageGeneralLiability, signaturePacketId)
	s.Require().NoError(err)

	mtcQuoteNumber, err := primitives.NewQuoteNumber(appenums.CoverageMotorTruckCargo, signaturePacketId)
	s.Require().NoError(err)

	pdfURL, err := url.Parse("https://s3.amazonaws.com/nirvana-forms/key/quote-pdf")
	s.Require().NoError(err)

	testCases := []struct {
		name          string
		applicationId uuid.UUID
		want          *dispatcher.RegisterQuotesPayload
		wantErr       error
	}{
		{
			name:          "Fleet app",
			applicationId: uuid.MustParse(s.fleetApp.ID),
			want:          nil,
			wantErr:       application.ErrUnsupportedProgramType,
		},
		{
			name:          "NF app",
			applicationId: s.nfApp.ID,
			want: &dispatcher.RegisterQuotesPayload{
				ApplicationId: s.nfApp.ID,
				CarrierId:     insurancecoreproto.InsuranceCarrier_InsuranceCarrier_SiriusPoint,
				Quotes: []dispatcher.QuotePayload{
					{
						PolicyNumber:   alPolicyNumber,
						QuoteNumber:    *alQuoteNumber,
						EffectiveDate:  effectiveDate.AsTime(),
						ExpirationDate: expirationDate.AsTime(),
						Premium:        decimal.NewFromFloat(500),
						Fees:           decimal.NewFromInt(1_000),
						Surcharges:     decimal.NewFromInt(750 + (200 + 250 + 300 + 350) + (50 + 75)), // DefaultSurcharges + SurplusLinesTaxes + SurplusLinesStampingFees
						SurplusLines:   decimal.NewFromFloat((200 + 250 + 300 + 350) + (50 + 75)),     // SurplusLinesTaxes + SurplusLinesStampingFees
					},
					{
						PolicyNumber:   alPolicyNumber,
						QuoteNumber:    *apdQuoteNumber,
						EffectiveDate:  effectiveDate.AsTime(),
						ExpirationDate: expirationDate.AsTime(),
						Premium:        decimal.NewFromFloat(100),
					},
					{
						PolicyNumber:   glPolicyNumber,
						QuoteNumber:    *glQuoteNumber,
						EffectiveDate:  effectiveDate.AsTime(),
						ExpirationDate: expirationDate.AsTime(),
						Premium:        decimal.NewFromFloat(400),
					},
					{
						PolicyNumber:   mtcPolicyNumber,
						QuoteNumber:    *mtcQuoteNumber,
						EffectiveDate:  effectiveDate.AsTime(),
						ExpirationDate: expirationDate.AsTime(),
						Premium:        decimal.NewFromFloat(500),
					},
				},
				PdfURL: pdfURL,
			},
			wantErr: nil,
		},
	}
	for _, tc := range testCases {
		s.Run(tc.name, func() {
			got, err := s.client.GetDefaultQuotesPayload(ctx, tc.applicationId)
			if tc.wantErr != nil {
				s.Require().ErrorIs(err, tc.wantErr)
				s.Nil(got)
				return
			}
			s.Require().NoError(err)

			slices.SortFunc(got.Quotes, func(a, b dispatcher.QuotePayload) int {
				return cmp.Compare(a.QuoteNumber.String(), b.QuoteNumber.String())
			})

			s.Equal(tc.want.ApplicationId.String(), got.ApplicationId.String())
			s.Equal(tc.want.CarrierId, got.CarrierId)

			s.Require().Equal(len(tc.want.Quotes), len(got.Quotes))
			for i, wantQuote := range tc.want.Quotes {
				gotQuote := got.Quotes[i]
				s.Equal(wantQuote.PolicyNumber, gotQuote.PolicyNumber)
				s.Equal(wantQuote.QuoteNumber, gotQuote.QuoteNumber)
				s.Equal(wantQuote.Premium.String(), gotQuote.Premium.String())
				s.Equal(wantQuote.EffectiveDate, gotQuote.EffectiveDate)
				s.Equal(wantQuote.ExpirationDate, gotQuote.ExpirationDate)
				s.Equal(wantQuote.Fees.String(), gotQuote.Fees.String())
				s.Equal(wantQuote.Surcharges.String(), gotQuote.Surcharges.String())
				s.Equal(wantQuote.SurplusLines.String(), gotQuote.SurplusLines.String())
			}
		})
	}
}

func TestRegisterQuotesPayload_Validate(t *testing.T) {
	validQuoteNumber, err := primitives.NewQuoteNumber(appenums.CoverageAutoLiability, uuid.New())
	require.NoError(t, err)

	validPayload := dispatcher.RegisterQuotesPayload{
		ApplicationId: uuid.New(),
		CarrierId:     insurancecoreproto.InsuranceCarrier_InsuranceCarrierFallsLake,
		Quotes: []dispatcher.QuotePayload{
			{
				PolicyNumber:   "PN123",
				QuoteNumber:    *validQuoteNumber,
				EffectiveDate:  time.Now(),
				ExpirationDate: time.Now().AddDate(1, 0, 0),
				Premium:        decimal.NewFromInt(1000),
				Fees:           decimal.NewFromInt(100),
				Surcharges:     decimal.NewFromInt(50),
				SurplusLines:   decimal.NewFromInt(100 + 25),
			},
		},
	}

	testCases := []struct {
		name       string
		reqFactory func() dispatcher.RegisterQuotesPayload
		wantErr    bool
	}{
		{
			name: "valid payload",
			reqFactory: func() dispatcher.RegisterQuotesPayload {
				return validPayload
			},
			wantErr: false,
		},
		{
			name: "missing ApplicationId",
			reqFactory: func() dispatcher.RegisterQuotesPayload {
				req := validPayload
				req.ApplicationId = uuid.Nil
				return req
			},
			wantErr: true,
		},
		{
			name: "invalid CarrierId",
			reqFactory: func() dispatcher.RegisterQuotesPayload {
				req := validPayload
				req.CarrierId = insurancecoreproto.InsuranceCarrier_InsuranceCarrier_Empty
				return req
			},
			wantErr: true,
		},
		{
			name: "missing Quotes",
			reqFactory: func() dispatcher.RegisterQuotesPayload {
				req := validPayload
				req.Quotes = []dispatcher.QuotePayload{}
				return req
			},
			wantErr: true,
		},
		{
			name: "missing PolicyNumber",
			reqFactory: func() dispatcher.RegisterQuotesPayload {
				req := validPayload
				req.Quotes[0].PolicyNumber = ""
				return req
			},
			wantErr: true,
		},
		{
			name: "zero-value effective date",
			reqFactory: func() dispatcher.RegisterQuotesPayload {
				req := validPayload
				req.Quotes[0].EffectiveDate = time.Time{}
				return req
			},
			wantErr: true,
		},
		{
			name: "zero-value expiration date",
			reqFactory: func() dispatcher.RegisterQuotesPayload {
				req := validPayload
				req.Quotes[0].ExpirationDate = time.Time{}
				return req
			},
			wantErr: true,
		},
		{
			name: "expiration date before effective date",
			reqFactory: func() dispatcher.RegisterQuotesPayload {
				req := validPayload
				req.Quotes[0].ExpirationDate = req.Quotes[0].EffectiveDate.AddDate(-1, 0, 0)
				return req
			},
			wantErr: true,
		},
		{
			name: "zero premium",
			reqFactory: func() dispatcher.RegisterQuotesPayload {
				req := validPayload
				req.Quotes[0].Premium = decimal.Zero
				return req
			},
			wantErr: true,
		},
		{
			name: "negative premium",
			reqFactory: func() dispatcher.RegisterQuotesPayload {
				req := validPayload
				req.Quotes[0].Premium = decimal.NewFromInt(-1000)
				return req
			},
			wantErr: true,
		},
	}
	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.reqFactory().Validate()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
