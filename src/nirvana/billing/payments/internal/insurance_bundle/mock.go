package insurance_bundle

import (
	"context"
	"database/sql"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	ibmodel "nirvanatech.com/nirvana/insurance-bundle/model"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

var (
	applicationIdNonFleet1 = uuid_utils.StableUUID("e2e-nf-application-1")
	applicationIdNonFleet2 = uuid_utils.StableUUID("e2e-nf-application-2")
	applicationIdNonFleet3 = uuid_utils.StableUUID("e2e-nf-application-3")
)

type MockClient struct {
	GetInMemoryInsuranceBundleFn func(ctx context.Context, applicationId uuid.UUID) (*model.InsuranceBundle, error)
}

func newMockedClient() *MockClient {
	return &MockClient{
		GetInMemoryInsuranceBundleFn: defaultGetInMemoryInsuranceBundleFn,
	}
}

func (m MockClient) GetInMemoryInsuranceBundle(ctx context.Context, applicationId uuid.UUID) (*model.InsuranceBundle, error) {
	return m.GetInMemoryInsuranceBundleFn(ctx, applicationId)
}

func defaultGetInMemoryInsuranceBundleFn(_ context.Context, applicationId uuid.UUID) (*model.InsuranceBundle, error) {
	switch applicationId {
	case applicationIdNonFleet1, applicationIdNonFleet2, applicationIdNonFleet3:
		return newNonFleetInsuranceBundle(applicationId), nil
	default:
		return nil, errors.Wrapf(sql.ErrNoRows, "no insurance bundle found for application %s", applicationId)
	}
}

func newNonFleetInsuranceBundle(appId uuid.UUID) *model.InsuranceBundle {
	alPolicyNumber := "NNFTK0012345-24"
	glPolicyNumber := "NNFGL0012345-24"
	mtcPolicyNumber := "NNMTC0012345-24"
	quoteFileHandleId := uuid_utils.StableUUID("e2e-nf-quote-file-handle-id")
	signaturePacketId := uuid.MustParse("03940108-ceeb-4773-89e6-897094e87905")

	programType := insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted
	effectiveDate := timestamppb.New(time_utils.NewDate(2024, time.April, 15).ToTime())
	expirationDate := timestamppb.New(time_utils.NewDate(2025, time.April, 15).ToTime())

	// TrailerInterchange maps to APD
	trailerInterchangeCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("100", effectiveDate).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_TrailerInterchange).
		Build()

	// PropertyDamage maps to AL
	propertyDamageCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("200", effectiveDate).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
		Build()

	// BodilyInjury maps to AL
	bodilyInjuryCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("300", effectiveDate).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
		Build()

	// GeneralLiability maps to GL
	glCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("400", effectiveDate).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
		Build()

	// ReeferWithoutHumanError maps to MTC
	reeferCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("500", effectiveDate).
		WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError).
		Build()

	defaultSurcharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails("750", effectiveDate).
		WithDefaultSurchargeType_TestOnly().
		Build()

	alSurplusTaxSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(alPolicyNumber).
		WithSurplusTaxSurchargeType().
		WithAmountBasedBillingDetails("200", effectiveDate).
		Build()

	alFeeChargeSurplusTaxSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(alPolicyNumber).
		WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
		WithAmountBasedBillingDetails("250", effectiveDate).
		Build()

	glSurplusTaxSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(glPolicyNumber).
		WithSurplusTaxSurchargeType().
		WithAmountBasedBillingDetails("300", effectiveDate).
		Build()

	mtcFeeChargeSurplusTaxSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(mtcPolicyNumber).
		WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
		WithAmountBasedBillingDetails("350", effectiveDate).
		Build()

	alStampingFeeSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(alPolicyNumber).
		WithStampingFeeSurchargeType().
		WithAmountBasedBillingDetails("50", effectiveDate).
		Build()

	alFeeChargeStampingFeeSurcharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(alPolicyNumber).
		WithStampingFeeFromFullyEarnedPremiumSurchargeType().
		WithAmountBasedBillingDetails("75", effectiveDate).
		Build()

	// Fee
	additionalInsuredCharge := ptypes.
		NewChargeBuilder().
		WithChargeablePolicy(mtcPolicyNumber).
		WithBaseChargeTypeWithBlanketRegularAdditionalInsured().
		WithAmountBasedBillingDetails("1000", effectiveDate).
		Build()

	rateBasedCharge := ptypes.
		NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithDefaultRateBasedBillingDetails_TestOnly().
		Build()

	alPolicy := ibmodel.NewPolicyBuilder(programType).
		WithPolicyNumber(alPolicyNumber).
		WithCoverages([]*ibmodel.Coverage{
			{Id: appenums.CoverageAutoLiability.String()},
			{Id: appenums.CoverageAutoPhysicalDamage.String()},
		}).
		WithEffectiveInterval(&proto.Interval{Start: effectiveDate, End: expirationDate}).
		WithCharges([]*ptypes.Charge{
			trailerInterchangeCharge,
			propertyDamageCharge,
			bodilyInjuryCharge,
			alSurplusTaxSurcharge,
			alFeeChargeSurplusTaxSurcharge,
			alStampingFeeSurcharge,
			alFeeChargeStampingFeeSurcharge,
		}).
		Build()

	glPolicy := ibmodel.NewPolicyBuilder(programType).
		WithPolicyNumber(glPolicyNumber).
		WithCoverages([]*ibmodel.Coverage{{Id: appenums.CoverageGeneralLiability.String()}}).
		WithEffectiveInterval(&proto.Interval{Start: effectiveDate, End: expirationDate}).
		WithCharges([]*ptypes.Charge{
			glCharge,
			defaultSurcharge,
			glSurplusTaxSurcharge,
		}).
		Build()

	mtcPolicy := ibmodel.NewPolicyBuilder(programType).
		WithPolicyNumber(mtcPolicyNumber).
		WithCoverages([]*ibmodel.Coverage{{Id: appenums.CoverageMotorTruckCargo.String()}}).
		WithEffectiveInterval(&proto.Interval{Start: effectiveDate, End: expirationDate}).
		WithCharges([]*ptypes.Charge{
			reeferCharge,
			additionalInsuredCharge,
			rateBasedCharge,
			mtcFeeChargeSurplusTaxSurcharge,
		}).
		Build()

	segment := ibmodel.NewInsuranceBundleSegmentBuilder(programType).
		WithPolicies(
			map[string]*ibmodel.Policy{
				alPolicy.GetPolicyNumber():  alPolicy,
				glPolicy.GetPolicyNumber():  glPolicy,
				mtcPolicy.GetPolicyNumber(): mtcPolicy,
			},
		).
		Build()

	return ibmodel.NewInsuranceBundleBuilder(programType).
		WithSegments([]*ibmodel.InsuranceBundleSegment{segment}).
		WithRootApplicationId(appId).
		WithCoreForm(&insurancecoreproto.FormCore{
			FormCompilationId:   signaturePacketId.String(),
			FormCompilationType: insurancecoreproto.FormCompilationType_FormCompilationTypeSignaturePacket,
			DocumentHandleId:    quoteFileHandleId.String(),
		}).
		Build()
}

var _ Client = &MockClient{}
