package charges

import (
	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	ibmodel "nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-core/coverage"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

// SurplusLines holds charges for non-admitted applications for certain states (e.g. TX in Non-Fleet).
type SurplusLines struct {
	Taxes          decimal.Decimal
	StampingFee    decimal.Decimal
	RecoupmentFees decimal.Decimal // e.g. NCRF (North Carolina Recoupment Fee)
}

func (s SurplusLines) Total() decimal.Decimal {
	// NCRF (North Carolina Recoupment Fee) is treated as a separate surcharge in our billing model
	// due to lack of third-party support. Thus Surplus Lines totals do not include them.
	return s.Taxes.Add(s.StampingFee).Sub(s.RecoupmentFees)
}

type IBChargesForBind struct {
	PremiumsByCoverage map[appenums.Coverage]decimal.Decimal
	Fees               decimal.Decimal
	Surcharges         decimal.Decimal
	SurplusLines       SurplusLines
}

// CalculateForBind calculates the premiums, fees & surcharges to be charged for an insurance bundle before bind by only
// considering the `AmountBasedBillingDetails` charges.
func CalculateForBind(ib *ibmodel.InsuranceBundle) (*IBChargesForBind, error) {
	if ib.ProgramType != insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted {
		return nil, errors.Newf("unsupported program type %s", ib.ProgramType)
	}

	var charges []*ptypes.Charge
	for _, s := range ib.GetSegments() {
		for _, p := range s.GetPolicies() {
			for _, charge := range p.Charges.GetCharges() {
				if charge.HasAmountBasedBillingDetails() {
					charges = append(charges, charge)
				}
			}
		}
	}

	premiumsByCoverage, err := getPremiumsByCoverage(charges, nil)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get premiums by coverage for IB %s", ib.ExternalId)
	}

	policyCharges, err := getPolicyCharges(charges, nil)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get policy charges for IB %s", ib.ExternalId)
	}

	surplusLines, err := getSurplusLines(charges, nil)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get surplus lines for IB %s", ib.ExternalId)
	}

	return &IBChargesForBind{
		PremiumsByCoverage: premiumsByCoverage,
		Fees:               policyCharges.Fees,
		Surcharges:         policyCharges.Surcharges,
		SurplusLines:       *surplusLines,
	}, nil
}

func getPremiumsByCoverage(
	charges []*ptypes.Charge,
	rateBasisValue *ptypes.RateBasisValue,
) (map[appenums.Coverage]decimal.Decimal, error) {
	result := make(map[appenums.Coverage]decimal.Decimal)
	for _, c := range charges {
		if !c.IsBaseCharge() || c.GetChargedSubCoverageGroup() == nil {
			continue
		}

		subCoverages := c.GetChargedSubCoverageGroup().GetGroup().GetSubCoverages()
		// TODO: this is assuming NF program, where groups are of size 1. We will need to change this when we support
		// other programs to make it more general-purpose.
		if len(subCoverages) != 1 {
			return nil, errors.Newf("expected exactly one sub-coverage in the group, got %+v", subCoverages)
		}
		subCoverage := subCoverages[0]

		cov, err := subCoverageToCoverage(subCoverage)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get coverage for sub coverage %s", subCoverage)
		}
		premium, err := c.Calculate(rateBasisValue)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to calculate charge %s for rate basis value %s", c, rateBasisValue)
		}
		result[*cov] = result[*cov].Add(premium)
	}
	return result, nil
}

type PolicyCharges struct {
	Fees       decimal.Decimal
	Surcharges decimal.Decimal
}

func getPolicyCharges(
	charges []*ptypes.Charge,
	rateBasisValue *ptypes.RateBasisValue,
) (*PolicyCharges, error) {
	result := PolicyCharges{}
	for _, charge := range charges {
		if !charge.IsFullyEarnedCharge() && !charge.IsSurcharge() {
			continue
		}

		amount, err := charge.Calculate(rateBasisValue)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to calculate charge %s for rate basis value %s", charge, rateBasisValue)
		}

		if charge.IsFullyEarnedCharge() {
			result.Fees = result.Fees.Add(amount)
		}
		// Exclude NCRF from surcharges as it's handled separately in surplus lines
		if charge.IsSurcharge() && !charge.IsNCRFSurcharge() {
			result.Surcharges = result.Surcharges.Add(amount)
		}
	}
	return &result, nil
}

func getSurplusLines(
	charges []*ptypes.Charge,
	rateBasisValue *ptypes.RateBasisValue,
) (*SurplusLines, error) {
	result := SurplusLines{}

	for _, c := range charges {
		isSurplusTax := c.IsSurplusTaxSurcharge() || c.IsSurplusTaxSurchargeFromFullyEarnedPremium()
		isStampingFee := c.IsStampingFeeSurcharge() || c.IsStampingFeeSurchargeFromFullyEarnedPremium()
		isNCRF := c.IsNCRFSurcharge()

		if !isSurplusTax && !isStampingFee && !isNCRF {
			continue
		}

		amount, err := c.Calculate(rateBasisValue)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to calculate charge %s for rate basis value %s", c, rateBasisValue)
		}

		if isSurplusTax {
			result.Taxes = result.Taxes.Add(amount)
		}
		if isStampingFee {
			result.StampingFee = result.StampingFee.Add(amount)
		}
		if isNCRF {
			result.RecoupmentFees = result.RecoupmentFees.Add(amount)
		}
	}

	return &result, nil
}

func subCoverageToCoverage(subCov ptypes.SubCoverageType) (*appenums.Coverage, error) {
	appCov, err := coverage.GetAppCoverageFromPricingSubCoverage(subCov)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get app coverage from pricing sub coverage %s", subCov)
	}

	primaryCov, err := appenums.GetPrimaryCoverageFromCoverage(*appCov)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to translate app coverage %s to primary coverage", appCov)
	}

	return primaryCov, nil
}
