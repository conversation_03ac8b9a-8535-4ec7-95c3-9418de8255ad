package charges_test

import (
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/billing/payments/internal/charges"
	"nirvanatech.com/nirvana/common-go/time_utils"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	ibmodel "nirvanatech.com/nirvana/insurance-bundle/model"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

// buildExpectedIBCharges constructs the expected IBChargesForBind for a given
// recoupment fee amount. It centralises the constants so that test cases are
// easier to read and maintain.
func buildExpectedIBCharges(recoupmentFees float64) *charges.IBChargesForBind {
	const (
		wantApdPremium = 100       // trailerInterchangeCharge
		wantAlPremium  = 200 + 300 // propertyDamageCharge + bodilyInjuryCharge
		wantGlPremium  = 400       // glCharge
		wantMtcPremium = 500       // reeferCharge

		wantSurplusLinesTaxes       = 200 + 250 + 300 + 350 // sum of surplus lines taxes
		wantSurplusLinesStampingFee = 50 + 75               // sum of surplus lines stamping fees

		wantFees = 1_000 // additionalInsuredCharge

		wantSurcharges = 750 + wantSurplusLinesTaxes + wantSurplusLinesStampingFee // defaultSurcharge + taxes + stamping fee
	)

	return &charges.IBChargesForBind{
		PremiumsByCoverage: map[appenums.Coverage]decimal.Decimal{
			appenums.CoverageAutoPhysicalDamage: decimal.NewFromInt(wantApdPremium),
			appenums.CoverageAutoLiability:      decimal.NewFromInt(wantAlPremium),
			appenums.CoverageGeneralLiability:   decimal.NewFromInt(wantGlPremium),
			appenums.CoverageMotorTruckCargo:    decimal.NewFromInt(wantMtcPremium),
		},
		Fees:       decimal.NewFromInt(wantFees),
		Surcharges: decimal.NewFromInt(wantSurcharges),
		SurplusLines: charges.SurplusLines{
			Taxes:          decimal.NewFromFloat(wantSurplusLinesTaxes),
			StampingFee:    decimal.NewFromFloat(wantSurplusLinesStampingFee),
			RecoupmentFees: decimal.NewFromFloat(recoupmentFees),
		},
	}
}

func TestCalculateForBind(t *testing.T) {
	tests := []struct {
		name string
		want *charges.IBChargesForBind
	}{
		{
			name: "non-North Carolina Policy",
			want: buildExpectedIBCharges(0),
		},
		{
			name: "North Carolina Policy",
			want: buildExpectedIBCharges(100),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			alPolicyNumber := "NNFTK0012345-24"
			glPolicyNumber := "NNFGL0012345-24"
			mtcPolicyNumber := "NNMTC0012345-24"

			programType := insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted
			effectiveDate := timestamppb.New(time_utils.NewDate(2024, time.April, 15).ToTime())

			// TrailerInterchange maps to APD
			trailerInterchangeCharge := ptypes.
				NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("100", effectiveDate).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_TrailerInterchange).
				Build()

			// PropertyDamage maps to AL
			propertyDamageCharge := ptypes.
				NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("200", effectiveDate).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
				Build()

			// BodilyInjury maps to AL
			bodilyInjuryCharge := ptypes.
				NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("300", effectiveDate).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
				Build()

			// GeneralLiability maps to GL
			glCharge := ptypes.
				NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("400", effectiveDate).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
				Build()

			// ReeferWithoutHumanError maps to MTC
			reeferCharge := ptypes.
				NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("500", effectiveDate).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError).
				Build()

			defaultSurcharge := ptypes.
				NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("750", effectiveDate).
				WithDefaultSurchargeType_TestOnly().
				Build()

			alSurplusTaxSurcharge := ptypes.
				NewChargeBuilder().
				WithChargeablePolicy(alPolicyNumber).
				WithSurplusTaxSurchargeType().
				WithAmountBasedBillingDetails("200", effectiveDate).
				Build()

			alFeeChargeSurplusTaxSurcharge := ptypes.
				NewChargeBuilder().
				WithChargeablePolicy(alPolicyNumber).
				WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
				WithAmountBasedBillingDetails("250", effectiveDate).
				Build()

			glSurplusTaxSurcharge := ptypes.
				NewChargeBuilder().
				WithChargeablePolicy(glPolicyNumber).
				WithSurplusTaxSurchargeType().
				WithAmountBasedBillingDetails("300", effectiveDate).
				Build()

			mtcFeeChargeSurplusTaxSurcharge := ptypes.
				NewChargeBuilder().
				WithChargeablePolicy(mtcPolicyNumber).
				WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
				WithAmountBasedBillingDetails("350", effectiveDate).
				Build()

			alStampingFeeSurcharge := ptypes.
				NewChargeBuilder().
				WithChargeablePolicy(alPolicyNumber).
				WithStampingFeeSurchargeType().
				WithAmountBasedBillingDetails("50", effectiveDate).
				Build()

			alFeeChargeStampingFeeSurcharge := ptypes.
				NewChargeBuilder().
				WithChargeablePolicy(alPolicyNumber).
				WithStampingFeeFromFullyEarnedPremiumSurchargeType().
				WithAmountBasedBillingDetails("75", effectiveDate).
				Build()

			// NCRF Charge - only for North Carolina Policy test case
			var ncrfCharge *ptypes.Charge
			if !tt.want.SurplusLines.RecoupmentFees.IsZero() {
				ncrfCharge = ptypes.
					NewChargeBuilder().
					WithChargeablePolicy(alPolicyNumber).
					WithNCRFSurchargeType().
					WithDefaultAmountBasedBillingDetails_TestOnly().
					Build()
			}

			// Fee
			additionalInsuredCharge := ptypes.
				NewChargeBuilder().
				WithChargeablePolicy(mtcPolicyNumber).
				WithBaseChargeTypeWithBlanketRegularAdditionalInsured().
				WithAmountBasedBillingDetails("1000", effectiveDate).
				Build()

			// RateBased are ignored by the CalculateForBind method.
			rateBasedCharge := ptypes.
				NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithDefaultRateBasedBillingDetails_TestOnly().
				Build()

			alCharges := []*ptypes.Charge{
				trailerInterchangeCharge,
				propertyDamageCharge,
				bodilyInjuryCharge,
				alSurplusTaxSurcharge,
				alFeeChargeSurplusTaxSurcharge,
				alStampingFeeSurcharge,
				alFeeChargeStampingFeeSurcharge,
			}
			if ncrfCharge != nil {
				alCharges = append(alCharges, ncrfCharge)
			}

			alPolicy := ibmodel.NewPolicyBuilder(programType).
				WithPolicyNumber(alPolicyNumber).
				WithCharges(alCharges).
				Build()

			glPolicy := ibmodel.NewPolicyBuilder(programType).
				WithPolicyNumber(glPolicyNumber).
				WithCharges([]*ptypes.Charge{
					glCharge,
					defaultSurcharge,
					glSurplusTaxSurcharge,
				}).
				Build()

			mtcPolicy := ibmodel.NewPolicyBuilder(programType).
				WithPolicyNumber(mtcPolicyNumber).
				WithCharges([]*ptypes.Charge{
					reeferCharge,
					additionalInsuredCharge,
					rateBasedCharge,
					mtcFeeChargeSurplusTaxSurcharge,
				}).
				Build()

			segment := ibmodel.NewInsuranceBundleSegmentBuilder(programType).
				WithPolicies(
					map[string]*ibmodel.Policy{
						alPolicy.GetPolicyNumber():  alPolicy,
						glPolicy.GetPolicyNumber():  glPolicy,
						mtcPolicy.GetPolicyNumber(): mtcPolicy,
					},
				).
				Build()

			ib := ibmodel.NewInsuranceBundleBuilder(programType).
				WithSegments([]*ibmodel.InsuranceBundleSegment{segment}).
				Build()

			got, err := charges.CalculateForBind(ib)
			require.NoError(t, err)

			mainCoverages := []appenums.Coverage{
				appenums.CoverageAutoPhysicalDamage,
				appenums.CoverageAutoLiability,
				appenums.CoverageGeneralLiability,
				appenums.CoverageMotorTruckCargo,
			}
			for _, cov := range mainCoverages {
				assert.Equalf(t, tt.want.PremiumsByCoverage[cov].String(), got.PremiumsByCoverage[cov].String(), "for %s", cov)
			}
			assert.Equal(t, tt.want.Fees.String(), got.Fees.String())
			assert.Equal(t, tt.want.Surcharges.String(), got.Surcharges.String())
			assert.Equal(t, tt.want.SurplusLines.Taxes.String(), got.SurplusLines.Taxes.String())
			assert.Equal(t, tt.want.SurplusLines.StampingFee.String(), got.SurplusLines.StampingFee.String())
			assert.Equal(t, tt.want.SurplusLines.RecoupmentFees.String(), got.SurplusLines.RecoupmentFees.String())
			assert.Equal(t, tt.want.SurplusLines.Taxes.Add(tt.want.SurplusLines.StampingFee).
				Sub(tt.want.SurplusLines.RecoupmentFees).String(), got.SurplusLines.Total().String())
		})
	}
}
