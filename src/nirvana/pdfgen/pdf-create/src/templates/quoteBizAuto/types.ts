export interface CustomerInfo {
  name: string;
  agency: string;
  producer: string;
  dotNumber: string;
  feinNumber: string;
  policyNumber: string;
  policyPeriod: {
    startDate: string;
    endDate: string;
  };
}

export interface PremiumDetails {
  totalInsValue: number;
  totalUnits: number;
  premiumSubTotal: number;
  assessments: number;
  totalPremium: number;
}

export interface Coverage {
  symbol: string;
  displayName: string;
  limit: number | string;
  limitMeta?: string;
  premium: number | string;
}

export interface Vehicle {
  year: number;
  make: string;
  model: string;
  vin: string;
  'coll/comp': number | null;
  deductible: number | null;
  radius: string;
  liabilityPremium: number | string | null;
  umPremium: number | string | null;
  uimPremium: number | string | null;
  'medPay/PipPremium': number | string | null;
  physDamagePremium: number | string | null;
  unitSubTotal: number;
}

export interface Form {
  code: string;
  name: string;
}

export interface QuoteBizAutoData {
  timestamp?: string;
  customerInfo: CustomerInfo;
  premiumDetails: PremiumDetails;
  coverages: Coverage[];
  vehicles: Vehicle[];
  forms: Form[];
}

// Component Props
export interface BaseProps {
  data: QuoteBizAutoData;
}

export interface CoverageTableProps {
  coverages: Coverage[];
  pricingInfo: PremiumDetails;
}

export interface VehicleTableProps {
  vehicles: Vehicle[];
}

export interface FormsTableProps {
  forms: Form[];
}

export interface PolicyFeesProps extends BaseProps {}

// DESIGN SYSTEM
export const THEME = {
  colors: {
    primary: '#1E3A8A',
    navy: '#013565',
    text: '#040C21',
    textSecondary: '#6B7280',
    textLight: '#8B8B8B',
    border: '#E5E7EB',
    background: '#F9FAFB',
    white: '#FFFFFF',
    warning: '#fbbf24',
    lightBlue: '#EFF3FC',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
  },
  fontSize: {
    xs: 8,
    sm: 9,
    base: 10,
    md: 11,
    lg: 12,
    xl: 14,
    xxl: 16,
    title: 20,
    hero: 24,
  },
  fontWeight: {
    normal: 'normal' as const,
    medium: 'medium' as const,
    bold: 'bold' as const,
    semibold: 'semibold' as const,
  },
} as const;
