package constants

import (
	"encoding/json"
	"fmt"
	"strings"

	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/us_states"
)

// TODO: Update this later to the right carrier
// DefaultInsuranceCarrier represents our existing default carrier.
var DefaultInsuranceCarrier = InsuranceCarrierFalseLake

type InsuranceCarrier int

const (
	InsuranceCarrierFalseLake InsuranceCarrier = iota
	InsuranceCarrierEmpty
	InsuranceCarrierSiriusPoint
	InsuranceCarrierFronterX
	InsuranceCarrierMSTransverse
	// InsuranceCarrierTransverse is the old name for MSTransverse and to be used only on PRINTS for states that haven't changed yet
	InsuranceCarrierTransverse
	// InsuranceCarrierMSTSpeciality is the to be used only on PRINTS for some US states where policy is fronted by MST (e.g. CA)
	InsuranceCarrierMSTSpeciality
	// InsuranceCarrierSiriusPointSpeciality is the to be used for non admitted states (e.g. non-fleet TX)
	InsuranceCarrierSiriusPointSpeciality
)

func GetInsuranceCarrier(carrier string) InsuranceCarrier {
	carrier = strings.NewReplacer(" ", "").Replace(carrier)
	carrier = strings.ToLower(carrier)
	switch carrier {
	case "fallslakenationalinsurancecompany":
		return InsuranceCarrierFalseLake
	case "siriuspointamericainsurancecompany":
		return InsuranceCarrierSiriusPoint
	case "fronterxinsurancecompany":
		return InsuranceCarrierFronterX
	case "mstransverseinsurancecompany":
		return InsuranceCarrierMSTransverse
	case "transverseinsurancecompany":
		return InsuranceCarrierTransverse
	case "mstransversespecialtyinsurancecompany":
		return InsuranceCarrierMSTSpeciality
	case "siriuspointspecialtyinsurancecorporation":
		return InsuranceCarrierSiriusPointSpeciality
	default:
		return InsuranceCarrierEmpty
	}
}

// EnumString returns the string representation of the enum
func (i InsuranceCarrier) EnumString() string {
	switch i {
	case InsuranceCarrierFalseLake:
		return "InsuranceCarrierFalseLake"
	case InsuranceCarrierSiriusPoint:
		return "InsuranceCarrierSiriusPoint"
	case InsuranceCarrierFronterX:
		return "InsuranceCarrierFronterX"
	case InsuranceCarrierEmpty:
		return ""
	case InsuranceCarrierMSTransverse:
		return "InsuranceCarrierMSTransverse"
	case InsuranceCarrierTransverse:
		return "InsuranceCarrierTransverse"
	case InsuranceCarrierMSTSpeciality:
		return "InsuranceCarrierMSTSpeciality"
	case InsuranceCarrierSiriusPointSpeciality:
		return "InsuranceCarrierSiriusPointSpeciality"
	default:
		return ""
	}
}

func (i InsuranceCarrier) String() string {
	switch i {
	case InsuranceCarrierFalseLake:
		return "Falls Lake National Insurance Company"
	case InsuranceCarrierSiriusPoint:
		return "SiriusPoint America Insurance Company"
	case InsuranceCarrierSiriusPointSpeciality:
		return "SiriusPoint Specialty Insurance Corporation"
	case InsuranceCarrierFronterX:
		return "FronterX Insurance Company"
	case InsuranceCarrierEmpty:
		return " "
	case InsuranceCarrierMSTransverse:
		return "MS Transverse Insurance Company"
	case InsuranceCarrierTransverse:
		return "Transverse Insurance Company"
	case InsuranceCarrierMSTSpeciality:
		return "MS Transverse Specialty Insurance Company"
	default:
		return ""
	}
}

// MarshalJSON implements the json.Marshaler interface for InsuranceCarrier
func (i InsuranceCarrier) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for InsuranceCarrier
func (i *InsuranceCarrier) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("InsuranceCarrier should be a string, got %s", data)
	}

	*i = GetInsuranceCarrier(s)

	if *i == InsuranceCarrierEmpty {
		return errors.Errorf("insurance carrier should be a valid carrier, got %s", s)
	}
	return nil
}

type InsuranceProducer int

const (
	InsuranceProducerNirvana InsuranceProducer = iota
)

func (i InsuranceProducer) String() string {
	switch i {
	case InsuranceProducerNirvana:
		return "Nirvana Insurance Services LLC"
	default:
		return ""
	}
}

func (i InsuranceProducer) AgentPhoneNumber(programType policyenums.ProgramType) (string, error) {
	if i != InsuranceProducerNirvana {
		return "", errors.New("invalid insurance producer")
	}

	// nolint:exhaustive
	switch programType {
	case policyenums.ProgramTypeNonFleetAdmitted:
		return "************", nil
	case policyenums.ProgramTypeFleet:
		return "************", nil
	case policyenums.ProgramTypeBusinessAuto:
		return "************", nil
	default:
		return "", errors.New("invalid program type")
	}
}

func (i InsuranceProducer) AgentMailingAddress() string {
	switch i {
	case InsuranceProducerNirvana:
		return "100 East Campus View Blvd Crosswoods, Suite 250 Columbus, OH 43235"
	default:
		return ""
	}
}

const (
	// TODO: refactor the limits into coverages
	OccurrenceLimit                             = 1000000
	RentedToYouLimit                            = 100000
	MedicalExpenseLimit                         = 5000
	PersonalAdvertisingInjuryLimit              = 1000000
	GeneralAggregateLimit                       = 2000000
	CompletedOperationsAggregateLimit           = 1000000
	RetroactiveDate                             = "NONE"
	BusinessDescription                         = "FOR HIRE TRUCKING"
	BusinessDescriptionGL                       = "Truckers GL"
	BusinessDescriptionMTC                      = "Trucking for Hire"
	IncludedPremium                             = "INCLUDED"
	SingleLimitBIAndPD                          = 1000000
	BodilyInjuryAndPropertyDamage               = 65000
	LimitBodilyInjuryByAccident                 = 100000
	LimitBodilyInjuryByDiseaseAggregate         = 500000
	LimitBodilyInjuryByDiseaseEachEmployee      = 100000
	DescriptionOfOperations                     = "Trucking for Hire"
	AdditionalInsuredEntityName                 = "Blanket applies where required by written contract"
	Classification                              = "TRUCKERS"
	Exposure                                    = "Per unit"
	CodeNumber                                  = 99793
	PDComprehensive                             = "Physical Damage Comprehensive"
	PDCollision                                 = "Physical Damage Collision"
	GLPremiumBase                               = "Power Unit Count"
	TerrorismCoverage                           = "TERRORISM"
	LossPayeeSchedule                           = "Refer to Schedule of Loss Payee(s)"
	CoveredAutoNumberAdmitted                   = "Refer to CAP DS 02"
	ReferToVehicleSchedule                      = "Refer to schedule on file"
	PropertyAtTerminalsDays                     = 3
	NAICCodeFLI                                 = "31925"
	NAICCodeMST                                 = "21075"
	OccurrenceLimitSingleAuto                   = 250000
	EarnedChargesLimit                          = 2500
	MCS90PhoneNumber                            = "************"
	MCS90PhoneNumberMST                         = "************"
	SFPStates                                   = "CA, ME, MO, OR, WI"
	DesignationOrDescriptionOfCoveredAutosShort = "See vehicle schedule"
	PAAutoDecWording                            = "THIS POLICY COVERS COLLISION DAMAGE TO RENTAL VEHICLES SUBJECT TO THE POLICY TERMS AND CONDITIONS FOR HIRED AUTO PHYSICAL DAMAGE COVERAGE"
	MSTContactNumber                            = "************"
	DebrisRemoval                               = 10000
	FalsePretense                               = 25000
	RewardsArrestConviction                     = 10000
	RewardsReturnOfProperty                     = 10000
	CargoHandlingEquipment                      = 10000
	ContractualPenalties                        = 5000
	FireDeptServiceCharges                      = 10000
	FireExtinguishingSystemExpense              = 10000
	FuelCharges                                 = 2500
	NewlyAcquiredTerminals                      = 100000
	PollutantCleanUpandRemovalLimit             = 10000
	PreservationOfProperty                      = 10000
	ElectronicEquipment                         = 10000
	CargoLossMitigationExpenses                 = 10000
	RemovalExpenses                             = 10000
	ReloadExpenses                              = 2500
	TrafficAndSecurityExpense                   = 2500
	LineOfBuisness                              = "Not Applicable"
	SpecialProvisions                           = "MOTOR TRUCK CARGO PREMIUMS AND EXPOSURES WILL BE AUDITED ON AN ANNUAL BASIS."
	LineOfBusinessAL                            = "Auto"
	LineOfBusinessGL                            = "General Liability"
	LineOfBusinessMTC                           = "Inland Marine"
	TXSLTaxRate                                 = "4.85%"
	CompanyWebAddress                           = "https://www.siriuspt.com/"
	ReferToTerminalSchedule                     = "Refer to Terminal Schedule"
	BlanketApplies                              = "Blanket applies where required by written contract"
	CameraProviderMotive                        = "Motive"
	CameraProviderSamsara                       = "Samsara"
	NoCoverage                                  = "No Coverage"
	CheckboxChecked                             = "Yes"
	CheckboxUnchecked                           = "Off"
)

//go:generate go run github.com/dmarkham/enumer -type=FilingType -json -trimprefix=FilingType
type FilingType int

const (
	FilingTypeAdmitted FilingType = iota + 1
	FilingTypeNonAdmitted
)

// nolint:exhaustive
var ProgramTypeToUSStateInsuranceCarrier = map[policyenums.ProgramType]map[us_states.USState]InsuranceCarrier{
	policyenums.ProgramTypeFleet: {
		us_states.AL: InsuranceCarrierMSTransverse,
		us_states.AZ: InsuranceCarrierMSTransverse,
		us_states.CA: InsuranceCarrierMSTSpeciality,
		us_states.CO: InsuranceCarrierMSTransverse,
		us_states.GA: InsuranceCarrierMSTransverse,
		us_states.IA: InsuranceCarrierMSTransverse,
		us_states.IL: InsuranceCarrierMSTransverse,
		us_states.IN: InsuranceCarrierMSTransverse,
		us_states.KS: InsuranceCarrierMSTransverse,
		us_states.KY: InsuranceCarrierMSTransverse,
		us_states.MI: InsuranceCarrierMSTransverse,
		us_states.MN: InsuranceCarrierMSTransverse,
		us_states.MO: InsuranceCarrierMSTransverse,
		us_states.NC: InsuranceCarrierMSTransverse,
		us_states.NE: InsuranceCarrierMSTransverse,
		us_states.NM: InsuranceCarrierMSTransverse,
		us_states.NV: InsuranceCarrierMSTransverse,
		us_states.OH: InsuranceCarrierMSTransverse,
		us_states.OK: InsuranceCarrierMSTransverse,
		us_states.OR: InsuranceCarrierMSTransverse,
		us_states.PA: InsuranceCarrierMSTransverse,
		us_states.SC: InsuranceCarrierMSTransverse,
		us_states.TN: InsuranceCarrierMSTransverse,
		us_states.TX: InsuranceCarrierMSTransverse,
		us_states.UT: InsuranceCarrierMSTransverse,
		us_states.WA: InsuranceCarrierMSTransverse,
		us_states.WI: InsuranceCarrierMSTransverse,
	},
	policyenums.ProgramTypeNonFleetAdmitted: {
		us_states.TX: InsuranceCarrierSiriusPointSpeciality,
		us_states.OH: InsuranceCarrierSiriusPoint,
		us_states.IL: InsuranceCarrierSiriusPoint,
		us_states.MN: InsuranceCarrierSiriusPoint,
		us_states.MI: InsuranceCarrierSiriusPoint,
		us_states.IN: InsuranceCarrierSiriusPoint,
		us_states.MO: InsuranceCarrierSiriusPoint,
		us_states.TN: InsuranceCarrierSiriusPoint,
		us_states.GA: InsuranceCarrierSiriusPoint,
		us_states.NC: InsuranceCarrierSiriusPoint,
		us_states.SC: InsuranceCarrierSiriusPoint,
		us_states.PA: InsuranceCarrierSiriusPoint,
	},
}

func GetCarrier(state us_states.USState, programType policyenums.ProgramType) InsuranceCarrier {
	if carrier, ok := ProgramTypeToUSStateInsuranceCarrier[programType][state]; ok {
		return carrier
	}

	if programType == policyenums.ProgramTypeNonFleetAdmitted {
		return InsuranceCarrierSiriusPoint
	}

	return InsuranceCarrierMSTransverse
}

func GetFilingType(state us_states.USState, programType policyenums.ProgramType) FilingType {
	if isNonAdmittedCarrier(state, programType) {
		return FilingTypeNonAdmitted
	}

	return FilingTypeAdmitted
}

func isNonAdmittedCarrier(state us_states.USState, programType policyenums.ProgramType) bool {
	carrier := GetCarrier(state, programType)

	return carrier == InsuranceCarrierSiriusPointSpeciality || carrier == InsuranceCarrierMSTSpeciality
}
