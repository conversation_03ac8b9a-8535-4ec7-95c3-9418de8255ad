// This rater is used to calculate the premium for the coverages UMBI and UMPD
// as a unit. In some states this is required (e.g. IN).
entity UMRater {
    vehicle Vehicle
    company Company
    coverage Coverage
}

entity UMRater includes BaseLiabRater

property UMRater umLimitFactor number.decimal {
    lookup([UMLimitFactor], [UMLimitFactor.factor],
        coverage->umLimit
    )
}


property UMRater umBasePremium number.decimal {
    liabMaxPremium / vehicle->liabLimitFactor
}

output UMRater vehicleUMPremium number.decimal {
    switch(vehicle->hasUMCoverage) {
        case True:
            round(
                umBasePremium
                * umLimitFactor
                * vehicle->umClassFactor
                * company->iniFactor
            )
        case False:
            0.0
    }
}
