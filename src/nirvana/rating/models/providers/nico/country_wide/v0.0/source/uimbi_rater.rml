entity UIMBIRater {
    vehicle Vehicle
    company Company
    coverage Coverage
}

entity UIMBIRater includes BaseLiabRater

property UIMBIRater uimbiLimitFactor number.decimal {
    lookup([UIMBILimitFactor], [UIMBILimitFactor.factor],
        coverage->uimbiLimit
    )
}


property UIMBIRater uimbiBasePremium number.decimal {
    liabMaxPremium / vehicle->liabLimitFactor
}

// Note: This property MUST be overridden by each state implementation.
// The @require annotation below serves as a failsafe to ensure that any state
// attempting to use this rater without providing their own stateSpecificFactor
// implementation will fail during validation. This is intentional - each state
// must provide its own factor calculation based on state-specific requirements.
// Common implementations:
// - Most states use vehicle->uimbiClassFactor
// - PA uses vehicle->uimbiTerritoryClassFactor
// The default value of 1.0 is purposely set to trigger the @require validation.
@require(stateSpecificFactor != 1.0)
property UIMBIRater stateSpecificFactor number.decimal {
    1.0
}

output UIMBIRater vehicleUIMBIPremium number.decimal {
    switch(coverage->hasUIMBICoverage) {
        case True:
            round(
                uimbiBasePremium
                * uimbiLimitFactor
                * stateSpecificFactor
                * company->iniFactor
            )
        case False:
            0.0
    }
}
