// This rater is used to calculate the premium for the coverages UMBI, UMPD, UIMBI and UIMPD
// as a unit. In some states this is required (e.g. TX).
entity UMUIMRater {
    vehicle Vehicle
    company Company
    coverage Coverage
}

entity UMUIMRater includes BaseLiabRater

property UMUIMRater umuimLimitFactor number.decimal {
    lookup([UMUIMLimitFactor], [UMUIMLimitFactor.factor],
        coverage->umuimLimit
    )
}


property UMUIMRater umuimBasePremium number.decimal {
    liabMaxPremium / vehicle->liabLimitFactor
}

// Note: This property MUST be overridden by each state implementation.
// The @require annotation below serves as a failsafe to ensure that any state
// attempting to use this rater without providing their own stateSpecificFactor
// implementation will fail during validation. This is intentional, each state
// must provide its own territory factor calculation based on state-specific
// requirements (e.g. TX uses vehicle->umuimTerritoryClassFactor).
// The default value of 1.0 is purposely set to trigger the @require validation.
@require(stateSpecificFactor != 1.0)
property UMUIMRater stateSpecificFactor number.decimal {
    1.0
}

output UMUIMRater vehicleUMUIMPremium number.decimal {
    switch(coverage->hasUMUIMCoverage) {
        case True:
            round(
                umuimBasePremium
                * umuimLimitFactor
                * stateSpecificFactor
                * company->iniFactor
            )
        case False:
            0.0
    }
}
