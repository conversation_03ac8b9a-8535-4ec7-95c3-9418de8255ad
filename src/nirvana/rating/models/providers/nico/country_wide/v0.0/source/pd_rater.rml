entity PDRater {
    vehicle Vehicle
    coverage Coverage
}

property PDRater pdBaseRate number.decimal {
    lookup([PDBaseRate], [PDBaseRate.baseRate],
        vehicle->pdIndustryType,
        vehicle->statedValue,
        vehicle->pdDeductible
    )
}

property PDRater pdBasePremium number.decimal {
    pdBaseRate * vehicle->statedValue
}

property PDRater pdMinPremium number.decimal {
    lookup([PDMinPremium], [PDMinPremium.minPremium],
        vehicle->pdIndustryType,
        vehicle->size,
        vehicle->pdDeductible
    )
}

property PDRater pdBasePremiumAdjusted number.decimal {
    number_max(pdBasePremium, pdMinPremium)
}

property PDRater pdClassFactor number.decimal {
    lookup([PDClassFactor], [PDClassFactor.factor],
        vehicle->specialtyType
    )
}

property PDRater pdRadiusFactor number.decimal {
    lookup([PDRadiusFactor], [PDRadiusFactor.factor],
        vehicle->radius
    )
}

property PDRater pdTerritoryFactor number.decimal {
    lookup([PDTerritoryFactor], [PDTerritoryFactor.factor],
        vehicle->territory
    )
}

property PDRater pdVehicleAgeFactor number.decimal {
    lookup([PDVehicleAgeFactor], [PDVehicleAgeFactor.factor],
        vehicle->type,
        vehicle->age
    )
}

property PDRater pdTrailerFactor number.decimal {
    lookup([PDTrailerFactor], [PDTrailerFactor.factor],
        vehicle->type
    )
}

property PDRater pdGlassLinedFactor number.decimal {
    lookup([PDGlassLinedFactor], [PDGlassLinedFactor.factor],
        vehicle->isGlassLined
    )
}

property PDRater pdRefrigeratedFactor number.decimal {
    lookup([PDRefrigeratedTruckFactor], [PDRefrigeratedTruckFactor.factor],
        vehicle->isRefrigerated
    )
}

property PDRater pdDoubleTrailerFactor number.decimal {
    lookup([PDDoubleTrailerFactor], [PDDoubleTrailerFactor.factor],
        vehicle->isDoubleTrailer 
    )
}

property PDRater pdComprehensiveFactor number.decimal {
    lookup([PDComprehensiveFactor], [PDComprehensiveFactor.factor],
        vehicle->industryType
    )
}

property PDRater vehiclePDPremium number.decimal {
    round(
        pdBasePremiumAdjusted
        * pdClassFactor
        * pdRadiusFactor
        * pdTerritoryFactor
        * pdVehicleAgeFactor
        * pdTrailerFactor
        * pdGlassLinedFactor
        * pdRefrigeratedFactor
        * pdDoubleTrailerFactor
        * pdComprehensiveFactor
        * vehicle->premiumCreditFactor
        * coverage->pdLossFreeMod
        * coverage->pdExperienceRatingMod
        * coverage->pdScheduleMod
    )
}

output PDRater vehicleCompCollPremium number.decimal {
    switch(vehicle->hasCompCoverage && vehicle->hasCollCoverage) {
        case True:
            vehiclePDPremium
        case False:
            0.0
    }
}

output PDRater vehicleCompOnlyPremium number.decimal {
    switch(vehicle->hasCompCoverage && !vehicle->hasCollCoverage) {
        case True:
            vehiclePDPremium * 0.5
        case False:
            0.0
    }
}
