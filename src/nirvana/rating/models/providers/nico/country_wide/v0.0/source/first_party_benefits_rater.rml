entity FPBRater {
    vehicle Vehicle
    coverage Coverage
}

entity FPBRater includes BaseLiabRater
entity FPBRater includes BaseFilingFactorRater

property FPBRater medicalExpenseBenefitsLimitFactor number.decimal {
    lookup([MedicalExpenseBenefitsLimit], [MedicalExpenseBenefitsLimit.factor],
        coverage->medicalExpenseBenefitsLimit
    )
}

property FPBRater workLossBenefits number.integer {
    lookup([WorkLossBenefits], [WorkLossBenefits.premium],
        coverage->workLossBenefitsLimit
    )
}

property FPBRater funeralExpenseBenefits number.integer {
    lookup([FuneralExpenseBenefits], [FuneralExpenseBenefits.premium],
        coverage->funeralExpenseBenefitsLimit
    )
}

property FPBRater accidentalDeathBenefits number.integer {
    lookup([AccidentalDeathBenefits], [AccidentalDeathBenefits.premium],
        coverage->accidentalDeathBenefitsLimit
    )
}

property FPBRater extraordinaryMedicalBenefits number.decimal {
    lookup([ExtraordinaryMedicalBenefits], [ExtraordinaryMedicalBenefits.factor],
        coverage->extraordinaryMedicalBenefitsLimit
    )
}

property FPBRater fpbBasePremium number.decimal {
    liabMaxPremium / vehicle->liabLimitFactor
}

output FPBRater vehicleMedicalExpenseBenefitsPremium number.decimal {
    switch(coverage->hasMedicalExpenseBenefitsCoverage) {
        case True:
            round(
                fpbBasePremium
                * medicalExpenseBenefitsLimitFactor
                * vehicle->fpbClassFactor
                * vehicle->premiumCreditFactor
            )
        case False:
            0.0
    }
}

output FPBRater vehicleWorkLossBenefitsPremium number.decimal {
    switch(coverage->hasWorkLossBenefitsCoverage) {
        case True:
            round(
                number.decimal{workLossBenefits}
            )
        case False:
            0.0
    }
}

output FPBRater vehicleFuneralExpenseBenefitsPremium number.decimal {
    switch(coverage->hasFuneralExpenseBenefitsCoverage) {
        case True:
            round(
                number.decimal{funeralExpenseBenefits}
            )
        case False:
            0.0
    }
}

output FPBRater vehicleAccidentalDeathBenefitsPremium number.decimal {
    switch(coverage->hasAccidentalDeathBenefitsCoverage) {
        case True:
            round(
                number.decimal{accidentalDeathBenefits}
            )
        case False:
            0.0
    }
}

output FPBRater vehicleExtraordinaryMedicalBenefitsPremium number.decimal {
    switch(coverage->hasExtraordinaryMedicalBenefitsCoverage) {
        case True:
            round(
                fpbBasePremium
                * extraordinaryMedicalBenefits
            )
        case False:
            0.0
    }
}
