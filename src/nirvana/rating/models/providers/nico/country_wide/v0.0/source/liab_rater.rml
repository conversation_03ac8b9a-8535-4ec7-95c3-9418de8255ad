entity LiabRater {
    vehicle Vehicle
    company Company
    coverage Coverage
}

entity LiabRater includes BaseFilingFactorRater

property LiabRater basePremium number.decimal {
    lookup([LiabBasePremium], [LiabBasePremium.basePremium],
        vehicle->businessUse,
        vehicle->industryType,
        vehicle->radius,
        vehicle->territory,
        vehicle->useSize
    )
}

property LiabRater retailUseFactor number.decimal {
    switch(vehicle->businessUse) {
        case "Retail":
            lookup([RetailUseFactor], [RetailUseFactor.factor],
                vehicle->useSize,
                vehicle->radius
            )
        case _:
            1.00
    }
}

property LiabRater classFactor number.decimal {
    lookup([LiabClassFactor], [LiabClassFactor.factor],
        vehicle->specialtyType
    )
}

property LiabRater fleetFactor number.decimal {
    lookup([FleetFactor], [FleetFactor.factor],
        company->numberOfPowerUnits
    )
}

property LiabRater trailerFactor number.decimal {
    lookup([TrailerFactor], [TrailerFactor.factor],
        vehicle->type
    )
}

output LiabRater vehicleLiabPremium number.decimal {
    switch(coverage->hasLiabCoverage) {
        case True:
            round(
                basePremium
                * retailUseFactor
                * classFactor
                * fleetFactor
                * vehicle->liabLimitFactor
                * trailerFactor
                * vehicle->premiumCreditFactor
                * filingFactor
                * coverage->driverClassMod
                * coverage->liabLossFreeMod
                * coverage->liabScheduleMod
                * coverage->liabExperienceRatingMod
            )
        case False:
            0.0
    }
}
