entity PIPRater {
    vehicle Vehicle
    company Company
    coverage Coverage
}

entity PIPRater includes BaseLiabRater

property PIPRater pipClassFactor number.decimal {
    lookup([PIPClassFactor], [PIPClassFactor.factor],
        vehicle->pdIndustryType,
        vehicle->size
    )
}

property PIPRater pipLimitFactor number.decimal {
    lookup([PIPLimitFactor], [PIPLimitFactor.factor],
        coverage->pipLimit
    )
}


property PIPRater filingFactor number.decimal {
    lookup([MultiStateFilingFactor], [MultiStateFilingFactor.factor],
        company->isMultiStateFiling,
        vehicle->pdIndustryType,
        vehicle->radius,
        vehicle->territory
    )
}

property PIPRater pipBasePremium number.decimal {
    liabMaxPremium / vehicle->liabLimitFactor
}

output PIPRater vehiclePIPPremium number.decimal {
    switch(coverage->hasPIPCoverage) {
        case True:
            round(
                pipBasePremium
                * pipClassFactor
                * pipLimitFactor
                * filingFactor
                * vehicle->premiumCreditFactor
            )
        case False:
            0.0
    }
}
