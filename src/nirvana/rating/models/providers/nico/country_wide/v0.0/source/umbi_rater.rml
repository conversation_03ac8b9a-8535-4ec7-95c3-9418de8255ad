entity UMBIRater {
    vehicle Vehicle
    company Company
    coverage Coverage
}

entity UMBIRater includes BaseLiabRater

property UMBIRater umbiLimitFactor number.decimal {
    lookup([UMBILimitFactor], [UMBILimitFactor.factor],
        coverage->umbiLimit
    )
}


// Note: This property MUST be overridden by each state implementation.
// The @require annotation below serves as a failsafe to ensure that any state
// attempting to use this rater without providing their own stateSpecificFactor
// implementation will fail during validation. This is intentional - each state
// must provide its own factor calculation based on state-specific requirements.
// Common implementations:
// - Most states use vehicle->umbiClassFactor
// - PA uses vehicle->umbiTerritoryClassFactor
// The default value of 1.0 is purposely set to trigger the @require validation.
@require(stateSpecificFactor != 1.0)
property UMBIRater stateSpecificFactor number.decimal {
    1.0
}

property UMBIRater umbiBasePremium number.decimal {
    liabMaxPremium / vehicle->liabLimitFactor
}

output UMBIRater vehicleUMBIPremium number.decimal {
    switch(vehicle->hasUMBICoverage) {
        case True:
            round(
                umbiBasePremium
                * umbiLimitFactor
                * stateSpecificFactor
                * company->iniFactor
            )
        case False:
            0.0
    }
}
