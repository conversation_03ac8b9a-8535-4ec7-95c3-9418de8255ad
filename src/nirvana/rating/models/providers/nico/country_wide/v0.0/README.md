# Introduction

The purpose of this document is to provide a high-level overview of the inputs and outputs
that this model receives and returns.

This document deals in terms of the Pricing request and response objects. The idea of
describing inputs/outputs in these terms, is to guide consumers of the Pricing API on
how to fill such objects, when calling the API for this model.

# Sub-coverages and sub-coverages groups

The supported sub-coverages / sub-coverage groups and their respective limits/deductibles
and outputs are described below.

For more details on the charges created you can take a look at this file:
`rating/adaptors/business_auto_adaptor/common/output_creator_charges_methods.go`. Note that
in this file more charges could be created that do not apply to this model. Read the
documentation below to understand which charges are created for this model.

## 1. Liab
- Is a group consisting of BI and PD (i.e. they are priced as a unit).
- A single charge is created for this group.
- The limit associated with this group is required. It is a per-occurrence limit.
- The deductible associated with this group is not relevant to the rating algorithm.
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
passed at the policy level in the Pricing request. This is because what the subcov
covers doesn't depend on the vehicle.

## 2. PD
- Is a group consisting of Comp and Coll (i.e. they are priced as a unit).
- A single charge is created for this group.
- The limits associated with this group is not relevant to the rating algorithm.
- There is no deductible associated with this group. Instead, there is one deductible for 
Comp and another for Coll. They should be passed separately to Pricing, despite the fact
that for this model we enforce that the deductible value is the same for both.
- The premium is calculated per vehicle and the inputs are passed within each vehicle object
in the Pricing request (not the chunk spec).
- The deductible value can vary among vehicles.
- We currently restrict the per-vehicle selection of this group. This means that
if a vehicle has this option selected (instead of Comp only) then all other vehicles must
either also have this option or no sub-coverage at all, but they can't have Comp only.

## 3. Comp (alone)
- This is priced as a standalone sub-cov. It only exists when the customer selected Comp
without Coll. Note that it's not possible to select Coll without Comp.
- A single charge is created for this sub-cov. Note that this charge is exclusive with
the charge created for the PD group.
- The limit associated with this sub-cov is not relevant to the rating algorithm.
- The deductible associated with this sub-cov is required.
- The premium is calculated per vehicle and the inputs are passed within each vehicle object
in the Pricing request (not the chunk spec).
- The deductible value can vary among vehicles.
- We currently restrict the per-vehicle selection of this sub-coverage. This means that
if a vehicle has this option selected (instead of Comp+Coll) then all other vehicles must
either also have this option or no sub-coverage at all, but they can't have Comp+Coll.

## 4. Hired Auto Liab
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- Neither the limit nor the deductible associated to this sub-cov are relevant.
- Only the presence of the sub-cov is relevant.
- The premium is calculated at the policy-level (not per vehicle).

## 5. Hired Auto PD
- This is priced as a standalone sub-cov. It can't be selected if the customer selected
Hired Auto Liab. If Hired Auto PD comes without Hired Auto Liab the API returns an error. 
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is not relevant to the rating algorithm.
- The deductible associated with this sub-cov is required.
- The premium is calculated at the policy-level (not per vehicle).

## 6. MedPay.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is required. It is a per-occurrence limit.
- The deductible associated with this sub-cov is not relevant to the rating algorithm.
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
passed at the policy level in the Pricing request. This is because what the subcov
covers doesn't depend on the vehicle.

## 7. Non-Owned Vehicle.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is not relevant to the rating algorithm.
- The deductible associated with this sub-cov is not relevant to the rating algorithm.
- The premium is calculated at the policy-level (not per vehicle).

## 8. Rental.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is required. It is a per-occurrence limit.
- The deductible associated with this sub-cov is not relevant to the rating algorithm.
- The premium is calculated per vehicle and the inputs are passed within each vehicle object
in the Pricing request (not the chunk spec).
- The limit value can vary among vehicles.

## 9. Towing.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is required. It is a per-occurrence limit.
There's a caveat with this limit. Limits for this sub-coverage are specified as "XperdayYmax".
The current limit model doesn't support non-numeric limits. The workaround is to pass
the "max" value as the limit. This works because currently we only have limits that have
a valid period of 30 days. Therefore, Pricing can transform the numeric limit into
the string (by assuming and hardcoding the valid period of 30 days). E.g. a value of 1200
would be transformed into "40perday1200max".
- The deductible associated with this sub-cov is not relevant to the rating algorithm.
- The premium is calculated per vehicle and the inputs are passed within each vehicle object
in the Pricing request (not the chunk spec).
- The limit value can vary among vehicles.
- This sub-coverage can only be selected for vehicles with size "Light Truck", "Medium Truck"
and "PPT".

## 10. UMBI.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is required. It is a per-occurrence limit.
- The Liab limit is also required to price this sub-cov (as per the rating logic).
- The deductible associated with this group is not relevant to the rating algorithm.
- See relation with UM sub-cov below.
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
passed at the policy level in the Pricing request. This is because what the subcov
covers doesn't depend on the vehicle.

## 11. UIMBI.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is required. It is a per-occurrence limit.
- The Liab limit is also required to price this sub-cov (as per the rating logic).
- The deductible associated with this group is not relevant to the rating algorithm.
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
passed at the policy level in the Pricing request. This is because what the subcov
covers doesn't depend on the vehicle.

## 12. UMPD.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is required. It is a per-occurrence limit.
- The Liab limit is also required to price this sub-cov (as per the rating logic).
- The deductible associated with this sub-cov is required.
- The premium is calculated per vehicle and the inputs are passed within each vehicle object
in the Pricing request (not the chunk spec).
- The limit and deductible values can vary among vehicles.
- For IL there are additional requirements. The UMPD sub-coverage can only be selected
for non-PPT vehicles. Additionally, the UMPD sub-coverage can only be selected if
the Coll sub-coverage is not selected for the vehicle.
- See relation with UM sub-cov below.

## 13. UM.
- Is a group consisting of UMBI and UMPD. In some states, like IN, these sub-coverages
are priced as a unit if they are both selected. In others, like OH, they are priced
independently.
- A single charge is created for this group.
- The limit associated with this group is required. It is a per-occurrence limit.
- There is no deductible associated with this group (the deductible is only for UMPD).
- The premium is calculated per vehicle.
- The sub-coverage is passed within each vehicle sub-coverages.
- The limit is passed as a policy-level limit (associated to the group).
- In some states (PA), there is a flag to toggle between stacked and unstacked limits (see Additional Coverage Behaviors section).

## 14. UMUIM.
- Is a group consisting of UMBI, UMPD, UIMBI and UIMPD (i.e. they are priced as a unit).
- A single charge is created for this group.
- The limit associated with this group is required. It is a per-occurrence limit.
- For some states (GA), a deductible is required.
- For some states (GA), there is an addedOn flag to toggle the coverage behavior (see Additional Coverage Behaviors section).
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
  passed at the policy level in the Pricing request.


## 15. Medical Expense Benefits.
- As of 07/24 this sub-coverage is only available in PA.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is required. It is a aggregate limit.
- The deductible associated with this sub-cov is not relevant to the rating algorithm.
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
  passed at the policy level in the Pricing request. This is because what the subcov
  covers doesn't depend on the vehicle.

## 16. Work Loss Benefits.
- As of 07/24 this sub-coverage is only available in PA.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limits associated with this sub-cov are required. It requires two limits:
  - A monthly limit.
  - An aggregate limit.
- The deductible associated with this sub-cov is not relevant to the rating algorithm.
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
  passed at the policy level in the Pricing request. This is because what the subcov
  covers doesn't depend on the vehicle.

## 17. Funeral Expense Benefits.
- As of 07/24 this sub-coverage is only available in PA.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is required. It is a aggregate limit.
- The deductible associated with this sub-cov is not relevant to the rating algorithm.
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
  passed at the policy level in the Pricing request. This is because what the subcov
  covers doesn't depend on the vehicle.

## 18. Funeral Benefits.
- As of 07/24 this sub-coverage is only available in PA.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is required. It is a aggregate limit.
- The deductible associated with this sub-cov is not relevant to the rating algorithm.
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
  passed at the policy level in the Pricing request. This is because what the subcov
  covers doesn't depend on the vehicle.

## 19. Accidental Death Benefits.
- As of 07/24 this sub-coverage is only available in PA.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is required. It is a aggregate limit.
- The deductible associated with this sub-cov is not relevant to the rating algorithm.
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
  passed at the policy level in the Pricing request. This is because what the subcov
  covers doesn't depend on the vehicle.

## 20. Extraordinary Medical Benefits.
- As of 07/24 this sub-coverage is only available in PA.
- This is priced as a standalone sub-cov.
- A single charge is created for this sub-cov.
- The limit associated with this sub-cov is required. It is a per-occurrence limit.
- The deductible associated with this sub-cov is not relevant to the rating algorithm.
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
  passed at the policy level in the Pricing request. This is because what the subcov
  covers doesn't depend on the vehicle.

## 21. PIP (Personal Injury Protection).
- Is a group consisting of Medical Expense Benefits, Funeral Expense Benefits, Work Loss
Benefits, and Essential Service Expenses (i.e. they are priced as a unit).
- A single charge is created for this group.
- The limit associated with this group is required. It is a per-occurrence limit.
- The deductible associated with this group is not relevant to the rating algorithm.
- The premium is calculated per vehicle, but the limit and the sub-cov it-self are
passed at the policy level in the Pricing request.
- Note: Currently only supported for Texas. The definition of PIP varies from state to state.
This group will be renamed as we learn about other states' PIP requirements.

# Fee charges

Fee charges correspond to fees that Nirvana applies for "extra coverage", which is
not tied to a specific sub-coverage or sub-coverage group. This model only
supports Blanket X charges (i.e. Regular AI, PNC AI and WOS). Specified X charges
are not supported. Sending them will result in an error.

Fee charges are calculated at the policy-level (not per vehicle).

# Surcharges

This model was filed as a Non-Admitted filing. This means that Nirvana is forced to
collect two surcharges: Surplus Lines Tax and Surplus Stamping Fee. These surcharges
are referred to as "Surplus Surcharges". Each one can come in two variants:
- Surcharges coming from refundable premium
- Surcharges coming from non-refundable premium
This distinction is necessary because the surcharge associated to refundable premium 
is also refundable (while the one associated to non-refundable premium is not).

On top of the Surplus Surcharges, there are also state-specific surcharges which are
not implemented in the country-wide model. As of 28/06, the only states that are
active are OH, IL and IN. There are no state-specific surcharges for these states.

Surcharges are calculated at the policy-level (not per vehicle), and are not associated
to any specific sub-coverage or sub-coverage group, despite the fact that their calculation
can be based on the premium of a specific sub-coverage or sub-coverage group.

# Schedule Modifications

The following sub-coverage groups are supported:
- BI+PD
- COMP+COLL

# Experience Rating Modifications

The following sub-coverage groups are supported:
- BI+PD
- COMP+COLL

# Loss Free Modifications

The following sub-coverage groups are supported:
- BI+PD
- COMP+COLL

# Additional Coverage Behaviors

## Added-On vs Reduced-By

For some states (GA), UMUIM coverage has an addedOn flag that toggles between two behaviors
for how Nirvana's coverage interacts with amounts recovered from at-fault drivers:

**Example scenario:** 
At-fault driver has $25K in liability, you have $100K UM limit with Nirvana,
and there's a $150K claim.

**Added-On:**
In an Added-On scenario, Nirvana’s coverage is paid in full on top of whatever the at-fault driver’s insurer covers,
we don’t reduce our payout.
- You collect $25K from the at-fault driver.
- Nirvana still pays the full $100K.
- Total compensation: $125K.

**Reduced-By:**
In a Reduced-By scenario, Nirvana’s payout is reduced by the amount recovered from the at-fault driver’s insurer,
we only cover the remaining balance.
- You collect $25K from the at-fault driver.
- Nirvana pays $75K (i.e. $100K – $25K).
- Total compensation: $100K.


## Stacked vs Unstacked Limits

For states that support this option (PA), UM coverage can be configured with stacked or
unstacked limits:

- **Unstacked:** If customer selects unstacked and a limit of X, then the final per-vehicle limit is X.
- **Stacked:** If customer selects stacked and a limit of X (and has V vehicles), then the
final per-vehicle limit is X × V.
