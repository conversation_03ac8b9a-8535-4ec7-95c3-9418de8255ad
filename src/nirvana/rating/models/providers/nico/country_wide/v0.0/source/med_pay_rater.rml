entity MedPayRater {
    vehicle Vehicle
    coverage Coverage
}

entity MedPayRater includes BaseFilingFactorRater

property MedPayRater medPayBaseRate number.decimal {
    lookup([MedPayBaseRate], [MedPayBaseRate.baseRate],
        vehicle->territory
    )
}

property MedPayRater medPayLimitFactor number.decimal {
    lookup([MedPayLimitFactor], [MedPayLimitFactor.factor],
        coverage->medPayLimit
    )
}

output MedPayRater vehicleMedPayPremium number.decimal {
    switch(coverage->hasMedPayCoverage) {
        case True:
            round(
                medPayBaseRate
                * medPayLimitFactor
                * filingFactor
                * vehicle->premiumCreditFactor
            )
        case False:
            0.0
    }
}
