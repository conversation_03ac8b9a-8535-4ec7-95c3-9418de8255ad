entity HiredAutoLiabRater {
    company Company
    coverage Coverage
}

entity HiredAutoLiabRater includes BaseLiabRater

property HiredAutoLiabRater liabTotalPremiumPPU number.decimal {
    liabTotalPremium / number.decimal{company->numberOfPowerUnits}
}

output HiredAutoLiabRater hiredAutoLiabPremium number.decimal {
    switch(coverage->hasHiredAutoLiabCoverage) {
        case True:
            round(liabTotalPremiumPPU * 0.015 * company->truncatedAnnualCostOfHire / 500.0)
        case False:
            0.0
    }
}
