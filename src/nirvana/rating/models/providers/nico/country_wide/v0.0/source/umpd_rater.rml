entity UMPDRater {
    vehicle Vehicle
}

entity UMPDRater includes BaseLiabRater

property UMPDRater umpdLimitFactor number.decimal {
    lookup([UMPDLimitDeductibleFactor], [UMPDLimitDeductibleFactor.factor],
        vehicle->umpdLimit,
        vehicle->umpdDeductible
    )
}


property UMPDRater umpdBasePremium number.decimal {
    liabMaxPremium / vehicle->liabLimitFactor
}

output UMPDRater vehicleUMPDPremium number.decimal {
    switch(vehicle->hasUMPDCoverage) {
        case True:
            round(
                umpdBasePremium
                * umpdLimitFactor
                * 0.02 // Uniform Class factor applied to all classes for UMPD
            )
        case False:
            0.0
    }
}
