entity HiredAutoPDRater {
    company Company
    coverage Coverage

    vehicles list[Vehicle]
}

entity HiredAutoPDRater includes BasePDRater

// Note that at most one of compCollTotalPremium and compOnlyTotalPremium will be non-zero at any given time.
property HiredAutoPDRater hiredAutoPDBaseRate number.decimal {
    (compCollTotalPremium + compOnlyTotalPremium) / sum(vehicles, [Vehicle.statedValue])
}

property HiredAutoPDRater hiredAutoPDDeductibleFactor number.decimal {
    lookup([HiredAutoPDDeductible], [HiredAutoPDDeductible.factor],
        coverage->hiredAutoPDDeductible
    )
}

property HiredAutoPDRater hiredAutoPDComprehensiveFactor number.decimal {
    lookup([PDComprehensiveFactor], [PDComprehensiveFactor.factor],
        IndustryTypeEnum("Contractor Trucks")
    )
}

output HiredAutoPDRater hiredAutoPDPremium number.decimal {
    switch(coverage->hasHiredAutoPDCoverage) {
        case True:
            round(
              hiredAutoPDBaseRate
              * number.decimal{company->maxValueOfHiredAutos}
              * 0.010
              * company->truncatedAnnualCostOfHire
              * hiredAutoPDDeductibleFactor
              * hiredAutoPDComprehensiveFactor
              / 500.0
            )
        case False:
            0.0
    }
}
