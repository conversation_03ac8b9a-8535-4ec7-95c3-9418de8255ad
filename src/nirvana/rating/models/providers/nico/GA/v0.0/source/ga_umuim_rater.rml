property UMUIMRater umuimDeductibleFactor number.decimal {
    lookup([UMUIMDeductibleFactor], [UMUIMDeductibleFactor.factor],
        coverage->umuimDeductible
    )
}

property UMUIMRater addedOnFactor number.decimal {
    lookup([UMUIMAddedOnFactor], [UMUIMAddedOnFactor.factor],
        coverage->umuimLimit,
        coverage->areUMUIMLimitsAddedOn
    )
}

@override
property UMUIMRater stateSpecificFactor number.decimal {
    vehicle->umuimClassFactor
    * addedOnFactor
    * umuimDeductibleFactor
}
