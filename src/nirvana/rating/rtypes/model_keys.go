package rtypes

import (
	"fmt"
	"strings"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/us_states"
)

type ModelKey struct {
	provider RatemlModelProvider
	state    string
	version  RatemlModelVersion
}

func (m ModelKey) Provider() RatemlModelProvider {
	return m.provider
}

func (m ModelKey) State() us_states.USState {
	state, err := us_states.StrToUSState(m.state)
	if err != nil {
		panic(err)
	}
	return state
}

func (m ModelKey) Version() RatemlModelVersion {
	return m.version
}

func (m ModelKey) String() string {
	return fmt.Sprintf("%s-%s-%s", m.provider, m.state, m.version.String())
}

func NewModelKey(
	provider RatemlModelProvider, state us_states.USState, version RatemlModelVersion,
) ModelKey {
	return ModelKey{
		provider: provider,
		state:    state.ToCode(),
		version:  version,
	}
}

func ModelKeyFromString(s string) (*<PERSON><PERSON><PERSON>, error) {
	units := strings.Split(s, "-")
	if len(units) != 3 {
		return nil, errors.Newf("incorrect number of elements in ModelKey %s: %d", s, len(units))
	}
	// We don't do any special validation for provider name since rateml
	// users can create new providers
	if len(units[0]) == 0 {
		return nil, errors.Newf("provider cannot be an empty string in ModelKey: %s", s)
	}
	state, err := us_states.StrToUSState(units[1])
	if err != nil {
		return nil, errors.Wrap(err, "invalid state in version")
	}
	version, err := GetVersionFromString(units[2])
	if err != nil {
		return nil, errors.Wrap(err, "invalid version in ModelKey")
	}
	modelKey := NewModelKey(NewRatemlModelProvider(units[0]), state, *version)
	return &modelKey, nil
}

var (
	ProviderSentry_AL_0_1_1 = NewModelKey(ProviderSentry, us_states.AL, Version011)
	ProviderSentry_AL_0_3_0 = NewModelKey(ProviderSentry, us_states.AL, Version030)
	ProviderSentry_AL_0_5_1 = NewModelKey(ProviderSentry, us_states.AL, Version051)

	ProviderSentry_AZ_0_1_1 = NewModelKey(ProviderSentry, us_states.AZ, Version011)
	ProviderSentry_AZ_0_1_3 = NewModelKey(ProviderSentry, us_states.AZ, Version013)
	ProviderSentry_AZ_0_2_3 = NewModelKey(ProviderSentry, us_states.AZ, Version023)
	ProviderSentry_AZ_0_3_1 = NewModelKey(ProviderSentry, us_states.AZ, Version031)

	ProviderSentry_CA_0_1_0 = NewModelKey(ProviderSentry, us_states.CA, Version010)
	ProviderSentry_CA_0_1_1 = NewModelKey(ProviderSentry, us_states.CA, Version011)

	ProviderSentry_CO_0_3_1 = NewModelKey(ProviderSentry, us_states.CO, Version031)

	ProviderSentry_GA_0_2_0 = NewModelKey(ProviderSentry, us_states.GA, Version020)
	ProviderSentry_GA_0_3_1 = NewModelKey(ProviderSentry, us_states.GA, Version031)
	ProviderSentry_GA_0_3_3 = NewModelKey(ProviderSentry, us_states.GA, Version033)
	ProviderSentry_GA_0_4_2 = NewModelKey(ProviderSentry, us_states.GA, Version042)

	ProviderSentry_IA_0_3_1 = NewModelKey(ProviderSentry, us_states.IA, Version031)
	ProviderSentry_IA_0_3_4 = NewModelKey(ProviderSentry, us_states.IA, Version034)
	ProviderSentry_IA_0_4_0 = NewModelKey(ProviderSentry, us_states.IA, Version040)
	ProviderSentry_IA_0_4_2 = NewModelKey(ProviderSentry, us_states.IA, Version042)
	ProviderSentry_IA_0_5_1 = NewModelKey(ProviderSentry, us_states.IA, Version051)

	ProviderSentry_IL_0_4_0 = NewModelKey(ProviderSentry, us_states.IL, Version040)
	ProviderSentry_IL_0_5_0 = NewModelKey(ProviderSentry, us_states.IL, Version050)
	ProviderSentry_IL_0_6_0 = NewModelKey(ProviderSentry, us_states.IL, Version060)
	ProviderSentry_IL_0_6_3 = NewModelKey(ProviderSentry, us_states.IL, Version063)
	ProviderSentry_IL_0_6_4 = NewModelKey(ProviderSentry, us_states.IL, Version064)
	ProviderSentry_IL_0_6_5 = NewModelKey(ProviderSentry, us_states.IL, Version065)
	ProviderSentry_IL_0_7_0 = NewModelKey(ProviderSentry, us_states.IL, Version070)
	ProviderSentry_IL_0_7_2 = NewModelKey(ProviderSentry, us_states.IL, Version072)
	ProviderSentry_IL_0_8_1 = NewModelKey(ProviderSentry, us_states.IL, Version081)
	ProviderSentry_IL_0_8_2 = NewModelKey(ProviderSentry, us_states.IL, Version082)
	ProviderSentry_IL_0_9_0 = NewModelKey(ProviderSentry, us_states.IL, Version090)
	ProviderSentry_IL_0_9_1 = NewModelKey(ProviderSentry, us_states.IL, Version091)

	ProviderSentry_IN_0_4_1 = NewModelKey(ProviderSentry, us_states.IN, Version041)
	ProviderSentry_IN_0_6_0 = NewModelKey(ProviderSentry, us_states.IN, Version060)
	ProviderSentry_IN_0_6_4 = NewModelKey(ProviderSentry, us_states.IN, Version064)
	ProviderSentry_IN_0_7_1 = NewModelKey(ProviderSentry, us_states.IN, Version071)
	ProviderSentry_IN_0_7_2 = NewModelKey(ProviderSentry, us_states.IN, Version072)
	ProviderSentry_IN_0_7_3 = NewModelKey(ProviderSentry, us_states.IN, Version073)
	ProviderSentry_IN_0_8_0 = NewModelKey(ProviderSentry, us_states.IN, Version080)
	ProviderSentry_IN_0_8_1 = NewModelKey(ProviderSentry, us_states.IN, Version081)

	ProviderSentry_KS_0_4_2 = NewModelKey(ProviderSentry, us_states.KS, Version042)
	ProviderSentry_KS_0_5_2 = NewModelKey(ProviderSentry, us_states.KS, Version052)

	ProviderSentry_KY_0_1_1 = NewModelKey(ProviderSentry, us_states.KY, Version011)
	ProviderSentry_KY_0_2_1 = NewModelKey(ProviderSentry, us_states.KY, Version021)

	ProviderSentry_MI_0_0_1 = NewModelKey(ProviderSentry, us_states.MI, Version001)
	ProviderSentry_MI_0_0_5 = NewModelKey(ProviderSentry, us_states.MI, Version005)
	ProviderSentry_MI_0_2_2 = NewModelKey(ProviderSentry, us_states.MI, Version022)
	ProviderSentry_MI_0_3_1 = NewModelKey(ProviderSentry, us_states.MI, Version031)
	ProviderSentry_MI_0_3_2 = NewModelKey(ProviderSentry, us_states.MI, Version032)
	ProviderSentry_MI_0_4_1 = NewModelKey(ProviderSentry, us_states.MI, Version041)

	ProviderSentry_MN_0_5_0 = NewModelKey(ProviderSentry, us_states.MN, Version050)
	ProviderSentry_MN_0_5_1 = NewModelKey(ProviderSentry, us_states.MN, Version051)
	ProviderSentry_MN_0_7_0 = NewModelKey(ProviderSentry, us_states.MN, Version070)
	ProviderSentry_MN_0_7_2 = NewModelKey(ProviderSentry, us_states.MN, Version072)
	ProviderSentry_MN_0_8_1 = NewModelKey(ProviderSentry, us_states.MN, Version081)
	ProviderSentry_MN_0_8_2 = NewModelKey(ProviderSentry, us_states.MN, Version082)
	ProviderSentry_MN_0_9_1 = NewModelKey(ProviderSentry, us_states.MN, Version091)

	ProviderSentry_MO_0_1_0 = NewModelKey(ProviderSentry, us_states.MO, Version010)
	ProviderSentry_MO_0_3_3 = NewModelKey(ProviderSentry, us_states.MO, Version033)
	ProviderSentry_MO_0_5_1 = NewModelKey(ProviderSentry, us_states.MO, Version051)
	ProviderSentry_MO_0_6_0 = NewModelKey(ProviderSentry, us_states.MO, Version060)
	ProviderSentry_MO_0_6_1 = NewModelKey(ProviderSentry, us_states.MO, Version061)

	ProviderSentry_NC_0_2_3 = NewModelKey(ProviderSentry, us_states.NC, Version023)
	ProviderSentry_NC_0_2_5 = NewModelKey(ProviderSentry, us_states.NC, Version025)
	ProviderSentry_NC_0_3_0 = NewModelKey(ProviderSentry, us_states.NC, Version030)
	ProviderSentry_NC_0_4_1 = NewModelKey(ProviderSentry, us_states.NC, Version041)
	ProviderSentry_NC_0_5_1 = NewModelKey(ProviderSentry, us_states.NC, Version051)

	ProviderSentry_NE_0_2_3 = NewModelKey(ProviderSentry, us_states.NE, Version023)
	ProviderSentry_NE_0_2_5 = NewModelKey(ProviderSentry, us_states.NE, Version025)
	ProviderSentry_NE_0_3_1 = NewModelKey(ProviderSentry, us_states.NE, Version031)
	ProviderSentry_NE_0_5_0 = NewModelKey(ProviderSentry, us_states.NE, Version050)
	ProviderSentry_NE_0_5_1 = NewModelKey(ProviderSentry, us_states.NE, Version051)

	ProviderSentry_NM_0_3_1 = NewModelKey(ProviderSentry, us_states.NM, Version031)

	ProviderSentry_NV_0_1_0 = NewModelKey(ProviderSentry, us_states.NV, Version010)
	ProviderSentry_NV_0_3_1 = NewModelKey(ProviderSentry, us_states.NV, Version031)

	ProviderSentry_OH_0_5_0 = NewModelKey(ProviderSentry, us_states.OH, Version050)
	ProviderSentry_OH_0_6_0 = NewModelKey(ProviderSentry, us_states.OH, Version060)
	ProviderSentry_OH_0_6_3 = NewModelKey(ProviderSentry, us_states.OH, Version063)
	ProviderSentry_OH_0_6_4 = NewModelKey(ProviderSentry, us_states.OH, Version064)
	ProviderSentry_OH_0_6_6 = NewModelKey(ProviderSentry, us_states.OH, Version066)
	ProviderSentry_OH_0_7_1 = NewModelKey(ProviderSentry, us_states.OH, Version071)
	ProviderSentry_OH_0_7_2 = NewModelKey(ProviderSentry, us_states.OH, Version072)
	ProviderSentry_OH_0_7_3 = NewModelKey(ProviderSentry, us_states.OH, Version073)
	ProviderSentry_OH_0_8_1 = NewModelKey(ProviderSentry, us_states.OH, Version081)

	ProviderSentry_OK_0_3_2 = NewModelKey(ProviderSentry, us_states.OK, Version032)
	ProviderSentry_OK_0_4_2 = NewModelKey(ProviderSentry, us_states.OK, Version042)
	ProviderSentry_OK_0_5_1 = NewModelKey(ProviderSentry, us_states.OK, Version051)

	ProviderSentry_OR_0_0_2 = NewModelKey(ProviderSentry, us_states.OR, Version002)
	ProviderSentry_OR_0_1_0 = NewModelKey(ProviderSentry, us_states.OR, Version010)
	ProviderSentry_OR_0_1_1 = NewModelKey(ProviderSentry, us_states.OR, Version011)
	ProviderSentry_OR_0_2_1 = NewModelKey(ProviderSentry, us_states.OR, Version021)
	ProviderSentry_OR_0_3_1 = NewModelKey(ProviderSentry, us_states.OR, Version031)

	ProviderSentry_PA_0_4_3 = NewModelKey(ProviderSentry, us_states.PA, Version043)
	ProviderSentry_PA_0_5_2 = NewModelKey(ProviderSentry, us_states.PA, Version052)
	ProviderSentry_PA_0_6_2 = NewModelKey(ProviderSentry, us_states.PA, Version062)

	ProviderSentry_SC_0_4_3 = NewModelKey(ProviderSentry, us_states.SC, Version043)
	ProviderSentry_SC_0_5_2 = NewModelKey(ProviderSentry, us_states.SC, Version052)

	ProviderSentry_TN_0_6_3 = NewModelKey(ProviderSentry, us_states.TN, Version063)
	ProviderSentry_TN_0_6_5 = NewModelKey(ProviderSentry, us_states.TN, Version065)
	ProviderSentry_TN_0_7_1 = NewModelKey(ProviderSentry, us_states.TN, Version071)
	ProviderSentry_TN_0_9_1 = NewModelKey(ProviderSentry, us_states.TN, Version091)

	ProviderSentry_TX_0_3_5 = NewModelKey(ProviderSentry, us_states.TX, Version035)
	ProviderSentry_TX_0_4_0 = NewModelKey(ProviderSentry, us_states.TX, Version040)
	ProviderSentry_TX_0_4_2 = NewModelKey(ProviderSentry, us_states.TX, Version042)
	ProviderSentry_TX_0_5_0 = NewModelKey(ProviderSentry, us_states.TX, Version050)
	ProviderSentry_TX_0_5_1 = NewModelKey(ProviderSentry, us_states.TX, Version051)
	ProviderSentry_TX_0_6_1 = NewModelKey(ProviderSentry, us_states.TX, Version061)

	ProviderSentry_UT_0_0_1 = NewModelKey(ProviderSentry, us_states.UT, Version001)
	ProviderSentry_UT_0_3_1 = NewModelKey(ProviderSentry, us_states.UT, Version031)

	ProviderSentry_WA_0_1_1 = NewModelKey(ProviderSentry, us_states.WA, Version011)
	ProviderSentry_WA_0_2_1 = NewModelKey(ProviderSentry, us_states.WA, Version021)

	ProviderSentry_WI_0_5_2 = NewModelKey(ProviderSentry, us_states.WI, Version052)
	ProviderSentry_WI_0_6_2 = NewModelKey(ProviderSentry, us_states.WI, Version062)
	ProviderSentry_WI_0_7_0 = NewModelKey(ProviderSentry, us_states.WI, Version070)
	ProviderSentry_WI_0_7_2 = NewModelKey(ProviderSentry, us_states.WI, Version072)
	ProviderSentry_WI_0_8_0 = NewModelKey(ProviderSentry, us_states.WI, Version080)
	ProviderSentry_WI_0_8_1 = NewModelKey(ProviderSentry, us_states.WI, Version081)

	ProviderSentryMST_AL_0_0_3 = NewModelKey(ProviderSentryMST, us_states.AL, Version003)
	ProviderSentryMST_AL_0_1_0 = NewModelKey(ProviderSentryMST, us_states.AL, Version010)
	ProviderSentryMST_AL_0_1_1 = NewModelKey(ProviderSentryMST, us_states.AL, Version011)
	ProviderSentryMST_AL_0_2_0 = NewModelKey(ProviderSentryMST, us_states.AL, Version020)
	ProviderSentryMST_AL_0_2_1 = NewModelKey(ProviderSentryMST, us_states.AL, Version021)
	ProviderSentryMST_AL_0_2_2 = NewModelKey(ProviderSentryMST, us_states.AL, Version022)
	ProviderSentryMST_AL_0_3_0 = NewModelKey(ProviderSentryMST, us_states.AL, Version030)
	ProviderSentryMST_AL_0_3_1 = NewModelKey(ProviderSentryMST, us_states.AL, Version031)
	ProviderSentryMST_AL_0_3_2 = NewModelKey(ProviderSentryMST, us_states.AL, Version032)
	ProviderSentryMST_AL_0_3_3 = NewModelKey(ProviderSentryMST, us_states.AL, Version033)
	ProviderSentryMST_AL_0_4_0 = NewModelKey(ProviderSentryMST, us_states.AL, Version040)
	ProviderSentryMST_AL_0_4_1 = NewModelKey(ProviderSentryMST, us_states.AL, Version041)
	ProviderSentryMST_AL_0_4_2 = NewModelKey(ProviderSentryMST, us_states.AL, Version042)
	ProviderSentryMST_AL_0_4_3 = NewModelKey(ProviderSentryMST, us_states.AL, Version043)
	ProviderSentryMST_AL_0_4_4 = NewModelKey(ProviderSentryMST, us_states.AL, Version044)

	ProviderSentryMST_AR_0_0_0 = NewModelKey(ProviderSentryMST, us_states.AR, Version000)
	ProviderSentryMST_AR_0_1_0 = NewModelKey(ProviderSentryMST, us_states.AR, Version010)
	ProviderSentryMST_AR_0_1_1 = NewModelKey(ProviderSentryMST, us_states.AR, Version011)
	ProviderSentryMST_AR_0_1_2 = NewModelKey(ProviderSentryMST, us_states.AR, Version012)
	ProviderSentryMST_AR_0_1_3 = NewModelKey(ProviderSentryMST, us_states.AR, Version013)
	ProviderSentryMST_AR_0_2_0 = NewModelKey(ProviderSentryMST, us_states.AR, Version020)
	ProviderSentryMST_AR_0_2_1 = NewModelKey(ProviderSentryMST, us_states.AR, Version021)
	ProviderSentryMST_AR_0_2_2 = NewModelKey(ProviderSentryMST, us_states.AR, Version022)
	ProviderSentryMST_AR_0_2_3 = NewModelKey(ProviderSentryMST, us_states.AR, Version023)
	ProviderSentryMST_AR_0_2_4 = NewModelKey(ProviderSentryMST, us_states.AR, Version024)

	ProviderSentryMST_AZ_0_0_0 = NewModelKey(ProviderSentryMST, us_states.AZ, Version000)
	ProviderSentryMST_AZ_0_0_1 = NewModelKey(ProviderSentryMST, us_states.AZ, Version001)
	ProviderSentryMST_AZ_0_0_3 = NewModelKey(ProviderSentryMST, us_states.AZ, Version003)
	ProviderSentryMST_AZ_0_1_0 = NewModelKey(ProviderSentryMST, us_states.AZ, Version010)
	ProviderSentryMST_AZ_0_1_1 = NewModelKey(ProviderSentryMST, us_states.AZ, Version011)
	ProviderSentryMST_AZ_0_1_2 = NewModelKey(ProviderSentryMST, us_states.AZ, Version012)
	ProviderSentryMST_AZ_0_2_0 = NewModelKey(ProviderSentryMST, us_states.AZ, Version020)
	ProviderSentryMST_AZ_0_2_1 = NewModelKey(ProviderSentryMST, us_states.AZ, Version021)
	ProviderSentryMST_AZ_0_2_2 = NewModelKey(ProviderSentryMST, us_states.AZ, Version022)
	ProviderSentryMST_AZ_0_3_0 = NewModelKey(ProviderSentryMST, us_states.AZ, Version030)
	ProviderSentryMST_AZ_0_3_1 = NewModelKey(ProviderSentryMST, us_states.AZ, Version031)
	ProviderSentryMST_AZ_0_3_2 = NewModelKey(ProviderSentryMST, us_states.AZ, Version032)
	ProviderSentryMST_AZ_0_3_3 = NewModelKey(ProviderSentryMST, us_states.AZ, Version033)
	ProviderSentryMST_AZ_0_4_0 = NewModelKey(ProviderSentryMST, us_states.AZ, Version040)
	ProviderSentryMST_AZ_0_4_1 = NewModelKey(ProviderSentryMST, us_states.AZ, Version041)
	ProviderSentryMST_AZ_0_4_2 = NewModelKey(ProviderSentryMST, us_states.AZ, Version042)
	ProviderSentryMST_AZ_0_4_3 = NewModelKey(ProviderSentryMST, us_states.AZ, Version043)
	ProviderSentryMST_AZ_0_4_4 = NewModelKey(ProviderSentryMST, us_states.AZ, Version044)

	ProviderSentryMST_CA_0_1_0 = NewModelKey(ProviderSentryMST, us_states.CA, Version010)
	ProviderSentryMST_CA_0_1_1 = NewModelKey(ProviderSentryMST, us_states.CA, Version011)
	ProviderSentryMST_CA_0_1_2 = NewModelKey(ProviderSentryMST, us_states.CA, Version012)
	ProviderSentryMST_CA_0_1_3 = NewModelKey(ProviderSentryMST, us_states.CA, Version013)
	ProviderSentryMST_CA_0_1_4 = NewModelKey(ProviderSentryMST, us_states.CA, Version014)
	ProviderSentryMST_CA_0_1_5 = NewModelKey(ProviderSentryMST, us_states.CA, Version015)
	ProviderSentryMST_CA_0_2_0 = NewModelKey(ProviderSentryMST, us_states.CA, Version020)
	ProviderSentryMST_CA_0_2_1 = NewModelKey(ProviderSentryMST, us_states.CA, Version021)
	ProviderSentryMST_CA_0_2_2 = NewModelKey(ProviderSentryMST, us_states.CA, Version022)
	ProviderSentryMST_CA_0_2_3 = NewModelKey(ProviderSentryMST, us_states.CA, Version023)
	ProviderSentryMST_CA_0_3_0 = NewModelKey(ProviderSentryMST, us_states.CA, Version030)
	ProviderSentryMST_CA_0_3_1 = NewModelKey(ProviderSentryMST, us_states.CA, Version031)
	ProviderSentryMST_CA_0_3_2 = NewModelKey(ProviderSentryMST, us_states.CA, Version032)
	ProviderSentryMST_CA_0_3_3 = NewModelKey(ProviderSentryMST, us_states.CA, Version033)
	ProviderSentryMST_CA_0_3_4 = NewModelKey(ProviderSentryMST, us_states.CA, Version034)

	ProviderSentryMST_CO_0_0_1 = NewModelKey(ProviderSentryMST, us_states.CO, Version001)
	ProviderSentryMST_CO_0_0_3 = NewModelKey(ProviderSentryMST, us_states.CO, Version003)
	ProviderSentryMST_CO_0_1_0 = NewModelKey(ProviderSentryMST, us_states.CO, Version010)
	ProviderSentryMST_CO_0_1_1 = NewModelKey(ProviderSentryMST, us_states.CO, Version011)
	ProviderSentryMST_CO_0_2_0 = NewModelKey(ProviderSentryMST, us_states.CO, Version020)
	ProviderSentryMST_CO_0_2_1 = NewModelKey(ProviderSentryMST, us_states.CO, Version021)
	ProviderSentryMST_CO_0_2_2 = NewModelKey(ProviderSentryMST, us_states.CO, Version022)
	ProviderSentryMST_CO_0_3_0 = NewModelKey(ProviderSentryMST, us_states.CO, Version030)
	ProviderSentryMST_CO_0_3_1 = NewModelKey(ProviderSentryMST, us_states.CO, Version031)
	ProviderSentryMST_CO_0_3_2 = NewModelKey(ProviderSentryMST, us_states.CO, Version032)
	ProviderSentryMST_CO_0_3_3 = NewModelKey(ProviderSentryMST, us_states.CO, Version033)
	ProviderSentryMST_CO_0_4_0 = NewModelKey(ProviderSentryMST, us_states.CO, Version040)
	ProviderSentryMST_CO_0_4_1 = NewModelKey(ProviderSentryMST, us_states.CO, Version041)
	ProviderSentryMST_CO_0_4_2 = NewModelKey(ProviderSentryMST, us_states.CO, Version042)
	ProviderSentryMST_CO_0_4_3 = NewModelKey(ProviderSentryMST, us_states.CO, Version043)
	ProviderSentryMST_CO_0_4_4 = NewModelKey(ProviderSentryMST, us_states.CO, Version044)

	ProviderSentryMST_GA_0_0_2 = NewModelKey(ProviderSentryMST, us_states.GA, Version002)
	ProviderSentryMST_GA_0_1_0 = NewModelKey(ProviderSentryMST, us_states.GA, Version010)
	ProviderSentryMST_GA_0_1_1 = NewModelKey(ProviderSentryMST, us_states.GA, Version011)
	ProviderSentryMST_GA_0_2_0 = NewModelKey(ProviderSentryMST, us_states.GA, Version020)
	ProviderSentryMST_GA_0_2_1 = NewModelKey(ProviderSentryMST, us_states.GA, Version021)
	ProviderSentryMST_GA_0_2_2 = NewModelKey(ProviderSentryMST, us_states.GA, Version022)
	ProviderSentryMST_GA_0_3_0 = NewModelKey(ProviderSentryMST, us_states.GA, Version030)
	ProviderSentryMST_GA_0_3_1 = NewModelKey(ProviderSentryMST, us_states.GA, Version031)
	ProviderSentryMST_GA_0_3_2 = NewModelKey(ProviderSentryMST, us_states.GA, Version032)
	ProviderSentryMST_GA_0_3_3 = NewModelKey(ProviderSentryMST, us_states.GA, Version033)
	ProviderSentryMST_GA_0_4_0 = NewModelKey(ProviderSentryMST, us_states.GA, Version040)
	ProviderSentryMST_GA_0_4_1 = NewModelKey(ProviderSentryMST, us_states.GA, Version041)
	ProviderSentryMST_GA_0_4_2 = NewModelKey(ProviderSentryMST, us_states.GA, Version042)
	ProviderSentryMST_GA_0_4_3 = NewModelKey(ProviderSentryMST, us_states.GA, Version043)
	ProviderSentryMST_GA_0_4_4 = NewModelKey(ProviderSentryMST, us_states.GA, Version044)

	ProviderSentryMST_IA_0_0_0 = NewModelKey(ProviderSentryMST, us_states.IA, Version000)
	ProviderSentryMST_IA_0_0_1 = NewModelKey(ProviderSentryMST, us_states.IA, Version001)
	ProviderSentryMST_IA_0_0_3 = NewModelKey(ProviderSentryMST, us_states.IA, Version003)
	ProviderSentryMST_IA_0_1_0 = NewModelKey(ProviderSentryMST, us_states.IA, Version010)
	ProviderSentryMST_IA_0_1_1 = NewModelKey(ProviderSentryMST, us_states.IA, Version011)
	ProviderSentryMST_IA_0_1_2 = NewModelKey(ProviderSentryMST, us_states.IA, Version012)
	ProviderSentryMST_IA_0_2_0 = NewModelKey(ProviderSentryMST, us_states.IA, Version020)
	ProviderSentryMST_IA_0_2_1 = NewModelKey(ProviderSentryMST, us_states.IA, Version021)
	ProviderSentryMST_IA_0_2_2 = NewModelKey(ProviderSentryMST, us_states.IA, Version022)
	ProviderSentryMST_IA_0_3_0 = NewModelKey(ProviderSentryMST, us_states.IA, Version030)
	ProviderSentryMST_IA_0_3_1 = NewModelKey(ProviderSentryMST, us_states.IA, Version031)
	ProviderSentryMST_IA_0_3_2 = NewModelKey(ProviderSentryMST, us_states.IA, Version032)
	ProviderSentryMST_IA_0_3_3 = NewModelKey(ProviderSentryMST, us_states.IA, Version033)
	ProviderSentryMST_IA_0_4_0 = NewModelKey(ProviderSentryMST, us_states.IA, Version040)
	ProviderSentryMST_IA_0_4_1 = NewModelKey(ProviderSentryMST, us_states.IA, Version041)
	ProviderSentryMST_IA_0_4_2 = NewModelKey(ProviderSentryMST, us_states.IA, Version042)
	ProviderSentryMST_IA_0_4_3 = NewModelKey(ProviderSentryMST, us_states.IA, Version043)
	ProviderSentryMST_IA_0_4_4 = NewModelKey(ProviderSentryMST, us_states.IA, Version044)

	ProviderSentryMST_IL_0_0_1 = NewModelKey(ProviderSentryMST, us_states.IL, Version001)
	ProviderSentryMST_IL_0_0_3 = NewModelKey(ProviderSentryMST, us_states.IL, Version003)
	ProviderSentryMST_IL_0_1_0 = NewModelKey(ProviderSentryMST, us_states.IL, Version010)
	ProviderSentryMST_IL_0_1_1 = NewModelKey(ProviderSentryMST, us_states.IL, Version011)
	ProviderSentryMST_IL_0_2_0 = NewModelKey(ProviderSentryMST, us_states.IL, Version020)
	ProviderSentryMST_IL_0_2_1 = NewModelKey(ProviderSentryMST, us_states.IL, Version021)
	ProviderSentryMST_IL_0_2_2 = NewModelKey(ProviderSentryMST, us_states.IL, Version022)
	ProviderSentryMST_IL_0_3_0 = NewModelKey(ProviderSentryMST, us_states.IL, Version030)
	ProviderSentryMST_IL_0_3_1 = NewModelKey(ProviderSentryMST, us_states.IL, Version031)
	ProviderSentryMST_IL_0_3_2 = NewModelKey(ProviderSentryMST, us_states.IL, Version032)
	ProviderSentryMST_IL_0_3_3 = NewModelKey(ProviderSentryMST, us_states.IL, Version033)
	ProviderSentryMST_IL_0_4_0 = NewModelKey(ProviderSentryMST, us_states.IL, Version040)
	ProviderSentryMST_IL_0_4_1 = NewModelKey(ProviderSentryMST, us_states.IL, Version041)
	ProviderSentryMST_IL_0_4_2 = NewModelKey(ProviderSentryMST, us_states.IL, Version042)
	ProviderSentryMST_IL_0_4_3 = NewModelKey(ProviderSentryMST, us_states.IL, Version043)
	ProviderSentryMST_IL_0_4_4 = NewModelKey(ProviderSentryMST, us_states.IL, Version044)

	ProviderSentryMST_IN_0_0_1 = NewModelKey(ProviderSentryMST, us_states.IN, Version001)
	ProviderSentryMST_IN_0_0_3 = NewModelKey(ProviderSentryMST, us_states.IN, Version003)
	ProviderSentryMST_IN_0_1_0 = NewModelKey(ProviderSentryMST, us_states.IN, Version010)
	ProviderSentryMST_IN_0_1_1 = NewModelKey(ProviderSentryMST, us_states.IN, Version011)
	ProviderSentryMST_IN_0_2_0 = NewModelKey(ProviderSentryMST, us_states.IN, Version020)
	ProviderSentryMST_IN_0_2_1 = NewModelKey(ProviderSentryMST, us_states.IN, Version021)
	ProviderSentryMST_IN_0_2_2 = NewModelKey(ProviderSentryMST, us_states.IN, Version022)
	ProviderSentryMST_IN_0_3_0 = NewModelKey(ProviderSentryMST, us_states.IN, Version030)
	ProviderSentryMST_IN_0_3_1 = NewModelKey(ProviderSentryMST, us_states.IN, Version031)
	ProviderSentryMST_IN_0_3_2 = NewModelKey(ProviderSentryMST, us_states.IN, Version032)
	ProviderSentryMST_IN_0_3_3 = NewModelKey(ProviderSentryMST, us_states.IN, Version033)
	ProviderSentryMST_IN_0_4_0 = NewModelKey(ProviderSentryMST, us_states.IN, Version040)
	ProviderSentryMST_IN_0_4_1 = NewModelKey(ProviderSentryMST, us_states.IN, Version041)
	ProviderSentryMST_IN_0_4_2 = NewModelKey(ProviderSentryMST, us_states.IN, Version042)
	ProviderSentryMST_IN_0_4_3 = NewModelKey(ProviderSentryMST, us_states.IN, Version043)
	ProviderSentryMST_IN_0_4_4 = NewModelKey(ProviderSentryMST, us_states.IN, Version044)

	ProviderSentryMST_KS_0_0_3 = NewModelKey(ProviderSentryMST, us_states.KS, Version003)
	ProviderSentryMST_KS_0_1_0 = NewModelKey(ProviderSentryMST, us_states.KS, Version010)
	ProviderSentryMST_KS_0_1_1 = NewModelKey(ProviderSentryMST, us_states.KS, Version011)
	ProviderSentryMST_KS_0_2_0 = NewModelKey(ProviderSentryMST, us_states.KS, Version020)
	ProviderSentryMST_KS_0_2_1 = NewModelKey(ProviderSentryMST, us_states.KS, Version021)
	ProviderSentryMST_KS_0_2_2 = NewModelKey(ProviderSentryMST, us_states.KS, Version022)
	ProviderSentryMST_KS_0_3_0 = NewModelKey(ProviderSentryMST, us_states.KS, Version030)
	ProviderSentryMST_KS_0_3_1 = NewModelKey(ProviderSentryMST, us_states.KS, Version031)
	ProviderSentryMST_KS_0_3_2 = NewModelKey(ProviderSentryMST, us_states.KS, Version032)
	ProviderSentryMST_KS_0_3_3 = NewModelKey(ProviderSentryMST, us_states.KS, Version033)
	ProviderSentryMST_KS_0_4_0 = NewModelKey(ProviderSentryMST, us_states.KS, Version040)
	ProviderSentryMST_KS_0_4_1 = NewModelKey(ProviderSentryMST, us_states.KS, Version041)
	ProviderSentryMST_KS_0_4_2 = NewModelKey(ProviderSentryMST, us_states.KS, Version042)
	ProviderSentryMST_KS_0_4_3 = NewModelKey(ProviderSentryMST, us_states.KS, Version043)
	ProviderSentryMST_KS_0_4_4 = NewModelKey(ProviderSentryMST, us_states.KS, Version044)

	ProviderSentryMST_KY_0_0_1 = NewModelKey(ProviderSentryMST, us_states.KY, Version001)
	ProviderSentryMST_KY_0_0_3 = NewModelKey(ProviderSentryMST, us_states.KY, Version003)
	ProviderSentryMST_KY_0_1_0 = NewModelKey(ProviderSentryMST, us_states.KY, Version010)
	ProviderSentryMST_KY_0_1_1 = NewModelKey(ProviderSentryMST, us_states.KY, Version011)
	ProviderSentryMST_KY_0_1_2 = NewModelKey(ProviderSentryMST, us_states.KY, Version012)
	ProviderSentryMST_KY_0_2_0 = NewModelKey(ProviderSentryMST, us_states.KY, Version020)
	ProviderSentryMST_KY_0_2_1 = NewModelKey(ProviderSentryMST, us_states.KY, Version021)
	ProviderSentryMST_KY_0_2_2 = NewModelKey(ProviderSentryMST, us_states.KY, Version022)
	ProviderSentryMST_KY_0_3_0 = NewModelKey(ProviderSentryMST, us_states.KY, Version030)
	ProviderSentryMST_KY_0_3_1 = NewModelKey(ProviderSentryMST, us_states.KY, Version031)
	ProviderSentryMST_KY_0_3_2 = NewModelKey(ProviderSentryMST, us_states.KY, Version032)
	ProviderSentryMST_KY_0_3_3 = NewModelKey(ProviderSentryMST, us_states.KY, Version033)
	ProviderSentryMST_KY_0_4_0 = NewModelKey(ProviderSentryMST, us_states.KY, Version040)
	ProviderSentryMST_KY_0_4_1 = NewModelKey(ProviderSentryMST, us_states.KY, Version041)
	ProviderSentryMST_KY_0_4_2 = NewModelKey(ProviderSentryMST, us_states.KY, Version042)
	ProviderSentryMST_KY_0_4_3 = NewModelKey(ProviderSentryMST, us_states.KY, Version043)
	ProviderSentryMST_KY_0_4_4 = NewModelKey(ProviderSentryMST, us_states.KY, Version044)

	ProviderSentryMST_MI_0_0_2 = NewModelKey(ProviderSentryMST, us_states.MI, Version002)
	ProviderSentryMST_MI_0_1_0 = NewModelKey(ProviderSentryMST, us_states.MI, Version010)
	ProviderSentryMST_MI_0_1_1 = NewModelKey(ProviderSentryMST, us_states.MI, Version011)
	ProviderSentryMST_MI_0_2_0 = NewModelKey(ProviderSentryMST, us_states.MI, Version020)
	ProviderSentryMST_MI_0_2_1 = NewModelKey(ProviderSentryMST, us_states.MI, Version021)
	ProviderSentryMST_MI_0_2_2 = NewModelKey(ProviderSentryMST, us_states.MI, Version022)
	ProviderSentryMST_MI_0_3_0 = NewModelKey(ProviderSentryMST, us_states.MI, Version030)
	ProviderSentryMST_MI_0_3_1 = NewModelKey(ProviderSentryMST, us_states.MI, Version031)
	ProviderSentryMST_MI_0_3_2 = NewModelKey(ProviderSentryMST, us_states.MI, Version032)
	ProviderSentryMST_MI_0_3_3 = NewModelKey(ProviderSentryMST, us_states.MI, Version033)
	ProviderSentryMST_MI_0_4_0 = NewModelKey(ProviderSentryMST, us_states.MI, Version040)
	ProviderSentryMST_MI_0_4_1 = NewModelKey(ProviderSentryMST, us_states.MI, Version041)
	ProviderSentryMST_MI_0_4_2 = NewModelKey(ProviderSentryMST, us_states.MI, Version042)
	ProviderSentryMST_MI_0_4_3 = NewModelKey(ProviderSentryMST, us_states.MI, Version043)
	ProviderSentryMST_MI_0_4_4 = NewModelKey(ProviderSentryMST, us_states.MI, Version044)

	ProviderSentryMST_MN_0_0_0 = NewModelKey(ProviderSentryMST, us_states.MN, Version000)
	ProviderSentryMST_MN_0_0_1 = NewModelKey(ProviderSentryMST, us_states.MN, Version001)
	ProviderSentryMST_MN_0_0_3 = NewModelKey(ProviderSentryMST, us_states.MN, Version003)
	ProviderSentryMST_MN_0_1_0 = NewModelKey(ProviderSentryMST, us_states.MN, Version010)
	ProviderSentryMST_MN_0_1_1 = NewModelKey(ProviderSentryMST, us_states.MN, Version011)
	ProviderSentryMST_MN_0_1_2 = NewModelKey(ProviderSentryMST, us_states.MN, Version012)
	ProviderSentryMST_MN_0_2_0 = NewModelKey(ProviderSentryMST, us_states.MN, Version020)
	ProviderSentryMST_MN_0_2_1 = NewModelKey(ProviderSentryMST, us_states.MN, Version021)
	ProviderSentryMST_MN_0_2_2 = NewModelKey(ProviderSentryMST, us_states.MN, Version022)
	ProviderSentryMST_MN_0_3_0 = NewModelKey(ProviderSentryMST, us_states.MN, Version030)
	ProviderSentryMST_MN_0_3_1 = NewModelKey(ProviderSentryMST, us_states.MN, Version031)
	ProviderSentryMST_MN_0_3_2 = NewModelKey(ProviderSentryMST, us_states.MN, Version032)
	ProviderSentryMST_MN_0_3_3 = NewModelKey(ProviderSentryMST, us_states.MN, Version033)
	ProviderSentryMST_MN_0_4_0 = NewModelKey(ProviderSentryMST, us_states.MN, Version040)
	ProviderSentryMST_MN_0_4_1 = NewModelKey(ProviderSentryMST, us_states.MN, Version041)
	ProviderSentryMST_MN_0_4_2 = NewModelKey(ProviderSentryMST, us_states.MN, Version042)
	ProviderSentryMST_MN_0_4_3 = NewModelKey(ProviderSentryMST, us_states.MN, Version043)
	ProviderSentryMST_MN_0_4_4 = NewModelKey(ProviderSentryMST, us_states.MN, Version044)

	ProviderSentryMST_MO_0_0_1 = NewModelKey(ProviderSentryMST, us_states.MO, Version001)
	ProviderSentryMST_MO_0_0_3 = NewModelKey(ProviderSentryMST, us_states.MO, Version003)
	ProviderSentryMST_MO_0_1_0 = NewModelKey(ProviderSentryMST, us_states.MO, Version010)
	ProviderSentryMST_MO_0_1_1 = NewModelKey(ProviderSentryMST, us_states.MO, Version011)
	ProviderSentryMST_MO_0_2_0 = NewModelKey(ProviderSentryMST, us_states.MO, Version020)
	ProviderSentryMST_MO_0_2_1 = NewModelKey(ProviderSentryMST, us_states.MO, Version021)
	ProviderSentryMST_MO_0_2_2 = NewModelKey(ProviderSentryMST, us_states.MO, Version022)
	ProviderSentryMST_MO_0_3_0 = NewModelKey(ProviderSentryMST, us_states.MO, Version030)
	ProviderSentryMST_MO_0_3_1 = NewModelKey(ProviderSentryMST, us_states.MO, Version031)
	ProviderSentryMST_MO_0_3_2 = NewModelKey(ProviderSentryMST, us_states.MO, Version032)
	ProviderSentryMST_MO_0_3_3 = NewModelKey(ProviderSentryMST, us_states.MO, Version033)
	ProviderSentryMST_MO_0_4_0 = NewModelKey(ProviderSentryMST, us_states.MO, Version040)
	ProviderSentryMST_MO_0_4_1 = NewModelKey(ProviderSentryMST, us_states.MO, Version041)
	ProviderSentryMST_MO_0_4_2 = NewModelKey(ProviderSentryMST, us_states.MO, Version042)
	ProviderSentryMST_MO_0_4_3 = NewModelKey(ProviderSentryMST, us_states.MO, Version043)
	ProviderSentryMST_MO_0_4_4 = NewModelKey(ProviderSentryMST, us_states.MO, Version044)

	ProviderSentryMST_NC_0_0_1 = NewModelKey(ProviderSentryMST, us_states.NC, Version001)
	ProviderSentryMST_NC_0_0_3 = NewModelKey(ProviderSentryMST, us_states.NC, Version003)
	ProviderSentryMST_NC_0_1_0 = NewModelKey(ProviderSentryMST, us_states.NC, Version010)
	ProviderSentryMST_NC_0_1_1 = NewModelKey(ProviderSentryMST, us_states.NC, Version011)
	ProviderSentryMST_NC_0_2_0 = NewModelKey(ProviderSentryMST, us_states.NC, Version020)
	ProviderSentryMST_NC_0_2_1 = NewModelKey(ProviderSentryMST, us_states.NC, Version021)
	ProviderSentryMST_NC_0_2_2 = NewModelKey(ProviderSentryMST, us_states.NC, Version022)
	ProviderSentryMST_NC_0_3_0 = NewModelKey(ProviderSentryMST, us_states.NC, Version030)
	ProviderSentryMST_NC_0_3_1 = NewModelKey(ProviderSentryMST, us_states.NC, Version031)
	ProviderSentryMST_NC_0_3_2 = NewModelKey(ProviderSentryMST, us_states.NC, Version032)
	ProviderSentryMST_NC_0_3_3 = NewModelKey(ProviderSentryMST, us_states.NC, Version033)
	ProviderSentryMST_NC_0_3_4 = NewModelKey(ProviderSentryMST, us_states.NC, Version034)
	ProviderSentryMST_NC_0_4_0 = NewModelKey(ProviderSentryMST, us_states.NC, Version040)
	ProviderSentryMST_NC_0_4_1 = NewModelKey(ProviderSentryMST, us_states.NC, Version041)
	ProviderSentryMST_NC_0_4_2 = NewModelKey(ProviderSentryMST, us_states.NC, Version042)
	ProviderSentryMST_NC_0_4_3 = NewModelKey(ProviderSentryMST, us_states.NC, Version043)
	ProviderSentryMST_NC_0_4_4 = NewModelKey(ProviderSentryMST, us_states.NC, Version044)

	ProviderSentryMST_NE_0_0_1 = NewModelKey(ProviderSentryMST, us_states.NE, Version001)
	ProviderSentryMST_NE_0_1_0 = NewModelKey(ProviderSentryMST, us_states.NE, Version010)
	ProviderSentryMST_NE_0_1_1 = NewModelKey(ProviderSentryMST, us_states.NE, Version011)
	ProviderSentryMST_NE_0_2_0 = NewModelKey(ProviderSentryMST, us_states.NE, Version020)
	ProviderSentryMST_NE_0_2_1 = NewModelKey(ProviderSentryMST, us_states.NE, Version021)
	ProviderSentryMST_NE_0_2_2 = NewModelKey(ProviderSentryMST, us_states.NE, Version022)
	ProviderSentryMST_NE_0_3_0 = NewModelKey(ProviderSentryMST, us_states.NE, Version030)
	ProviderSentryMST_NE_0_3_1 = NewModelKey(ProviderSentryMST, us_states.NE, Version031)
	ProviderSentryMST_NE_0_3_2 = NewModelKey(ProviderSentryMST, us_states.NE, Version032)
	ProviderSentryMST_NE_0_3_3 = NewModelKey(ProviderSentryMST, us_states.NE, Version033)
	ProviderSentryMST_NE_0_4_0 = NewModelKey(ProviderSentryMST, us_states.NE, Version040)
	ProviderSentryMST_NE_0_4_1 = NewModelKey(ProviderSentryMST, us_states.NE, Version041)
	ProviderSentryMST_NE_0_4_2 = NewModelKey(ProviderSentryMST, us_states.NE, Version042)
	ProviderSentryMST_NE_0_4_3 = NewModelKey(ProviderSentryMST, us_states.NE, Version043)
	ProviderSentryMST_NE_0_4_4 = NewModelKey(ProviderSentryMST, us_states.NE, Version044)

	ProviderSentryMST_NM_0_1_0 = NewModelKey(ProviderSentryMST, us_states.NM, Version010)
	ProviderSentryMST_NM_0_1_1 = NewModelKey(ProviderSentryMST, us_states.NM, Version011)
	ProviderSentryMST_NM_0_1_2 = NewModelKey(ProviderSentryMST, us_states.NM, Version012)
	ProviderSentryMST_NM_0_2_0 = NewModelKey(ProviderSentryMST, us_states.NM, Version020)
	ProviderSentryMST_NM_0_2_1 = NewModelKey(ProviderSentryMST, us_states.NM, Version021)
	ProviderSentryMST_NM_0_2_2 = NewModelKey(ProviderSentryMST, us_states.NM, Version022)
	ProviderSentryMST_NM_0_3_0 = NewModelKey(ProviderSentryMST, us_states.NM, Version030)
	ProviderSentryMST_NM_0_3_1 = NewModelKey(ProviderSentryMST, us_states.NM, Version031)
	ProviderSentryMST_NM_0_3_2 = NewModelKey(ProviderSentryMST, us_states.NM, Version032)
	ProviderSentryMST_NM_0_3_3 = NewModelKey(ProviderSentryMST, us_states.NM, Version033)
	ProviderSentryMST_NM_0_4_0 = NewModelKey(ProviderSentryMST, us_states.NM, Version040)
	ProviderSentryMST_NM_0_4_1 = NewModelKey(ProviderSentryMST, us_states.NM, Version041)
	ProviderSentryMST_NM_0_4_2 = NewModelKey(ProviderSentryMST, us_states.NM, Version042)
	ProviderSentryMST_NM_0_4_3 = NewModelKey(ProviderSentryMST, us_states.NM, Version043)
	ProviderSentryMST_NM_0_4_4 = NewModelKey(ProviderSentryMST, us_states.NM, Version044)

	ProviderSentryMST_NV_0_0_1 = NewModelKey(ProviderSentryMST, us_states.NV, Version001)
	ProviderSentryMST_NV_0_0_3 = NewModelKey(ProviderSentryMST, us_states.NV, Version003)
	ProviderSentryMST_NV_0_1_0 = NewModelKey(ProviderSentryMST, us_states.NV, Version010)
	ProviderSentryMST_NV_0_1_1 = NewModelKey(ProviderSentryMST, us_states.NV, Version011)
	ProviderSentryMST_NV_0_1_2 = NewModelKey(ProviderSentryMST, us_states.NV, Version012)
	ProviderSentryMST_NV_0_2_0 = NewModelKey(ProviderSentryMST, us_states.NV, Version020)
	ProviderSentryMST_NV_0_2_1 = NewModelKey(ProviderSentryMST, us_states.NV, Version021)
	ProviderSentryMST_NV_0_2_2 = NewModelKey(ProviderSentryMST, us_states.NV, Version022)
	ProviderSentryMST_NV_0_3_0 = NewModelKey(ProviderSentryMST, us_states.NV, Version030)
	ProviderSentryMST_NV_0_3_1 = NewModelKey(ProviderSentryMST, us_states.NV, Version031)
	ProviderSentryMST_NV_0_3_2 = NewModelKey(ProviderSentryMST, us_states.NV, Version032)
	ProviderSentryMST_NV_0_3_3 = NewModelKey(ProviderSentryMST, us_states.NV, Version033)
	ProviderSentryMST_NV_0_4_0 = NewModelKey(ProviderSentryMST, us_states.NV, Version040)
	ProviderSentryMST_NV_0_4_1 = NewModelKey(ProviderSentryMST, us_states.NV, Version041)
	ProviderSentryMST_NV_0_4_2 = NewModelKey(ProviderSentryMST, us_states.NV, Version042)
	ProviderSentryMST_NV_0_4_3 = NewModelKey(ProviderSentryMST, us_states.NV, Version043)
	ProviderSentryMST_NV_0_4_4 = NewModelKey(ProviderSentryMST, us_states.NV, Version044)

	ProviderSentryMST_OH_0_0_1 = NewModelKey(ProviderSentryMST, us_states.OH, Version001)
	ProviderSentryMST_OH_0_0_3 = NewModelKey(ProviderSentryMST, us_states.OH, Version003)
	ProviderSentryMST_OH_0_1_0 = NewModelKey(ProviderSentryMST, us_states.OH, Version010)
	ProviderSentryMST_OH_0_1_1 = NewModelKey(ProviderSentryMST, us_states.OH, Version011)
	ProviderSentryMST_OH_0_2_0 = NewModelKey(ProviderSentryMST, us_states.OH, Version020)
	ProviderSentryMST_OH_0_2_1 = NewModelKey(ProviderSentryMST, us_states.OH, Version021)
	ProviderSentryMST_OH_0_2_2 = NewModelKey(ProviderSentryMST, us_states.OH, Version022)
	ProviderSentryMST_OH_0_3_0 = NewModelKey(ProviderSentryMST, us_states.OH, Version030)
	ProviderSentryMST_OH_0_3_1 = NewModelKey(ProviderSentryMST, us_states.OH, Version031)
	ProviderSentryMST_OH_0_3_2 = NewModelKey(ProviderSentryMST, us_states.OH, Version032)
	ProviderSentryMST_OH_0_3_3 = NewModelKey(ProviderSentryMST, us_states.OH, Version033)
	ProviderSentryMST_OH_0_4_0 = NewModelKey(ProviderSentryMST, us_states.OH, Version040)
	ProviderSentryMST_OH_0_4_1 = NewModelKey(ProviderSentryMST, us_states.OH, Version041)
	ProviderSentryMST_OH_0_4_2 = NewModelKey(ProviderSentryMST, us_states.OH, Version042)
	ProviderSentryMST_OH_0_4_3 = NewModelKey(ProviderSentryMST, us_states.OH, Version043)
	ProviderSentryMST_OH_0_4_4 = NewModelKey(ProviderSentryMST, us_states.OH, Version044)

	ProviderSentryMST_OK_0_0_1 = NewModelKey(ProviderSentryMST, us_states.OK, Version001)
	ProviderSentryMST_OK_0_1_0 = NewModelKey(ProviderSentryMST, us_states.OK, Version010)
	ProviderSentryMST_OK_0_1_1 = NewModelKey(ProviderSentryMST, us_states.OK, Version011)
	ProviderSentryMST_OK_0_2_0 = NewModelKey(ProviderSentryMST, us_states.OK, Version020)
	ProviderSentryMST_OK_0_2_1 = NewModelKey(ProviderSentryMST, us_states.OK, Version021)
	ProviderSentryMST_OK_0_2_2 = NewModelKey(ProviderSentryMST, us_states.OK, Version022)
	ProviderSentryMST_OK_0_3_0 = NewModelKey(ProviderSentryMST, us_states.OK, Version030)
	ProviderSentryMST_OK_0_3_1 = NewModelKey(ProviderSentryMST, us_states.OK, Version031)
	ProviderSentryMST_OK_0_3_2 = NewModelKey(ProviderSentryMST, us_states.OK, Version032)
	ProviderSentryMST_OK_0_3_3 = NewModelKey(ProviderSentryMST, us_states.OK, Version033)
	ProviderSentryMST_OK_0_4_0 = NewModelKey(ProviderSentryMST, us_states.OK, Version040)
	ProviderSentryMST_OK_0_4_1 = NewModelKey(ProviderSentryMST, us_states.OK, Version041)
	ProviderSentryMST_OK_0_4_2 = NewModelKey(ProviderSentryMST, us_states.OK, Version042)
	ProviderSentryMST_OK_0_4_3 = NewModelKey(ProviderSentryMST, us_states.OK, Version043)
	ProviderSentryMST_OK_0_4_4 = NewModelKey(ProviderSentryMST, us_states.OK, Version044)

	ProviderSentryMST_OR_0_0_1 = NewModelKey(ProviderSentryMST, us_states.OR, Version001)
	ProviderSentryMST_OR_0_1_0 = NewModelKey(ProviderSentryMST, us_states.OR, Version010)
	ProviderSentryMST_OR_0_1_1 = NewModelKey(ProviderSentryMST, us_states.OR, Version011)
	ProviderSentryMST_OR_0_2_0 = NewModelKey(ProviderSentryMST, us_states.OR, Version020)
	ProviderSentryMST_OR_0_2_1 = NewModelKey(ProviderSentryMST, us_states.OR, Version021)
	ProviderSentryMST_OR_0_2_2 = NewModelKey(ProviderSentryMST, us_states.OR, Version022)
	ProviderSentryMST_OR_0_3_0 = NewModelKey(ProviderSentryMST, us_states.OR, Version030)
	ProviderSentryMST_OR_0_3_1 = NewModelKey(ProviderSentryMST, us_states.OR, Version031)
	ProviderSentryMST_OR_0_3_2 = NewModelKey(ProviderSentryMST, us_states.OR, Version032)
	ProviderSentryMST_OR_0_3_3 = NewModelKey(ProviderSentryMST, us_states.OR, Version033)
	ProviderSentryMST_OR_0_4_0 = NewModelKey(ProviderSentryMST, us_states.OR, Version040)
	ProviderSentryMST_OR_0_4_1 = NewModelKey(ProviderSentryMST, us_states.OR, Version041)
	ProviderSentryMST_OR_0_4_2 = NewModelKey(ProviderSentryMST, us_states.OR, Version042)
	ProviderSentryMST_OR_0_4_3 = NewModelKey(ProviderSentryMST, us_states.OR, Version043)
	ProviderSentryMST_OR_0_4_4 = NewModelKey(ProviderSentryMST, us_states.OR, Version044)

	ProviderSentryMST_PA_0_0_1 = NewModelKey(ProviderSentryMST, us_states.PA, Version001)
	ProviderSentryMST_PA_0_0_2 = NewModelKey(ProviderSentryMST, us_states.PA, Version002)
	ProviderSentryMST_PA_0_1_0 = NewModelKey(ProviderSentryMST, us_states.PA, Version010)
	ProviderSentryMST_PA_0_1_1 = NewModelKey(ProviderSentryMST, us_states.PA, Version011)
	ProviderSentryMST_PA_0_2_0 = NewModelKey(ProviderSentryMST, us_states.PA, Version020)
	ProviderSentryMST_PA_0_2_1 = NewModelKey(ProviderSentryMST, us_states.PA, Version021)
	ProviderSentryMST_PA_0_2_2 = NewModelKey(ProviderSentryMST, us_states.PA, Version022)
	ProviderSentryMST_PA_0_3_0 = NewModelKey(ProviderSentryMST, us_states.PA, Version030)
	ProviderSentryMST_PA_0_3_1 = NewModelKey(ProviderSentryMST, us_states.PA, Version031)
	ProviderSentryMST_PA_0_3_2 = NewModelKey(ProviderSentryMST, us_states.PA, Version032)
	ProviderSentryMST_PA_0_3_3 = NewModelKey(ProviderSentryMST, us_states.PA, Version033)
	ProviderSentryMST_PA_0_4_0 = NewModelKey(ProviderSentryMST, us_states.PA, Version040)
	ProviderSentryMST_PA_0_4_1 = NewModelKey(ProviderSentryMST, us_states.PA, Version041)
	ProviderSentryMST_PA_0_4_2 = NewModelKey(ProviderSentryMST, us_states.PA, Version042)
	ProviderSentryMST_PA_0_4_3 = NewModelKey(ProviderSentryMST, us_states.PA, Version043)
	ProviderSentryMST_PA_0_4_4 = NewModelKey(ProviderSentryMST, us_states.PA, Version044)

	ProviderSentryMST_SC_0_0_2 = NewModelKey(ProviderSentryMST, us_states.SC, Version002)
	ProviderSentryMST_SC_0_1_0 = NewModelKey(ProviderSentryMST, us_states.SC, Version010)
	ProviderSentryMST_SC_0_1_1 = NewModelKey(ProviderSentryMST, us_states.SC, Version011)
	ProviderSentryMST_SC_0_2_0 = NewModelKey(ProviderSentryMST, us_states.SC, Version020)
	ProviderSentryMST_SC_0_2_1 = NewModelKey(ProviderSentryMST, us_states.SC, Version021)
	ProviderSentryMST_SC_0_2_2 = NewModelKey(ProviderSentryMST, us_states.SC, Version022)
	ProviderSentryMST_SC_0_3_0 = NewModelKey(ProviderSentryMST, us_states.SC, Version030)
	ProviderSentryMST_SC_0_3_1 = NewModelKey(ProviderSentryMST, us_states.SC, Version031)
	ProviderSentryMST_SC_0_3_2 = NewModelKey(ProviderSentryMST, us_states.SC, Version032)
	ProviderSentryMST_SC_0_3_3 = NewModelKey(ProviderSentryMST, us_states.SC, Version033)
	ProviderSentryMST_SC_0_4_0 = NewModelKey(ProviderSentryMST, us_states.SC, Version040)
	ProviderSentryMST_SC_0_4_1 = NewModelKey(ProviderSentryMST, us_states.SC, Version041)
	ProviderSentryMST_SC_0_4_2 = NewModelKey(ProviderSentryMST, us_states.SC, Version042)
	ProviderSentryMST_SC_0_4_3 = NewModelKey(ProviderSentryMST, us_states.SC, Version043)
	ProviderSentryMST_SC_0_4_4 = NewModelKey(ProviderSentryMST, us_states.SC, Version044)

	ProviderSentryMST_TN_0_0_1 = NewModelKey(ProviderSentryMST, us_states.TN, Version001)
	ProviderSentryMST_TN_0_0_3 = NewModelKey(ProviderSentryMST, us_states.TN, Version003)
	ProviderSentryMST_TN_0_1_0 = NewModelKey(ProviderSentryMST, us_states.TN, Version010)
	ProviderSentryMST_TN_0_1_1 = NewModelKey(ProviderSentryMST, us_states.TN, Version011)
	ProviderSentryMST_TN_0_2_0 = NewModelKey(ProviderSentryMST, us_states.TN, Version020)
	ProviderSentryMST_TN_0_2_1 = NewModelKey(ProviderSentryMST, us_states.TN, Version021)
	ProviderSentryMST_TN_0_2_2 = NewModelKey(ProviderSentryMST, us_states.TN, Version022)
	ProviderSentryMST_TN_0_3_0 = NewModelKey(ProviderSentryMST, us_states.TN, Version030)
	ProviderSentryMST_TN_0_3_1 = NewModelKey(ProviderSentryMST, us_states.TN, Version031)
	ProviderSentryMST_TN_0_3_2 = NewModelKey(ProviderSentryMST, us_states.TN, Version032)
	ProviderSentryMST_TN_0_3_3 = NewModelKey(ProviderSentryMST, us_states.TN, Version033)
	ProviderSentryMST_TN_0_4_0 = NewModelKey(ProviderSentryMST, us_states.TN, Version040)
	ProviderSentryMST_TN_0_4_1 = NewModelKey(ProviderSentryMST, us_states.TN, Version041)
	ProviderSentryMST_TN_0_4_2 = NewModelKey(ProviderSentryMST, us_states.TN, Version042)
	ProviderSentryMST_TN_0_4_3 = NewModelKey(ProviderSentryMST, us_states.TN, Version043)
	ProviderSentryMST_TN_0_4_4 = NewModelKey(ProviderSentryMST, us_states.TN, Version044)

	ProviderSentryMST_TX_0_1_0 = NewModelKey(ProviderSentryMST, us_states.TX, Version010)
	ProviderSentryMST_TX_0_1_1 = NewModelKey(ProviderSentryMST, us_states.TX, Version011)
	ProviderSentryMST_TX_0_2_0 = NewModelKey(ProviderSentryMST, us_states.TX, Version020)
	ProviderSentryMST_TX_0_2_1 = NewModelKey(ProviderSentryMST, us_states.TX, Version021)
	ProviderSentryMST_TX_0_2_2 = NewModelKey(ProviderSentryMST, us_states.TX, Version022)
	ProviderSentryMST_TX_0_3_0 = NewModelKey(ProviderSentryMST, us_states.TX, Version030)
	ProviderSentryMST_TX_0_3_1 = NewModelKey(ProviderSentryMST, us_states.TX, Version031)
	ProviderSentryMST_TX_0_3_2 = NewModelKey(ProviderSentryMST, us_states.TX, Version032)
	ProviderSentryMST_TX_0_3_3 = NewModelKey(ProviderSentryMST, us_states.TX, Version033)
	ProviderSentryMST_TX_0_4_0 = NewModelKey(ProviderSentryMST, us_states.TX, Version040)
	ProviderSentryMST_TX_0_4_1 = NewModelKey(ProviderSentryMST, us_states.TX, Version041)
	ProviderSentryMST_TX_0_4_2 = NewModelKey(ProviderSentryMST, us_states.TX, Version042)
	ProviderSentryMST_TX_0_4_3 = NewModelKey(ProviderSentryMST, us_states.TX, Version043)
	ProviderSentryMST_TX_0_4_4 = NewModelKey(ProviderSentryMST, us_states.TX, Version044)

	ProviderSentryMST_UT_0_0_3 = NewModelKey(ProviderSentryMST, us_states.UT, Version003)
	ProviderSentryMST_UT_0_1_0 = NewModelKey(ProviderSentryMST, us_states.UT, Version010)
	ProviderSentryMST_UT_0_1_1 = NewModelKey(ProviderSentryMST, us_states.UT, Version011)
	ProviderSentryMST_UT_0_2_0 = NewModelKey(ProviderSentryMST, us_states.UT, Version020)
	ProviderSentryMST_UT_0_2_1 = NewModelKey(ProviderSentryMST, us_states.UT, Version021)
	ProviderSentryMST_UT_0_2_2 = NewModelKey(ProviderSentryMST, us_states.UT, Version022)
	ProviderSentryMST_UT_0_2_3 = NewModelKey(ProviderSentryMST, us_states.UT, Version023)
	ProviderSentryMST_UT_0_3_0 = NewModelKey(ProviderSentryMST, us_states.UT, Version030)
	ProviderSentryMST_UT_0_3_1 = NewModelKey(ProviderSentryMST, us_states.UT, Version031)
	ProviderSentryMST_UT_0_3_2 = NewModelKey(ProviderSentryMST, us_states.UT, Version032)
	ProviderSentryMST_UT_0_3_3 = NewModelKey(ProviderSentryMST, us_states.UT, Version033)
	ProviderSentryMST_UT_0_4_0 = NewModelKey(ProviderSentryMST, us_states.UT, Version040)
	ProviderSentryMST_UT_0_4_1 = NewModelKey(ProviderSentryMST, us_states.UT, Version041)
	ProviderSentryMST_UT_0_4_2 = NewModelKey(ProviderSentryMST, us_states.UT, Version042)
	ProviderSentryMST_UT_0_4_3 = NewModelKey(ProviderSentryMST, us_states.UT, Version043)
	ProviderSentryMST_UT_0_4_4 = NewModelKey(ProviderSentryMST, us_states.UT, Version044)

	ProviderSentryMST_WA_0_1_0 = NewModelKey(ProviderSentryMST, us_states.WA, Version010)
	ProviderSentryMST_WA_0_1_1 = NewModelKey(ProviderSentryMST, us_states.WA, Version011)
	ProviderSentryMST_WA_0_1_2 = NewModelKey(ProviderSentryMST, us_states.WA, Version012)
	ProviderSentryMST_WA_0_1_3 = NewModelKey(ProviderSentryMST, us_states.WA, Version013)
	ProviderSentryMST_WA_0_1_4 = NewModelKey(ProviderSentryMST, us_states.WA, Version014)
	ProviderSentryMST_WA_0_1_5 = NewModelKey(ProviderSentryMST, us_states.WA, Version015)
	ProviderSentryMST_WA_0_1_6 = NewModelKey(ProviderSentryMST, us_states.WA, Version016)
	ProviderSentryMST_WA_0_2_0 = NewModelKey(ProviderSentryMST, us_states.WA, Version020)
	ProviderSentryMST_WA_0_2_1 = NewModelKey(ProviderSentryMST, us_states.WA, Version021)
	ProviderSentryMST_WA_0_2_2 = NewModelKey(ProviderSentryMST, us_states.WA, Version022)
	ProviderSentryMST_WA_0_2_3 = NewModelKey(ProviderSentryMST, us_states.WA, Version023)
	ProviderSentryMST_WA_0_2_4 = NewModelKey(ProviderSentryMST, us_states.WA, Version024)

	ProviderSentryMST_WI_0_0_1 = NewModelKey(ProviderSentryMST, us_states.WI, Version001)
	ProviderSentryMST_WI_0_0_3 = NewModelKey(ProviderSentryMST, us_states.WI, Version003)
	ProviderSentryMST_WI_0_1_0 = NewModelKey(ProviderSentryMST, us_states.WI, Version010)
	ProviderSentryMST_WI_0_1_1 = NewModelKey(ProviderSentryMST, us_states.WI, Version011)
	ProviderSentryMST_WI_0_2_0 = NewModelKey(ProviderSentryMST, us_states.WI, Version020)
	ProviderSentryMST_WI_0_2_1 = NewModelKey(ProviderSentryMST, us_states.WI, Version021)
	ProviderSentryMST_WI_0_2_2 = NewModelKey(ProviderSentryMST, us_states.WI, Version022)
	ProviderSentryMST_WI_0_3_0 = NewModelKey(ProviderSentryMST, us_states.WI, Version030)
	ProviderSentryMST_WI_0_3_1 = NewModelKey(ProviderSentryMST, us_states.WI, Version031)
	ProviderSentryMST_WI_0_3_2 = NewModelKey(ProviderSentryMST, us_states.WI, Version032)
	ProviderSentryMST_WI_0_3_3 = NewModelKey(ProviderSentryMST, us_states.WI, Version033)
	ProviderSentryMST_WI_0_4_0 = NewModelKey(ProviderSentryMST, us_states.WI, Version040)
	ProviderSentryMST_WI_0_4_1 = NewModelKey(ProviderSentryMST, us_states.WI, Version041)
	ProviderSentryMST_WI_0_4_2 = NewModelKey(ProviderSentryMST, us_states.WI, Version042)
	ProviderSentryMST_WI_0_4_3 = NewModelKey(ProviderSentryMST, us_states.WI, Version043)
	ProviderSentryMST_WI_0_4_4 = NewModelKey(ProviderSentryMST, us_states.WI, Version044)

	ProviderProgressive_AZ_2_0_1 = NewModelKey(ProviderProgressive, us_states.AZ, Version201)

	ProviderProgressive_GA_0_0_0 = NewModelKey(ProviderProgressive, us_states.GA, Version000)
	ProviderProgressive_GA_0_1_0 = NewModelKey(ProviderProgressive, us_states.GA, Version010)
	ProviderProgressive_GA_1_0_0 = NewModelKey(ProviderProgressive, us_states.GA, Version100)
	ProviderProgressive_GA_1_0_1 = NewModelKey(ProviderProgressive, us_states.GA, Version101)
	ProviderProgressive_GA_2_0_0 = NewModelKey(ProviderProgressive, us_states.GA, Version200)
	ProviderProgressive_GA_2_0_1 = NewModelKey(ProviderProgressive, us_states.GA, Version201)
	ProviderProgressive_GA_2_0_2 = NewModelKey(ProviderProgressive, us_states.GA, Version202)

	ProviderProgressive_IA_2_0_0 = NewModelKey(ProviderProgressive, us_states.IA, Version200)
	ProviderProgressive_IA_2_0_1 = NewModelKey(ProviderProgressive, us_states.IA, Version201)

	ProviderProgressive_IL_0_0_0 = NewModelKey(ProviderProgressive, us_states.IL, Version000)
	ProviderProgressive_IL_0_0_1 = NewModelKey(ProviderProgressive, us_states.IL, Version001)
	ProviderProgressive_IL_0_0_2 = NewModelKey(ProviderProgressive, us_states.IL, Version002)
	ProviderProgressive_IL_0_1_0 = NewModelKey(ProviderProgressive, us_states.IL, Version010)
	ProviderProgressive_IL_0_1_1 = NewModelKey(ProviderProgressive, us_states.IL, Version011)
	ProviderProgressive_IL_0_1_2 = NewModelKey(ProviderProgressive, us_states.IL, Version012)
	ProviderProgressive_IL_1_0_0 = NewModelKey(ProviderProgressive, us_states.IL, Version100)
	ProviderProgressive_IL_2_0_0 = NewModelKey(ProviderProgressive, us_states.IL, Version200)
	ProviderProgressive_IL_2_0_1 = NewModelKey(ProviderProgressive, us_states.IL, Version201)
	ProviderProgressive_IL_2_0_2 = NewModelKey(ProviderProgressive, us_states.IL, Version202)

	ProviderProgressive_IN_0_0_0 = NewModelKey(ProviderProgressive, us_states.IN, Version000)
	ProviderProgressive_IN_0_0_1 = NewModelKey(ProviderProgressive, us_states.IN, Version001)
	ProviderProgressive_IN_0_0_2 = NewModelKey(ProviderProgressive, us_states.IN, Version002)
	ProviderProgressive_IN_0_0_3 = NewModelKey(ProviderProgressive, us_states.IN, Version003)
	ProviderProgressive_IN_0_1_1 = NewModelKey(ProviderProgressive, us_states.IN, Version011)
	ProviderProgressive_IN_1_0_0 = NewModelKey(ProviderProgressive, us_states.IN, Version100)
	ProviderProgressive_IN_1_0_1 = NewModelKey(ProviderProgressive, us_states.IN, Version101)
	ProviderProgressive_IN_2_0_2 = NewModelKey(ProviderProgressive, us_states.IN, Version202)

	ProviderProgressive_MI_0_0_0 = NewModelKey(ProviderProgressive, us_states.MI, Version000)
	ProviderProgressive_MI_0_0_1 = NewModelKey(ProviderProgressive, us_states.MI, Version001)
	ProviderProgressive_MI_0_0_2 = NewModelKey(ProviderProgressive, us_states.MI, Version002)
	ProviderProgressive_MI_0_1_0 = NewModelKey(ProviderProgressive, us_states.MI, Version010)
	ProviderProgressive_MI_0_1_1 = NewModelKey(ProviderProgressive, us_states.MI, Version011)
	ProviderProgressive_MI_0_1_2 = NewModelKey(ProviderProgressive, us_states.MI, Version012)
	ProviderProgressive_MI_1_0_0 = NewModelKey(ProviderProgressive, us_states.MI, Version100)
	ProviderProgressive_MI_2_0_0 = NewModelKey(ProviderProgressive, us_states.MI, Version200)
	ProviderProgressive_MI_2_0_1 = NewModelKey(ProviderProgressive, us_states.MI, Version201)
	ProviderProgressive_MI_2_0_2 = NewModelKey(ProviderProgressive, us_states.MI, Version202)

	ProviderProgressive_MN_0_0_0 = NewModelKey(ProviderProgressive, us_states.MN, Version000)
	ProviderProgressive_MN_0_0_1 = NewModelKey(ProviderProgressive, us_states.MN, Version001)
	ProviderProgressive_MN_0_0_2 = NewModelKey(ProviderProgressive, us_states.MN, Version002)
	ProviderProgressive_MN_1_0_0 = NewModelKey(ProviderProgressive, us_states.MN, Version100)

	ProviderProgressive_MO_0_0_0 = NewModelKey(ProviderProgressive, us_states.MO, Version000)
	ProviderProgressive_MO_0_0_1 = NewModelKey(ProviderProgressive, us_states.MO, Version001)
	ProviderProgressive_MO_0_0_2 = NewModelKey(ProviderProgressive, us_states.MO, Version002)
	ProviderProgressive_MO_0_0_3 = NewModelKey(ProviderProgressive, us_states.MO, Version003)
	ProviderProgressive_MO_0_1_1 = NewModelKey(ProviderProgressive, us_states.MO, Version011)
	ProviderProgressive_MO_1_0_0 = NewModelKey(ProviderProgressive, us_states.MO, Version100)
	ProviderProgressive_MO_1_0_1 = NewModelKey(ProviderProgressive, us_states.MO, Version101)
	ProviderProgressive_MO_2_0_2 = NewModelKey(ProviderProgressive, us_states.MO, Version202)

	ProviderProgressive_NC_0_0_0 = NewModelKey(ProviderProgressive, us_states.NC, Version000)
	ProviderProgressive_NC_0_1_0 = NewModelKey(ProviderProgressive, us_states.NC, Version010)
	ProviderProgressive_NC_0_1_1 = NewModelKey(ProviderProgressive, us_states.NC, Version011)
	ProviderProgressive_NC_1_0_0 = NewModelKey(ProviderProgressive, us_states.NC, Version100)
	ProviderProgressive_NC_1_0_1 = NewModelKey(ProviderProgressive, us_states.NC, Version101)
	ProviderProgressive_NC_2_0_2 = NewModelKey(ProviderProgressive, us_states.NC, Version202)

	ProviderProgressive_NV_2_0_1 = NewModelKey(ProviderProgressive, us_states.NV, Version201)

	ProviderProgressive_OH_0_0_0 = NewModelKey(ProviderProgressive, us_states.OH, Version000)
	ProviderProgressive_OH_0_0_1 = NewModelKey(ProviderProgressive, us_states.OH, Version001)
	ProviderProgressive_OH_0_0_2 = NewModelKey(ProviderProgressive, us_states.OH, Version002)
	ProviderProgressive_OH_0_0_3 = NewModelKey(ProviderProgressive, us_states.OH, Version003)
	ProviderProgressive_OH_0_1_1 = NewModelKey(ProviderProgressive, us_states.OH, Version011)
	ProviderProgressive_OH_1_0_0 = NewModelKey(ProviderProgressive, us_states.OH, Version100)
	ProviderProgressive_OH_1_0_1 = NewModelKey(ProviderProgressive, us_states.OH, Version101)
	ProviderProgressive_OH_2_0_2 = NewModelKey(ProviderProgressive, us_states.OH, Version202)

	ProviderProgressive_PA_0_0_0 = NewModelKey(ProviderProgressive, us_states.PA, Version000)
	ProviderProgressive_PA_0_1_0 = NewModelKey(ProviderProgressive, us_states.PA, Version010)
	ProviderProgressive_PA_1_0_0 = NewModelKey(ProviderProgressive, us_states.PA, Version100)
	ProviderProgressive_PA_1_0_1 = NewModelKey(ProviderProgressive, us_states.PA, Version101)
	ProviderProgressive_PA_2_0_2 = NewModelKey(ProviderProgressive, us_states.PA, Version202)

	ProviderProgressive_SC_0_0_0 = NewModelKey(ProviderProgressive, us_states.SC, Version000)
	ProviderProgressive_SC_0_1_0 = NewModelKey(ProviderProgressive, us_states.SC, Version010)
	ProviderProgressive_SC_1_0_0 = NewModelKey(ProviderProgressive, us_states.SC, Version100)
	ProviderProgressive_SC_2_0_0 = NewModelKey(ProviderProgressive, us_states.SC, Version200)
	ProviderProgressive_SC_2_0_1 = NewModelKey(ProviderProgressive, us_states.SC, Version201)
	ProviderProgressive_SC_2_0_2 = NewModelKey(ProviderProgressive, us_states.SC, Version202)

	ProviderProgressive_TN_0_0_0 = NewModelKey(ProviderProgressive, us_states.TN, Version000)
	ProviderProgressive_TN_0_1_0 = NewModelKey(ProviderProgressive, us_states.TN, Version010)
	ProviderProgressive_TN_1_0_0 = NewModelKey(ProviderProgressive, us_states.TN, Version100)
	ProviderProgressive_TN_1_0_1 = NewModelKey(ProviderProgressive, us_states.TN, Version101)
	ProviderProgressive_TN_2_0_2 = NewModelKey(ProviderProgressive, us_states.TN, Version202)

	ProviderProgressive_WI_2_0_0 = NewModelKey(ProviderProgressive, us_states.WI, Version200)
	ProviderProgressive_WI_2_0_1 = NewModelKey(ProviderProgressive, us_states.WI, Version201)

	ProviderProgressiveSurplus_TX_1_0_0 = NewModelKey(ProviderProgressiveSurplus, us_states.TX, Version100)
	ProviderProgressiveSurplus_TX_2_0_0 = NewModelKey(ProviderProgressiveSurplus, us_states.TX, Version200)
	ProviderProgressiveSurplus_TX_2_0_1 = NewModelKey(ProviderProgressiveSurplus, us_states.TX, Version201)

	ProviderNico_AZ_0_0_0 = NewModelKey(ProviderNico, us_states.AZ, Version000)

	ProviderNico_GA_0_0_0 = NewModelKey(ProviderNico, us_states.GA, Version000)

	ProviderNico_IL_0_0_0 = NewModelKey(ProviderNico, us_states.IL, Version000)

	ProviderNico_IN_0_0_0 = NewModelKey(ProviderNico, us_states.IN, Version000)

	ProviderNico_OH_0_0_0 = NewModelKey(ProviderNico, us_states.OH, Version000)

	ProviderNico_PA_0_0_0 = NewModelKey(ProviderNico, us_states.PA, Version000)

	ProviderNico_TX_0_0_0 = NewModelKey(ProviderNico, us_states.TX, Version000)
)
