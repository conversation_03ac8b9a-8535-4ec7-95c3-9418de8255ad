// Code generated by goyacc - DO NOT EDIT.

package parser

import __yyfmt__ "fmt"

func setResult(l yyLexer, v interface{}) {
	l.(*Lexer).parseResult = v
}

type yySymType struct {
	yys                 int
	String              string
	LineNo              int
	Program             *Program
	ProgramPart         *ProgramPart
	Namespace           *Namespace
	NamespacePart       *NamespacePart
	NamespacePartList   []*NamespacePart
	NamespaceConfig     *NamespaceConfig
	Entry               *Entry
	EntryExtension      *EntryExtension
	EntryInclusion      *EntryInclusion
	FieldList           []*Field
	Field               *Field
	Check               *Check
	Clause              *Clause
	Compare             *Compare
	Member              *Member
	TypeContainer       *TypeContainer
	Enum                *Enum
	LookupTable         *LookupTable
	LookupTableIODec    *LookupTableIODeclaration
	LookupTableData     *LookupTableInlineData
	LookupTableOverride *LookupTableOverride
	Row                 *Row
	RowList             []*Row
	Cell                *Cell
	Property            *Property
	PropertyOverride    *PropertyOverride
	Expression          *Expression
	ExpList             []*Expression
	Disjunction         *Disjunction
	Conjunction         *Conjunction
	Equality            *Equality
	Relational          *Relational
	Negation            *Negation
	Addition            *Addition
	Factor              *Factor
	Atom                *Atom
	Cast                *Cast
	FunctionCall        *FunctionCall
	Switch              *Switch
	IfThenElse          *IfThenElse
	SwitchList          []*SwitchCase
	SwitchCase          *SwitchCase
	Literal             *Literal
	FieldAccess         *FieldAccess
	QName               *QName
	EnumValuesList      []string
	IODecList           []*LookupTableIODeclaration
	ExpLine             *ExpressionWithLineNumber
}

type yyXError struct {
	state, xsym int
}

const (
	yyDefault  = 57382
	yyEofCode  = 57344
	AND        = 57366
	B_CONSTANT = 57348
	CASE       = 57378
	DATA       = 57361
	DEREF      = 57365
	ELSE       = 57381
	ENTITY     = 57355
	ENUM_TAG   = 57375
	EXTEND     = 57363
	E_CONSTANT = 57349
	F_CONSTANT = 57347
	GTE        = 57370
	GUARD      = 57357
	IDENTIFIER = 57350
	IF         = 57379
	INCLUDES   = 57364
	INPUTS     = 57359
	IN_OP      = 57371
	I_CONSTANT = 57346
	LBRACE     = 57353
	LIST       = 57373
	LOOKUP     = 57376
	LTE        = 57369
	NAMESPACE  = 57358
	NEQ        = 57368
	NOTIN_OP   = 57372
	OR         = 57367
	OUTPUT     = 57352
	OUTPUTS    = 57360
	OVERRIDE   = 57362
	PROPERTY   = 57351
	RANGE      = 57374
	RBRACE     = 57354
	REQUIRE    = 57356
	SWITCH     = 57377
	THEN       = 57380
	yyErrCode  = 57345

	yyMaxDepth = 200
	yyTabOfs   = -111
)

var (
	yyPrec = map[int]int{}

	yyXLAT = map[int]int{
		57350: 0,   // IDENTIFIER (101x)
		57354: 1,   // RBRACE (88x)
		41:    2,   // ')' (59x)
		93:    3,   // ']' (52x)
		44:    4,   // ',' (51x)
		57349: 5,   // E_CONSTANT (51x)
		57348: 6,   // B_CONSTANT (48x)
		57347: 7,   // F_CONSTANT (48x)
		57346: 8,   // I_CONSTANT (48x)
		57378: 9,   // CASE (45x)
		64:    10,  // '@' (44x)
		57424: 11,  // q_name (43x)
		58:    12,  // ':' (42x)
		57373: 13,  // LIST (41x)
		57374: 14,  // RANGE (41x)
		57431: 15,  // type_container (41x)
		40:    16,  // '(' (40x)
		57367: 17,  // OR (40x)
		91:    18,  // '[' (39x)
		57366: 19,  // AND (39x)
		61:    20,  // '=' (38x)
		57408: 21,  // literal (38x)
		57368: 22,  // NEQ (38x)
		57352: 23,  // OUTPUT (37x)
		57351: 24,  // PROPERTY (37x)
		60:    25,  // '<' (36x)
		62:    26,  // '>' (36x)
		57370: 27,  // GTE (36x)
		57369: 28,  // LTE (36x)
		57384: 29,  // atom (35x)
		57385: 30,  // cast (35x)
		57403: 31,  // field_access (35x)
		57405: 32,  // function_call (35x)
		57379: 33,  // IF (35x)
		57407: 34,  // if_then_else (35x)
		57428: 35,  // switch (35x)
		57377: 36,  // SWITCH (35x)
		57358: 37,  // NAMESPACE (34x)
		57401: 38,  // factor (33x)
		57383: 39,  // addition (31x)
		33:    40,  // '!' (30x)
		57419: 41,  // negation (30x)
		57355: 42,  // ENTITY (28x)
		57376: 43,  // LOOKUP (28x)
		57344: 44,  // $end (27x)
		43:    45,  // '+' (27x)
		45:    46,  // '-' (27x)
		57375: 47,  // ENUM_TAG (27x)
		57363: 48,  // EXTEND (27x)
		57425: 49,  // relational (26x)
		42:    50,  // '*' (25x)
		47:    51,  // '/' (25x)
		57398: 52,  // equality (24x)
		57391: 53,  // conjunction (23x)
		57392: 54,  // disjunction (22x)
		57399: 55,  // expression (22x)
		57353: 56,  // LBRACE (21x)
		95:    57,  // '_' (14x)
		46:    58,  // '.' (6x)
		57387: 59,  // check (6x)
		57388: 60,  // check_list (6x)
		57386: 61,  // cell (3x)
		57365: 62,  // DEREF (3x)
		57400: 63,  // expression_list (3x)
		57422: 64,  // property (3x)
		57356: 65,  // REQUIRE (3x)
		57393: 66,  // entry (2x)
		57402: 67,  // field (2x)
		57404: 68,  // field_list (2x)
		57406: 69,  // guard (2x)
		57357: 70,  // GUARD (2x)
		57410: 71,  // lookup_io_dec (2x)
		57411: 72,  // lookup_io_dec_list (2x)
		57412: 73,  // lookup_table (2x)
		57415: 74,  // namespace (2x)
		57426: 75,  // row (2x)
		57389: 76,  // clause (1x)
		57390: 77,  // compare (1x)
		57361: 78,  // DATA (1x)
		57381: 79,  // ELSE (1x)
		57394: 80,  // entry_extension (1x)
		57395: 81,  // entry_inclusion (1x)
		57396: 82,  // enum (1x)
		57397: 83,  // enum_values_list (1x)
		57371: 84,  // IN_OP (1x)
		57364: 85,  // INCLUDES (1x)
		57359: 86,  // INPUTS (1x)
		57409: 87,  // lookup_data (1x)
		57413: 88,  // lookup_table_override (1x)
		57414: 89,  // membership (1x)
		57416: 90,  // namespace_config (1x)
		57417: 91,  // namespace_part (1x)
		57418: 92,  // namespace_part_list (1x)
		57372: 93,  // NOTIN_OP (1x)
		57360: 94,  // OUTPUTS (1x)
		57362: 95,  // OVERRIDE (1x)
		57420: 96,  // program (1x)
		57421: 97,  // program_part (1x)
		57423: 98,  // property_override (1x)
		57427: 99,  // row_list (1x)
		57429: 100, // switch_case (1x)
		57430: 101, // switch_case_list (1x)
		57380: 102, // THEN (1x)
		57382: 103, // $default (0x)
		57345: 104, // error (0x)
	}

	yySymNames = []string{
		"IDENTIFIER",
		"RBRACE",
		"')'",
		"']'",
		"','",
		"E_CONSTANT",
		"B_CONSTANT",
		"F_CONSTANT",
		"I_CONSTANT",
		"CASE",
		"'@'",
		"q_name",
		"':'",
		"LIST",
		"RANGE",
		"type_container",
		"'('",
		"OR",
		"'['",
		"AND",
		"'='",
		"literal",
		"NEQ",
		"OUTPUT",
		"PROPERTY",
		"'<'",
		"'>'",
		"GTE",
		"LTE",
		"atom",
		"cast",
		"field_access",
		"function_call",
		"IF",
		"if_then_else",
		"switch",
		"SWITCH",
		"NAMESPACE",
		"factor",
		"addition",
		"'!'",
		"negation",
		"ENTITY",
		"LOOKUP",
		"$end",
		"'+'",
		"'-'",
		"ENUM_TAG",
		"EXTEND",
		"relational",
		"'*'",
		"'/'",
		"equality",
		"conjunction",
		"disjunction",
		"expression",
		"LBRACE",
		"'_'",
		"'.'",
		"check",
		"check_list",
		"cell",
		"DEREF",
		"expression_list",
		"property",
		"REQUIRE",
		"entry",
		"field",
		"field_list",
		"guard",
		"GUARD",
		"lookup_io_dec",
		"lookup_io_dec_list",
		"lookup_table",
		"namespace",
		"row",
		"clause",
		"compare",
		"DATA",
		"ELSE",
		"entry_extension",
		"entry_inclusion",
		"enum",
		"enum_values_list",
		"IN_OP",
		"INCLUDES",
		"INPUTS",
		"lookup_data",
		"lookup_table_override",
		"membership",
		"namespace_config",
		"namespace_part",
		"namespace_part_list",
		"NOTIN_OP",
		"OUTPUTS",
		"OVERRIDE",
		"program",
		"program_part",
		"property_override",
		"row_list",
		"switch_case",
		"switch_case_list",
		"THEN",
		"$default",
		"error",
	}

	yyTokenLiteralStrings = map[int]string{}

	yyReductions = map[int]struct{ xsym, components int }{
		0:   {0, 1},
		1:   {96, 2},
		2:   {96, 0},
		3:   {97, 1},
		4:   {97, 1},
		5:   {97, 1},
		6:   {97, 1},
		7:   {97, 1},
		8:   {97, 1},
		9:   {97, 1},
		10:  {97, 1},
		11:  {97, 1},
		12:  {97, 1},
		13:  {81, 4},
		14:  {80, 2},
		15:  {66, 5},
		16:  {68, 2},
		17:  {68, 0},
		18:  {67, 3},
		19:  {15, 4},
		20:  {15, 4},
		21:  {15, 1},
		22:  {11, 1},
		23:  {11, 3},
		24:  {82, 2},
		25:  {82, 5},
		26:  {83, 2},
		27:  {83, 0},
		28:  {88, 3},
		29:  {73, 13},
		30:  {72, 2},
		31:  {72, 0},
		32:  {71, 2},
		33:  {87, 4},
		34:  {87, 0},
		35:  {99, 1},
		36:  {99, 2},
		37:  {75, 1},
		38:  {75, 3},
		39:  {61, 1},
		40:  {61, 1},
		41:  {21, 1},
		42:  {21, 1},
		43:  {21, 1},
		44:  {21, 1},
		45:  {98, 3},
		46:  {64, 8},
		47:  {64, 8},
		48:  {55, 1},
		49:  {54, 3},
		50:  {54, 1},
		51:  {53, 3},
		52:  {53, 1},
		53:  {52, 3},
		54:  {52, 3},
		55:  {52, 1},
		56:  {49, 3},
		57:  {49, 3},
		58:  {49, 3},
		59:  {49, 3},
		60:  {49, 1},
		61:  {41, 2},
		62:  {41, 1},
		63:  {39, 3},
		64:  {39, 3},
		65:  {39, 1},
		66:  {38, 3},
		67:  {38, 3},
		68:  {38, 1},
		69:  {29, 3},
		70:  {29, 1},
		71:  {29, 1},
		72:  {29, 1},
		73:  {29, 1},
		74:  {29, 1},
		75:  {29, 1},
		76:  {29, 3},
		77:  {30, 4},
		78:  {32, 4},
		79:  {32, 3},
		80:  {63, 3},
		81:  {63, 1},
		82:  {35, 7},
		83:  {101, 2},
		84:  {101, 0},
		85:  {100, 4},
		86:  {100, 4},
		87:  {34, 12},
		88:  {31, 1},
		89:  {31, 3},
		90:  {60, 2},
		91:  {60, 0},
		92:  {59, 5},
		93:  {76, 2},
		94:  {76, 2},
		95:  {77, 2},
		96:  {77, 2},
		97:  {77, 2},
		98:  {77, 2},
		99:  {77, 2},
		100: {77, 2},
		101: {89, 4},
		102: {89, 4},
		103: {74, 6},
		104: {69, 5},
		105: {69, 0},
		106: {92, 2},
		107: {92, 0},
		108: {91, 1},
		109: {91, 1},
		110: {90, 4},
	}

	yyXErrors = map[yyXError]string{}

	yyParseTab = [231][]uint16{
		// 0
		{109, 10: 109, 23: 109, 109, 37: 109, 42: 109, 109, 109, 47: 109, 109, 96: 112},
		{132, 10: 127, 23: 20, 20, 37: 6, 42: 124, 128, 111, 47: 126, 125, 59: 130, 129, 64: 121, 66: 116, 69: 131, 73: 119, 122, 80: 115, 114, 117, 88: 118, 90: 123, 97: 113, 120},
		{110, 10: 110, 23: 110, 110, 37: 110, 42: 110, 110, 110, 47: 110, 110},
		{108, 10: 108, 23: 108, 108, 37: 108, 42: 108, 108, 108, 47: 108, 108},
		{107, 10: 107, 23: 107, 107, 37: 107, 42: 107, 107, 107, 47: 107, 107},
		// 5
		{106, 10: 106, 23: 106, 106, 37: 106, 42: 106, 106, 106, 47: 106, 106},
		{105, 10: 105, 23: 105, 105, 37: 105, 42: 105, 105, 105, 47: 105, 105},
		{104, 10: 104, 23: 104, 104, 37: 104, 42: 104, 104, 104, 47: 104, 104},
		{103, 10: 103, 23: 103, 103, 37: 103, 42: 103, 103, 103, 47: 103, 103},
		{102, 10: 102, 23: 102, 102, 37: 102, 42: 102, 102, 102, 47: 102, 102},
		// 10
		{101, 10: 101, 23: 101, 101, 37: 101, 42: 101, 101, 101, 47: 101, 101},
		{100, 10: 100, 23: 100, 100, 37: 100, 42: 100, 100, 100, 47: 100, 100},
		{99, 10: 99, 23: 99, 99, 37: 99, 42: 99, 99, 99, 47: 99, 99},
		{339},
		{42: 334, 66: 333},
		// 15
		{219, 11: 328},
		{65: 139, 70: 280, 95: 325},
		{299},
		{23: 286, 285},
		{20, 10: 137, 23: 20, 20, 59: 130, 284},
		// 20
		{37: 271},
		{56: 133},
		{94, 94, 10: 94, 68: 134},
		{20, 138, 10: 137, 59: 130, 136, 67: 135},
		{95, 95, 10: 95},
		// 25
		{269},
		{65: 139},
		{1, 10: 1, 23: 1, 1, 37: 1, 42: 1, 1, 1, 47: 1, 1},
		{16: 140},
		{142, 76: 141},
		// 30
		{2: 268},
		{20: 145, 22: 146, 25: 147, 149, 150, 148, 77: 143, 84: 151, 89: 144, 93: 152},
		{2: 18},
		{2: 17},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 267},
		// 35
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 266},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 265},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 264},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 263},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 262},
		// 40
		{18: 259},
		{18: 153},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 181, 63: 180},
		{18: 256},
		{18: 253},
		// 45
		{90, 90, 3: 90, 10: 90, 56: 90, 58: 221},
		{1: 23, 23, 23, 23, 9: 23, 12: 23, 16: 249, 23, 19: 23, 23, 22: 23, 25: 23, 23, 23, 23, 45: 23, 23, 50: 23, 23, 56: 89, 58: 89, 62: 23},
		{1: 70, 70, 70, 70, 70, 70, 70, 70, 70, 12: 70, 17: 70, 19: 70, 70, 22: 70, 25: 70, 70, 70, 70, 45: 70, 70, 50: 70, 70, 57: 70},
		{1: 69, 69, 69, 69, 69, 69, 69, 69, 69, 12: 69, 17: 69, 19: 69, 69, 22: 69, 25: 69, 69, 69, 69, 45: 69, 69, 50: 69, 69, 57: 69},
		{1: 68, 68, 68, 68, 68, 68, 68, 68, 68, 12: 68, 17: 68, 19: 68, 68, 22: 68, 25: 68, 68, 68, 68, 45: 68, 68, 50: 68, 68, 57: 68},
		// 50
		{1: 67, 67, 67, 67, 67, 67, 67, 67, 67, 12: 67, 17: 67, 19: 67, 67, 22: 67, 25: 67, 67, 67, 67, 45: 67, 67, 50: 67, 67, 57: 67},
		{1: 63, 63, 63, 63, 9: 63, 12: 63, 17: 247},
		{1: 61, 61, 61, 61, 9: 61, 12: 61, 17: 61, 19: 245},
		{1: 59, 59, 59, 59, 9: 59, 12: 59, 17: 59, 19: 59, 241, 22: 242},
		{1: 56, 56, 56, 56, 9: 56, 12: 56, 17: 56, 19: 56, 56, 22: 56, 25: 233, 234, 236, 235},
		// 55
		{1: 51, 51, 51, 51, 9: 51, 12: 51, 17: 51, 19: 51, 51, 22: 51, 25: 51, 51, 51, 51},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 232},
		{1: 49, 49, 49, 49, 9: 49, 12: 49, 17: 49, 19: 49, 49, 22: 49, 25: 49, 49, 49, 49, 45: 228, 229},
		{1: 46, 46, 46, 46, 9: 46, 12: 46, 17: 46, 19: 46, 46, 22: 46, 25: 46, 46, 46, 46, 45: 46, 46, 50: 224, 225},
		{1: 43, 43, 43, 43, 9: 43, 12: 43, 17: 43, 19: 43, 43, 22: 43, 25: 43, 43, 43, 43, 45: 43, 43, 50: 43, 43},
		// 60
		{219, 11: 220},
		{1: 41, 41, 41, 41, 9: 41, 12: 41, 17: 41, 19: 41, 41, 22: 41, 25: 41, 41, 41, 41, 45: 41, 41, 50: 41, 41},
		{1: 40, 40, 40, 40, 9: 40, 12: 40, 17: 40, 19: 40, 40, 22: 40, 25: 40, 40, 40, 40, 45: 40, 40, 50: 40, 40},
		{1: 39, 39, 39, 39, 9: 39, 12: 39, 17: 39, 19: 39, 39, 22: 39, 25: 39, 39, 39, 39, 45: 39, 39, 50: 39, 39},
		{1: 38, 38, 38, 38, 9: 38, 12: 38, 17: 38, 19: 38, 38, 22: 38, 25: 38, 38, 38, 38, 45: 38, 38, 50: 38, 38},
		// 65
		{1: 37, 37, 37, 37, 9: 37, 12: 37, 17: 37, 19: 37, 37, 22: 37, 25: 37, 37, 37, 37, 45: 37, 37, 50: 37, 37},
		{1: 36, 36, 36, 36, 9: 36, 12: 36, 17: 36, 19: 36, 36, 22: 36, 25: 36, 36, 36, 36, 45: 36, 36, 50: 36, 36, 62: 217},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 215},
		{56: 212},
		{3: 210, 209},
		// 70
		{2: 30, 30, 30},
		{16: 195},
		{16: 184},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 185},
		{2: 186},
		// 75
		{102: 187},
		{56: 188},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 189},
		{1: 190},
		{79: 191},
		// 80
		{56: 192},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 193},
		{1: 194},
		{1: 24, 24, 24, 24, 9: 24, 12: 24, 17: 24, 19: 24, 24, 22: 24, 25: 24, 24, 24, 24, 45: 24, 24, 50: 24, 24},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 196},
		// 85
		{2: 197},
		{56: 198},
		{1: 27, 9: 27, 101: 199},
		{1: 200, 9: 202, 100: 201},
		{1: 29, 29, 29, 29, 9: 29, 12: 29, 17: 29, 19: 29, 29, 22: 29, 25: 29, 29, 29, 29, 45: 29, 29, 50: 29, 29},
		// 90
		{1: 28, 9: 28},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 203, 57: 204},
		{12: 207},
		{12: 205},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 206},
		// 95
		{1: 25, 9: 25},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 208},
		{1: 26, 9: 26},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 211},
		{2: 9},
		// 100
		{2: 31, 31, 31},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 213},
		{1: 214},
		{1: 34, 34, 34, 34, 9: 34, 12: 34, 17: 34, 19: 34, 34, 22: 34, 25: 34, 34, 34, 34, 45: 34, 34, 50: 34, 34},
		{2: 216},
		// 105
		{1: 35, 35, 35, 35, 9: 35, 12: 35, 17: 35, 19: 35, 35, 22: 35, 25: 35, 35, 35, 35, 45: 35, 35, 50: 35, 35},
		{218},
		{1: 22, 22, 22, 22, 9: 22, 12: 22, 17: 22, 19: 22, 22, 22: 22, 25: 22, 22, 22, 22, 45: 22, 22, 50: 22, 22, 62: 22},
		{89, 89, 3: 89, 10: 89, 23: 89, 89, 37: 89, 42: 89, 89, 89, 47: 89, 89, 56: 89, 58: 89},
		{3: 222, 58: 221},
		// 110
		{223},
		{1: 42, 42, 42, 42, 9: 42, 12: 42, 17: 42, 19: 42, 42, 22: 42, 25: 42, 42, 42, 42, 45: 42, 42, 50: 42, 42},
		{88, 88, 3: 88, 10: 88, 23: 88, 88, 37: 88, 42: 88, 88, 88, 47: 88, 88, 56: 88, 58: 88},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 227, 176, 177, 174, 183, 173, 172, 182},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 226, 176, 177, 174, 183, 173, 172, 182},
		// 115
		{1: 44, 44, 44, 44, 9: 44, 12: 44, 17: 44, 19: 44, 44, 22: 44, 25: 44, 44, 44, 44, 45: 44, 44, 50: 44, 44},
		{1: 45, 45, 45, 45, 9: 45, 12: 45, 17: 45, 19: 45, 45, 22: 45, 25: 45, 45, 45, 45, 45: 45, 45, 50: 45, 45},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 231},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 230},
		{1: 47, 47, 47, 47, 9: 47, 12: 47, 17: 47, 19: 47, 47, 22: 47, 25: 47, 47, 47, 47, 45: 47, 47, 50: 224, 225},
		// 120
		{1: 48, 48, 48, 48, 9: 48, 12: 48, 17: 48, 19: 48, 48, 22: 48, 25: 48, 48, 48, 48, 45: 48, 48, 50: 224, 225},
		{1: 50, 50, 50, 50, 9: 50, 12: 50, 17: 50, 19: 50, 50, 22: 50, 25: 50, 50, 50, 50, 45: 228, 229},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 240},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 239},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 238},
		// 125
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 237},
		{1: 52, 52, 52, 52, 9: 52, 12: 52, 17: 52, 19: 52, 52, 22: 52, 25: 52, 52, 52, 52},
		{1: 53, 53, 53, 53, 9: 53, 12: 53, 17: 53, 19: 53, 53, 22: 53, 25: 53, 53, 53, 53},
		{1: 54, 54, 54, 54, 9: 54, 12: 54, 17: 54, 19: 54, 54, 22: 54, 25: 54, 54, 54, 54},
		{1: 55, 55, 55, 55, 9: 55, 12: 55, 17: 55, 19: 55, 55, 22: 55, 25: 55, 55, 55, 55},
		// 130
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 244},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 243},
		{1: 57, 57, 57, 57, 9: 57, 12: 57, 17: 57, 19: 57, 57, 22: 57, 25: 233, 234, 236, 235},
		{1: 58, 58, 58, 58, 9: 58, 12: 58, 17: 58, 19: 58, 58, 22: 58, 25: 233, 234, 236, 235},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 246},
		// 135
		{1: 60, 60, 60, 60, 9: 60, 12: 60, 17: 60, 19: 60, 241, 22: 242},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 248},
		{1: 62, 62, 62, 62, 9: 62, 12: 62, 17: 62, 19: 245},
		{157, 2: 251, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 181, 63: 250},
		{2: 252, 4: 209},
		// 140
		{1: 32, 32, 32, 32, 9: 32, 12: 32, 17: 32, 19: 32, 32, 22: 32, 25: 32, 32, 32, 32, 45: 32, 32, 50: 32, 32},
		{1: 33, 33, 33, 33, 9: 33, 12: 33, 17: 33, 19: 33, 33, 22: 33, 25: 33, 33, 33, 33, 45: 33, 33, 50: 33, 33},
		{219, 11: 156, 13: 154, 155, 254},
		{3: 255},
		{91, 91, 3: 91, 10: 91, 56: 91},
		// 145
		{219, 11: 156, 13: 154, 155, 257},
		{3: 258},
		{92, 92, 3: 92, 10: 92, 56: 92},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 181, 63: 260},
		{3: 261, 209},
		// 150
		{2: 10},
		{2: 11},
		{2: 12},
		{2: 13},
		{2: 14},
		// 155
		{2: 15},
		{2: 16},
		{19, 10: 19, 23: 19, 19},
		{219, 11: 156, 13: 154, 155, 270},
		{93, 93, 10: 93},
		// 160
		{272},
		{56: 273},
		{1: 4, 10: 4, 23: 4, 4, 37: 4, 92: 274},
		{1: 276, 10: 275, 23: 20, 20, 37: 6, 59: 130, 129, 64: 279, 69: 131, 74: 278, 91: 277},
		{65: 139, 70: 280},
		// 165
		{8, 8, 10: 8, 23: 8, 8, 37: 8, 42: 8, 8, 8, 47: 8, 8},
		{1: 5, 10: 5, 23: 5, 5, 37: 5},
		{1: 3, 10: 3, 23: 3, 3, 37: 3},
		{1: 2, 10: 2, 23: 2, 2, 37: 2},
		{16: 281},
		// 170
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 282},
		{2: 283},
		{37: 7},
		{21, 23: 21, 21},
		{293},
		// 175
		{287},
		{288},
		{219, 11: 156, 13: 154, 155, 289},
		{56: 290},
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 291},
		// 180
		{1: 292},
		{64, 64, 10: 64, 23: 64, 64, 37: 64, 42: 64, 64, 64, 47: 64, 64},
		{294},
		{219, 11: 156, 13: 154, 155, 295},
		{56: 296},
		// 185
		{157, 5: 161, 160, 158, 159, 11: 156, 13: 154, 155, 179, 178, 18: 171, 21: 175, 29: 170, 176, 177, 174, 183, 173, 172, 182, 38: 169, 168, 167, 166, 49: 165, 52: 164, 163, 162, 297},
		{1: 298},
		{65, 65, 10: 65, 23: 65, 65, 37: 65, 42: 65, 65, 65, 47: 65, 65},
		{56: 300},
		{86: 301},
		// 190
		{56: 302},
		{80, 80, 72: 303},
		{306, 304, 71: 305},
		{94: 308},
		{81, 81},
		// 195
		{219, 11: 156, 13: 154, 155, 307},
		{79, 79},
		{56: 309},
		{80, 80, 72: 310},
		{306, 311, 71: 305},
		// 200
		{1: 77, 78: 313, 87: 312},
		{1: 324},
		{56: 314},
		{5: 161, 160, 158, 159, 21: 318, 57: 319, 61: 317, 75: 316, 99: 315},
		{1: 322, 5: 161, 160, 158, 159, 21: 318, 57: 319, 61: 317, 75: 323},
		// 205
		{1: 76, 4: 320, 76, 76, 76, 76, 57: 76},
		{1: 74, 4: 74, 74, 74, 74, 74, 57: 74},
		{1: 72, 4: 72, 72, 72, 72, 72, 57: 72},
		{1: 71, 4: 71, 71, 71, 71, 71, 57: 71},
		{5: 161, 160, 158, 159, 21: 318, 57: 319, 61: 321},
		// 210
		{1: 73, 4: 73, 73, 73, 73, 73, 57: 73},
		{1: 78},
		{1: 75, 4: 320, 75, 75, 75, 75, 57: 75},
		{82, 10: 82, 23: 82, 82, 37: 82, 42: 82, 82, 82, 47: 82, 82},
		{10: 137, 23: 20, 20, 43: 128, 59: 130, 129, 64: 327, 73: 326},
		// 215
		{83, 10: 83, 23: 83, 83, 37: 83, 42: 83, 83, 83, 47: 83, 83},
		{66, 10: 66, 23: 66, 66, 37: 66, 42: 66, 66, 66, 47: 66, 66},
		{87, 10: 87, 23: 87, 87, 37: 87, 42: 87, 87, 87, 47: 87, 87, 56: 329, 58: 221},
		{1: 84, 5: 84, 83: 330},
		{1: 331, 5: 332},
		// 220
		{86, 10: 86, 23: 86, 86, 37: 86, 42: 86, 86, 86, 47: 86, 86},
		{1: 85, 5: 85},
		{97, 10: 97, 23: 97, 97, 37: 97, 42: 97, 97, 97, 47: 97, 97},
		{335},
		{56: 336},
		// 225
		{94, 94, 10: 94, 68: 337},
		{20, 338, 10: 137, 59: 130, 136, 67: 135},
		{96, 10: 96, 23: 96, 96, 37: 96, 42: 96, 96, 96, 47: 96, 96},
		{56: 336, 85: 340},
		{341},
		// 230
		{98, 10: 98, 23: 98, 98, 37: 98, 42: 98, 98, 98, 47: 98, 98},
	}
)

var yyDebug = 0

type yyLexer interface {
	Lex(lval *yySymType) int
	Error(s string)
}

type yyLexerEx interface {
	yyLexer
	Reduced(rule, state int, lval *yySymType) bool
}

func yySymName(c int) (s string) {
	x, ok := yyXLAT[c]
	if ok {
		return yySymNames[x]
	}

	if c < 0x7f {
		return __yyfmt__.Sprintf("%q", c)
	}

	return __yyfmt__.Sprintf("%d", c)
}

func yylex1(yylex yyLexer, lval *yySymType) (n int) {
	n = yylex.Lex(lval)
	if n <= 0 {
		n = yyEofCode
	}
	if yyDebug >= 3 {
		__yyfmt__.Printf("\nlex %s(%#x %d), lval: %+v\n", yySymName(n), n, n, lval)
	}
	return n
}

func yyParse(yylex yyLexer) int {
	const yyError = 104

	yyEx, _ := yylex.(yyLexerEx)
	var yyn int
	var yylval yySymType
	var yyVAL yySymType
	yyS := make([]yySymType, 200)

	Nerrs := 0   /* number of errors */
	Errflag := 0 /* error recovery flag */
	yyerrok := func() {
		if yyDebug >= 2 {
			__yyfmt__.Printf("yyerrok()\n")
		}
		Errflag = 0
	}
	_ = yyerrok
	yystate := 0
	yychar := -1
	var yyxchar int
	var yyshift int
	yyp := -1
	goto yystack

ret0:
	return 0

ret1:
	return 1

yystack:
	/* put a state and value onto the stack */
	yyp++
	if yyp >= len(yyS) {
		nyys := make([]yySymType, len(yyS)*2)
		copy(nyys, yyS)
		yyS = nyys
	}
	yyS[yyp] = yyVAL
	yyS[yyp].yys = yystate

yynewstate:
	if yychar < 0 {
		yylval.yys = yystate
		yychar = yylex1(yylex, &yylval)
		var ok bool
		if yyxchar, ok = yyXLAT[yychar]; !ok {
			yyxchar = len(yySymNames) // > tab width
		}
	}
	if yyDebug >= 4 {
		var a []int
		for _, v := range yyS[:yyp+1] {
			a = append(a, v.yys)
		}
		__yyfmt__.Printf("state stack %v\n", a)
	}
	row := yyParseTab[yystate]
	yyn = 0
	if yyxchar < len(row) {
		if yyn = int(row[yyxchar]); yyn != 0 {
			yyn += yyTabOfs
		}
	}
	switch {
	case yyn > 0: // shift
		yychar = -1
		yyVAL = yylval
		yystate = yyn
		yyshift = yyn
		if yyDebug >= 2 {
			__yyfmt__.Printf("shift, and goto state %d\n", yystate)
		}
		if Errflag > 0 {
			Errflag--
		}
		goto yystack
	case yyn < 0: // reduce
	case yystate == 1: // accept
		if yyDebug >= 2 {
			__yyfmt__.Println("accept")
		}
		goto ret0
	}

	if yyn == 0 {
		/* error ... attempt to resume parsing */
		switch Errflag {
		case 0: /* brand new error */
			if yyDebug >= 1 {
				__yyfmt__.Printf("no action for %s in state %d\n", yySymName(yychar), yystate)
			}
			msg, ok := yyXErrors[yyXError{yystate, yyxchar}]
			if !ok {
				msg, ok = yyXErrors[yyXError{yystate, -1}]
			}
			if !ok && yyshift != 0 {
				msg, ok = yyXErrors[yyXError{yyshift, yyxchar}]
			}
			if !ok {
				msg, ok = yyXErrors[yyXError{yyshift, -1}]
			}
			if yychar > 0 {
				ls := yyTokenLiteralStrings[yychar]
				if ls == "" {
					ls = yySymName(yychar)
				}
				if ls != "" {
					switch {
					case msg == "":
						msg = __yyfmt__.Sprintf("unexpected %s", ls)
					default:
						msg = __yyfmt__.Sprintf("unexpected %s, %s", ls, msg)
					}
				}
			}
			if msg == "" {
				msg = "syntax error"
			}
			yylex.Error(msg)
			Nerrs++
			fallthrough

		case 1, 2: /* incompletely recovered error ... try again */
			Errflag = 3

			/* find a state where "error" is a legal shift action */
			for yyp >= 0 {
				row := yyParseTab[yyS[yyp].yys]
				if yyError < len(row) {
					yyn = int(row[yyError]) + yyTabOfs
					if yyn > 0 { // hit
						if yyDebug >= 2 {
							__yyfmt__.Printf("error recovery found error shift in state %d\n", yyS[yyp].yys)
						}
						yystate = yyn /* simulate a shift of "error" */
						goto yystack
					}
				}

				/* the current p has no shift on "error", pop stack */
				if yyDebug >= 2 {
					__yyfmt__.Printf("error recovery pops state %d\n", yyS[yyp].yys)
				}
				yyp--
			}
			/* there is no state on the stack with an error shift ... abort */
			if yyDebug >= 2 {
				__yyfmt__.Printf("error recovery failed\n")
			}
			goto ret1

		case 3: /* no shift yet; clobber input char */
			if yyDebug >= 2 {
				__yyfmt__.Printf("error recovery discards %s\n", yySymName(yychar))
			}
			if yychar == yyEofCode {
				goto ret1
			}

			yychar = -1
			goto yynewstate /* try again in the same state */
		}
	}

	r := -yyn
	x0 := yyReductions[r]
	x, n := x0.xsym, x0.components
	yypt := yyp
	_ = yypt // guard against "declared and not used"

	yyp -= n
	if yyp+1 >= len(yyS) {
		nyys := make([]yySymType, len(yyS)*2)
		copy(nyys, yyS)
		yyS = nyys
	}
	yyVAL = yyS[yyp+1]

	/* consult goto table to find next state */
	exState := yystate
	yystate = int(yyParseTab[yyS[yyp].yys][x]) + yyTabOfs
	/* reduction by production r */
	if yyDebug >= 2 {
		__yyfmt__.Printf("reduce using rule %v (%s), and goto state %d\n", r, yySymNames[x], yystate)
	}

	switch r {
	case 1:
		{
			yyS[yypt-1].Program.Parts = append(yyS[yypt-1].Program.Parts, yyS[yypt-0].ProgramPart)
			yyVAL.Program = yyS[yypt-1].Program
			setResult(yylex, yyVAL.Program)
		}
	case 2:
		{
			yyVAL.Program = &Program{}
			setResult(yylex, yyVAL.Program)
		}
	case 3:
		{
			yyVAL.ProgramPart = &ProgramPart{TypeInclusion: yyS[yypt-0].EntryInclusion}
		}
	case 4:
		{
			yyVAL.ProgramPart = &ProgramPart{TypeExtension: yyS[yypt-0].EntryExtension}
		}
	case 5:
		{
			yyVAL.ProgramPart = &ProgramPart{Type: yyS[yypt-0].Entry}
		}
	case 6:
		{
			yyVAL.ProgramPart = &ProgramPart{Enum: yyS[yypt-0].Enum}
		}
	case 7:
		{
			yyVAL.ProgramPart = &ProgramPart{LookupTableOverride: yyS[yypt-0].LookupTableOverride}
		}
	case 8:
		{
			yyVAL.ProgramPart = &ProgramPart{LookupTable: yyS[yypt-0].LookupTable}
		}
	case 9:
		{
			yyVAL.ProgramPart = &ProgramPart{PropertyOverride: yyS[yypt-0].PropertyOverride}
		}
	case 10:
		{
			yyVAL.ProgramPart = &ProgramPart{Property: yyS[yypt-0].Property}
		}
	case 11:
		{
			yyVAL.ProgramPart = &ProgramPart{Namespace: yyS[yypt-0].Namespace}
		}
	case 12:
		{
			yyVAL.ProgramPart = &ProgramPart{NamespaceConfig: yyS[yypt-0].NamespaceConfig}
		}
	case 13:
		{
			yyVAL.EntryInclusion = &EntryInclusion{IncluderName: yyS[yypt-2].String, IncludedName: yyS[yypt-0].String, Line: yyS[yypt-3].LineNo}
		}
	case 14:
		{
			yyVAL.EntryExtension = &EntryExtension{Entry: yyS[yypt-0].Entry}
		}
	case 15:
		{
			yyVAL.Entry = &Entry{Name: yyS[yypt-3].String, Fields: yyS[yypt-1].FieldList, StartLine: yyS[yypt-4].LineNo, EndLine: yyS[yypt-0].LineNo}
		}
	case 16:
		{
			yyVAL.FieldList = append(yyS[yypt-1].FieldList, yyS[yypt-0].Field)
		}
	case 17:
		{
			yyVAL.FieldList = make([]*Field, 0)
		}
	case 18:
		{
			yyVAL.Field = &Field{Checks: yyS[yypt-2].Check, Name: yyS[yypt-1].String, Type: yyS[yypt-0].TypeContainer}
		}
	case 19:
		{
			yyVAL.TypeContainer = &TypeContainer{Array: yyS[yypt-1].TypeContainer}
		}
	case 20:
		{
			yyVAL.TypeContainer = &TypeContainer{Range: yyS[yypt-1].TypeContainer}
		}
	case 21:
		{
			yyVAL.TypeContainer = &TypeContainer{Type: yyS[yypt-0].QName}
		}
	case 22:
		{
			yyVAL.QName = &QName{Path: []string{yyS[yypt-0].String}}
		}
	case 23:
		{
			yyS[yypt-2].QName.Path = append(yyS[yypt-2].QName.Path, yyS[yypt-0].String)
			yyVAL.QName = yyS[yypt-2].QName
		}
	case 24:
		{
			yyVAL.Enum = &Enum{Name: yyS[yypt-0].QName, HasEntries: false}
		}
	case 25:
		{
			yyVAL.Enum = &Enum{Name: yyS[yypt-3].QName, HasEntries: true, Entries: yyS[yypt-1].EnumValuesList}
		}
	case 26:
		{
			yyVAL.EnumValuesList = append(yyS[yypt-1].EnumValuesList, yyS[yypt-0].String)
		}
	case 27:
		{
			yyVAL.EnumValuesList = make([]string, 0)
		}
	case 28:
		{
			yyVAL.LookupTableOverride = &LookupTableOverride{LookupTable: yyS[yypt-0].LookupTable}
		}
	case 29:
		{
			inputs := &LookupTableIOWithLineNumbers{Columns: yyS[yypt-7].IODecList, StartLine: yyS[yypt-9].LineNo, EndLine: yyS[yypt-6].LineNo}
			outputs := &LookupTableIOWithLineNumbers{Columns: yyS[yypt-3].IODecList, StartLine: yyS[yypt-5].LineNo, EndLine: yyS[yypt-2].LineNo}
			yyVAL.LookupTable = &LookupTable{Name: yyS[yypt-11].String, Inputs: inputs, Outputs: outputs, InlineData: yyS[yypt-1].LookupTableData}
		}
	case 30:
		{
			yyVAL.IODecList = append(yyS[yypt-1].IODecList, yyS[yypt-0].LookupTableIODec)
		}
	case 31:
		{
			yyVAL.IODecList = make([]*LookupTableIODeclaration, 0)
		}
	case 32:
		{
			yyVAL.LookupTableIODec = &LookupTableIODeclaration{Name: yyS[yypt-1].String, Type: yyS[yypt-0].TypeContainer}
		}
	case 33:
		{
			yyVAL.LookupTableData = &LookupTableInlineData{Rows: yyS[yypt-1].RowList, StartLine: yyS[yypt-3].LineNo, EndLine: yyS[yypt-0].LineNo}
		}
	case 34:
		{
			yyVAL.LookupTableData = nil
		}
	case 35:
		{
			yyVAL.RowList = []*Row{yyS[yypt-0].Row}
		}
	case 36:
		{
			yyVAL.RowList = append(yyS[yypt-1].RowList, yyS[yypt-0].Row)
		}
	case 37:
		{
			yyVAL.Row = &Row{Cells: []*Cell{yyS[yypt-0].Cell}}
		}
	case 38:
		{
			yyS[yypt-2].Row.Cells = append(yyS[yypt-2].Row.Cells, yyS[yypt-0].Cell)
			yyVAL.Row = yyS[yypt-2].Row
		}
	case 39:
		{
			yyVAL.Cell = &Cell{Literal: yyS[yypt-0].Literal}
		}
	case 40:
		{
			yyVAL.Cell = &Cell{Any: "_"}
		}
	case 41:
		{
			yyVAL.Literal = &Literal{DecimalLiteral: yyS[yypt-0].String}
		}
	case 42:
		{
			yyVAL.Literal = &Literal{IntegerLiteral: yyS[yypt-0].String}
		}
	case 43:
		{
			yyVAL.Literal = &Literal{BooleanLiteral: yyS[yypt-0].String}
		}
	case 44:
		{
			yyVAL.Literal = &Literal{EnumLiteral: yyS[yypt-0].String}
		}
	case 45:
		{
			yyVAL.PropertyOverride = &PropertyOverride{Property: yyS[yypt-0].Property}
		}
	case 46:
		{
			yyVAL.Property = &Property{Checks: yyS[yypt-7].Check, PropertyClass: "property", OwnerEntity: yyS[yypt-5].String, Name: yyS[yypt-4].String, Type: yyS[yypt-3].TypeContainer, Expression: yyS[yypt-1].Expression, StartLine: yyS[yypt-6].LineNo, EndLine: yyS[yypt-0].LineNo}
		}
	case 47:
		{
			yyVAL.Property = &Property{Checks: yyS[yypt-7].Check, PropertyClass: "output", OwnerEntity: yyS[yypt-5].String, Name: yyS[yypt-4].String, Type: yyS[yypt-3].TypeContainer, Expression: yyS[yypt-1].Expression, StartLine: yyS[yypt-6].LineNo, EndLine: yyS[yypt-0].LineNo}
		}
	case 48:
		{
			yyVAL.Expression = &Expression{Body: yyS[yypt-0].Disjunction}
		}
	case 49:
		{
			yyVAL.Disjunction = &Disjunction{LeftExpr: yyS[yypt-2].Disjunction, Op: "||", RightExpr: yyS[yypt-0].Conjunction}
		}
	case 50:
		{
			yyVAL.Disjunction = &Disjunction{RightExpr: yyS[yypt-0].Conjunction}
		}
	case 51:
		{
			yyVAL.Conjunction = &Conjunction{LeftExpr: yyS[yypt-2].Conjunction, Op: "&&", RightExpr: yyS[yypt-0].Equality}
		}
	case 52:
		{
			yyVAL.Conjunction = &Conjunction{RightExpr: yyS[yypt-0].Equality}
		}
	case 53:
		{
			yyVAL.Equality = &Equality{LeftExpr: yyS[yypt-2].Equality, Op: "=", RightExpr: yyS[yypt-0].Relational}
		}
	case 54:
		{
			yyVAL.Equality = &Equality{LeftExpr: yyS[yypt-2].Equality, Op: "!=", RightExpr: yyS[yypt-0].Relational}
		}
	case 55:
		{
			yyVAL.Equality = &Equality{RightExpr: yyS[yypt-0].Relational}
		}
	case 56:
		{
			yyVAL.Relational = &Relational{LeftExpr: yyS[yypt-2].Relational, Op: "<", RightExpr: yyS[yypt-0].Negation}
		}
	case 57:
		{
			yyVAL.Relational = &Relational{LeftExpr: yyS[yypt-2].Relational, Op: ">", RightExpr: yyS[yypt-0].Negation}
		}
	case 58:
		{
			yyVAL.Relational = &Relational{LeftExpr: yyS[yypt-2].Relational, Op: "<=", RightExpr: yyS[yypt-0].Negation}
		}
	case 59:
		{
			yyVAL.Relational = &Relational{LeftExpr: yyS[yypt-2].Relational, Op: ">=", RightExpr: yyS[yypt-0].Negation}
		}
	case 60:
		{
			yyVAL.Relational = &Relational{RightExpr: yyS[yypt-0].Negation}
		}
	case 61:
		{
			yyVAL.Negation = &Negation{Op: "!", UnaryExpr: yyS[yypt-0].Addition}
		}
	case 62:
		{
			yyVAL.Negation = &Negation{UnaryExpr: yyS[yypt-0].Addition}
		}
	case 63:
		{
			yyVAL.Addition = &Addition{LeftExpr: yyS[yypt-2].Addition, Op: "+", RightExpr: yyS[yypt-0].Factor}
		}
	case 64:
		{
			yyVAL.Addition = &Addition{LeftExpr: yyS[yypt-2].Addition, Op: "-", RightExpr: yyS[yypt-0].Factor}
		}
	case 65:
		{
			yyVAL.Addition = &Addition{RightExpr: yyS[yypt-0].Factor}
		}
	case 66:
		{
			yyVAL.Factor = &Factor{LeftExpr: yyS[yypt-2].Factor, Op: "*", RightExpr: yyS[yypt-0].Atom}
		}
	case 67:
		{
			yyVAL.Factor = &Factor{LeftExpr: yyS[yypt-2].Factor, Op: "/", RightExpr: yyS[yypt-0].Atom}
		}
	case 68:
		{
			yyVAL.Factor = &Factor{RightExpr: yyS[yypt-0].Atom}
		}
	case 69:
		{
			yyVAL.Atom = &Atom{TypeRef: yyS[yypt-1].QName}
		}
	case 70:
		{
			yyVAL.Atom = &Atom{Switch: yyS[yypt-0].Switch}
		}
	case 71:
		{
			yyVAL.Atom = &Atom{IfThenElse: yyS[yypt-0].IfThenElse}
		}
	case 72:
		{
			yyVAL.Atom = &Atom{FunctionCall: yyS[yypt-0].FunctionCall}
		}
	case 73:
		{
			yyVAL.Atom = &Atom{Literal: yyS[yypt-0].Literal}
		}
	case 74:
		{
			yyVAL.Atom = &Atom{Cast: yyS[yypt-0].Cast}
		}
	case 75:
		{
			yyVAL.Atom = &Atom{FieldAccess: yyS[yypt-0].FieldAccess}
		}
	case 76:
		{
			yyVAL.Atom = &Atom{SubExpression: yyS[yypt-1].Expression}
		}
	case 77:
		{
			yyVAL.Cast = &Cast{Type: yyS[yypt-3].TypeContainer, Expression: yyS[yypt-1].Expression}
		}
	case 78:
		{
			yyVAL.FunctionCall = &FunctionCall{Name: yyS[yypt-3].String, Arguments: yyS[yypt-1].ExpList}
		}
	case 79:
		{
			yyVAL.FunctionCall = &FunctionCall{Name: yyS[yypt-2].String}
		}
	case 80:
		{
			yyVAL.ExpList = append(yyS[yypt-2].ExpList, yyS[yypt-0].Expression)
		}
	case 81:
		{
			yyVAL.ExpList = []*Expression{yyS[yypt-0].Expression}
		}
	case 82:
		{
			yyVAL.Switch = &Switch{SwitchExpression: yyS[yypt-4].Expression, Cases: yyS[yypt-1].SwitchList}
		}
	case 83:
		{
			yyVAL.SwitchList = append(yyS[yypt-1].SwitchList, yyS[yypt-0].SwitchCase)
		}
	case 84:
		{
			yyVAL.SwitchList = make([]*SwitchCase, 0)
		}
	case 85:
		{
			yyVAL.SwitchCase = &SwitchCase{MatchExpression: yyS[yypt-2].Expression, IsDefault: false, ValueExpression: yyS[yypt-0].Expression}
		}
	case 86:
		{
			yyVAL.SwitchCase = &SwitchCase{IsDefault: true, ValueExpression: yyS[yypt-0].Expression}
		}
	case 87:
		{
			yyVAL.IfThenElse = &IfThenElse{BranchExpression: yyS[yypt-9].Expression, ThenExpression: yyS[yypt-5].Expression, ElseExpression: yyS[yypt-1].Expression}
		}
	case 88:
		{
			yyVAL.FieldAccess = &FieldAccess{Path: []string{yyS[yypt-0].String}}
		}
	case 89:
		{
			yyS[yypt-2].FieldAccess.Path = append(yyS[yypt-2].FieldAccess.Path, yyS[yypt-0].String)
			yyVAL.FieldAccess = yyS[yypt-2].FieldAccess
		}
	case 90:
		{
			yyS[yypt-0].Check.ClauseList = append([]*Clause{yyS[yypt-1].Clause}, yyS[yypt-0].Check.ClauseList...)
			yyVAL.Check = yyS[yypt-0].Check
		}
	case 91:
		{
			yyVAL.Check = &Check{}
		}
	case 92:
		{
			yyS[yypt-1].Clause.Line = yyS[yypt-3].LineNo
			yyVAL.Clause = yyS[yypt-1].Clause
		}
	case 93:
		{
			yyVAL.Clause = &Clause{LeftIdent: yyS[yypt-1].String, Comp: yyS[yypt-0].Compare}
		}
	case 94:
		{
			yyVAL.Clause = &Clause{LeftIdent: yyS[yypt-1].String, Mem: yyS[yypt-0].Member}
		}
	case 95:
		{
			yyVAL.Compare = &Compare{Op: "=", RightExpr: yyS[yypt-0].Expression}
		}
	case 96:
		{
			yyVAL.Compare = &Compare{Op: "!=", RightExpr: yyS[yypt-0].Expression}
		}
	case 97:
		{
			yyVAL.Compare = &Compare{Op: "<", RightExpr: yyS[yypt-0].Expression}
		}
	case 98:
		{
			yyVAL.Compare = &Compare{Op: "<=", RightExpr: yyS[yypt-0].Expression}
		}
	case 99:
		{
			yyVAL.Compare = &Compare{Op: ">", RightExpr: yyS[yypt-0].Expression}
		}
	case 100:
		{
			yyVAL.Compare = &Compare{Op: ">=", RightExpr: yyS[yypt-0].Expression}
		}
	case 101:
		{
			yyVAL.Member = &Member{Op: "in", ExprSet: yyS[yypt-1].ExpList}
		}
	case 102:
		{
			yyVAL.Member = &Member{Op: "notin", ExprSet: yyS[yypt-1].ExpList}
		}
	case 103:
		{
			yyVAL.Namespace = &Namespace{Guard: yyS[yypt-5].ExpLine, Name: yyS[yypt-3].String, NamespaceParts: yyS[yypt-1].NamespacePartList, StartLine: yyS[yypt-4].LineNo, EndLine: yyS[yypt-0].LineNo}
		}
	case 104:
		{
			yyVAL.ExpLine = &ExpressionWithLineNumber{Expression: yyS[yypt-1].Expression, Line: yyS[yypt-3].LineNo}
		}
	case 105:
		{
			yyVAL.ExpLine = nil
		}
	case 106:
		{
			yyVAL.NamespacePartList = append(yyS[yypt-1].NamespacePartList, yyS[yypt-0].NamespacePart)
		}
	case 107:
		{
			yyVAL.NamespacePartList = make([]*NamespacePart, 0)
		}
	case 108:
		{
			yyVAL.NamespacePart = &NamespacePart{Namespace: yyS[yypt-0].Namespace}
		}
	case 109:
		{
			yyVAL.NamespacePart = &NamespacePart{Property: yyS[yypt-0].Property}
		}
	case 110:
		{
			yyVAL.NamespaceConfig = &NamespaceConfig{Name: yyS[yypt-3].String, Fields: yyS[yypt-1].FieldList, StartLine: yyS[yypt-2].LineNo, EndLine: yyS[yypt-0].LineNo}
		}

	}

	if yyEx != nil && yyEx.Reduced(r, exState, &yyVAL) {
		return -1
	}
	goto yystack /* stack new state and value */
}
