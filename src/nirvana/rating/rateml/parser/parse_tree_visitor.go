package parser

import (
	"strconv"
	"strings"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/rating/rateml/graph"
)

var EB = graph.NewExpReprBuilder()

func convertCheck(check *Check) ([]*graph.ExpReprWithLineNumber, error) {
	clauses := make([]*graph.ExpReprWithLineNumber, 0)
	if check == nil {
		return clauses, nil
	}
	for _, clause := range check.ClauseList {
		clauseExp, err := convertClause(clause)
		if err != nil {
			return nil, err
		}
		expWithLineNumber := &graph.ExpReprWithLineNumber{
			Expr: clauseExp,
			Line: clause.Line,
		}
		clauses = append(clauses, expWithLineNumber)
	}
	return clauses, nil
}

func convertClause(clause *Clause) (graph.ExpRepr, error) {
	left, err := convertAtom(&Atom{FieldAccess: &FieldAccess{Path: []string{clause.LeftIdent}}})
	if err != nil {
		return nil, err
	}

	if clause.Comp != nil {
		right, err := convertExpression(clause.Comp.RightExpr)
		if err != nil {
			return nil, err
		}
		switch clause.Comp.Op {
		case "<=":
			return EB.LTE(left, right), nil
		case "<":
			return EB.LT(left, right), nil
		case ">=":
			return EB.GTE(left, right), nil
		case ">":
			return EB.GT(left, right), nil
		case "=":
			return EB.EQ(left, right), nil
		case "!=":
			return EB.NEQ(left, right), nil
		default:
			return nil, errors.New("unsupported comparison operation")
		}
	} else if clause.Mem != nil {
		memberSet := make([]graph.ExpRepr, 0)
		for i := range clause.Mem.ExprSet {
			member, err := convertExpression(clause.Mem.ExprSet[i])
			if err != nil {
				return nil, err
			}
			memberSet = append(memberSet, member)
		}
		switch clause.Mem.Op {
		case "in":
			return EB.CheckIn(left, memberSet), nil
		case "notin":
			return EB.CheckNotIn(left, memberSet), nil
		default:
			return nil, errors.New("unsupported membership operation")
		}
	}
	return nil, errors.New("empty clause")
}

func convertExpression(exp *Expression) (graph.ExpRepr, error) {
	if exp.Body != nil {
		return convertDisjunction(exp.Body)
	}
	return nil, errors.New("unsupported expression")
}

func convertDisjunction(disjunct *Disjunction) (graph.ExpRepr, error) {
	if disjunct.LeftExpr != nil {
		op := disjunct.Op
		left, err := convertDisjunction(disjunct.LeftExpr)
		if err != nil {
			return nil, err
		}
		right, err := convertConjunction(disjunct.RightExpr)
		if err != nil {
			return nil, err
		}
		switch op {
		case "||":
			return EB.OR(left, right), nil
		default:
			return nil, errors.Errorf("Unsupported operation in Disjunction Expression. Op = %s", op)
		}
	}
	return convertConjunction(disjunct.RightExpr)
}

func convertConjunction(conjunct *Conjunction) (graph.ExpRepr, error) {
	if conjunct.LeftExpr != nil {
		op := conjunct.Op
		left, err := convertConjunction(conjunct.LeftExpr)
		if err != nil {
			return nil, err
		}
		right, err := convertEquality(conjunct.RightExpr)
		if err != nil {
			return nil, err
		}
		switch op {
		case "&&":
			return EB.AND(left, right), nil
		default:
			return nil, errors.Errorf("Unsupported operation in Conjunction Expression. Op = %s", op)
		}
	}
	return convertEquality(conjunct.RightExpr)
}

func convertEquality(eq *Equality) (graph.ExpRepr, error) {
	if eq.LeftExpr != nil {
		op := eq.Op
		left, err := convertEquality(eq.LeftExpr)
		if err != nil {
			return nil, err
		}
		right, err := convertRelational(eq.RightExpr)
		if err != nil {
			return nil, err
		}
		switch op {
		case "=":
			return EB.EQ(left, right), nil
		case "!=":
			return EB.NEQ(left, right), nil
		default:
			return nil, errors.Errorf("Unsupported operation in Equality Expression. Op = %s", op)
		}
	}
	return convertRelational(eq.RightExpr)
}

func convertRelational(rel *Relational) (graph.ExpRepr, error) {
	if rel.LeftExpr != nil {
		op := rel.Op
		left, err := convertRelational(rel.LeftExpr)
		if err != nil {
			return nil, err
		}
		right, err := convertNegation(rel.RightExpr)
		if err != nil {
			return nil, err
		}
		switch op {
		case "<":
			return EB.LT(left, right), nil
		case "<=":
			return EB.LTE(left, right), nil
		case ">":
			return EB.GT(left, right), nil
		case ">=":
			return EB.GTE(left, right), nil
		default:
			return nil, errors.Errorf("Unsupported operation in Relational Expression. Op = %s", op)
		}
	}
	return convertNegation(rel.RightExpr)
}

func convertNegation(neg *Negation) (graph.ExpRepr, error) {
	unaryExp, err := convertAddition(neg.UnaryExpr)
	if err != nil {
		return nil, err
	}
	switch neg.Op {
	case "!":
		return EB.NOT(unaryExp), nil
	case "":
		return unaryExp, nil
	default:
		return nil, errors.Errorf("Unsupported operation in Negation. Op = %s", neg.Op)
	}
}

func convertAddition(add *Addition) (graph.ExpRepr, error) {
	if add.LeftExpr != nil {
		op := add.Op
		left, err := convertAddition(add.LeftExpr)
		if err != nil {
			return nil, err
		}
		right, err := convertFactor(add.RightExpr)
		if err != nil {
			return nil, err
		}
		switch op {
		case "+":
			return EB.Add(left, right), nil
		case "-":
			return EB.Subtract(left, right), nil
		default:
			return nil, errors.New("unsupported addition expression")
		}
	}
	return convertFactor(add.RightExpr)
}

func convertFactor(f *Factor) (graph.ExpRepr, error) {
	if f.LeftExpr != nil {
		op := f.Op
		left, err := convertFactor(f.LeftExpr)
		if err != nil {
			return nil, err
		}
		right, err := convertAtom(f.RightExpr)
		if err != nil {
			return nil, err
		}
		switch op {
		case "*":
			return EB.Mult(left, right), nil
		case "/":
			return EB.Divide(left, right), nil
		default:
			return nil, errors.New("unsupported factor expression")
		}
	}
	return convertAtom(f.RightExpr)
}

func convertAtom(exp *Atom) (graph.ExpRepr, error) {
	// TODO: Switch to switch :P.
	if exp.TypeRef != nil {
		return EB.Type(exp.TypeRef.String()), nil
	} else if exp.Cast != nil {
		castExpr, err := convertExpression(exp.Cast.Expression)
		if err != nil {
			return nil, err
		}
		return EB.Cast(exp.Cast.Type.ToRMLTypeName(), castExpr), nil
	} else if exp.Switch != nil {
		swExp, err := convertExpression(exp.Switch.SwitchExpression)
		if err != nil {
			return nil, err
		}
		cases := make(map[graph.ExpRepr]graph.ExpRepr)
		for _, e := range exp.Switch.Cases {
			var matchExp graph.ExpRepr
			var err error

			if e.IsDefault {
				matchExp = EB.SwitchCaseCatchAll()
			} else {
				matchExp, err = convertExpression(e.MatchExpression)
			}
			if err != nil {
				return nil, err
			}

			valueExp, err := convertExpression(e.ValueExpression)
			if err != nil {
				return nil, err
			}

			cases[matchExp] = valueExp
		}
		return EB.Switch(swExp, cases), nil
	} else if exp.FunctionCall != nil {
		lookupArgs := make([]graph.ExpRepr, 0)
		for argIdx := range exp.FunctionCall.Arguments {
			argExp, err := convertExpression(exp.FunctionCall.Arguments[argIdx])
			if err != nil {
				return nil, err
			}
			lookupArgs = append(lookupArgs, argExp)
		}
		return EB.Function(exp.FunctionCall.Name, lookupArgs...), nil
	} else if exp.Literal != nil {
		if exp.Literal.IntegerLiteral != "" {
			i, err := strconv.ParseInt(exp.Literal.IntegerLiteral, 10, 64)
			if err != nil {
				return nil, err
			}
			return EB.LiteralNumber(i), nil
		} else if exp.Literal.DecimalLiteral != "" {
			d, err := strconv.ParseFloat(exp.Literal.DecimalLiteral, 64)
			if err != nil {
				return nil, err
			}
			return EB.LiteralNumber(d), nil
		} else if exp.Literal.BooleanLiteral != "" {
			value := exp.Literal.BooleanLiteral
			if value != "True" && value != "False" {
				return nil, errors.Errorf("Invalid Boolean Literal = %s", value)
			}
			return EB.LiteralBoolean(value == "True"), nil
		} else if exp.Literal.EnumLiteral != "" {
			value := strings.ToLower(exp.Literal.EnumLiteral)
			return EB.LiteralEnumValue(value), nil
		} else {
			return nil, errors.New("unsupported numerical constant")
		}
	} else if exp.Cast != nil {
		castExpr, err := convertExpression(exp.Cast.Expression)
		if err != nil {
			return nil, err
		}
		return EB.Cast(exp.Cast.Type.ToRMLTypeName(), castExpr), nil
	} else if exp.FieldAccess != nil {
		return EB.Field(exp.FieldAccess.String()), nil
	} else if exp.SubExpression != nil {
		return convertExpression(exp.SubExpression)
	} else if exp.IfThenElse != nil {
		branchExp, err := convertExpression(exp.IfThenElse.BranchExpression)
		if err != nil {
			return nil, err
		}
		thenExp, err := convertExpression(exp.IfThenElse.ThenExpression)
		if err != nil {
			return nil, err
		}
		elseExp, err := convertExpression(exp.IfThenElse.ElseExpression)
		if err != nil {
			return nil, err
		}
		return EB.ITE(branchExp, thenExp, elseExp), nil
	}
	return nil, errors.New("unsupported atomic expression")
}

// Parses Namespace into NamespaceBuilder
//
// This function assumes that only children namespaces and properties/output
// fields can be defined inside a namespace. This is due to the definition of
// NamespacePart.
func parseNamespaces(
	gb *graph.SemanticGraphBuilder,
	namespace *Namespace,
	nb *graph.NamespaceBuilder,
) error {
	for _, part := range namespace.NamespaceParts {
		if part.Namespace != nil {
			var child *graph.NamespaceBuilder
			child = nb.GetChildNamespace(part.Namespace.Name)
			if child == nil {
				child = graph.NewNamespaceBuilder(part.Namespace.Name)
				gb.AddNamespaceBuilder(child)
				child.SetParentNamespace(nb)
				nb.AddChildNamespace(child)
				nb.SetLineNumbers(part.Namespace.StartLine, part.Namespace.EndLine)
			} else {
				if child.Guard != nil && part.Namespace.Guard != nil {
					return errors.Errorf("Found Multiple Guards for Namespace %s with Id %s", nb.Name, nb.Id)
				}
			}
			if part.Namespace.Guard != nil {
				exp, err := convertExpression(part.Namespace.Guard.Expression)
				if err != nil {
					return err
				}
				child.Guard = &graph.ExpReprWithLineNumber{
					Expr: exp,
					Line: part.Namespace.Guard.Line,
				}
			}
			err := parseNamespaces(gb, part.Namespace, child)
			if err != nil {
				return err
			}
		} else if part.Property != nil {
			// TODO: This part can be packaged into a function
			prop := part.Property
			etb := gb.GetEntityTypeBuilder(prop.OwnerEntity)
			if etb == nil {
				return errors.New("No entity with name '" + prop.OwnerEntity + "' exists")
			}
			propExpr, err := convertExpression(prop.Expression)
			if err != nil {
				return err
			}
			checkExprs, err := convertCheck(prop.Checks)
			if err != nil {
				return err
			}
			etb.AddComputedField(
				prop.Name,
				prop.Type.ToRMLTypeName(),
				propExpr,
				prop.PropertyClass == "output",
				checkExprs,
				nb,
				prop.StartLine,
				prop.EndLine,
			)
		} else {
			return errors.Errorf("Unsupported Namespace Expression %v", part)
		}
	}
	return nil
}

// ParseRateMLProgram parses programText to SemanticGraphBuilder.
func ParseRateMLProgram(programText string, gb *graph.SemanticGraphBuilder) error {
	ast, err := ParseProgramFromString(programText)
	if err != nil {
		return err
	}

	for _, e := range ast.getEnums() {
		if e.HasEntries {
			rmlEnum := graph.NewEnumType(
				e.Name.String(),
				e.Entries...)
			gb.AddEnumWithConcreteValues(rmlEnum)
		} else {
			gb.AddEnumWithExternalValues(e.Name.String())
		}
	}

	for _, typ := range ast.getTypes() {
		etb := graph.NewEntityTypeBuilder(typ.Name)
		gb.AddEntityTypeBuilder(etb)
		etb.AddLineNumbers(typ.StartLine, typ.EndLine)
		for _, f := range typ.Fields {
			rcExpr, err := convertCheck(f.Checks)
			if err != nil {
				return err
			}

			etb.AddConcreteField(f.Name, f.Type.ToRMLTypeName(), rcExpr)
		}
	}

	for _, typExt := range ast.getTypeExtensions() {
		typ := typExt.Entry

		etb := gb.GetEntityTypeBuilder(typ.Name)
		if etb == nil {
			return errors.Newf("Entity %s not found (can't extend it)", typ.Name)
		}

		etb.AddExtensionLineNumbers(typ.StartLine, typ.EndLine)
		for _, f := range typ.Fields {
			rcExpr, err := convertCheck(f.Checks)
			if err != nil {
				return err
			}

			etb.AddConcreteFieldFromTypeExtension(f.Name, f.Type.ToRMLTypeName(), rcExpr)
		}
	}

	// Store the NamespaceConfig separately in GraphBuilder
	// We assume here that the NamespaceConfig entity can only have concrete fields
	config, err := ast.getNamespaceConfig()
	if err != nil {
		return err
	}
	if config != nil {
		configEntity := graph.NewEntityTypeBuilder(config.Name)
		configEntity.AddLineNumbers(config.StartLine, config.EndLine)
		gb.AddNamespaceConfigBuilder(configEntity)
		for i := range config.Fields {
			rcExpr, err := convertCheck(config.Fields[i].Checks)
			if err != nil {
				return err
			}
			configEntity.AddConcreteField(config.Fields[i].Name, config.Fields[i].Type.ToRMLTypeName(), rcExpr)
		}
	}

	rootNamespaceBuilder := graph.NewRootNamespaceBuilder()
	gb.AddNamespaceBuilder(rootNamespaceBuilder)

	for _, prop := range ast.getProperties() {
		etb := gb.GetEntityTypeBuilder(prop.OwnerEntity)
		if etb == nil {
			return errors.New("No entity with name '" + prop.OwnerEntity + "' exists")
		}

		propExpr, err := convertExpression(prop.Expression)
		if err != nil {
			return err
		}

		rcExpr, err := convertCheck(prop.Checks)
		if err != nil {
			return err
		}

		etb.AddComputedField(
			prop.Name,
			prop.Type.ToRMLTypeName(),
			propExpr,
			prop.PropertyClass == "output",
			rcExpr,
			rootNamespaceBuilder, // The default namespace is root
			prop.StartLine,
			prop.EndLine,
		)
	}

	for _, propOverride := range ast.getPropertyOverrides() {
		prop := propOverride.Property

		etb := gb.GetEntityTypeBuilder(prop.OwnerEntity)
		if etb == nil {
			return errors.New("No entity with name '" + prop.OwnerEntity + "' exists")
		}

		propExpr, err := convertExpression(prop.Expression)
		if err != nil {
			return err
		}

		rcExpr, err := convertCheck(prop.Checks)
		if err != nil {
			return err
		}

		if !etb.ComputedFieldExistsInNamespace(rootNamespaceBuilder.Id, prop.Name) {
			return errors.Errorf(
				"Property %s not found in root namespace for entity %s (can't override it)",
				prop.Name,
				prop.OwnerEntity,
			)
		}

		etb.OverrideComputedField(
			prop.Name,
			prop.Type.ToRMLTypeName(),
			propExpr,
			prop.PropertyClass == "output",
			rcExpr,
			rootNamespaceBuilder,
			prop.StartLine,
			prop.EndLine,
		)
	}

	namespaces := ast.getTopLevelNamespaces()
	for i := range namespaces {
		var nb *graph.NamespaceBuilder
		nb = rootNamespaceBuilder.GetChildNamespace(namespaces[i].Name)
		if nb == nil {
			nb = graph.NewNamespaceBuilder(namespaces[i].Name)
			gb.AddNamespaceBuilder(nb)
			nb.SetParentNamespace(rootNamespaceBuilder)
			rootNamespaceBuilder.AddChildNamespace(nb)
			nb.SetLineNumbers(namespaces[i].StartLine, namespaces[i].EndLine)
		} else {
			if nb.Guard != nil && namespaces[i].Guard != nil {
				return errors.Errorf("Found Multiple Guards for Namespace %s with Id %s", nb.Name, nb.Id)
			}
		}
		if namespaces[i].Guard != nil {
			exp, err := convertExpression(namespaces[i].Guard.Expression)
			if err != nil {
				return err
			}
			nb.Guard = &graph.ExpReprWithLineNumber{
				Expr: exp,
				Line: namespaces[i].Guard.Line,
			}
		}
		err := parseNamespaces(gb, namespaces[i], nb)
		if err != nil {
			return err
		}

	}

	for _, typIncl := range ast.getTypeInclusions() {
		baseEntityName := typIncl.IncluderName
		includedEntityName := typIncl.IncludedName

		baseEntityBuilder := gb.GetEntityTypeBuilder(baseEntityName)
		if baseEntityBuilder == nil {
			return errors.Newf("No entity with name %s exists", baseEntityName)
		}

		includedEntityBuilder := gb.GetEntityTypeBuilder(includedEntityName)
		if includedEntityBuilder == nil {
			return errors.Newf("No entity with name %s exists", includedEntityName)
		}

		for _, f := range includedEntityBuilder.GetConcreteFieldInfos() {
			// If the field already exists in the base entity, we don't override it.
			if baseEntityBuilder.ConcreteFieldExists(f.GetFieldName()) {
				continue
			}

			baseEntityBuilder.AddConcreteField(
				f.GetFieldName(),
				f.GetTypeName(),
				f.GetChecks(),
			)
		}

		for _, f := range includedEntityBuilder.GetComputedFieldInfos() {
			// If the field already exists in the base entity, we don't override it.
			if baseEntityBuilder.ComputedFieldExistsInNamespace(f.GetNamespace().Id, f.GetFieldName()) {
				continue
			}

			baseEntityBuilder.AddComputedField(
				f.GetFieldName(),
				f.GetTypeName(),
				f.GetExpRepr(),
				f.IsOutput(),
				f.GetChecks(),
				f.GetNamespace(),
				f.GetStartLine(),
				f.GetEndLine(),
			)
		}

		baseEntityBuilder.AddInclusionLineNumbers(typIncl.Line)
	}

	tables := ast.getLookupTables()
	for i := range tables {
		table := tables[i]

		builder, err := createLookupTableBuilder(table)
		if err != nil {
			return errors.Wrapf(err, "Failed to parse lookup table %s", table.Name)
		}

		gb.AddLookupTableBuilders(builder)
	}

	tableOverrides := ast.getLookupTableOverrides()
	for i := range tableOverrides {
		tableOverride := tableOverrides[i]
		table := tableOverride.LookupTable

		if !gb.LookupTableBuilderAlreadyAdded(table.Name) {
			return errors.Errorf("Lookup table %s not found (can't override it)", table.Name)
		}

		builder, err := createLookupTableBuilder(table)
		if err != nil {
			return errors.Wrapf(err, "Failed to parse lookup table override %s", table.Name)
		}

		gb.OverrideLookupTableBuilder(builder)
	}

	return nil
}

func createLookupTableBuilder(
	table *LookupTable,
) (*graph.LookupTableBuilder, error) {
	builder := graph.NewLookupTableBuilder(table.Name, len(table.Inputs.Columns), len(table.Outputs.Columns))

	// header is only used for inline data source
	header := make([]string, 0)
	tableLineNumbers := graph.LookupTableLineNumbers{}

	for inputIdx := range table.Inputs.Columns {
		in := table.Inputs.Columns[inputIdx]

		// For range type inputs, we create two columns
		if in.IsRangeType() {
			header = append(header, in.Name+graph.RangeColumnLowerSuffix)
			header = append(header, in.Name+graph.RangeColumnUpperSuffix)
		} else {
			header = append(header, in.Name)
		}

		builder.AddInput(inputIdx, in.Name, in.Type.ToRMLTypeName())
	}
	tableLineNumbers.InputsStartLine = table.Inputs.StartLine
	tableLineNumbers.InputsEndLine = table.Inputs.EndLine

	for outputIdx := range table.Outputs.Columns {
		out := table.Outputs.Columns[outputIdx]
		header = append(header, out.Name)
		builder.AddOutput(outputIdx, out.Name, out.Type.ToRMLTypeName())
	}
	tableLineNumbers.OutputsStartLine = table.Outputs.StartLine
	tableLineNumbers.OutputsEndLine = table.Outputs.EndLine

	if table.InlineData != nil {
		cells, err := table.InlineData.ToStrings()
		if err != nil {
			return nil, err
		}
		var inlineDS graph.LookupTableDataSource = &inlineLookupTableData{
			cells: cells, header: header,
		}
		builder.AddInlineDataSource(inlineDS)
		tableLineNumbers.DataSourceStartLine = table.InlineData.StartLine
		tableLineNumbers.DataSourceEndLine = table.InlineData.EndLine
	} else {
		// Set both of these to -1 if inline data is not specified
		tableLineNumbers.DataSourceStartLine = -1
		tableLineNumbers.DataSourceEndLine = -1
	}
	builder.SetLineNumbers(tableLineNumbers)

	return builder, nil
}

type inlineLookupTableData struct {
	cells  [][]string
	header []string
}

func (d *inlineLookupTableData) GetHeader() []string {
	return d.header
}

func (d *inlineLookupTableData) GetRows() [][]string {
	return d.cells
}

var _ graph.LookupTableDataSource = &inlineLookupTableData{}
