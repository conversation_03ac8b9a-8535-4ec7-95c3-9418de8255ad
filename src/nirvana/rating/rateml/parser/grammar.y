%{
package parser

func setResult(l yyLexer, v interface{}) {
  l.(*Lexer).parseResult = v
}

%}

%union{
String string
LineNo int
Program *Program
ProgramPart *ProgramPart
Namespace *Namespace
NamespacePart *NamespacePart
NamespacePartList []*NamespacePart
NamespaceConfig *NamespaceConfig
Entry *Entry
EntryExtension *EntryExtension
EntryInclusion *EntryInclusion
FieldList []*Field
Field *Field
Check *Check
Clause *Clause
Compare *Compare
Member *Member
TypeContainer *TypeContainer
Enum *Enum
LookupTable *LookupTable
LookupTableIODec *LookupTableIODeclaration
LookupTableData *LookupTableInlineData
LookupTableOverride *LookupTableOverride
Row *Row
RowList []*Row
Cell *Cell
Property *Property
PropertyOverride *PropertyOverride
Expression *Expression
ExpList []*Expression
Disjunction *Disjunction
Conjunction *Conjunction
Equality *Equality
Relational *Relational
Negation *Negation
Addition *Addition
Factor *Factor
Atom *Atom
Cast *Cast
FunctionCall *FunctionCall
Switch *Switch
IfThenElse *IfThenElse
SwitchList []*SwitchCase
SwitchCase *SwitchCase
Literal *Literal
FieldAccess *FieldAccess
QName *QName
EnumValuesList []string
IODecList []*LookupTableIODeclaration
ExpLine *ExpressionWithLineNumber
}

%token<String> I_CONSTANT F_CONSTANT B_CONSTANT E_CONSTANT IDENTIFIER
%token<LineNo> PROPERTY OUTPUT LBRACE RBRACE ENTITY REQUIRE GUARD NAMESPACE INPUTS OUTPUTS DATA OVERRIDE EXTEND INCLUDES
%token DEREF AND OR NEQ LTE GTE IN_OP NOTIN_OP
%token LIST RANGE ENUM_TAG LOOKUP
%token SWITCH CASE
%token IF THEN ELSE

%type<Program> program
%type<ProgramPart> program_part
%type<Entry> entry
%type<EntryExtension> entry_extension
%type<EntryInclusion> entry_inclusion
%type<FieldList> field_list
%type<Field> field
%type<TypeContainer> type_container
%type<QName> q_name
%type<Enum> enum
%type<EnumValuesList> enum_values_list
%type<LookupTable> lookup_table
%type<IODecList> lookup_io_dec_list
%type<LookupTableIODec> lookup_io_dec
%type<LookupTableData> lookup_data
%type<LookupTableOverride> lookup_table_override
%type<RowList> row_list
%type<Row> row
%type<Cell> cell
%type<Literal> literal
%type<Property> property
%type<PropertyOverride> property_override
%type<Expression> expression
%type<ExpLine> guard
%type<Disjunction> disjunction
%type<Conjunction> conjunction
%type<Equality> equality
%type<Relational> relational
%type<Negation> negation
%type<Addition> addition
%type<Factor> factor
%type<Atom> atom
%type<Cast> cast
%type<FunctionCall> function_call
%type<ExpList> expression_list
%type<Switch> switch
%type<IfThenElse> if_then_else
%type<SwitchList> switch_case_list
%type<SwitchCase> switch_case
%type<Check> check_list
%type<Clause> check
%type<Clause> clause
%type<Compare> compare
%type<Member> membership
%type<Namespace> namespace
%type<NamespacePartList> namespace_part_list
%type<NamespacePart> namespace_part
%type<NamespaceConfig> namespace_config
%type<FieldAccess> field_access

%start program

%%
program:	  program program_part	{ $1.Parts = append($1.Parts, $2); $$ = $1; setResult(yylex, $$)}
		| /* empty */	{ $$ = &Program{}; setResult(yylex, $$) }
;

program_part: entry_inclusion { $$ = &ProgramPart{TypeInclusion: $1} }
    | entry_extension { $$ = &ProgramPart{TypeExtension: $1} }
    | entry	{ $$ = &ProgramPart{Type: $1} }
		| enum	{ $$ = &ProgramPart{Enum: $1} }
		| lookup_table_override { $$ = &ProgramPart{LookupTableOverride: $1} }
		| lookup_table	{ $$ = &ProgramPart{LookupTable: $1} }
		| property_override	{ $$ = &ProgramPart{PropertyOverride: $1} }
		| property	{ $$ = &ProgramPart{Property: $1} }
		| namespace	{ $$ = &ProgramPart{Namespace: $1} }
		| namespace_config	{ $$ = &ProgramPart{NamespaceConfig: $1} }
;

entry_inclusion: ENTITY IDENTIFIER INCLUDES IDENTIFIER
    { $$ = &EntryInclusion{IncluderName: $2, IncludedName: $4, Line: $1} }

entry_extension: EXTEND entry { $$ = &EntryExtension{Entry: $2} }
;

entry:	ENTITY IDENTIFIER LBRACE field_list RBRACE	{ $$ = &Entry{Name: $2, Fields: $4, StartLine: $1, EndLine: $5} }
;

field_list:	  field_list field	{ $$ = append($1, $2) }
		| /* empty */	{$$ = make([]*Field, 0) }
;

field:	check_list IDENTIFIER type_container	{ $$ = &Field{Checks: $1, Name: $2, Type: $3} }
;

type_container:	  LIST '[' type_container ']'	{$$ = &TypeContainer{Array: $3} }
		| RANGE '[' type_container ']'	{$$ = &TypeContainer{Range: $3} }
		| q_name	{$$ = &TypeContainer{Type: $1} }
;

q_name:	  IDENTIFIER	{ $$ = &QName{Path: []string{$1}} }
	| q_name '.' IDENTIFIER	{ $1.Path = append($1.Path, $3); $$ = $1 }
;

enum:	ENUM_TAG q_name	{ $$ = &Enum{Name: $2, HasEntries: false} }
	| ENUM_TAG q_name LBRACE enum_values_list RBRACE	{ $$ = &Enum{Name: $2, HasEntries: true, Entries: $4} }
;

enum_values_list:	  enum_values_list E_CONSTANT	{ $$ = append($1, $2) }
			| /* empty */	{ $$ = make([]string, 0) }
;

lookup_table_override: '@' OVERRIDE lookup_table { $$ = &LookupTableOverride{LookupTable: $3} }
;

lookup_table:	  LOOKUP IDENTIFIER LBRACE
			INPUTS LBRACE lookup_io_dec_list RBRACE
			OUTPUTS LBRACE lookup_io_dec_list RBRACE
			lookup_data
		  RBRACE	{ inputs := &LookupTableIOWithLineNumbers{Columns: $6, StartLine: $4, EndLine: $7};
		  		outputs := &LookupTableIOWithLineNumbers{Columns: $10, StartLine: $8, EndLine: $11};
		  		$$ = &LookupTable{Name: $2, Inputs: inputs, Outputs: outputs, InlineData: $12} }
;

lookup_io_dec_list:	  lookup_io_dec_list lookup_io_dec	{ $$ = append($1, $2) }
			| /* empty */	{ $$ = make([]*LookupTableIODeclaration, 0) }
;

lookup_io_dec:	  IDENTIFIER type_container	{ $$ = &LookupTableIODeclaration{Name: $1, Type: $2} }
;

lookup_data:	  DATA LBRACE row_list RBRACE	{ $$ = &LookupTableInlineData{Rows: $3, StartLine: $1, EndLine: $4} }
		| /* empty */	{ $$ = nil }
;

row_list:	 row	{ $$ = []*Row{$1} }
		| row_list row	{ $$ = append($1, $2) }
;

row:	  cell	{ $$ = &Row{Cells: []*Cell{$1}} }
	| row ',' cell	{ $1.Cells = append($1.Cells, $3); $$ = $1 }
;

cell:	  literal	{ $$ = &Cell{Literal: $1} }
	| '_'		{ $$ = &Cell{Any: "_"} }
;

literal:	  F_CONSTANT	{ $$ = &Literal{DecimalLiteral: $1} }
		| I_CONSTANT	{ $$ = &Literal{IntegerLiteral: $1} }
		| B_CONSTANT	{ $$ = &Literal{BooleanLiteral: $1} }
		| E_CONSTANT	{ $$ = &Literal{EnumLiteral: $1} }
;

property_override: '@' OVERRIDE property { $$ = &PropertyOverride{Property: $3} }
;

property:	  check_list PROPERTY IDENTIFIER IDENTIFIER type_container LBRACE expression RBRACE
			{ $$ = &Property{Checks: $1, PropertyClass: "property", OwnerEntity: $3, Name: $4, Type: $5, Expression: $7, StartLine: $2, EndLine:$8} }
		| check_list OUTPUT IDENTIFIER IDENTIFIER type_container LBRACE expression RBRACE
			{ $$ = &Property{Checks: $1, PropertyClass: "output", OwnerEntity: $3, Name: $4, Type: $5, Expression: $7, StartLine: $2, EndLine:$8} }
;

expression:	disjunction	{ $$ = &Expression{Body: $1} }
;

disjunction:	  disjunction OR conjunction	{ $$ = &Disjunction{LeftExpr: $1, Op: "||", RightExpr: $3} }
		| conjunction	{ $$ = &Disjunction{RightExpr: $1} }
;

conjunction:	  conjunction AND equality	{ $$ = &Conjunction{LeftExpr: $1, Op: "&&", RightExpr: $3} }
		| equality	{ $$ = &Conjunction{RightExpr: $1} }
;

equality:	  equality '=' relational	{ $$ = &Equality{LeftExpr: $1, Op: "=", RightExpr: $3} }
		| equality NEQ relational	{ $$ = &Equality{LeftExpr: $1, Op: "!=", RightExpr: $3} }
		| relational	{ $$ = &Equality{RightExpr: $1} }
		;

relational:	  relational '<' negation	{ $$ = &Relational{LeftExpr: $1, Op: "<", RightExpr: $3} }
		| relational '>' negation	{ $$ = &Relational{LeftExpr: $1, Op: ">", RightExpr: $3} }
		| relational LTE negation	{ $$ = &Relational{LeftExpr: $1, Op: "<=", RightExpr: $3} }
		| relational GTE negation	{ $$ = &Relational{LeftExpr: $1, Op: ">=", RightExpr: $3} }
		| negation	{ $$ = &Relational{RightExpr: $1} }
;

negation:	  '!' addition	{ $$ = &Negation{Op: "!", UnaryExpr: $2} }
		| addition	{ $$ = &Negation{UnaryExpr: $1} }
;

addition:	  addition '+' factor	{ $$ = &Addition{LeftExpr: $1, Op: "+", RightExpr: $3} }
		| addition '-' factor	{ $$ = &Addition{LeftExpr: $1, Op: "-", RightExpr: $3} }
		| factor	{ $$ = &Addition{RightExpr: $1} }
;

factor:	  factor '*' atom	{ $$ = &Factor{LeftExpr: $1, Op: "*", RightExpr: $3} }
	| factor '/' atom	{ $$ = &Factor{LeftExpr: $1, Op: "/", RightExpr: $3} }
	| atom	{ $$ = &Factor{RightExpr: $1} }
;

atom:	  '[' q_name ']'	{ $$ = &Atom{TypeRef: $2} }
	| switch		{ $$ = &Atom{Switch: $1} }
	| if_then_else		{ $$ = &Atom{IfThenElse: $1}}
	| function_call		{ $$ = &Atom{FunctionCall: $1} }
	| literal		{ $$ = &Atom{Literal: $1} }
	| cast			{ $$ = &Atom{Cast: $1} }
	| field_access		{ $$ = &Atom{FieldAccess: $1} }
	| '(' expression ')'	{ $$ = &Atom{SubExpression: $2} }
;

cast:	type_container LBRACE expression RBRACE	{ $$ = &Cast{Type: $1, Expression: $3} }
;

function_call:	  IDENTIFIER '(' expression_list ')'	{ $$ = &FunctionCall{Name: $1, Arguments: $3} }
		| IDENTIFIER '(' ')'	{ $$ = &FunctionCall{Name: $1} }
;

expression_list:	  expression_list ',' expression	{ $$ = append($1, $3) }
			| expression	{ $$ = []*Expression{$1} }
;

switch:	SWITCH '(' expression ')' LBRACE switch_case_list RBRACE	{ $$ = &Switch{SwitchExpression: $3, Cases: $6} }
;

switch_case_list:	  switch_case_list switch_case	{ $$ = append($1, $2) }
			| /* empty */	{ $$ = make([]*SwitchCase, 0) }
;

switch_case:	  CASE expression ':' expression	{ $$ = &SwitchCase{MatchExpression: $2, IsDefault: false, ValueExpression: $4} }
		| CASE '_' ':' expression	{ $$ = &SwitchCase{IsDefault: true, ValueExpression: $4} }
;

if_then_else:	IF '(' expression ')' THEN LBRACE expression RBRACE ELSE LBRACE expression RBRACE
	{ $$ = &IfThenElse{BranchExpression: $3, ThenExpression: $7, ElseExpression: $11} }
;

field_access:	  IDENTIFIER	{ $$ = &FieldAccess{Path: []string{$1}} }
		| field_access DEREF IDENTIFIER	{ $1.Path = append($1.Path, $3); $$ = $1 }
;

check_list:	  check check_list	{ $2.ClauseList = append([]*Clause{$1}, $2.ClauseList...); $$ = $2}
			| /* empty */	{ $$ = &Check{} }
;

check:	'@' REQUIRE '(' clause ')' { $4.Line = $2; $$ = $4 }
;

clause:	  IDENTIFIER compare	{ $$ = &Clause{LeftIdent: $1, Comp: $2} }
	| IDENTIFIER membership	{ $$ = &Clause{LeftIdent: $1, Mem: $2} }
;

compare:	  '=' expression	{ $$ = &Compare{Op: "=", RightExpr: $2} }
		| NEQ expression	{ $$ = &Compare{Op: "!=", RightExpr: $2} }
		| '<' expression	{ $$ = &Compare{Op: "<", RightExpr: $2} }
		| LTE expression	{ $$ = &Compare{Op: "<=", RightExpr: $2} }
		| '>' expression	{ $$ = &Compare{Op: ">", RightExpr: $2} }
		| GTE expression	{ $$ = &Compare{Op: ">=", RightExpr: $2} }
;

membership:	  IN_OP '[' expression_list ']'		{ $$ = &Member{Op: "in", ExprSet: $3} }
	| NOTIN_OP '[' expression_list ']'	{ $$ = &Member{Op: "notin", ExprSet: $3} }
;

namespace:	guard NAMESPACE IDENTIFIER LBRACE namespace_part_list RBRACE	{ $$ = &Namespace{Guard: $1, Name: $3, NamespaceParts: $5, StartLine: $2, EndLine: $6} }
;

guard:	  '@' GUARD '(' expression ')'	{ $$ = &ExpressionWithLineNumber{Expression: $4, Line: $2} }
	| /* empty */	{ $$ = nil }
;

namespace_part_list:	namespace_part_list namespace_part	{ $$ = append($1, $2) }
			| /* empty */	{ $$ = make([]*NamespacePart, 0) }
;

namespace_part:	  namespace	{ $$ = &NamespacePart{Namespace: $1} }
		| property	{ $$ = &NamespacePart{Property: $1} }
;

namespace_config:	IDENTIFIER LBRACE field_list RBRACE	{ $$ = &NamespaceConfig{Name: $1, Fields: $3, StartLine: $2, EndLine: $4} }
;


%%
