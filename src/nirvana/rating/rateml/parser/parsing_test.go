package parser

import (
	"testing"

	"github.com/cockroachdb/errors"

	"github.com/stretchr/testify/assert"
)

func TestParseEntity(t *testing.T) {
	p, err := parseProgram(
		`
		entity PolicyApplication {
			userInput PolicyUserInput
			owners list[Owner]
			pets list[Pet]
			numDogs number.integer
			numCats number.integer
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
}

func TestParseEnum(t *testing.T) {
	p, err := parseProgram(
		`
		tag_enum dog.Breed {
			"Bulldog"
			"GoldenRetriever"
			"Labrador Retriever"
			"German-Shepherd"
			"Curly-coated Retriever"
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
}

func TestParseTable(t *testing.T) {
	p, err := parseProgram(
		`
		lookup_table MultiplePetMultiplierTable {
		  inputs {
			numDogs range[number.integer]
			numCats range[number.integer]
		  }
		  outputs {
			value number.integer
		  }
		  data {
			0,0,0,0,1
			0,0,1,100,1
			1,100,0,0,1
			1,100,1,100,3
		  }
		}
		lookup_table Table2 {
		  inputs {
			numDogs range[number.integer]
			numCats range[number.integer]
		  }
		  outputs {
			value number.integer
		  }
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
}

func TestParseProperty(t *testing.T) {
	p, err := parseProgram(
		`
		property Pet multiplePetsMultiplier number.integer {
			lookup([MultiplePetMultiplierTable], [MultiplePetMultiplierTable.value], app->numDogs, app->numCats)
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
}

func TestParseNamespace(t *testing.T) {
	p, err := parseProgram(
		`
		@guard(n->x > y)
		namespace foo {
			property Pet multiplePetsMultiplier number.integer {
				lookup([MultiplePetMultiplierTable], [MultiplePetMultiplierTable.value], app->numDogs, app->numCats)
			}
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
}

func TestParseNamespaceNested(t *testing.T) {
	p, err := parseProgram(
		`
		namespace foo {
			property Pet multiplePetsMultiplier number.integer {
				lookup([MultiplePetMultiplierTable], [MultiplePetMultiplierTable.value], app->numDogs, app->numCats)
			}
			@guard(Bar && !Foo)
			namespace bar {
				output Pet MultiNum number.integer {
					numDogs * 10
				}
			}
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
}

func TestParseNamespaceAssertions(t *testing.T) {
	p, err := parseProgram(
		`
		namespace foo {
			@require(multiplePetsMultiplier != 2)
			property Pet multiplePetsMultiplier number.integer {
				lookup([MultiplePetMultiplierTable], [MultiplePetMultiplierTable.value], app->numDogs, app->numCats)
			}
			@guard(False)
			namespace bar {
				@require(MultiNum < 100)
				@require(MultiNum > 0)
				output Pet MultiNum number.integer {
					numDogs * 10
				}
			}
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
}

// Expression tests

func TestTypeRef(t *testing.T) {
	exp, err := parseExpression("[MultiplePetMultiplierTable]")
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.TypeRef)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.TypeRef.String(), "MultiplePetMultiplierTable")
}

func TestSwitch_SimpleEnum(t *testing.T) {
	exp, err := parseExpression(
		`switch(x) {
			case "A": 0
			case 10: 1
			case 5.1: 2
			case True: 3
			case B: 4
			case myfunc(): 6
		    case y+z: 5
			case v && w: 7
		}`)
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	sw := exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Switch
	assert.NotNil(t, sw)
	assert.Equal(t, len(sw.Cases), 8)

	// Corresponds to x
	assert.NotNil(t, sw.SwitchExpression.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FieldAccess)
	// Corresponds to A
	assert.NotEmpty(t, sw.Cases[0].MatchExpression.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal.EnumLiteral)
	// Corresponds to 10
	assert.NotEmpty(t, sw.Cases[1].MatchExpression.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal.IntegerLiteral)
	// Corresponds to 5.1
	assert.NotEmpty(t, sw.Cases[2].MatchExpression.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal.DecimalLiteral)
	// Corresponds to True
	assert.NotEmpty(t, sw.Cases[3].MatchExpression.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal.BooleanLiteral)
	// Corresponds to B
	assert.NotNil(t, sw.Cases[4].MatchExpression.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FieldAccess)
	// Corresponds to myfunc()
	assert.NotNil(t, sw.Cases[5].MatchExpression.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FunctionCall)
	// Corresponds to y
	assert.NotNil(t, sw.Cases[6].MatchExpression.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.LeftExpr.RightExpr.RightExpr.FieldAccess)
	// Corresponds to z
	assert.NotNil(t, sw.Cases[6].MatchExpression.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FieldAccess)
	// Corresponds to v
	assert.NotNil(t, sw.Cases[7].MatchExpression.Body.RightExpr.LeftExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FieldAccess)
	// Corresponds to w
	assert.NotNil(t, sw.Cases[7].MatchExpression.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FieldAccess)
}

func TestFunctionCallNoArgs(t *testing.T) {
	exp, err := parseExpression("fnName()")
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FunctionCall)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FunctionCall.Name, "fnName")
	assert.Equal(t, len(exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FunctionCall.Arguments), 0)
}

func TestFunctionCallArgs(t *testing.T) {
	exp, err := parseExpression("fnName(1, x, fn2())")
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FunctionCall)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FunctionCall.Name, "fnName")
	assert.Equal(t, len(exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FunctionCall.Arguments), 3)
}

func TestIntLiteralExpression(t *testing.T) {
	exp, err := parseExpression("1")
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal.IntegerLiteral)
}

func TestDecimalLiteralExpression(t *testing.T) {
	exp, err := parseExpression("1.0")
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal.DecimalLiteral)
}

func TestBooleanLiteralTrueExpression(t *testing.T) {
	exp, err := parseExpression("True")
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal.BooleanLiteral)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal.BooleanLiteral, "True")
}

func TestBooleanLiteralFalseExpression(t *testing.T) {
	exp, err := parseExpression("False")
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal.BooleanLiteral)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal.BooleanLiteral, "False")
}

func TestFieldAccessExpression(t *testing.T) {
	exp, err := parseExpression("x")
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FieldAccess)
}

func TestFieldAccessExpressionMulti(t *testing.T) {
	exp, err := parseExpression("x->y->z")
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FieldAccess)
	fa := exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.FieldAccess
	assert.Equal(t, len(fa.Path), 3)
}

func TestCastExpression(t *testing.T) {
	exp, err := parseExpression("number.integer{1.5}")
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Cast)

	c := exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Cast
	assert.Equal(t, c.Type.ToRMLTypeName(), "number.integer")
	assert.Equal(t, c.Expression.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.Literal.DecimalLiteral, "1.5")
}

func TestSubExpression(t *testing.T) {
	exp, err := parseExpression("(1 + 2 * 3)")
	assert.Nil(t, err)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.SubExpression)
}

func TestFactorExpression(t *testing.T) {
	exp, err := parseExpression("1 * x / y")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.Op, "")
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	f := exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr
	assert.NotNil(t, f.LeftExpr)
	assert.NotNil(t, f.RightExpr)
	assert.Equal(t, f.Op, "/")
}

func TestAdditionExpression(t *testing.T) {
	exp, err := parseExpression("x + y * z")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.Op, "")
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	add := exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr
	assert.NotNil(t, add.LeftExpr)
	assert.NotNil(t, add.RightExpr)
	assert.Equal(t, add.Op, "+")
}

func TestSubtractionLeftAssociativity(t *testing.T) {
	exp, err := parseExpression("x - y - z")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.Op, "")
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	sub := exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr
	assert.NotNil(t, sub.RightExpr)
	assert.Equal(t, sub.Op, "-")
	assert.NotNil(t, sub.LeftExpr)
	assert.NotNil(t, sub.LeftExpr.LeftExpr)
	assert.NotNil(t, sub.LeftExpr.RightExpr)
	assert.Equal(t, sub.LeftExpr.Op, "-")
}

func TestDivisionLeftAssociativity(t *testing.T) {
	exp, err := parseExpression("x / y / z")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.Op, "")
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	div := exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr
	assert.NotNil(t, div.RightExpr)
	assert.Equal(t, div.Op, "/")
	assert.NotNil(t, div.LeftExpr)
	assert.NotNil(t, div.LeftExpr.LeftExpr)
	assert.NotNil(t, div.LeftExpr.RightExpr)
	assert.Equal(t, div.LeftExpr.Op, "/")
}

func TestEqualityExpression(t *testing.T) {
	exp, err := parseExpression("x + y != z")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	neq := exp.Body.RightExpr.RightExpr
	assert.NotNil(t, neq.LeftExpr)
	assert.NotNil(t, neq.RightExpr)
	assert.Equal(t, neq.Op, "!=")
}

func TestConjunctionExpression(t *testing.T) {
	exp, err := parseExpression("(x < y) && z")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	and := exp.Body.RightExpr
	assert.NotNil(t, and.LeftExpr)
	assert.NotNil(t, and.RightExpr)
	assert.Equal(t, and.Op, "&&")
}

func TestDisjunctionExpression(t *testing.T) {
	exp, err := parseExpression("x || y && z")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.LeftExpr)
	assert.Equal(t, exp.Body.Op, "||")
	assert.NotNil(t, exp.Body.RightExpr)
	conj := exp.Body.RightExpr
	assert.NotNil(t, conj.LeftExpr)
	assert.NotNil(t, conj.RightExpr)
	assert.Equal(t, conj.Op, "&&")
}

func TestChainedComparisonExpression(t *testing.T) {
	exp, err := parseExpression("x < y <= z")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.LeftExpr)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.RightExpr.Op, "<=")
	l := exp.Body.RightExpr.RightExpr.RightExpr.LeftExpr
	assert.NotNil(t, l.LeftExpr)
	assert.Equal(t, l.Op, "<")
	assert.NotNil(t, l.RightExpr)
}

func TestComparisonExpression(t *testing.T) {
	exp, err := parseExpression("x + y <= 2 * z")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	cmp := exp.Body.RightExpr.RightExpr.RightExpr
	assert.NotNil(t, cmp.LeftExpr)
	assert.Equal(t, cmp.Op, "<=")
	assert.NotNil(t, cmp.RightExpr)
}

func TestNegationLiteral(t *testing.T) {
	exp, err := parseExpression("!False")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.Nil(t, exp.Body.RightExpr.RightExpr.RightExpr.LeftExpr)
	neg := exp.Body.RightExpr.RightExpr.RightExpr.RightExpr
	assert.Equal(t, neg.Op, "!")
	assert.NotNil(t, neg.UnaryExpr)
	assert.NotNil(t, neg.UnaryExpr.RightExpr)
	assert.NotNil(t, neg.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, neg.UnaryExpr.RightExpr.RightExpr.Literal)
	assert.NotNil(t, neg.UnaryExpr.RightExpr.RightExpr.Literal.BooleanLiteral)
}

func TestCompoundNegation(t *testing.T) {
	exp, err := parseExpression("!x && y")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.Equal(t, exp.Body.RightExpr.Op, "&&")
	assert.NotNil(t, exp.Body.RightExpr.LeftExpr)
	assert.NotNil(t, exp.Body.RightExpr.LeftExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.LeftExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.LeftExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.Equal(t, exp.Body.RightExpr.LeftExpr.RightExpr.RightExpr.RightExpr.Op, "!")
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
}

func TestNegation(t *testing.T) {
	exp, err := parseExpression("!(a < 0) = z")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.Op, "=")
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.LeftExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.LeftExpr.RightExpr)
	assert.Equal(t, exp.Body.RightExpr.RightExpr.LeftExpr.RightExpr.RightExpr.Op, "!")
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.LeftExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.LeftExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.LeftExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.LeftExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.SubExpression)
}

func TestConditionals(t *testing.T) {
	exp, err := parseExpression("if (True) then { 4 } else { 5 } ")
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	ite := exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.IfThenElse
	assert.NotNil(t, ite)
	assert.NotNil(t, ite.BranchExpression)
	assert.NotNil(t, ite.ThenExpression)
	assert.NotNil(t, ite.ElseExpression)
}

func TestConditionalsNested(t *testing.T) {
	exp, err := parseExpression(
		`if (b) then {
				if (False) then {
					5.5
				} else {
					0.1
				}
			} else {
				if (c) then {a} else {b}
			}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, exp)
	assert.NotNil(t, exp.Body)
	assert.NotNil(t, exp.Body.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr)
	assert.NotNil(t, exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr)
	ite := exp.Body.RightExpr.RightExpr.RightExpr.RightExpr.UnaryExpr.RightExpr.RightExpr.IfThenElse
	assert.NotNil(t, ite)
	assert.NotNil(t, ite.BranchExpression)
	assert.NotNil(t, ite.ThenExpression)
	assert.NotNil(t, ite.ElseExpression)
}

func TestRangeCheckEntity(t *testing.T) {
	p, err := parseProgram(
		`
		entity PolicyApplication {
			@require(userInput < 6)
			userInput number.decimal
			
			@require(GK notin [7, 8.8, bar])
			@require(numCats != 88)
			numCats number.integer
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, len(p.Parts), 1)
	assert.NotNil(t, p.Parts[0].Type)
	assert.Equal(t, len(p.Parts[0].Type.Fields), 2)
	assert.NotNil(t, p.Parts[0].Type.Fields[0].Checks)
	assert.Equal(t, len(p.Parts[0].Type.Fields[0].Checks.ClauseList), 1)
	assert.Equal(t, p.Parts[0].Type.Fields[0].Checks.ClauseList[0].LeftIdent, "userInput")
	assert.NotNil(t, p.Parts[0].Type.Fields[0].Checks.ClauseList[0].Comp)
	assert.Equal(t, p.Parts[0].Type.Fields[1].Checks.ClauseList[0].LeftIdent, "GK")
	assert.Equal(t, len(p.Parts[0].Type.Fields[1].Checks.ClauseList), 2)
	assert.NotNil(t, p.Parts[0].Type.Fields[1].Checks.ClauseList[0].Mem)
	assert.Equal(t, len(p.Parts[0].Type.Fields[1].Checks.ClauseList[0].Mem.ExprSet), 3)
}

func TestRangeCheckProperty(t *testing.T) {
	p, err := parseProgram(
		`
		@require(multiplier <= 1.5)
		@require(multiplier >= 0.0)
		output Dummy multiplier number.decimal {
			lookup([MultiplePetMultiplierTable], [MultiplePetMultiplierTable.value], app->numDogs, app->numCats)
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, len(p.Parts), 1)
	assert.NotNil(t, p.Parts[0].Property)
	assert.NotNil(t, p.Parts[0].Property.Checks)
	assert.Equal(t, len(p.Parts[0].Property.Checks.ClauseList), 2)
	assert.Equal(t, p.Parts[0].Property.Checks.ClauseList[0].LeftIdent, "multiplier")
	assert.NotNil(t, p.Parts[0].Property.Checks.ClauseList[0].Comp)
	assert.NotNil(t, p.Parts[0].Property.Checks.ClauseList[0].Comp.RightExpr)
	assert.NotNil(t, p.Parts[0].Property.Checks.ClauseList[0].Comp.RightExpr.Body)
}

func TestPropertyLineNumberSingleLine(t *testing.T) {
	p, err := parseProgram(
		`property Dummy prop number.integer { x + y }
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, len(p.Parts), 1)
	assert.NotNil(t, p.Parts[0].Property)
	assert.Equal(t, p.Parts[0].Property.StartLine, 1)
	assert.Equal(t, p.Parts[0].Property.EndLine, 1)
}

func TestPropertyLineNumberMultipleLines(t *testing.T) {
	p, err := parseProgram(
		`property Dummy prop number.integer {
			x + y
		}

		output Dummy prop2 number.integer
		{
			x * z
		}

		property Dummy prop3 number.integer {
			x + y
				+ z
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, len(p.Parts), 3)
	assert.NotNil(t, p.Parts[0].Property)
	assert.Equal(t, p.Parts[0].Property.StartLine, 1)
	assert.Equal(t, p.Parts[0].Property.EndLine, 3)
	assert.NotNil(t, p.Parts[1].Property)
	assert.Equal(t, p.Parts[1].Property.StartLine, 5)
	assert.Equal(t, p.Parts[1].Property.EndLine, 8)
	assert.NotNil(t, p.Parts[2].Property)
	assert.Equal(t, p.Parts[2].Property.StartLine, 10)
	assert.Equal(t, p.Parts[2].Property.EndLine, 13)
}

func TestEntityPropertyLineNumber(t *testing.T) {
	p, err := parseProgram(
		`entity Dummy {
			x number.integer
			y number.integer
			z number.integer
		}

		entity ent {
			a boolean
			b number.decimal
		}
		
		property Dummy prop number.integer {
			x + y
		}
		property Dummy prop2 number.integer {
			x + y
				+ z
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, len(p.Parts), 4)
	assert.NotNil(t, p.Parts[0].Type)
	assert.Equal(t, p.Parts[0].Type.StartLine, 1)
	assert.Equal(t, p.Parts[0].Type.EndLine, 5)
	assert.NotNil(t, p.Parts[1].Type)
	assert.Equal(t, p.Parts[1].Type.StartLine, 7)
	assert.Equal(t, p.Parts[1].Type.EndLine, 10)
	assert.NotNil(t, p.Parts[2].Property)
	assert.Equal(t, p.Parts[2].Property.StartLine, 12)
	assert.Equal(t, p.Parts[2].Property.EndLine, 14)
	assert.NotNil(t, p.Parts[3].Property)
	assert.Equal(t, p.Parts[3].Property.StartLine, 15)
	assert.Equal(t, p.Parts[3].Property.EndLine, 18)
}

func TestCheckLineNumber(t *testing.T) {
	p, err := parseProgram(
		`entity Dummy {
			@require(x != 3)
			x number.integer
			y number.integer
			@require(z > 0)
			@require(z < 10)
			z number.integer
		}
		@require(prop = 45)
		property Dummy prop number.integer {
			x + y
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, len(p.Parts), 2)
	assert.NotNil(t, p.Parts[0].Type)
	assert.Equal(t, p.Parts[0].Type.StartLine, 1)
	assert.Equal(t, p.Parts[0].Type.EndLine, 8)
	assert.NotNil(t, p.Parts[1].Property)
	assert.Equal(t, p.Parts[1].Property.StartLine, 10)
	assert.Equal(t, p.Parts[1].Property.EndLine, 12)
	assert.Equal(t, len(p.Parts[0].Type.Fields), 3)
	assert.Equal(t, len(p.Parts[0].Type.Fields[0].Checks.ClauseList), 1)
	assert.Equal(t, p.Parts[0].Type.Fields[0].Checks.ClauseList[0].Line, 2)
	assert.Equal(t, len(p.Parts[0].Type.Fields[2].Checks.ClauseList), 2)
	assert.Equal(t, p.Parts[0].Type.Fields[2].Checks.ClauseList[0].Line, 5)
	assert.Equal(t, p.Parts[0].Type.Fields[2].Checks.ClauseList[1].Line, 6)
	assert.Equal(t, len(p.Parts[1].Property.Checks.ClauseList), 1)
	assert.Equal(t, p.Parts[1].Property.Checks.ClauseList[0].Line, 9)
}

func TestNamespaceGuardLineNumber(t *testing.T) {
	p, err := parseProgram(
		`@guard(NamespaceConfig->enable)
		namespace foo {

		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, len(p.Parts), 1)
	assert.NotNil(t, p.Parts[0].Namespace)
	assert.Equal(t, p.Parts[0].Namespace.StartLine, 2)
	assert.Equal(t, p.Parts[0].Namespace.EndLine, 4)
	assert.NotNil(t, p.Parts[0].Namespace.Guard)
	assert.Equal(t, p.Parts[0].Namespace.Guard.Line, 1)
}

func TestNamespaceConfigLineNumber(t *testing.T) {
	p, err := parseProgram(
		`NamespaceConfig {
			x boolean
			y boolean
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, len(p.Parts), 1)
	assert.NotNil(t, p.Parts[0].NamespaceConfig)
	assert.Equal(t, p.Parts[0].NamespaceConfig.StartLine, 1)
	assert.Equal(t, p.Parts[0].NamespaceConfig.EndLine, 4)
}

func TestLookupTableLineNumber(t *testing.T) {
	p, err := parseProgram(
		`lookup_table lt {
			inputs {
				x range[number.integer]
				y range[number.integer]
			}
			
			outputs {
				z number.integer
			}
			
			data {
				0,1,0,1,2
			}
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, len(p.Parts), 1)
	assert.NotNil(t, p.Parts[0].LookupTable)
	table := p.Parts[0].LookupTable
	assert.NotNil(t, table.Inputs)
	assert.Equal(t, table.Inputs.StartLine, 2)
	assert.Equal(t, table.Inputs.EndLine, 5)
	assert.NotNil(t, table.Outputs)
	assert.Equal(t, table.Outputs.StartLine, 7)
	assert.Equal(t, table.Outputs.EndLine, 9)
	assert.NotNil(t, table.InlineData)
	assert.Equal(t, table.InlineData.StartLine, 11)
	assert.Equal(t, table.InlineData.EndLine, 13)
}

func TestEntityExtension(t *testing.T) {
	p, err := parseProgram(`
		entity Dummy {
			@require(x > 0)
			x number.integer
			y number.decimal
		}

		extend entity Dummy {
			x number.integer
		}

		extend entity Dummy {
			@require(y > 1)
			y number.integer
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, 3, len(p.Parts))
	assert.NotNil(t, p.Parts[0].Type)
	assert.Equal(t, 2, p.Parts[0].Type.StartLine)
	assert.Equal(t, 6, p.Parts[0].Type.EndLine)
	assert.NotNil(t, p.Parts[1].TypeExtension)
	assert.Equal(t, 8, p.Parts[1].TypeExtension.Entry.StartLine)
	assert.Equal(t, 10, p.Parts[1].TypeExtension.Entry.EndLine)
	assert.NotNil(t, p.Parts[2].TypeExtension)
	assert.Equal(t, 12, p.Parts[2].TypeExtension.Entry.StartLine)
	assert.Equal(t, 15, p.Parts[2].TypeExtension.Entry.EndLine)
}

func TestPropertyOverride(t *testing.T) {
	p, err := parseProgram(`
		entity Dummy {
		}
		
		@require(prop > 0)
		property Dummy prop number.integer {
			1
		}

		@override
		property Dummy prop number.decimal {
			2.0
		}

		@override
		@require(prop > 1)
		property Dummy prop number.integer {
			3
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, 4, len(p.Parts))
	assert.NotNil(t, p.Parts[0].Type)
	assert.Equal(t, 2, p.Parts[0].Type.StartLine)
	assert.Equal(t, 3, p.Parts[0].Type.EndLine)
	assert.NotNil(t, p.Parts[1].Property)
	assert.Equal(t, 6, p.Parts[1].Property.StartLine)
	assert.Equal(t, 8, p.Parts[1].Property.EndLine)
	assert.NotNil(t, p.Parts[2].PropertyOverride)
	assert.Equal(t, 11, p.Parts[2].PropertyOverride.Property.StartLine)
	assert.Equal(t, 13, p.Parts[2].PropertyOverride.Property.EndLine)
	assert.NotNil(t, p.Parts[3].PropertyOverride)
	assert.Equal(t, 17, p.Parts[3].PropertyOverride.Property.StartLine)
	assert.Equal(t, 19, p.Parts[3].PropertyOverride.Property.EndLine)
}

func TestLookupTableOverride(t *testing.T) {
	p, err := parseProgram(`
		tag_enum MyEnum {
			"foo"
			"bar"
			"baz"
		}

		lookup_table MyLookupTable {
			inputs {
				x range[number.integer]
				y MyEnum
			}
			outputs {
				z number.decimal
			}
			data {
				0,1,"bar",4.5
			}
		}

		@override
		lookup_table MyLookupTable {
			inputs {
				a MyEnum
				b boolean
			}
			outputs {
				c number.integer
				d boolean
			}
			data {
				"baz", True, 2, False
			}
		}
		`,
	)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, 3, len(p.Parts))

	enum := p.Parts[0].Enum
	assert.NotNil(t, enum)

	lookupTable := p.Parts[1].LookupTable
	assert.NotNil(t, lookupTable)

	lookupTableOverride := p.Parts[2].LookupTableOverride
	assert.NotNil(t, lookupTableOverride)

	expectedInputs := &LookupTableIOWithLineNumbers{
		Columns: []*LookupTableIODeclaration{
			{
				Name: "x",
				Type: &TypeContainer{
					Range: &TypeContainer{
						Type: &QName{Path: []string{"number.integer"}},
					},
				},
			},
			{
				Name: "y",
				Type: &TypeContainer{
					Type: &QName{Path: []string{"MyEnum"}},
				},
			},
		},
		StartLine: 9,
		EndLine:   12,
	}
	assert.Equal(t, expectedInputs, lookupTable.Inputs)

	expectedOutputs := &LookupTableIOWithLineNumbers{
		Columns: []*LookupTableIODeclaration{
			{
				Name: "z",
				Type: &TypeContainer{
					Type: &QName{Path: []string{"number.decimal"}},
				},
			},
		},
		StartLine: 13,
		EndLine:   15,
	}
	assert.Equal(t, expectedOutputs, lookupTable.Outputs)

	expectedInlineData := &LookupTableInlineData{
		Rows: []*Row{
			{
				Cells: []*Cell{
					{
						Literal: &Literal{IntegerLiteral: "0"},
					},
					{
						Literal: &Literal{IntegerLiteral: "1"},
					},
					{
						Literal: &Literal{EnumLiteral: "bar"},
					},
					{
						Literal: &Literal{DecimalLiteral: "4.5"},
					},
				},
			},
		},
		StartLine: 16,
		EndLine:   18,
	}
	assert.Equal(t, expectedInlineData, lookupTable.InlineData)

	expectedOverriddenInputs := &LookupTableIOWithLineNumbers{
		Columns: []*LookupTableIODeclaration{
			{
				Name: "a",
				Type: &TypeContainer{
					Type: &QName{Path: []string{"MyEnum"}},
				},
			},
			{
				Name: "b",
				Type: &TypeContainer{
					Type: &QName{Path: []string{"boolean"}},
				},
			},
		},
		StartLine: 23,
		EndLine:   26,
	}
	assert.Equal(t, expectedOverriddenInputs, lookupTableOverride.LookupTable.Inputs)

	expectedOverriddenOutputs := &LookupTableIOWithLineNumbers{
		Columns: []*LookupTableIODeclaration{
			{
				Name: "c",
				Type: &TypeContainer{
					Type: &QName{Path: []string{"number.integer"}},
				},
			},
			{
				Name: "d",
				Type: &TypeContainer{
					Type: &QName{Path: []string{"boolean"}},
				},
			},
		},
		StartLine: 27,
		EndLine:   30,
	}
	assert.Equal(t, expectedOverriddenOutputs, lookupTableOverride.LookupTable.Outputs)

	expectedOverriddenInlineData := &LookupTableInlineData{
		Rows: []*Row{
			{
				Cells: []*Cell{
					{
						Literal: &Literal{EnumLiteral: "baz"},
					},
					{
						Literal: &Literal{BooleanLiteral: "True"},
					},
					{
						Literal: &Literal{IntegerLiteral: "2"},
					},
					{
						Literal: &Literal{BooleanLiteral: "False"},
					},
				},
			},
		},
		StartLine: 31,
		EndLine:   33,
	}
	assert.Equal(t, expectedOverriddenInlineData, lookupTableOverride.LookupTable.InlineData)
}

func TestEntityInclusion(t *testing.T) {
	p, err := parseProgram(`
		entity A {
		}

		entity B {
		}

		entity A includes B
	`)
	assert.Nil(t, err)
	assert.NotNil(t, p.Parts)
	assert.Equal(t, 3, len(p.Parts))
	assert.NotNil(t, p.Parts[0].Type)
	assert.Equal(t, 2, p.Parts[0].Type.StartLine)
	assert.Equal(t, 3, p.Parts[0].Type.EndLine)
	assert.NotNil(t, p.Parts[1].Type)
	assert.Equal(t, 5, p.Parts[1].Type.StartLine)
	assert.Equal(t, 6, p.Parts[1].Type.EndLine)
	assert.NotNil(t, p.Parts[2].TypeInclusion)
	assert.Equal(t, 8, p.Parts[2].TypeInclusion.Line)
}

func parseExpression(s string) (*Expression, error) {
	// Since goyacc only supports a single starting non-terminal,
	// we need to convert the input string into a dummy program and
	// extract the Expression out of it

	s = `property Dummy exp type { ` + s + ` }`

	ast, err := ParseProgramFromString(s)
	if err != nil {
		return nil, err
	}

	if len(ast.Parts) != 1 {
		return nil, errors.Errorf("Error while parsing expression.")
	}

	return ast.Parts[0].Property.Expression, err
}

func parseProgram(s string) (*Program, error) {
	ast, err := ParseProgramFromString(s)
	return ast, err
}
