/(#|\/\/)[^\n]*\n?/   { }
/\./    { return int('.') }
/{/   { lval.LineNo = yylex.Line()+1; return LBRACE }
/}/   { lval.LineNo = yylex.Line()+1; return RBRACE }
/\[/   { return int('[') }
/\]/   { return int(']') }
/\(/   { return int('(') }
/\)/   { return int(')') }
/:/   { return int(':') }
/"/    { return int('"') }
/,/   { return int(',') }
/->/  { return DEREF }
/\*/   { return int('*') }
/\//   { return int('/') }
/\+/   { return int('+') }
/-/   { return int('-') }
/&&/  { return AND }
/\|\|/  { return OR }
/!=/  { return NEQ }
/<=/  { return LTE }
/</   { return int('<') }
/>=/  { return GTE }
/>/   { return int('>') }
/=/   { return int('=') }
/!/   { return int('!') }
/in/  { return IN_OP }
/notin/   { return NOTIN_OP }
/[0-9]+\.[0-9]+/    { lval.String = yylex.Text(); return F_CONSTANT }
/[0-9]+/    { lval.String = yylex.Text(); return I_CONSTANT }
/True|False/ { lval.String = yylex.Text(); return B_CONSTANT }
/"([a-zA-Z]+[a-zA-Z0-9]*[\.]?)*([a-zA-Z]+[- _a-zA-Z0-9]*)+"/ { text := yylex.Text(); lval.String = text[1:len(text)-1]; return E_CONSTANT }
/namespace/   { lval.LineNo = yylex.Line()+1; return NAMESPACE }
/entity/   { lval.LineNo = yylex.Line()+1; return ENTITY }
/guard/   { lval.LineNo = yylex.Line()+1; return GUARD }
/require/   { lval.LineNo = yylex.Line()+1; return REQUIRE }
/override/ { lval.LineNo = yylex.Line()+1; return OVERRIDE }
/extend/ { lval.LineNo = yylex.Line()+1; return EXTEND }
/includes/ { lval.LineNo = yylex.Line()+1; return INCLUDES }
/list/   { return LIST }
/range/   { return RANGE }
/tag_enum/   { return ENUM_TAG }
/lookup_table/   { return LOOKUP }
/inputs/   { lval.LineNo = yylex.Line()+1; return INPUTS }
/outputs/   { lval.LineNo = yylex.Line()+1; return OUTPUTS }
/data/   { lval.LineNo = yylex.Line()+1; return DATA }
/property/   { lval.LineNo = yylex.Line()+1; return PROPERTY }
/output/   { lval.LineNo = yylex.Line()+1; return OUTPUT }
/switch/   { return SWITCH }
/case/   { return CASE }
/if/    { return IF }
/then/  { return THEN }
/else/  { return ELSE }
/([a-zA-Z]+[a-zA-Z0-9]*[\.]?)*([a-zA-Z]+[_a-zA-Z0-9]*)+/ { lval.String = yylex.Text(); return IDENTIFIER }
/_/   { return int('_') }
/@/   { return int('@') }
/[ \t\n\r]+/    { }
/./ { }
//
package parser
import "github.com/cockroachdb/errors"

func ParseProgramFromString(inputString string) (program *Program, err error) {
    defer func() {
        if ret := recover(); ret != nil {
            err = errors.Errorf("Error while parsing program input. Error = %v", ret)
            program = nil
        }
    }()
    stringReader := strings.NewReader(inputString)
    l := NewLexer(stringReader)
    yyParse(l)
    return l.parseResult.(*Program), nil
}
