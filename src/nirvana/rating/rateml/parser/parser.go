package parser

import (
	"strings"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/rating/rateml/graph"
)

// Last time these were run it was done with the following version: github.com/blynn/nex v0.0.0-20210330102341-1a3320dab988
//go:generate nex lexer.nex
//go:generate goyacc -o grammar.go grammar.y

type Program struct {
	Parts []*ProgramPart
}

type ProgramPart struct {
	Type                *Entry
	TypeExtension       *EntryExtension
	TypeInclusion       *EntryInclusion
	Enum                *Enum
	LookupTable         *LookupTable
	LookupTableOverride *LookupTableOverride
	Property            *Property
	PropertyOverride    *PropertyOverride
	Namespace           *Namespace
	NamespaceConfig     *NamespaceConfig
}

type Namespace struct {
	Guard          *ExpressionWithLineNumber
	Name           string
	NamespaceParts []*NamespacePart
	StartLine      int
	EndLine        int
}

type NamespacePart struct {
	Namespace *Namespace
	Property  *Property
}

type NamespaceConfig struct {
	Name      string
	Fields    []*Field
	StartLine int
	EndLine   int
}

type Entry struct {
	Name      string
	Fields    []*Field
	StartLine int
	EndLine   int
}

type EntryExtension struct {
	Entry *Entry
}

type EntryInclusion struct {
	IncluderName string
	IncludedName string
	Line         int
}

type Field struct {
	Checks *Check
	Name   string
	Type   *TypeContainer
}

type Check struct {
	ClauseList []*Clause
}

type Clause struct {
	LeftIdent string
	Comp      *Compare
	Mem       *Member
	Line      int
}

type Compare struct {
	Op        string
	RightExpr *Expression
}

type Member struct {
	Op      string
	ExprSet []*Expression
}

type TypeContainer struct {
	Array *TypeContainer
	Range *TypeContainer
	Type  *QName
}

type Enum struct {
	Name       *QName
	Entries    []string
	HasEntries bool
}

type LookupTable struct {
	Name       string
	Inputs     *LookupTableIOWithLineNumbers
	Outputs    *LookupTableIOWithLineNumbers
	InlineData *LookupTableInlineData
}

type LookupTableOverride struct {
	LookupTable *LookupTable
}

type LookupTableIOWithLineNumbers struct {
	Columns   []*LookupTableIODeclaration
	StartLine int
	EndLine   int
}

type LookupTableIODeclaration struct {
	Name string
	Type *TypeContainer
}

type LookupTableColumns struct {
	Columns   []*LookupTableIODeclaration
	StartLine int
	EndLine   int
}

func (l LookupTableIODeclaration) IsRangeType() bool {
	return l.Type.Range != nil
}

type LookupTableInlineData struct {
	Rows      []*Row
	StartLine int
	EndLine   int
}

type Row struct {
	Cells []*Cell
}

type Cell struct {
	Literal *Literal
	Any     string
}

type Property struct {
	Checks        *Check
	PropertyClass string
	OwnerEntity   string
	Name          string
	Type          *TypeContainer
	Expression    *Expression
	StartLine     int
	EndLine       int
}

type PropertyOverride struct {
	Property *Property
}

type Expression struct {
	Body *Disjunction
}

type Disjunction struct {
	LeftExpr  *Disjunction
	Op        string
	RightExpr *Conjunction
}

type Conjunction struct {
	LeftExpr  *Conjunction
	Op        string
	RightExpr *Equality
}

type Equality struct {
	LeftExpr  *Equality
	Op        string
	RightExpr *Relational
}

type Relational struct {
	LeftExpr  *Relational
	Op        string
	RightExpr *Negation
}

type Negation struct {
	Op        string
	UnaryExpr *Addition
}

type Addition struct {
	LeftExpr  *Addition
	Op        string
	RightExpr *Factor
}

type Factor struct {
	LeftExpr  *Factor
	Op        string
	RightExpr *Atom
}

type Atom struct {
	TypeRef       *QName
	Switch        *Switch
	IfThenElse    *IfThenElse
	FunctionCall  *FunctionCall
	Literal       *Literal
	Cast          *Cast
	FieldAccess   *FieldAccess
	SubExpression *Expression
}

type Cast struct {
	Type       *TypeContainer
	Expression *Expression
}

type FunctionCall struct {
	Name      string
	Arguments []*Expression
}

type Switch struct {
	SwitchExpression *Expression
	Cases            []*SwitchCase
}

type SwitchCase struct {
	MatchExpression *Expression
	IsDefault       bool
	ValueExpression *Expression
}

type Literal struct {
	DecimalLiteral string
	IntegerLiteral string
	BooleanLiteral string
	EnumLiteral    string
}

type FieldAccess struct {
	Path []string
}

type QName struct {
	Path []string
}

type ExpressionWithLineNumber struct {
	Expression *Expression
	Line       int
}

type IfThenElse struct {
	BranchExpression *Expression
	ThenExpression   *Expression
	ElseExpression   *Expression
}

func (p *Program) getTypes() []*Entry {
	ts := make([]*Entry, 0)
	for _, e := range p.Parts {
		if e.Type != nil {
			ts = append(ts, e.Type)
		}
	}
	return ts
}

func (p *Program) getTypeExtensions() []*EntryExtension {
	ts := make([]*EntryExtension, 0)
	for _, e := range p.Parts {
		if e.TypeExtension != nil {
			ts = append(ts, e.TypeExtension)
		}
	}
	return ts
}

func (p *Program) getTypeInclusions() []*EntryInclusion {
	ts := make([]*EntryInclusion, 0)
	for _, e := range p.Parts {
		if e.TypeInclusion != nil {
			ts = append(ts, e.TypeInclusion)
		}
	}
	return ts
}

func (p *Program) getEnums() []*Enum {
	ts := make([]*Enum, 0)
	for _, e := range p.Parts {
		if e.Enum != nil {
			ts = append(ts, e.Enum)
		}
	}
	return ts
}

func (p *Program) getProperties() []*Property {
	ts := make([]*Property, 0)
	for _, e := range p.Parts {
		if e.Property != nil {
			ts = append(ts, e.Property)
		}
	}
	return ts
}

func (p *Program) getPropertyOverrides() []*PropertyOverride {
	ts := make([]*PropertyOverride, 0)
	for _, e := range p.Parts {
		if e.PropertyOverride != nil {
			ts = append(ts, e.PropertyOverride)
		}
	}
	return ts
}

func (p *Program) getLookupTables() []*LookupTable {
	ts := make([]*LookupTable, 0)
	for _, e := range p.Parts {
		if e.LookupTable != nil {
			ts = append(ts, e.LookupTable)
		}
	}
	return ts
}

func (p *Program) getLookupTableOverrides() []*LookupTableOverride {
	ts := make([]*LookupTableOverride, 0)
	for _, e := range p.Parts {
		if e.LookupTableOverride != nil {
			ts = append(ts, e.LookupTableOverride)
		}
	}
	return ts
}

func (p *Program) getTopLevelNamespaces() []*Namespace {
	ts := make([]*Namespace, 0)
	for _, e := range p.Parts {
		if e.Namespace != nil {
			ts = append(ts, e.Namespace)
		}
	}
	return ts
}

func (p *Program) getNamespaceConfig() (*NamespaceConfig, error) {
	ts := make([]*NamespaceConfig, 0)
	for _, e := range p.Parts {
		if e.NamespaceConfig != nil {
			if e.NamespaceConfig.Name != graph.NamespaceConfigName {
				return nil, errors.Errorf("Wrong name for NamespaceConfig entity. Found %s.", e.NamespaceConfig.Name)
			}
			ts = append(ts, e.NamespaceConfig)
		}
	}
	// Allowed to only specify one config block in the model
	if len(ts) > 1 {
		return nil, errors.Errorf("Only one NamespaceConfig allowed. Found %d.", len(ts))
	}
	if len(ts) == 0 {
		return nil, nil //nolint
	}
	return ts[0], nil
}

func (r TypeContainer) ToRMLTypeName() string {
	if r.Array != nil {
		return graph.SetTypePrefix + r.Array.Type.String()
	} else if r.Range != nil {
		return r.Range.Type.String()
	}
	return r.Type.String()
}

func (q *FieldAccess) String() string {
	return strings.Join(q.Path, graph.FieldLocatorSymbol)
}

func (q *QName) String() string {
	s := strings.Join(q.Path, ".")
	return s
}

func (c *Cell) String() (string, error) {
	if c.Literal != nil {
		return c.Literal.String()
	}

	return "_", nil
}

func (l *Literal) String() (string, error) {
	if l.DecimalLiteral != "" {
		return l.DecimalLiteral, nil
	} else if l.IntegerLiteral != "" {
		return l.IntegerLiteral, nil
	} else if l.BooleanLiteral != "" {
		return l.BooleanLiteral, nil
	} else if l.EnumLiteral != "" {
		return l.EnumLiteral, nil
	}
	return "", errors.New("unexpected literal")
}

func (dr *LookupTableInlineData) ToStrings() ([][]string, error) {
	ss := make([][]string, 0)
	for i := range dr.Rows {
		row := dr.Rows[i]
		cellStrs := make([]string, 0)
		for j := range row.Cells {
			cellStr, err := row.Cells[j].String()
			if err != nil {
				return nil, err
			}
			cellStrs = append(cellStrs, cellStr)
		}
		ss = append(ss, cellStrs)
	}
	return ss, nil
}
