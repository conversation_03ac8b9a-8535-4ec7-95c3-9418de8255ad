load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_test(
    name = "tests_test",
    srcs = [
        "asg_test.go",
        "assertions_test.go",
        "boolean_test.go",
        "conditionals_test.go",
        "entity_extension_test.go",
        "entity_inclusion_test.go",
        "functions_test.go",
        "line_number_test.go",
        "literal_enum_value_test.go",
        "lookup_table_override_test.go",
        "lookup_table_test.go",
        "namespace_test.go",
        "output_dummy_test.go",
        "output_test.go",
        "parser_e2e_test.go",
        "primitive_container_test.go",
        "program_functions_test.go",
        "program_test.go",
        "property_override_test.go",
        "switch_test.go",
    ],
    embed = [":tests"],
    deps = [
        "//nirvana/rating/adaptors/common",
        "//nirvana/rating/rateml/graph",
        "//nirvana/rating/rateml/parser",
        "//nirvana/rating/rateml/program",
        "//nirvana/rating/rateml/utils",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
    ],
)

go_library(
    name = "tests",
    srcs = [
        "graph_helper.go",
        "pet_insurance.go",
    ],
    importpath = "nirvanatech.com/nirvana/rating/rateml/tests",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/rating/rateml/graph",
        "//nirvana/rating/rateml/utils",
    ],
)
