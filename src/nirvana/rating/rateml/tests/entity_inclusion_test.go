package tests

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/rating/rateml/parser"
	"nirvanatech.com/nirvana/rating/rateml/program"
	ratemlutils "nirvanatech.com/nirvana/rating/rateml/utils"
)

func Test_EntityInclusion(t *testing.T) {
	gb := ratemlutils.NewGraphBuilderWithStdlib()

	err := parser.ParseRateMLProgram(
		`
		entity MyEntityA {
			inputA number.integer
		}

		property MyEntityA propertyA number.integer {
			2 * inputA * propertyB
		}

		output MyEntityA outputA number.integer {
			propertyA + 3
		}

		entity MyEntityB {
			inputB number.integer
		}

		property MyEntityB propertyB number.integer {
			3 - inputB
		}

		entity MyEntityA includes MyEntityB
		`,
		gb,
	)
	require.NoError(t, err)

	g, err := gb.Build()
	require.NoError(t, err)
	require.NotNil(t, g)

	pb := ratemlutils.NewProgramBuilderWithStdlib(g)
	pb.AddInstanceEntityBuilders(
		program.NewInstanceEntityBuilder("MyEntityA", "1", g).
			AddConcreteField("inputA", "number.integer", "5").
			AddConcreteField("inputB", "number.integer", "7"),
	)

	ctx := context.Background()
	p, err := pb.Build(ctx)
	require.NoError(t, err)
	require.NotNil(t, p)

	err = p.Run(ctx)
	require.NoError(t, err)

	entity := p.GetInstanceEntity("MyEntityA", "1")
	require.NotNil(t, entity)

	out := entity.GetComputedField("outputA")
	require.NotNil(t, out)
	require.Equal(t, "number.integer", out.Type().Name())
	require.Equal(t, int64(-37), out.(*program.InstancePrimitive).AsInteger().Value)
}

func Test_EntityInclusion_WhenIncludedEntityDoesNotExist(t *testing.T) {
	gb := ratemlutils.NewGraphBuilderWithStdlib()

	err := parser.ParseRateMLProgram(
		`
		entity MyEntityA {
		}

		entity MyEntityA includes MyEntityB
		`,
		gb,
	)
	require.Error(t, err)
	require.Regexp(t, "No entity with name MyEntityB exists", err.Error())
}

func Test_EntityInclusion_WhenIncluderEntityDoesNotExist(t *testing.T) {
	gb := ratemlutils.NewGraphBuilderWithStdlib()

	err := parser.ParseRateMLProgram(
		`
		entity MyEntityB {
		}

		entity MyEntityA includes MyEntityB
		`,
		gb,
	)
	require.Error(t, err)
	require.Regexp(t, "No entity with name MyEntityA exists", err.Error())
}

func Test_EntityInclusion_WithConcreteFieldCheckFromIncludedEntity(t *testing.T) {
	gb := ratemlutils.NewGraphBuilderWithStdlib()

	err := parser.ParseRateMLProgram(
		`
		entity MyEntityA {
		}

		entity MyEntityB {
			@require(inputB > 0)
			inputB number.integer
		}

		entity MyEntityA includes MyEntityB
		`,
		gb,
	)
	require.NoError(t, err)

	g, err := gb.Build()
	require.NoError(t, err)
	require.NotNil(t, g)

	pb := ratemlutils.NewProgramBuilderWithStdlib(g)
	pb.AddInstanceEntityBuilders(
		program.NewInstanceEntityBuilder("MyEntityA", "1", g).
			AddConcreteField("inputB", "number.integer", "-1"),
	)

	ctx := context.Background()
	p, err := pb.Build(ctx)
	require.NoError(t, err)

	err = p.Run(ctx)
	require.Error(t, err)
	require.Regexp(t, "Assertion check #0 over field inputB failed", err.Error())
	require.NotNil(t, p)
}

func Test_EntityInclusion_WithComputedFieldCheckFromIncludedEntity(t *testing.T) {
	gb := ratemlutils.NewGraphBuilderWithStdlib()

	err := parser.ParseRateMLProgram(
		`
		entity MyEntityA {
		}

		entity MyEntityB {
			inputB number.integer
		}

		@require(outputB < 15)
		output MyEntityB outputB number.integer {
			2 * inputB
		}

		entity MyEntityA includes MyEntityB
		`,
		gb,
	)
	require.NoError(t, err)

	g, err := gb.Build()
	require.NoError(t, err)
	require.NotNil(t, g)

	pb := ratemlutils.NewProgramBuilderWithStdlib(g)
	pb.AddInstanceEntityBuilders(
		program.NewInstanceEntityBuilder("MyEntityA", "1", g).
			AddConcreteField("inputB", "number.integer", "8"),
	)

	ctx := context.Background()
	p, err := pb.Build(ctx)
	require.NoError(t, err)

	err = p.Run(ctx)
	require.Error(t, err)
	require.Regexp(t, "Assertion check #0 over field outputB failed", err.Error())
	require.NotNil(t, p)
}

func Test_EntityInclusion_WhenFieldsExistInIncluderEntity(t *testing.T) {
	gb := ratemlutils.NewGraphBuilderWithStdlib()

	err := parser.ParseRateMLProgram(
		`
		entity MyEntityA {
			inputField number.integer
		}

		output MyEntityA outputField number.integer {
			2 * inputField
		}

		entity MyEntityB {
			inputField number.decimal
		}

		property MyEntityB outputField number.decimal {
			3.0 * inputField
		}

		entity MyEntityA includes MyEntityB
		`,
		gb,
	)
	require.NoError(t, err)

	g, err := gb.Build()
	require.NoError(t, err)
	require.NotNil(t, g)

	pb := ratemlutils.NewProgramBuilderWithStdlib(g)
	pb.AddInstanceEntityBuilders(
		program.NewInstanceEntityBuilder("MyEntityA", "1", g).
			AddConcreteField("inputField", "number.integer", "5"),
	)

	ctx := context.Background()
	p, err := pb.Build(ctx)
	require.NoError(t, err)
	require.NotNil(t, p)

	err = p.Run(ctx)
	require.NoError(t, err)

	entity := p.GetInstanceEntity("MyEntityA", "1")
	require.NotNil(t, entity)

	out := entity.GetComputedField("outputField")
	require.NotNil(t, out)
	require.Equal(t, "number.integer", out.Type().Name())
	require.Equal(t, int64(10), out.(*program.InstancePrimitive).AsInteger().Value)
}
