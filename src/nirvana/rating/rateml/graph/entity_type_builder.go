package graph

func NewEntityTypeBuilder(name string) *EntityTypeBuilder {
	return &EntityTypeBuilder{name: name}
}

type EntityTypeBuilder struct {
	name                  string
	concreteFields        []ConcreteFieldInfo
	computedFields        []ComputedFieldInfo
	startLine             int
	endLine               int
	extensionsLineNumbers []LineNumbers
	inclusionLineNumbers  []int
}

func (b *EntityTypeBuilder) AddLineNumbers(start, end int) *EntityTypeBuilder {
	b.startLine = start
	b.endLine = end
	return b
}

func (b *EntityTypeBuilder) AddExtensionLineNumbers(start, end int) *EntityTypeBuilder {
	b.extensionsLineNumbers = append(b.extensionsLineNumbers, LineNumbers{StartLine: start, EndLine: end})
	return b
}

func (b *EntityTypeBuilder) AddInclusionLineNumbers(line int) *EntityTypeBuilder {
	b.inclusionLineNumbers = append(b.inclusionLineNumbers, line)
	return b
}

func (b *EntityTypeBuilder) AddConcreteField(
	fieldName, typeName string, checks []*ExpReprWithLineNumber,
) *EntityTypeBuilder {
	b.concreteFields = append(b.concreteFields, ConcreteFieldInfo{
		fieldName: fieldName,
		typeName:  typeName,
		checks:    checks,
	})
	return b
}

func (b *EntityTypeBuilder) AddConcreteFieldFromTypeExtension(
	fieldName, typeName string, checks []*ExpReprWithLineNumber,
) *EntityTypeBuilder {
	// If the field already exists, we update its type and checks (total overite).
	// We use the name as the unique identifier for the field.
	for i := range b.concreteFields {
		cf := &b.concreteFields[i]

		if cf.fieldName == fieldName {
			cf.typeName = typeName
			cf.checks = checks
			return b
		}
	}

	// If the field does not exist, we add it as a new field.
	b.concreteFields = append(b.concreteFields, ConcreteFieldInfo{
		fieldName: fieldName,
		typeName:  typeName,
		checks:    checks,
	})

	return b
}

func (b *EntityTypeBuilder) AddComputedField(
	fieldName,
	typeName string,
	expRepr ExpRepr,
	isOutput bool,
	checks []*ExpReprWithLineNumber,
	namespace *NamespaceBuilder,
	startLine,
	endLine int,
) *EntityTypeBuilder {
	b.computedFields = append(b.computedFields, ComputedFieldInfo{
		fieldName: fieldName,
		typeName:  typeName,
		expRepr:   expRepr,
		isOutput:  isOutput,
		checks:    checks,
		namespace: namespace,
		startLine: startLine,
		endLine:   endLine,
	})
	return b
}

// OverrideComputedField updates a field's information, except for its namespace builder.
// The reason for this is that a computed field can only be overridden in the same namespace
// it was defined in.
//
// Currently, we don't support overriding properties in namespaces other than the root namespace.
func (b *EntityTypeBuilder) OverrideComputedField(
	fieldName,
	typeName string,
	expRepr ExpRepr,
	isOutput bool,
	checks []*ExpReprWithLineNumber,
	namespace *NamespaceBuilder,
	startLine,
	endLine int,
) *EntityTypeBuilder {
	// if the field exists, we update all of its information.
	// We use the name as the unique identifier for the field.
	for i := range b.computedFields {
		cf := &b.computedFields[i]
		if cf.namespace.Id == namespace.Id && cf.fieldName == fieldName {
			cf.typeName = typeName
			cf.expRepr = expRepr
			cf.isOutput = isOutput
			cf.checks = checks
			cf.startLine = startLine
			cf.endLine = endLine
			return b
		}
	}

	// if the field does not exist we panic. This point should not be reached
	// as the consumer should first check if the field exists using ComputedFieldExistsInNamespace.
	panic("Computed field does not exist, cannot override it")
}

func (b *EntityTypeBuilder) ConcreteFieldExists(fieldName string) bool {
	for _, field := range b.concreteFields {
		if field.fieldName == fieldName {
			return true
		}
	}
	return false
}

// ComputedFieldExistsInNamespace checks whether a computed field with the given name exists
// within the given namespace.
//
// This is used to check whether a property override is valid (must be defined in the same namespace
// as the property being overridden).
//
// Having said that, this function can be used in other contexts as well.
func (b *EntityTypeBuilder) ComputedFieldExistsInNamespace(namespaceId, fieldName string) bool {
	for _, field := range b.computedFields {
		if field.namespace.Id == namespaceId && field.fieldName == fieldName {
			return true
		}
	}
	return false
}

func (b *EntityTypeBuilder) GetConcreteFieldInfos() []ConcreteFieldInfo {
	return b.concreteFields
}

func (b *EntityTypeBuilder) GetComputedFieldInfos() []ComputedFieldInfo {
	return b.computedFields
}

type ConcreteFieldInfo struct {
	fieldName string
	typeName  string
	checks    []*ExpReprWithLineNumber
}

func (f *ConcreteFieldInfo) GetFieldName() string {
	return f.fieldName
}

func (f *ConcreteFieldInfo) GetTypeName() string {
	return f.typeName
}

func (f *ConcreteFieldInfo) GetChecks() []*ExpReprWithLineNumber {
	return f.checks
}

type ComputedFieldInfo struct {
	fieldName string
	typeName  string
	expRepr   ExpRepr
	isOutput  bool
	checks    []*ExpReprWithLineNumber
	namespace *NamespaceBuilder
	startLine int
	endLine   int
}

func (f *ComputedFieldInfo) GetFieldName() string {
	return f.fieldName
}

func (f *ComputedFieldInfo) GetTypeName() string {
	return f.typeName
}

func (f *ComputedFieldInfo) GetExpRepr() ExpRepr {
	return f.expRepr
}

func (f *ComputedFieldInfo) IsOutput() bool {
	return f.isOutput
}

func (f *ComputedFieldInfo) GetChecks() []*ExpReprWithLineNumber {
	return f.checks
}

func (f *ComputedFieldInfo) GetNamespace() *NamespaceBuilder {
	return f.namespace
}

func (f *ComputedFieldInfo) GetStartLine() int {
	return f.startLine
}

func (f *ComputedFieldInfo) GetEndLine() int {
	return f.endLine
}
