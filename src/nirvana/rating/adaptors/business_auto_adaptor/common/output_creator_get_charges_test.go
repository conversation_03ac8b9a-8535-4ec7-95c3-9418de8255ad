package common

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/rating/adaptors/business_auto_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/business_auto_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/rtypes"
)

func TestGetCharges_WithNoCharges(t *testing.T) {
	ctx := context.Background()

	mk := rtypes.ModelKey{}
	pc := common.NewProgramContext(mk)

	haLiabRater := &entities.HiredAutoLiabRater{}
	haPDRater := &entities.HiredAutoPDRater{}
	nonOwnedVehicleRater := &entities.NonOwnedVehicleRater{}
	coverage := &entities.Coverage{}
	surcharge := &entities.Surcharge{}

	allEntities := []common.Entity{
		haLiabRater,
		haPDRater,
		nonOwnedVehicleRater,
		coverage,
		surcharge,
	}

	for _, e := range allEntities {
		err := pc.AddEntity(ctx, e)
		require.NoError(t, err)
	}

	policyNumber := "policyNumber"
	chunkID := "chunkID"
	chunkStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2025").ToTime())
	chunkDates := &proto.Interval{Start: chunkStartDate}

	input := &creator_functions.Input{
		Input: common.Input{
			PolicyNumber: policyNumber,
			BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
				ChunkId: chunkID,
				Dates:   chunkDates,
			},
		},
	}

	oc := &OutputCreator{ProgramContext: pc, Input: input}

	charges, err := oc.GetCharges()
	require.NoError(t, err)
	require.Empty(t, charges)
}

// We want to test the case when there are missing charges, but some present.
// We do this by adding only one charge per "category".
func TestGetCharges_WithSomeCharges(t *testing.T) {
	ctx := context.Background()

	mk := rtypes.ModelKey{}
	pc := common.NewProgramContext(mk)

	pdRater1 := &entities.PDRater{
		Id:                     "pdRater1",
		Vehicle:                "vin1",
		VehicleCompOnlyPremium: 100,
	}
	pdRater2 := &entities.PDRater{
		Id:                     "pdRater2",
		Vehicle:                "vin2",
		VehicleCompOnlyPremium: 200,
	}
	haLiabRater := &entities.HiredAutoLiabRater{}
	haPDRater := &entities.HiredAutoPDRater{
		HiredAutoPDPremium: 400,
	}
	nonOwnedVehicleRater := &entities.NonOwnedVehicleRater{}
	coverage := &entities.Coverage{
		BlanketPNCAdditionalInsuredPremium: 500,
	}
	surcharge := &entities.Surcharge{
		SurplusStampingFeeFromRefundablePremium: 600,
	}

	allEntities := []common.Entity{
		pdRater1,
		pdRater2,
		haLiabRater,
		haPDRater,
		nonOwnedVehicleRater,
		coverage,
		surcharge,
	}

	for _, e := range allEntities {
		err := pc.AddEntity(ctx, e)
		require.NoError(t, err)
	}

	policyNumber := "policyNumber"
	chunkID := "chunkID"
	chunkStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2025").ToTime())
	chunkDates := &proto.Interval{Start: chunkStartDate}

	input := &creator_functions.Input{
		Input: common.Input{
			PolicyNumber: policyNumber,
			BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
				ChunkId: chunkID,
				Dates:   chunkDates,
			},
		},
	}

	oc := &OutputCreator{ProgramContext: pc, Input: input}

	charges, err := oc.GetCharges()
	require.NoError(t, err)
	require.Len(t, charges, 4)

	expectedCharges := []*ptypes.Charge{
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("300", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Comprehensive).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.33333333"),
					ptypes.NewChargeDistributionItem("vin2", "0.66666667"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("400", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().
			WithAmountBasedBillingDetails("500", chunkStartDate).
			WithChargeablePolicy(policyNumber).
			Build(),
		ptypes.NewChargeBuilder().
			WithStampingFeeSurchargeType().
			WithAmountBasedBillingDetails("600", chunkStartDate).
			WithChargeablePolicy(policyNumber).
			Build(),
	}
	require.ElementsMatch(t, expectedCharges, charges)
}

// Rounding is required, and last item gets "the remainder"
func TestGetCharges_WithComplexDistribution(t *testing.T) {
	ctx := context.Background()

	mk := rtypes.ModelKey{}
	pc := common.NewProgramContext(mk)

	pdRater1 := &entities.PDRater{
		Id:                     "pdRater1",
		Vehicle:                "vin1",
		VehicleCompOnlyPremium: 39.333333333,
	}
	pdRater2 := &entities.PDRater{
		Id:                     "pdRater2",
		Vehicle:                "vin2",
		VehicleCompOnlyPremium: 50.333333334,
	}
	pdRater3 := &entities.PDRater{
		Id:                     "pdRater3",
		Vehicle:                "vin3",
		VehicleCompOnlyPremium: 10.333333333,
	}
	haLiabRater := &entities.HiredAutoLiabRater{}
	haPDRater := &entities.HiredAutoPDRater{}
	nonOwnedVehicleRater := &entities.NonOwnedVehicleRater{}
	coverage := &entities.Coverage{}
	surcharge := &entities.Surcharge{}

	allEntities := []common.Entity{
		pdRater1,
		pdRater2,
		pdRater3,
		haLiabRater,
		haPDRater,
		nonOwnedVehicleRater,
		coverage,
		surcharge,
	}

	for _, e := range allEntities {
		err := pc.AddEntity(ctx, e)
		require.NoError(t, err)
	}

	policyNumber := "policyNumber"
	chunkID := "chunkID"
	chunkStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2025").ToTime())
	chunkDates := &proto.Interval{Start: chunkStartDate}

	input := &creator_functions.Input{
		Input: common.Input{
			PolicyNumber: policyNumber,
			BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
				ChunkId: chunkID,
				Dates:   chunkDates,
			},
		},
	}

	oc := &OutputCreator{ProgramContext: pc, Input: input}

	charges, err := oc.GetCharges()
	require.NoError(t, err)
	require.Len(t, charges, 1)

	expectedCharges := []*ptypes.Charge{
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("100", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Comprehensive).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin3", "0.10333333"),
					ptypes.NewChargeDistributionItem("vin1", "0.39333333"),
					ptypes.NewChargeDistributionItem("vin2", "0.50333334"),
				},
			).
			Build(),
	}
	require.ElementsMatch(t, expectedCharges, charges)
}

func TestGetCharges_WithAllCharges(t *testing.T) {
	ctx := context.Background()

	mk := rtypes.ModelKey{}
	pc := common.NewProgramContext(mk)

	liabRater1 := &entities.LiabRater{
		Id:                 "liabRater1",
		Vehicle:            "vin1",
		VehicleLiabPremium: 10,
	}
	liabRater2 := &entities.LiabRater{
		Id:                 "liabRater2",
		Vehicle:            "vin2",
		VehicleLiabPremium: 10,
	}
	pdRater1 := &entities.PDRater{
		Id:                     "pdRater1",
		Vehicle:                "vin1",
		VehicleCompCollPremium: 20,
		VehicleCompOnlyPremium: 30,
	}
	pdRater2 := &entities.PDRater{
		Id:                     "pdRater2",
		Vehicle:                "vin2",
		VehicleCompCollPremium: 20,
		VehicleCompOnlyPremium: 30,
	}
	medPayRater1 := &entities.MedPayRater{
		Id:                   "medPayRater1",
		Vehicle:              "vin1",
		VehicleMedPayPremium: 40,
	}
	medPayRater2 := &entities.MedPayRater{
		Id:                   "medPayRater2",
		Vehicle:              "vin2",
		VehicleMedPayPremium: 40,
	}
	rentalRater1 := &entities.RentalRater{
		Id:                   "rentalRater1",
		Vehicle:              "vin1",
		VehicleRentalPremium: 50,
	}
	rentalRater2 := &entities.RentalRater{
		Id:                   "rentalRater2",
		Vehicle:              "vin2",
		VehicleRentalPremium: 50,
	}
	towingRater1 := &entities.TowingRater{
		Id:                   "towingRater1",
		Vehicle:              "vin1",
		VehicleTowingPremium: 60,
	}
	towingRater2 := &entities.TowingRater{
		Id:                   "towingRater2",
		Vehicle:              "vin2",
		VehicleTowingPremium: 60,
	}
	umbiRater1 := &entities.UMBIRater{
		Id:                 "umbiRater1",
		Vehicle:            "vin1",
		VehicleUMBIPremium: 70,
	}
	umbiRater2 := &entities.UMBIRater{
		Id:                 "umbiRater2",
		Vehicle:            "vin2",
		VehicleUMBIPremium: 70,
	}
	uimbiRater1 := &entities.UIMBIRater{
		Id:                  "uimbiRater1",
		Vehicle:             "vin1",
		VehicleUIMBIPremium: 80,
	}
	uimbiRater2 := &entities.UIMBIRater{
		Id:                  "uimbiRater2",
		Vehicle:             "vin2",
		VehicleUIMBIPremium: 80,
	}
	umpdRater1 := &entities.UMPDRater{
		Id:                 "umpdRater1",
		Vehicle:            "vin1",
		VehicleUMPDPremium: 90,
	}
	umpdRater2 := &entities.UMPDRater{
		Id:                 "umpdRater2",
		Vehicle:            "vin2",
		VehicleUMPDPremium: 90,
	}
	umRater1 := &entities.UMRater{
		Id:               "umRater1",
		Vehicle:          "vin1",
		VehicleUMPremium: 95,
	}
	umRater2 := &entities.UMRater{
		Id:               "umRater2",
		Vehicle:          "vin2",
		VehicleUMPremium: 95,
	}
	umuimRater1 := &entities.UMUIMRater{
		Id:                  "umuimRater1",
		Vehicle:             "vin1",
		VehicleUMUIMPremium: 100,
	}
	umuimRater2 := &entities.UMUIMRater{
		Id:                  "umuimRater2",
		Vehicle:             "vin2",
		VehicleUMUIMPremium: 100,
	}
	fpbRater1 := &entities.FPBRater{
		Id:                                    "fpbRater1",
		Vehicle:                               "vin1",
		VehicleMedicalExpenseBenefitsPremium:  150,
		VehicleWorkLossBenefitsPremium:        160,
		VehicleFuneralExpenseBenefitsPremium:  170,
		VehicleAccidentalDeathBenefitsPremium: 180,
		VehicleExtraordinaryMedicalBenefitsPremium: 190,
	}
	fpbRater2 := &entities.FPBRater{
		Id:                                    "fpbRater2",
		Vehicle:                               "vin2",
		VehicleMedicalExpenseBenefitsPremium:  150,
		VehicleWorkLossBenefitsPremium:        160,
		VehicleFuneralExpenseBenefitsPremium:  170,
		VehicleAccidentalDeathBenefitsPremium: 180,
		VehicleExtraordinaryMedicalBenefitsPremium: 190,
	}
	pipRater1 := &entities.PIPRater{
		Id:                "pipRater1",
		Vehicle:           "vin1",
		VehiclePIPPremium: 200,
	}
	pipRater2 := &entities.PIPRater{
		Id:                "pipRater2",
		Vehicle:           "vin2",
		VehiclePIPPremium: 200,
	}
	haLiabRater := &entities.HiredAutoLiabRater{
		HiredAutoLiabPremium: 500,
	}
	haPDRater := &entities.HiredAutoPDRater{
		HiredAutoPDPremium: 600,
	}
	nonOwnedVehicleRater := &entities.NonOwnedVehicleRater{
		NonOwnedVehiclePremium: 700,
	}
	coverage := &entities.Coverage{
		BlanketRegularAdditionalInsuredPremium: 800,
		BlanketPNCAdditionalInsuredPremium:     900,
		BlanketWaiverOfSubrogationPremium:      1000,
	}
	surcharge := &entities.Surcharge{
		SurplusLinesTaxFromRefundablePremium:       1100,
		SurplusLinesTaxFromNonRefundablePremium:    1200,
		SurplusStampingFeeFromRefundablePremium:    1300,
		SurplusStampingFeeFromNonRefundablePremium: 1400,
	}

	allEntities := []common.Entity{
		liabRater1,
		liabRater2,
		pdRater1,
		pdRater2,
		medPayRater1,
		medPayRater2,
		rentalRater1,
		rentalRater2,
		towingRater1,
		towingRater2,
		umbiRater1,
		umbiRater2,
		uimbiRater1,
		uimbiRater2,
		umpdRater1,
		umpdRater2,
		umRater1,
		umRater2,
		umuimRater1,
		umuimRater2,
		fpbRater1,
		fpbRater2,
		pipRater1,
		pipRater2,
		haLiabRater,
		haPDRater,
		nonOwnedVehicleRater,
		coverage,
		surcharge,
	}

	for _, e := range allEntities {
		err := pc.AddEntity(ctx, e)
		require.NoError(t, err)
	}

	policyNumber := "policyNumber"
	chunkID := "chunkID"
	chunkStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2025").ToTime())
	chunkDates := &proto.Interval{Start: chunkStartDate}

	input := &creator_functions.Input{
		Input: common.Input{
			PolicyNumber: policyNumber,
			BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
				ChunkId: chunkID,
				Dates:   chunkDates,
			},
		},
	}

	oc := &OutputCreator{ProgramContext: pc, Input: input}

	charges, err := oc.GetCharges()
	require.NoError(t, err)
	require.Len(t, charges, 27)
	expectedCharges := []*ptypes.Charge{
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("20", chunkStartDate).
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
			).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("40", chunkStartDate).
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_Comprehensive,
				ptypes.SubCoverageType_SubCoverageType_Collision,
			).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("60", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Comprehensive).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("80", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_MedicalPayments).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("100", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_RentalReimbursement).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("120", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Towing).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("140", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("160", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("180", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("190", chunkStartDate).
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
			).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().WithAmountBasedBillingDetails("200", chunkStartDate).
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
			).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("300", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("320", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("340", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("360", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_AccidentalDeathBenefits).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("380", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_ExtraordinaryMedicalBenefits).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("400", chunkStartDate).
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
				ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits,
				ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
				ptypes.SubCoverageType_SubCoverageType_EssentialServiceExpenses,
			).
			WithDistribution(
				ptypes.Charge_DistributionType_Vehicle,
				[]*ptypes.Charge_DistributionItem{
					ptypes.NewChargeDistributionItem("vin1", "0.5"),
					ptypes.NewChargeDistributionItem("vin2", "0.5"),
				},
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("500", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("600", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails("700", chunkStartDate).
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithBlanketRegularAdditionalInsured().
			WithAmountBasedBillingDetails("800", chunkStartDate).
			WithChargeablePolicy(policyNumber).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().
			WithAmountBasedBillingDetails("900", chunkStartDate).
			WithChargeablePolicy(policyNumber).
			Build(),
		ptypes.NewChargeBuilder().
			WithBaseChargeTypeWithBlanketWaiverOfSubrogation().
			WithAmountBasedBillingDetails("1000", chunkStartDate).
			WithChargeablePolicy(policyNumber).
			Build(),
		ptypes.NewChargeBuilder().
			WithSurplusTaxSurchargeType().
			WithAmountBasedBillingDetails("1100", chunkStartDate).
			WithChargeablePolicy(policyNumber).
			Build(),
		ptypes.NewChargeBuilder().
			WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
			WithAmountBasedBillingDetails("1200", chunkStartDate).
			WithChargeablePolicy(policyNumber).
			Build(),
		ptypes.NewChargeBuilder().
			WithStampingFeeSurchargeType().
			WithAmountBasedBillingDetails("1300", chunkStartDate).
			WithChargeablePolicy(policyNumber).
			Build(),
		ptypes.NewChargeBuilder().
			WithStampingFeeFromFullyEarnedPremiumSurchargeType().
			WithAmountBasedBillingDetails("1400", chunkStartDate).
			WithChargeablePolicy(policyNumber).
			Build(),
	}
	require.ElementsMatch(t, expectedCharges, charges)
}
