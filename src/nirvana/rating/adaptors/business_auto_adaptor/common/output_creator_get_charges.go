package common

import (
	"slices"

	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/rating/adaptors/business_auto_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/business_auto"
)

const precisionForInputDistribution = 8 // decimal places

// The GetCharges method is needed so the OutputCreator struct implements the engine.ChunkOutputCreatorI.
//
// It's important to be aware that new OutputCreator implementations should be added if
// the logic to create charges changes in a non-backward compatible way. For instance:
//   - OK: add charges that would never be created for old model versions.
//   - NOT OK: change the way in which an existing charge's amount is calculated.
func (oc *OutputCreator) GetCharges() ([]*ptypes.Charge, error) {
	policyNumber := oc.Input.GetPolicyNumber()

	chunkDates, err := oc.Input.GetChunkDates()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get chunk dates")
	}

	charges := make([]*ptypes.Charge, 0)

	// First we create "regular" base charges (i.e. without extra info).
	baseCharges, err := oc.createBaseChargesWithNoExtraInfoAndSubCoverageGroupChargedItem(chunkDates)
	if err != nil {
		return nil, err
	}
	charges = append(charges, baseCharges...)

	// Then we create fee charges (i.e. base charges with extra info).
	feeCharges, err := oc.createFeeCharges(policyNumber, chunkDates)
	if err != nil {
		return nil, err
	}
	charges = append(charges, feeCharges...)

	// Then we create surcharges
	surcharges, err := oc.createSurcharges(policyNumber, chunkDates)
	if err != nil {
		return nil, err
	}
	charges = append(charges, surcharges...)

	// Finally, we filter out nil charges
	charges = slice_utils.Filter(charges, func(c *ptypes.Charge) bool {
		return c != nil
	})

	return charges, nil
}

func (oc *OutputCreator) createBaseChargesWithNoExtraInfoAndSubCoverageGroupChargedItem(
	chunkDates *proto.Interval,
) ([]*ptypes.Charge, error) {
	// Key indicated whether the sub coverage is rated at the vehicle level (false means company level rating).
	subCovGroupsWithBaseChargesWithNoExtraInfo := map[*ptypes.SubCoverageGroup]bool{
		business_auto.LiabSubCoverageGroup:                         true,
		business_auto.PDSubCoverageGroup:                           true,
		business_auto.CompSubCoverageGroup:                         true,
		business_auto.MedPaySubCoverageGroup:                       true,
		business_auto.RentalSubCoverageGroup:                       true,
		business_auto.TowingSubCoverageGroup:                       true,
		business_auto.UMBISubCoverageGroup:                         true,
		business_auto.UIMBISubCoverageGroup:                        true,
		business_auto.UMPDSubCoverageGroup:                         true,
		business_auto.UMSubCoverageGroup:                           true,
		business_auto.UMUIMSubCoverageGroup:                        true,
		business_auto.HiredAutoLiabSubCoverageGroup:                false,
		business_auto.HiredAutoPDSubCoverageGroup:                  false,
		business_auto.NonOwnedVehicleSubCoverageGroup:              false,
		business_auto.MedicalExpenseBenefitsSubCoverageGroup:       true,
		business_auto.WorkLossBenefitsSubCoverageGroup:             true,
		business_auto.FuneralExpenseBenefitsSubCoverageGroup:       true,
		business_auto.AccidentalDeathBenefitsSubCoverageGroup:      true,
		business_auto.ExtraordinaryMedicalBenefitsSubCoverageGroup: true,
		business_auto.PIPSubCoverageGroup:                          true,
	}

	charges := make([]*ptypes.Charge, 0)
	for subCovGroup, isRatedAtVehicleLevel := range subCovGroupsWithBaseChargesWithNoExtraInfo {
		var totalAmount decimal.Decimal
		var distributions []*ptypes.Charge_Distribution
		var vehicleDistribution *ptypes.Charge_Distribution
		var err error

		if isRatedAtVehicleLevel {
			totalAmount, vehicleDistribution, err = oc.getTotalAmountAndVehicleDistributionForSubCovGroup(subCovGroup)
			if err != nil {
				return nil, errors.Wrapf(
					err,
					"failed to get total amount and vehicle distribution for sub coverage group %s",
					subCovGroup.GetID(),
				)
			}

			distributions = append(distributions, vehicleDistribution)
		} else {
			totalAmount, err = oc.getTotalAmountForSubCovGroup(subCovGroup)
			if err != nil {
				return nil, errors.Wrapf(
					err,
					"failed to calculate total amount for sub coverage group %s",
					subCovGroup.GetID(),
				)
			}
		}

		charges = append(charges, oc.createBaseChargeWithNoExtraInfoAndChargedSubCoverageGroupAndAmountBasedBillingDetails(
			chunkDates,
			subCovGroup,
			totalAmount,
			distributions...,
		))
	}

	return charges, nil
}

func (oc *OutputCreator) createBaseChargeWithNoExtraInfoAndChargedSubCoverageGroupAndAmountBasedBillingDetails(
	chunkDates *proto.Interval,
	subCovGroup *ptypes.SubCoverageGroup,
	totalAmount decimal.Decimal,
	distributions ...*ptypes.Charge_Distribution,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithNoExtraInfo()
	chargedItem := ptypes.NewChargeChargedSubCoverageGroup(subCovGroup.GetSubCoverages()...)

	return oc.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		totalAmount,
		distributions...,
	)
}

// getTotalAmountAndVehicleDistributionForSubCovGroup returns the total amount and
// an optional vehicle distribution for the given sub coverage group.
func (oc *OutputCreator) getTotalAmountAndVehicleDistributionForSubCovGroup(
	subCovGroup *ptypes.SubCoverageGroup,
) (decimal.Decimal, *ptypes.Charge_Distribution, error) {
	switch subCovGroup {
	case business_auto.LiabSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForLiab()
	case business_auto.PDSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForPD()
	case business_auto.CompSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForComp()
	case business_auto.MedPaySubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForMedPay()
	case business_auto.RentalSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForRental()
	case business_auto.TowingSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForTowing()
	case business_auto.UMBISubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForUMBI()
	case business_auto.UIMBISubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForUIMBI()
	case business_auto.UMPDSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForUMPD()
	case business_auto.UMSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForUM()
	case business_auto.UMUIMSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForUMUIM()
	case business_auto.MedicalExpenseBenefitsSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForMedicalExpenseBenefits()
	case business_auto.WorkLossBenefitsSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForWorkLossBenefits()
	case business_auto.FuneralExpenseBenefitsSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForFuneralExpenseBenefits()
	case business_auto.AccidentalDeathBenefitsSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForAccidentalDeathBenefits()
	case business_auto.ExtraordinaryMedicalBenefitsSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForExtraordinaryMedicalBenefits()
	case business_auto.PIPSubCoverageGroup:
		return oc.getTotalAmountAndBuildVehicleDistributionForPIP()
	default:
		return decimal.Zero, nil, errors.Newf("sub coverage group unknown: %s", subCovGroup.GetID())
	}
}

// Functions ordered to match the switch statement order

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForLiab() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.LiabRater](
		oc.ProgramContext,
		common.EntityTypeLiabRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get LiabRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for LiabRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleLiabPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForPD() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.PDRater](
		oc.ProgramContext,
		common.EntityTypePDRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get PDRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for PDRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleCompCollPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForComp() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.PDRater](
		oc.ProgramContext,
		common.EntityTypePDRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get PDRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for PDRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleCompOnlyPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForMedPay() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.MedPayRater](
		oc.ProgramContext,
		common.EntityTypeMedPayRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get MedPayRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for MedPayRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleMedPayPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForRental() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.RentalRater](
		oc.ProgramContext,
		common.EntityTypeRentalRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get RentalRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for RentalRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleRentalPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForTowing() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.TowingRater](
		oc.ProgramContext,
		common.EntityTypeTowingRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get TowingRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for TowingRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleTowingPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForUMBI() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.UMBIRater](
		oc.ProgramContext,
		common.EntityTypeUMBIRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get UMBIRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for UMBIRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleUMBIPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForUIMBI() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.UIMBIRater](
		oc.ProgramContext,
		common.EntityTypeUIMBIRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get UIMBIRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for UIMBIRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleUIMBIPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForUMPD() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.UMPDRater](
		oc.ProgramContext,
		common.EntityTypeUMPDRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get UMPDRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for UMPDRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleUMPDPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForUM() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.UMRater](
		oc.ProgramContext,
		common.EntityTypeUMRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get UMRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for UMRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleUMPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForUMUIM() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.UMUIMRater](
		oc.ProgramContext,
		common.EntityTypeUMUIMRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get UMUIMRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for UMUIMRater (ID=%s)",
				rater.Id,
			)
		}

		premium := decimal.NewFromFloat(rater.VehicleUMUIMPremium)
		inputItems[vehicleID] = premium
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForMedicalExpenseBenefits() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.FPBRater](
		oc.ProgramContext,
		common.EntityTypeFPBRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get FPBRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for FPBRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleMedicalExpenseBenefitsPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForWorkLossBenefits() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.FPBRater](
		oc.ProgramContext,
		common.EntityTypeFPBRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get FPBRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for FPBRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleWorkLossBenefitsPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForFuneralExpenseBenefits() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.FPBRater](
		oc.ProgramContext,
		common.EntityTypeFPBRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get FPBRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for FPBRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleFuneralExpenseBenefitsPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForAccidentalDeathBenefits() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.FPBRater](
		oc.ProgramContext,
		common.EntityTypeFPBRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get FPBRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for FPBRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleAccidentalDeathBenefitsPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForExtraordinaryMedicalBenefits() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.FPBRater](
		oc.ProgramContext,
		common.EntityTypeFPBRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get FPBRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for FPBRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehicleExtraordinaryMedicalBenefitsPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistributionForPIP() (
	decimal.Decimal,
	*ptypes.Charge_Distribution,
	error,
) {
	raters, err := common.GetEntitiesFromProgramContext[entities.PIPRater](
		oc.ProgramContext,
		common.EntityTypePIPRater,
	)
	if err != nil {
		return decimal.Zero, nil, errors.Wrap(err, "failed to get PIPRater entities")
	}

	inputItems := make(map[string]decimal.Decimal)
	for _, rater := range raters {
		vehicleID := rater.Vehicle
		if vehicleID == "" {
			return decimal.Zero, nil, errors.Newf(
				"vehicle is empty for PIPRater (ID=%s)",
				rater.Id,
			)
		}

		inputItems[vehicleID] = decimal.NewFromFloat(rater.VehiclePIPPremium)
	}

	return oc.getTotalAmountAndBuildVehicleDistribution(inputItems)
}

func (oc *OutputCreator) getTotalAmountAndBuildVehicleDistribution(
	inputItems map[string]decimal.Decimal,
) (decimal.Decimal, *ptypes.Charge_Distribution, error) {
	totalAmount := decimal.Zero
	for _, inputAmount := range inputItems {
		totalAmount = totalAmount.Add(inputAmount)
	}

	if totalAmount.IsZero() {
		return totalAmount, nil, nil
	}

	itemsIDs := map_utils.Keys(inputItems)
	slices.SortFunc(itemsIDs, func(id1, id2 string) int {
		return inputItems[id1].Cmp(inputItems[id2])
	})

	items := make([]*ptypes.Charge_DistributionItem, 0)

	accFraction := decimal.Zero
	for i, itemID := range itemsIDs {
		var fraction decimal.Decimal

		// We do this to ensure that the sum of fractions is 1.
		if i == len(itemsIDs)-1 {
			fraction = decimal.NewFromFloat(1.0).Sub(accFraction)
		} else {
			inputAmount := inputItems[itemID]
			fraction = inputAmount.Div(totalAmount).Round(precisionForInputDistribution)
		}

		accFraction = accFraction.Add(fraction)

		items = append(items, ptypes.NewChargeDistributionItem(itemID, fraction.String()))
	}

	vehicleLevelDistribution := ptypes.NewChargeDistribution(ptypes.Charge_DistributionType_Vehicle, items...)

	return totalAmount, vehicleLevelDistribution, nil
}

func (oc *OutputCreator) getTotalAmountForSubCovGroup(subCovGroup *ptypes.SubCoverageGroup) (decimal.Decimal, error) {
	switch subCovGroup {
	case business_auto.HiredAutoLiabSubCoverageGroup:
		return oc.getTotalAmountForHiredAutoLiab()
	case business_auto.HiredAutoPDSubCoverageGroup:
		return oc.getTotalAmountForHiredAutoPD()
	case business_auto.NonOwnedVehicleSubCoverageGroup:
		return oc.getTotalAmountForNonOwnedVehicle()
	default:
		return decimal.Zero, errors.Newf("sub coverage group unknown: %s", subCovGroup.GetID())
	}
}

func (oc *OutputCreator) getTotalAmountForHiredAutoLiab() (decimal.Decimal, error) {
	raters, err := common.GetEntitiesFromProgramContext[entities.HiredAutoLiabRater](
		oc.ProgramContext,
		common.EntityTypeHiredAutoLiabRater,
	)
	if err != nil {
		return decimal.Zero, errors.Wrap(err, "failed to get HiredAutoLiabRater entities")
	}

	if len(raters) != 1 {
		return decimal.Zero, errors.Newf(
			"unexpected number of HiredAutoLiabRaters entities found: %d",
			len(raters),
		)
	}

	amount := decimal.NewFromFloat(raters[0].HiredAutoLiabPremium)
	return amount, nil
}

func (oc *OutputCreator) getTotalAmountForHiredAutoPD() (decimal.Decimal, error) {
	raters, err := common.GetEntitiesFromProgramContext[entities.HiredAutoPDRater](
		oc.ProgramContext,
		common.EntityTypeHiredAutoPDRater,
	)
	if err != nil {
		return decimal.Zero, errors.Wrap(err, "failed to get HiredAutoPDRaters entities")
	}

	if len(raters) != 1 {
		return decimal.Zero, errors.Newf(
			"unexpected number of HiredAutoPDRater entities found: %d",
			len(raters),
		)
	}

	amount := decimal.NewFromFloat(raters[0].HiredAutoPDPremium)
	return amount, nil
}

func (oc *OutputCreator) getTotalAmountForNonOwnedVehicle() (decimal.Decimal, error) {
	raters, err := common.GetEntitiesFromProgramContext[entities.NonOwnedVehicleRater](
		oc.ProgramContext,
		common.EntityTypeNonOwnedVehicleRater,
	)
	if err != nil {
		return decimal.Zero, errors.Wrap(err, "failed to get NonOwnedVehicleRater entities")
	}

	if len(raters) != 1 {
		return decimal.Zero, errors.Newf(
			"unexpected number of NonOwnedVehicleRater entities found: %d",
			len(raters),
		)
	}

	amount := decimal.NewFromFloat(raters[0].NonOwnedVehiclePremium)
	return amount, nil
}

func (oc *OutputCreator) createFeeCharges(
	policyNumber string,
	chunkDates *proto.Interval,
) ([]*ptypes.Charge, error) {
	coverages, err := common.GetEntitiesFromProgramContext[entities.Coverage](
		oc.ProgramContext,
		common.EntityTypeCoverage,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get Coverage entities")
	}

	if len(coverages) != 1 {
		return nil, errors.Newf(
			"unexpected number of Coverage entities found: %d",
			len(coverages),
		)
	}

	coverage := coverages[0]

	charges := make([]*ptypes.Charge, 0)

	ch := oc.createBRAICharge(
		policyNumber,
		chunkDates,
		decimal.NewFromFloat(coverage.BlanketRegularAdditionalInsuredPremium),
	)
	charges = append(charges, ch)

	ch = oc.createBPNCAICharge(
		policyNumber,
		chunkDates,
		decimal.NewFromFloat(coverage.BlanketPNCAdditionalInsuredPremium),
	)
	charges = append(charges, ch)

	ch = oc.createBWOSCharge(
		policyNumber,
		chunkDates,
		decimal.NewFromFloat(coverage.BlanketWaiverOfSubrogationPremium),
	)
	charges = append(charges, ch)

	return charges, nil
}

// createBAICharge creates a charge for the blanket regular additional insured.
func (oc *OutputCreator) createBRAICharge(
	policyNumber string,
	chunkDates *proto.Interval,
	premium decimal.Decimal,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithBlanketRegularAdditionalInsured()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return oc.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

// createBAICharge creates a charge for the blanket primary and non-contributory additional insured.
func (oc *OutputCreator) createBPNCAICharge(
	policyNumber string,
	chunkDates *proto.Interval,
	premium decimal.Decimal,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithBlanketPrimaryAndNonContributoryAdditionalInsured()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return oc.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

// createBWOSCharge creates a charge for the blanket waiver of subrogation.
func (oc *OutputCreator) createBWOSCharge(
	policyNumber string,
	chunkDates *proto.Interval,
	premium decimal.Decimal,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithBlanketWaiverOfSubrogation()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return oc.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

func (oc *OutputCreator) createSurcharges(
	policyNumber string,
	chunkDates *proto.Interval,
) ([]*ptypes.Charge, error) {
	surcharges, err := common.GetEntitiesFromProgramContext[entities.Surcharge](
		oc.ProgramContext,
		common.EntityTypeSurcharge,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get Surcharge entities")
	}

	if len(surcharges) != 1 {
		return nil, errors.Newf(
			"unexpected number of Surcharge entities found: %d",
			len(surcharges),
		)
	}

	surcharge := surcharges[0]

	charges := make([]*ptypes.Charge, 0)

	charges = append(charges, oc.createSurplusLinesTaxFromRefundablePremiumSurcharge(
		policyNumber,
		chunkDates,
		decimal.NewFromFloat(surcharge.SurplusLinesTaxFromRefundablePremium),
	))

	charges = append(charges, oc.createSurplusLinesTaxFromNonRefundablePremiumSurcharge(
		policyNumber,
		chunkDates,
		decimal.NewFromFloat(surcharge.SurplusLinesTaxFromNonRefundablePremium),
	))

	charges = append(charges, oc.createSurplusStampingFeeFromRefundablePremiumSurcharge(
		policyNumber,
		chunkDates,
		decimal.NewFromFloat(surcharge.SurplusStampingFeeFromRefundablePremium),
	))

	charges = append(charges, oc.createSurplusStampingFeeFromNonRefundablePremiumSurcharge(
		policyNumber,
		chunkDates,
		decimal.NewFromFloat(surcharge.SurplusStampingFeeFromNonRefundablePremium),
	))

	return charges, nil
}

func (oc *OutputCreator) createSurplusLinesTaxFromRefundablePremiumSurcharge(
	policyNumber string,
	chunkDates *proto.Interval,
	amount decimal.Decimal,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeSurchargeWithSurplusTax()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return oc.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		amount,
	)
}

func (oc *OutputCreator) createSurplusLinesTaxFromNonRefundablePremiumSurcharge(
	policyNumber string,
	chunkDates *proto.Interval,
	amount decimal.Decimal,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeSurchargeWithSurplusTaxFromFullyEarnedPremium()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return oc.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		amount,
	)
}

func (oc *OutputCreator) createSurplusStampingFeeFromRefundablePremiumSurcharge(
	policyNumber string,
	chunkDates *proto.Interval,
	amount decimal.Decimal,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeSurchargeWithStampingFee()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return oc.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		amount,
	)
}

func (oc *OutputCreator) createSurplusStampingFeeFromNonRefundablePremiumSurcharge(
	policyNumber string,
	chunkDates *proto.Interval,
	amount decimal.Decimal,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeSurchargeWithStampingFeeFromFullyEarnedPremium()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return oc.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		amount,
	)
}

// For charges with AmountBasedBillingDetails that originate from a chunk,
// we assume that the charge date is the start date of the chunk. The
// reason for this, is to ensure that the entire premium is charged in
// a billing interval that includes the start date of the chunk (i.e.
// that includes the endorsement that resulted in the chunk).
func (oc *OutputCreator) createChargeWithAmountBasedBillingDetails(
	chunkDates *proto.Interval,
	chargeType ptypes.ChargeTypeI,
	chargedItem ptypes.ChargedItemI,
	totalAmount decimal.Decimal,
	distributions ...*ptypes.Charge_Distribution,
) *ptypes.Charge {
	if totalAmount.IsZero() {
		return nil
	}

	return ptypes.NewChargeBuilder().
		WithChargeType(chargeType).
		WithChargedItem(chargedItem).
		WithDistributions(distributions...).
		WithAmountBasedBillingDetails(totalAmount.String(), chunkDates.Start).
		Build()
}
