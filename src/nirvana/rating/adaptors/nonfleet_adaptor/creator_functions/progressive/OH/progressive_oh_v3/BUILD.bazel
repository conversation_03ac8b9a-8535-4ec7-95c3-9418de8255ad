load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "progressive_oh_v3",
    srcs = ["creator_functions.go"],
    importpath = "nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/OH/progressive_oh_v3",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/rating/adaptors/common",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/bi_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/cargo_coll_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/cargo_comp_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/coll_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/commodities",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/comp_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/company",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/coverage",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/credit_data",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/drivers",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/gl_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/hired_auto_rater",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/med_pay_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/not_coll_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/not_comp_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/outputs_quote",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/pd_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/pip_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/quote_data",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/rental_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/specified_pnc_ai",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/specified_regular_ai",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/specified_wos",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/surcharge",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/tls_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/trl_int_coll_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/trl_int_comp_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/uim_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/um_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/umpd_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/umuim_raters",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/underwriting",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/vehicles",
    ],
)
