package progressive_oh_v3

import (
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/bi_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/cargo_coll_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/cargo_comp_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/coll_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/commodities"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/comp_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/company"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/coverage"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/credit_data"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/drivers"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/gl_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/hired_auto_rater"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/med_pay_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/not_coll_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/not_comp_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/outputs_quote"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/pd_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/pip_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/quote_data"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/rental_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/specified_pnc_ai"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/specified_regular_ai"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/specified_wos"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/surcharge"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/tls_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/trl_int_coll_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/trl_int_comp_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/uim_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/um_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/umpd_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/umuim_raters"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/underwriting"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/vehicles"
)

func GetCreatorFunctions() []common.EntitiesCreatorFn[creator_functions.Input] {
	return []common.EntitiesCreatorFn[creator_functions.Input]{
		common.NewEntitiesCreatorFnFromSingletonFns(quote_data.New),
		common.NewEntitiesCreatorFnFromSingletonFns(outputs_quote.New),
		common.NewEntitiesCreatorFnFromSingletonFns(credit_data.New),
		common.NewEntitiesCreatorFnFromSingletonFns(surcharge.New),
		common.NewEntitiesCreatorFnFromSingletonFns(company.New, company.Modifier1),
		common.NewEntitiesCreatorFnFromSingletonFns(coverage.New, coverage.Modifier1),
		common.NewEntitiesCreatorFnFromSingletonFns(hired_auto_rater.New),
		common.NewEntitiesCreatorFnFromSingletonFns(underwriting.New),
		common.NewEntitiesCreatorFnFromSliceFns(vehicles.New),
		common.NewEntitiesCreatorFnFromSliceFns(bi_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(pd_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(coll_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(comp_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(cargo_comp_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(cargo_coll_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(med_pay_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(gl_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(pip_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(um_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(uim_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(umuim_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(umpd_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(rental_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(tls_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(trl_int_coll_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(trl_int_comp_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(not_coll_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(not_comp_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(drivers.New),
		common.NewEntitiesCreatorFnFromSliceFns(commodities.New),
		common.NewEntitiesCreatorFnFromSliceFns(specified_regular_ai.New),
		common.NewEntitiesCreatorFnFromSliceFns(specified_pnc_ai.New),
		common.NewEntitiesCreatorFnFromSliceFns(specified_wos.New),
	}
}
