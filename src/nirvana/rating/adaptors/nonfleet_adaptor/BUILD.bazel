load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "nonfleet_adaptor",
    srcs = [
        "creator_functions.go",
        "fx.go",
    ],
    importpath = "nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/rating/adaptors/common",
        "//nirvana/rating/adaptors/nonfleet_adaptor/common",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/AZ/progressive_az_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/GA/progressive_ga_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/GA/progressive_ga_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/GA/progressive_ga_v3",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IA/progressive_ia_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IL/progressive_il_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IL/progressive_il_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IL/progressive_il_v3",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IN/progressive_in_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IN/progressive_in_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IN/progressive_in_v3",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MI/progressive_mi_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MI/progressive_mi_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MI/progressive_mi_v3",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MN/progressive_mn_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MN/progressive_mn_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MO/progressive_mo_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MO/progressive_mo_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MO/progressive_mo_v3",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/NC/progressive_nc_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/NC/progressive_nc_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/NC/progressive_nc_v3",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/NV/progressive_nv_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/OH/progressive_oh_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/OH/progressive_oh_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/OH/progressive_oh_v3",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/PA/progressive_pa_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/PA/progressive_pa_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/PA/progressive_pa_v3",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/SC/progressive_sc_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/SC/progressive_sc_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/SC/progressive_sc_v3",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/TN/progressive_tn_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/TN/progressive_tn_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/TN/progressive_tn_v3",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/WI/progressive_wi_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressiveSurplus/TN/progressiveSurplus_TX_v1",
        "//nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressiveSurplus/TN/progressiveSurplus_TX_v2",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions",
        "//nirvana/rating/rateml/utils",
        "//nirvana/rating/rtypes",
        "@org_uber_go_fx//:fx",
    ],
)
