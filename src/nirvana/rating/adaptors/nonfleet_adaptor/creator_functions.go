package nonfleet_adaptor

import (
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/AZ/progressive_az_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/GA/progressive_ga_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/GA/progressive_ga_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/GA/progressive_ga_v3"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IA/progressive_ia_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IL/progressive_il_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IL/progressive_il_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IL/progressive_il_v3"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IN/progressive_in_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IN/progressive_in_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/IN/progressive_in_v3"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MI/progressive_mi_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MI/progressive_mi_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MI/progressive_mi_v3"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MN/progressive_mn_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MN/progressive_mn_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MO/progressive_mo_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MO/progressive_mo_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/MO/progressive_mo_v3"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/NC/progressive_nc_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/NC/progressive_nc_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/NC/progressive_nc_v3"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/NV/progressive_nv_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/OH/progressive_oh_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/OH/progressive_oh_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/OH/progressive_oh_v3"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/PA/progressive_pa_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/PA/progressive_pa_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/PA/progressive_pa_v3"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/SC/progressive_sc_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/SC/progressive_sc_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/SC/progressive_sc_v3"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/TN/progressive_tn_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/TN/progressive_tn_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/TN/progressive_tn_v3"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressive/WI/progressive_wi_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressiveSurplus/TN/progressiveSurplus_TX_v1"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/creator_functions/progressiveSurplus/TN/progressiveSurplus_TX_v2"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/rtypes"
)

type creatorFunctionsFactory[I any] func() []common.EntitiesCreatorFn[I]

var creatorFunctionsFactoriesMap = map[rtypes.ModelKey]creatorFunctionsFactory[creator_functions.Input]{
	rtypes.ProviderProgressive_AZ_2_0_1: progressive_az_v1.GetCreatorFunctions,

	rtypes.ProviderProgressive_GA_0_0_0: progressive_ga_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_GA_0_1_0: progressive_ga_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_GA_1_0_0: progressive_ga_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_GA_1_0_1: progressive_ga_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_GA_2_0_0: progressive_ga_v3.GetCreatorFunctions,
	rtypes.ProviderProgressive_GA_2_0_1: progressive_ga_v3.GetCreatorFunctions,
	rtypes.ProviderProgressive_GA_2_0_2: progressive_ga_v3.GetCreatorFunctions,

	rtypes.ProviderProgressive_IA_2_0_0: progressive_ia_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IA_2_0_1: progressive_ia_v1.GetCreatorFunctions,

	rtypes.ProviderProgressive_IL_0_0_0: progressive_il_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IL_0_0_1: progressive_il_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IL_0_0_2: progressive_il_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IL_0_1_0: progressive_il_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IL_0_1_1: progressive_il_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IL_0_1_2: progressive_il_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IL_1_0_0: progressive_il_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_IL_2_0_0: progressive_il_v3.GetCreatorFunctions,
	rtypes.ProviderProgressive_IL_2_0_1: progressive_il_v3.GetCreatorFunctions,
	rtypes.ProviderProgressive_IL_2_0_2: progressive_il_v3.GetCreatorFunctions,

	rtypes.ProviderProgressive_IN_0_0_0: progressive_in_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IN_0_0_1: progressive_in_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IN_0_0_2: progressive_in_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IN_0_0_3: progressive_in_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IN_0_1_1: progressive_in_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_IN_1_0_0: progressive_in_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_IN_1_0_1: progressive_in_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_IN_2_0_2: progressive_in_v3.GetCreatorFunctions,

	rtypes.ProviderProgressive_MI_0_0_0: progressive_mi_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MI_0_0_1: progressive_mi_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MI_0_0_2: progressive_mi_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MI_0_1_0: progressive_mi_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MI_0_1_1: progressive_mi_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MI_0_1_2: progressive_mi_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MI_1_0_0: progressive_mi_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_MI_2_0_0: progressive_mi_v3.GetCreatorFunctions,
	rtypes.ProviderProgressive_MI_2_0_1: progressive_mi_v3.GetCreatorFunctions,
	rtypes.ProviderProgressive_MI_2_0_2: progressive_mi_v3.GetCreatorFunctions,

	rtypes.ProviderProgressive_MN_0_0_0: progressive_mn_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MN_0_0_1: progressive_mn_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MN_0_0_2: progressive_mn_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MN_1_0_0: progressive_mn_v2.GetCreatorFunctions,

	rtypes.ProviderProgressive_MO_0_0_0: progressive_mo_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MO_0_0_1: progressive_mo_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MO_0_0_2: progressive_mo_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MO_0_0_3: progressive_mo_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MO_0_1_1: progressive_mo_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_MO_1_0_0: progressive_mo_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_MO_1_0_1: progressive_mo_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_MO_2_0_2: progressive_mo_v3.GetCreatorFunctions,

	rtypes.ProviderProgressive_NC_0_0_0: progressive_nc_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_NC_0_1_0: progressive_nc_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_NC_0_1_1: progressive_nc_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_NC_1_0_0: progressive_nc_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_NC_1_0_1: progressive_nc_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_NC_2_0_2: progressive_nc_v3.GetCreatorFunctions,

	rtypes.ProviderProgressive_NV_2_0_1: progressive_nv_v1.GetCreatorFunctions,

	rtypes.ProviderProgressive_OH_0_0_0: progressive_oh_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_OH_0_0_1: progressive_oh_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_OH_0_0_2: progressive_oh_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_OH_0_0_3: progressive_oh_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_OH_0_1_1: progressive_oh_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_OH_1_0_0: progressive_oh_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_OH_1_0_1: progressive_oh_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_OH_2_0_2: progressive_oh_v3.GetCreatorFunctions,

	rtypes.ProviderProgressive_PA_0_0_0: progressive_pa_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_PA_0_1_0: progressive_pa_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_PA_1_0_0: progressive_pa_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_PA_1_0_1: progressive_pa_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_PA_2_0_2: progressive_pa_v3.GetCreatorFunctions,

	rtypes.ProviderProgressive_SC_0_0_0: progressive_sc_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_SC_0_1_0: progressive_sc_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_SC_1_0_0: progressive_sc_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_SC_2_0_0: progressive_sc_v3.GetCreatorFunctions,
	rtypes.ProviderProgressive_SC_2_0_1: progressive_sc_v3.GetCreatorFunctions,
	rtypes.ProviderProgressive_SC_2_0_2: progressive_sc_v3.GetCreatorFunctions,

	rtypes.ProviderProgressive_TN_0_0_0: progressive_tn_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_TN_0_1_0: progressive_tn_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_TN_1_0_0: progressive_tn_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_TN_1_0_1: progressive_tn_v2.GetCreatorFunctions,
	rtypes.ProviderProgressive_TN_2_0_2: progressive_tn_v3.GetCreatorFunctions,

	rtypes.ProviderProgressive_WI_2_0_0: progressive_wi_v1.GetCreatorFunctions,
	rtypes.ProviderProgressive_WI_2_0_1: progressive_wi_v1.GetCreatorFunctions,

	rtypes.ProviderProgressiveSurplus_TX_1_0_0: progressiveSurplus_TX_v1.GetCreatorFunctions,
	rtypes.ProviderProgressiveSurplus_TX_2_0_0: progressiveSurplus_TX_v2.GetCreatorFunctions,
	rtypes.ProviderProgressiveSurplus_TX_2_0_1: progressiveSurplus_TX_v2.GetCreatorFunctions,
}
