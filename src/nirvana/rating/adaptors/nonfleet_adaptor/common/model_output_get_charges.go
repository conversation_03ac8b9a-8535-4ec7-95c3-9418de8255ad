package common

import (
	"cmp"
	"slices"

	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

const precisionForInputDistribution = 8 // decimal places

// All sub covs with base charges.
// Not all of them have a vehicle level distribution,
// e.g., Hired Auto.
//
//nolint:exhaustive
var subCovsWithBaseCharges = []ptypes.SubCoverageType{
	ptypes.SubCoverageType_SubCoverageType_Collision,
	ptypes.SubCoverageType_SubCoverageType_Comprehensive,
	ptypes.SubCoverageType_SubCoverageType_TrailerInterchange,
	ptypes.SubCoverageType_SubCoverageType_NonOwnedTrailer,
	ptypes.SubCoverageType_SubCoverageType_Towing,
	ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
	ptypes.SubCoverageType_SubCoverageType_Cargo,
	ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError,
	ptypes.SubCoverageType_SubCoverageType_ReeferWithHumanError,
	ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
	ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
	ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist,
	ptypes.SubCoverageType_SubCoverageType_UnderInsuredMotorist,
	ptypes.SubCoverageType_SubCoverageType_UMUIM,
	ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
	ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
	ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection,
	ptypes.SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService,
	ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare,
	ptypes.SubCoverageType_SubCoverageType_PropertyProtectionInsurance,
	ptypes.SubCoverageType_SubCoverageType_GeneralLiability,
	ptypes.SubCoverageType_SubCoverageType_HiredAuto,
}

//nolint:exhaustive
var subCovsWithVehicleDistribution = map[ptypes.SubCoverageType]struct{}{
	ptypes.SubCoverageType_SubCoverageType_Collision:                       {},
	ptypes.SubCoverageType_SubCoverageType_Comprehensive:                   {},
	ptypes.SubCoverageType_SubCoverageType_TrailerInterchange:              {},
	ptypes.SubCoverageType_SubCoverageType_NonOwnedTrailer:                 {},
	ptypes.SubCoverageType_SubCoverageType_Towing:                          {},
	ptypes.SubCoverageType_SubCoverageType_RentalReimbursement:             {},
	ptypes.SubCoverageType_SubCoverageType_Cargo:                           {},
	ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError:         {},
	ptypes.SubCoverageType_SubCoverageType_ReeferWithHumanError:            {},
	ptypes.SubCoverageType_SubCoverageType_BodilyInjury:                    {},
	ptypes.SubCoverageType_SubCoverageType_PropertyDamage:                  {},
	ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist:               {},
	ptypes.SubCoverageType_SubCoverageType_UnderInsuredMotorist:            {},
	ptypes.SubCoverageType_SubCoverageType_UMUIM:                           {},
	ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage: {},
	ptypes.SubCoverageType_SubCoverageType_MedicalPayments:                 {},
	ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection:        {},
	ptypes.SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService:        {},
	ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare:                {},
	ptypes.SubCoverageType_SubCoverageType_PropertyProtectionInsurance:     {},
	ptypes.SubCoverageType_SubCoverageType_GeneralLiability:                {},
}

// The GetCharges method is needed so the model output implements the engine.ChunkOutputCreatorI.
//
// It's important to be aware that new ModelOutput implementations should be added if
// the logic to create charges changes in a non-backward compatible way. For instance:
//   - OK: add charges that would never be created for old model versions.
//   - NOT OK: change the way in which an existing charge's premium is calculated.
func (m *ModelOutputImpl) GetCharges() ([]*ptypes.Charge, error) {
	policyNumber := m.Input.GetPolicyNumber()

	chunkDates, err := m.Input.GetChunkDates()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get chunk dates")
	}

	charges := make([]*ptypes.Charge, 0)

	// First we create base charges
	baseCharges, err := createBaseChargesWithNoExtraInfo(m, chunkDates)
	if err != nil {
		return nil, err
	}

	charges = append(charges, baseCharges...)

	// Then we create surcharges
	surcharges, err := createSurcharges(m, policyNumber, chunkDates)
	if err != nil {
		return nil, err
	}

	charges = append(charges, surcharges...)

	// Then we create fee charges
	feeCharges, err := createFeeCharges(m, policyNumber, chunkDates)
	if err != nil {
		return nil, err
	}

	charges = append(charges, feeCharges...)

	// Finally, we filter out nil charges
	charges = slice_utils.Filter(charges, func(c *ptypes.Charge) bool {
		return c != nil
	})

	return charges, nil
}

func createBaseChargesWithNoExtraInfo(
	m *ModelOutputImpl,
	chunkDates *proto.Interval,
) ([]*ptypes.Charge, error) {
	charges := make([]*ptypes.Charge, 0)
	for _, subCov := range subCovsWithBaseCharges {
		charge, err := createBaseChargeWithNoExtraInfo(
			m,
			chunkDates,
			subCov,
		)
		if err != nil {
			return nil, err
		}

		charges = append(charges, charge)
	}
	return charges, nil
}

func createBaseChargeWithNoExtraInfo(
	m *ModelOutputImpl,
	chunkDates *proto.Interval,
	subCov ptypes.SubCoverageType,
) (*ptypes.Charge, error) {
	chargeType := ptypes.NewChargeBaseChargeWithNoExtraInfo()

	chargedItem := ptypes.NewChargeChargedSubCoverageGroup(subCov)

	premium, err := getSubCovBasePolicyChargePremium(m, subCov)
	if err != nil {
		return nil, err
	}

	var distributions []*ptypes.Charge_Distribution
	if _, ok := subCovsWithVehicleDistribution[subCov]; ok {
		vehicleLevelDistribution, err := createVehicleLevelDistribution(m, subCov, premium)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to create vehicle level distribution for subcov %s",
				subCov.String(),
			)
		}
		distributions = append(distributions, vehicleLevelDistribution)
	}

	ch := createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
		distributions...,
	)

	return ch, nil
}

func getSubCovBasePolicyChargePremium(
	m *ModelOutputImpl,
	sc ptypes.SubCoverageType,
) (float64, error) {
	//nolint:exhaustive
	switch sc {
	case ptypes.SubCoverageType_SubCoverageType_Collision:
		return m.CollPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_Comprehensive:
		return m.CompPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_TrailerInterchange:
		return m.TiPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_NonOwnedTrailer:
		return m.NotPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_Towing:
		return m.TlsPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_RentalReimbursement:
		return m.RentalPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_Cargo:
		return m.CargoPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError:
		return m.ReeferBreakdownPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_ReeferWithHumanError:
		return m.ReeferBreakdownWithHumanErrorPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_BodilyInjury:
		return m.BiPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_PropertyDamage:
		return m.PdPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_UnderInsuredMotorist:
		return m.UimPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist:
		return m.UmPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_UMUIM:
		return m.UmUimPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage:
		return m.UmpdPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_MedicalPayments:
		return m.MedPayPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection:
		return m.PipPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService:
		return m.PipWorkLossAndRplServicePolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare:
		return m.PipAttendantCarePolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_PropertyProtectionInsurance:
		return m.PpiPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_GeneralLiability:
		return m.GlPolicyPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_HiredAuto:
		return m.HiredAutoPolicyPremium, nil
	default:
		return 0, errors.Newf("unsupported sub-cov for base charge %s", sc.String())
	}
}

func createVehicleLevelDistribution(
	m *ModelOutputImpl,
	subCov ptypes.SubCoverageType,
	expectedTotalPremium float64,
) (*ptypes.Charge_Distribution, error) {
	items := make(map[string]float64)
	for _, veh := range m.VehicleEntities {
		premium, err := getSubCovVehicleBaseChargePremium(veh, subCov)
		if err != nil {
			return nil, err
		}

		if premium == 0 {
			continue
		}

		items[veh.Id] = premium
	}

	return createChargeDistribution(
		ptypes.Charge_DistributionType_Vehicle,
		items,
		expectedTotalPremium,
	)
}

func getSubCovVehicleBaseChargePremium(
	v *entities.Vehicle,
	sc ptypes.SubCoverageType,
) (float64, error) {
	//nolint:exhaustive
	switch sc {
	case ptypes.SubCoverageType_SubCoverageType_Collision:
		return v.CollVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_Comprehensive:
		return v.CompVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_TrailerInterchange:
		return v.TiVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_NonOwnedTrailer:
		return v.NotVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_Towing:
		return v.TlsVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_RentalReimbursement:
		return v.RentalVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_Cargo:
		return v.CargoVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError:
		return v.ReeferVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_ReeferWithHumanError:
		return v.ReeferWithHumanVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_BodilyInjury:
		return v.BiVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_PropertyDamage:
		return v.PdVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_UnderInsuredMotorist:
		return v.UimVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist:
		return v.UmVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_UMUIM:
		return v.UmuimVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage:
		return v.UmpdVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_MedicalPayments:
		return v.MedPayVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection:
		return v.PipVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService:
		return v.PipWorkLossAndRplServiceVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare:
		return v.PipAttendantCareVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_PropertyProtectionInsurance:
		return v.PpiVehPremium, nil
	case ptypes.SubCoverageType_SubCoverageType_GeneralLiability:
		return v.GlVehPremium, nil
	default:
		return 0, errors.Newf("unsupported sub-cov for vehicle refundable base charge %s", sc.String())
	}
}

// createChargeDistribution receives a map where the key is the id of the item
// and the value is the premium.
func createChargeDistribution(
	distributionType ptypes.Charge_DistributionType,
	rawItems map[string]float64,
	expectedTotalPremium float64,
) (*ptypes.Charge_Distribution, error) {
	totalPremium := decimal.Zero
	for _, premium := range rawItems {
		totalPremium = totalPremium.Add(decimal.NewFromFloat(premium))
	}

	if !totalPremium.Equal(decimal.NewFromFloat(expectedTotalPremium)) {
		return nil, errors.Newf(
			"total premium does not match expected total premium: %s != %f",
			totalPremium,
			expectedTotalPremium,
		)
	}

	itemsIDs := map_utils.Keys(rawItems)
	slices.SortFunc(itemsIDs, func(id1, id2 string) int {
		return cmp.Compare(rawItems[id1], rawItems[id2])
	})

	items := make([]*ptypes.Charge_DistributionItem, 0)

	accFraction := decimal.Zero
	for i, itemID := range itemsIDs {
		var fraction decimal.Decimal

		// We do this to ensure that the sum of fractions is 1.
		if i == len(itemsIDs)-1 {
			fraction = decimal.NewFromFloat(1.0).Sub(accFraction)
		} else {
			inputPremium := decimal.NewFromFloat(rawItems[itemID])
			fraction = inputPremium.Div(totalPremium).Round(precisionForInputDistribution)
		}

		accFraction = accFraction.Add(fraction)

		items = append(items, ptypes.NewChargeDistributionItem(itemID, fraction.String()))
	}

	return ptypes.NewChargeDistribution(distributionType, items...), nil
}

func createSurcharges(
	m *ModelOutputImpl,
	policyNumber string,
	chunkDates *proto.Interval,
) ([]*ptypes.Charge, error) {
	charges := make([]*ptypes.Charge, 0)

	ncrfCharge := createNCRFSurcharge(m, policyNumber, chunkDates)
	charges = append(charges, ncrfCharge)

	mccaCharge := createMCCASurcharge(m, policyNumber, chunkDates)
	charges = append(charges, mccaCharge)

	stCharges := createSurplusTaxSurcharges(m, policyNumber, chunkDates)
	charges = append(charges, stCharges...)

	sfCharges := createStampingFeeSurcharges(m, policyNumber, chunkDates)
	charges = append(charges, sfCharges...)

	return charges, nil
}

func createNCRFSurcharge(
	m *ModelOutputImpl,
	policyNumber string,
	chunkDates *proto.Interval,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeSurchargeWithNCRFType()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		m.NCRFSurchargePremium,
	)
}

func createMCCASurcharge(
	m *ModelOutputImpl,
	policyNumber string,
	chunkDates *proto.Interval,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeSurchargeWithMCCAType()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		m.MCCASurchargePremium,
	)
}

func createSurplusTaxSurcharges(
	m *ModelOutputImpl,
	policyNumber string,
	chunkDates *proto.Interval,
) []*ptypes.Charge {
	charges := make([]*ptypes.Charge, 0)

	premiumRelatedLinesTax := m.SurplusLinesTax
	if premiumRelatedLinesTax != nil {
		chargeType := ptypes.NewChargeSurchargeWithSurplusTax()
		chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
		charge := createChargeWithAmountBasedBillingDetails(
			chunkDates,
			chargeType,
			chargedItem,
			*premiumRelatedLinesTax,
		)
		charges = append(charges, charge)
	}

	feeRelatedLinesTax := m.FlatSurplusLinesTax
	if feeRelatedLinesTax != nil {
		chargeType := ptypes.NewChargeSurchargeWithSurplusTaxFromFullyEarnedPremium()
		chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
		charge := createChargeWithAmountBasedBillingDetails(
			chunkDates,
			chargeType,
			chargedItem,
			*feeRelatedLinesTax,
		)
		charges = append(charges, charge)
	}

	return charges
}

func createStampingFeeSurcharges(
	m *ModelOutputImpl,
	policyNumber string,
	chunkDates *proto.Interval,
) []*ptypes.Charge {
	charges := make([]*ptypes.Charge, 0)

	premiumRelatedStampingFee := m.SurplusStampingFee
	if premiumRelatedStampingFee != nil {
		chargeType := ptypes.NewChargeSurchargeWithStampingFee()
		chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
		charge := createChargeWithAmountBasedBillingDetails(
			chunkDates,
			chargeType,
			chargedItem,
			*premiumRelatedStampingFee,
		)
		charges = append(charges, charge)
	}

	feeRelatedStampingFee := m.FlatSurplusStampingFee
	if feeRelatedStampingFee != nil {
		chargeType := ptypes.NewChargeSurchargeWithStampingFeeFromFullyEarnedPremium()
		chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
		charge := createChargeWithAmountBasedBillingDetails(
			chunkDates,
			chargeType,
			chargedItem,
			*feeRelatedStampingFee,
		)
		charges = append(charges, charge)
	}

	return charges
}

func createFeeCharges(
	m *ModelOutputImpl,
	policyNumber string,
	chunkDates *proto.Interval,
) ([]*ptypes.Charge, error) {
	charges := make([]*ptypes.Charge, 0)

	ch := createBRAICharge(
		policyNumber,
		chunkDates,
		m.BlanketAdditionalInsuredBaseCharge,
	)
	charges = append(charges, ch)

	ch = createBPNCAICharge(
		policyNumber,
		chunkDates,
		m.BlanketPNCAdditionalInsuredBaseCharge,
	)
	charges = append(charges, ch)

	for _, ai := range m.AdditionalInsuredsRegular {
		ch = createSRAICharge(policyNumber, chunkDates, ai)
		charges = append(charges, ch)
	}

	for _, ai := range m.AdditionalInsuredsPNC {
		ch = createSPNCAICharge(policyNumber, chunkDates, ai)
		charges = append(charges, ch)
	}

	ch = createBWOSCharge(policyNumber, chunkDates, m.BlanketWaiverOfSubrogationBaseCharge)
	charges = append(charges, ch)

	for _, tp := range m.ThirdPartiesWithWOS {
		ch = createSWOSCharge(policyNumber, chunkDates, tp)
		charges = append(charges, ch)
	}

	return charges, nil
}

// createBAICharge creates a charge for the blanket regular additional insured.
func createBRAICharge(
	policyNumber string,
	chunkDates *proto.Interval,
	premium float64,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithBlanketRegularAdditionalInsured()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

// createBAICharge creates a charge for the blanket primary and non-contributory additional insured.
func createBPNCAICharge(
	policyNumber string,
	chunkDates *proto.Interval,
	premium float64,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithBlanketPrimaryAndNonContributoryAdditionalInsured()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

// createBWOSCharge creates a charge for the blanket waiver of subrogation.
func createBWOSCharge(
	policyNumber string,
	chunkDates *proto.Interval,
	premium float64,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithBlanketWaiverOfSubrogation()
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

// createSRAICharge creates a charge for a specified regular additional insured.
func createSRAICharge(
	policyNumber string,
	chunkDates *proto.Interval,
	additionalInsured *entities.SpecifiedRegularAI,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithSpecifiedRegularAdditionalInsured(additionalInsured.Id)
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		additionalInsured.SpecifiedRegularAIBaseCharge,
	)
}

// createSPNCAICharge creates a charge for a specified primary and non-contributory additional insured.
func createSPNCAICharge(
	policyNumber string,
	chunkDates *proto.Interval,
	additionalInsured *entities.SpecifiedPNCAI,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured(additionalInsured.Id)
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		additionalInsured.SpecifiedPNCAIBaseCharge,
	)
}

// createSWOSCharge creates a charge for a specified third party with a waiver of subrogation.
func createSWOSCharge(
	policyNumber string,
	chunkDates *proto.Interval,
	thirdParty *entities.SpecifiedWOS,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithSpecifiedThirdPartyWithWaiverOfSubrogation(thirdParty.Id)
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		thirdParty.SpecifiedWOSBaseCharge,
	)
}

func createChargeWithAmountBasedBillingDetails(
	chunkDates *proto.Interval,
	chargeType ptypes.ChargeTypeI,
	chargedItem ptypes.ChargedItemI,
	premium float64,
	distributions ...*ptypes.Charge_Distribution,
) *ptypes.Charge {
	if premium == 0 {
		return nil
	}

	premiumAsString := decimal.NewFromFloat(premium).String()

	// For charges with AmountBasedBillingDetails that originate from a chunk,
	// we assume that the charge date is the start date of the chunk. The
	// reason for this, is to ensure that the entire premium is charged in
	// a billing interval that includes the start date of the chunk (i.e.
	// that includes the endorsement that resulted in the chunk).
	chargeDate := chunkDates.Start

	return ptypes.NewChargeBuilder().
		WithChargeType(chargeType).
		WithChargedItem(chargedItem).
		WithAmountBasedBillingDetails(premiumAsString, chargeDate).
		WithDistributions(distributions...).
		Build()
}
