package plugins

import (
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/rtypes"
)

type supportedMapValue struct {
	mandatoryPlugins []ptypes.PluginID
	optionalPlugins  []ptypes.PluginID
}

type SupportedMap map[rtypes.ModelKey]supportedMapValue

var supportedMap = SupportedMap{
	rtypes.ProviderSentry_AL_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_AL_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_AL_0_5_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_AZ_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_AZ_0_1_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_AZ_0_2_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_AZ_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_CA_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_CA_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_CO_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_GA_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_GA_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_GA_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_GA_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_IA_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IA_0_3_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IA_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IA_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IA_0_5_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_IL_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IL_0_5_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IL_0_6_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IL_0_6_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IL_0_6_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IL_0_6_5: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IL_0_7_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IL_0_7_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IL_0_8_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IL_0_8_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IL_0_9_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IL_0_9_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_IN_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IN_0_6_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IN_0_6_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IN_0_7_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IN_0_7_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IN_0_7_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IN_0_8_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_IN_0_8_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_KS_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_KS_0_5_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_KY_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_KY_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_MI_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MI_0_0_5: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MI_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MI_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MI_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MI_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_MN_0_5_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MN_0_5_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MN_0_7_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MN_0_7_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MN_0_8_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MN_0_8_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MN_0_9_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_MO_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MO_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MO_0_5_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MO_0_6_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_MO_0_6_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_NC_0_2_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_NC_0_2_5: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_NC_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_NC_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_NC_0_5_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_NE_0_2_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_NE_0_2_5: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_NE_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_NE_0_5_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_NE_0_5_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_NM_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_NV_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_NV_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_OH_0_5_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OH_0_6_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OH_0_6_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OH_0_6_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OH_0_6_6: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OH_0_7_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OH_0_7_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OH_0_7_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OH_0_8_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_OK_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OK_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OK_0_5_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_OR_0_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OR_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OR_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OR_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_OR_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_PA_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_PA_0_5_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_PA_0_6_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_SC_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_SC_0_5_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_TN_0_6_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_TN_0_6_5: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_TN_0_7_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_TN_0_9_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_TX_0_3_5: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_TX_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_TX_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_TX_0_5_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_TX_0_5_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_TX_0_6_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_UT_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_UT_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_WA_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_WA_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentry_WI_0_5_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_WI_0_6_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_WI_0_7_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_WI_0_7_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_WI_0_8_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentry_WI_0_8_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderSentryMST_AL_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AL_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_AR_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AR_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AR_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AR_0_1_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AR_0_1_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AR_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AR_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AR_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AR_0_2_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AR_0_2_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_AZ_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_1_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_AZ_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_CA_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_1_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_1_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_1_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_1_5: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_2_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_CA_0_3_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_CO_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_CO_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_GA_0_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_GA_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_IA_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_1_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IA_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_IL_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IL_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_IN_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_IN_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_KS_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_KS_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_KY_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_1_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_KY_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_MI_0_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MI_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_MN_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_1_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MN_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_MO_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_MO_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_NC_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_3_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NC_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_NE_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NE_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_NM_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_1_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NM_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_NV_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_1_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_NV_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_OH_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OH_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_OK_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OK_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_OR_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_OR_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_PA_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_PA_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_SC_0_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_SC_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_TN_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_TN_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_TX_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_TX_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_UT_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_2_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_UT_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_WA_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WA_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WA_0_1_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WA_0_1_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WA_0_1_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WA_0_1_5: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WA_0_1_6: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WA_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_WA_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_WA_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_WA_0_2_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_WA_0_2_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderSentryMST_WI_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_2_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_2_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_2_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_3_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_3_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_3_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_3_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_4_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_4_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_4_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_4_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},
	rtypes.ProviderSentryMST_WI_0_4_4: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
		optionalPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
		},
	},

	rtypes.ProviderProgressive_AZ_2_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_GA_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_GA_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_GA_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_GA_1_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_GA_2_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},
	rtypes.ProviderProgressive_GA_2_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},
	rtypes.ProviderProgressive_GA_2_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_IA_2_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},
	rtypes.ProviderProgressive_IA_2_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_IL_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IL_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IL_0_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IL_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IL_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IL_0_1_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IL_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IL_2_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},
	rtypes.ProviderProgressive_IL_2_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},
	rtypes.ProviderProgressive_IL_2_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_IN_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IN_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IN_0_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IN_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IN_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IN_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IN_1_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_IN_2_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_MI_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MI_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MI_0_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MI_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MI_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MI_0_1_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MI_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MI_2_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},
	rtypes.ProviderProgressive_MI_2_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},
	rtypes.ProviderProgressive_MI_2_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_MN_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MN_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MN_0_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MN_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},

	rtypes.ProviderProgressive_MO_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MO_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MO_0_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MO_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MO_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MO_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MO_1_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_MO_2_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_NC_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_NC_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_NC_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_NC_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_NC_1_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_NC_2_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_NV_2_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_OH_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_OH_0_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_OH_0_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_OH_0_0_3: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_OH_0_1_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_OH_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_OH_1_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_OH_2_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_PA_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_PA_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_PA_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_PA_1_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_PA_2_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_SC_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_SC_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_SC_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_SC_2_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},
	rtypes.ProviderProgressive_SC_2_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},
	rtypes.ProviderProgressive_SC_2_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_TN_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_TN_0_1_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_TN_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_TN_1_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressive_TN_2_0_2: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressive_WI_2_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},
	rtypes.ProviderProgressive_WI_2_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderProgressiveSurplus_TX_1_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
		},
	},
	rtypes.ProviderProgressiveSurplus_TX_2_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},
	rtypes.ProviderProgressiveSurplus_TX_2_0_1: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
			ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
			ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
			ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
			ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
		},
	},

	rtypes.ProviderNico_AZ_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MedPayRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UMBIRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UIMBIRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoLiabRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_NonOwnedVehicleRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_BIAndPDRequireJointSelectionValidation_V1,
			ptypes.PluginID_PluginID_TowingRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_RentalRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_CollRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_SamePDCombinationForAllVehiclesValidation_V1,
			ptypes.PluginID_PluginID_TowingRestrictedToCertainVehiclesValidation_V1,
			ptypes.PluginID_PluginID_NoSpecifiedXModifierValidation_V1,
			ptypes.PluginID_PluginID_OnlyBAPolicyIsSupportedValidation_V1,

			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderNico_GA_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MedPayRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoLiabRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_NonOwnedVehicleRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_BIAndPDRequireJointSelectionValidation_V1,
			ptypes.PluginID_PluginID_TowingRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_RentalRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_CollRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_SamePDCombinationForAllVehiclesValidation_V1,
			ptypes.PluginID_PluginID_TowingRestrictedToCertainVehiclesValidation_V1,
			ptypes.PluginID_PluginID_NoSpecifiedXModifierValidation_V1,
			ptypes.PluginID_PluginID_OnlyBAPolicyIsSupportedValidation_V1,

			ptypes.PluginID_PluginID_UMUIMRequiresLiabValidation_V1,

			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderNico_IL_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MedPayRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UMBIRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UIMBIRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoLiabRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_NonOwnedVehicleRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_BIAndPDRequireJointSelectionValidation_V1,
			ptypes.PluginID_PluginID_TowingRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_RentalRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_CollRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_SamePDCombinationForAllVehiclesValidation_V1,
			ptypes.PluginID_PluginID_TowingRestrictedToCertainVehiclesValidation_V1,
			ptypes.PluginID_PluginID_NoSpecifiedXModifierValidation_V1,
			ptypes.PluginID_PluginID_OnlyBAPolicyIsSupportedValidation_V1,

			ptypes.PluginID_PluginID_UMPDRestrictedToPPTVehiclesValidation_V1,
			ptypes.PluginID_PluginID_CollAndUMPDMutualExclusivityValidation_V1,
			ptypes.PluginID_PluginID_UMPDRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UMBIIsRequiredIfLiabIsPresentValidation_V1,
			ptypes.PluginID_PluginID_UMPDRequiresUMBIValidation_V1,

			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderNico_IN_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MedPayRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UMBIRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UIMBIRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoLiabRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_NonOwnedVehicleRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_BIAndPDRequireJointSelectionValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1,
			ptypes.PluginID_PluginID_TowingRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_RentalRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_CollRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_SamePDCombinationForAllVehiclesValidation_V1,
			ptypes.PluginID_PluginID_TowingRestrictedToCertainVehiclesValidation_V1,
			ptypes.PluginID_PluginID_NoSpecifiedXModifierValidation_V1,
			ptypes.PluginID_PluginID_OnlyBAPolicyIsSupportedValidation_V1,

			ptypes.PluginID_PluginID_UMPDRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UMPDRequiresUMBIValidation_V1,

			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderNico_OH_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MedPayRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UMBIRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UIMBIRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoLiabRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_NonOwnedVehicleRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_BIAndPDRequireJointSelectionValidation_V1,
			ptypes.PluginID_PluginID_TowingRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_RentalRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_CollRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_SamePDCombinationForAllVehiclesValidation_V1,
			ptypes.PluginID_PluginID_TowingRestrictedToCertainVehiclesValidation_V1,
			ptypes.PluginID_PluginID_NoSpecifiedXModifierValidation_V1,
			ptypes.PluginID_PluginID_OnlyBAPolicyIsSupportedValidation_V1,

			ptypes.PluginID_PluginID_SamePerOccurrenceLimitForUMBIAndUIMBIValidation_V1,
			ptypes.PluginID_PluginID_UMPDRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UMPDRequiresUMBIValidation_V1,

			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderNico_PA_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MedPayRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UMBIRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UIMBIRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoLiabRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_NonOwnedVehicleRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_BIAndPDRequireJointSelectionValidation_V1,
			ptypes.PluginID_PluginID_TowingRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_RentalRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_CollRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_SamePDCombinationForAllVehiclesValidation_V1,
			ptypes.PluginID_PluginID_TowingRestrictedToCertainVehiclesValidation_V1,
			ptypes.PluginID_PluginID_NoSpecifiedXModifierValidation_V1,
			ptypes.PluginID_PluginID_OnlyBAPolicyIsSupportedValidation_V1,

			ptypes.PluginID_PluginID_MedicalExpenseBenefitsRequiresLiabValidation_V1,

			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},

	rtypes.ProviderNico_TX_0_0_0: {
		mandatoryPlugins: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MedPayRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoLiabRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_NonOwnedVehicleRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_BIAndPDRequireJointSelectionValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1,
			ptypes.PluginID_PluginID_TowingRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_RentalRequiresPDValidation_V1,
			ptypes.PluginID_PluginID_HiredAutoPDRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_CollRequiresCompValidation_V1,
			ptypes.PluginID_PluginID_SamePDCombinationForAllVehiclesValidation_V1,
			ptypes.PluginID_PluginID_TowingRestrictedToCertainVehiclesValidation_V1,
			ptypes.PluginID_PluginID_NoSpecifiedXModifierValidation_V1,
			ptypes.PluginID_PluginID_OnlyBAPolicyIsSupportedValidation_V1,

			ptypes.PluginID_PluginID_PIPRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_UMUIMRequiresLiabValidation_V1,
			ptypes.PluginID_PluginID_MedPayAndPIPMutualExclusivityValidation_V1,

			ptypes.PluginID_PluginID_MetricsReporting_V1,
			ptypes.PluginID_PluginID_ChargesProration_V1,
			ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
		},
	},
}
