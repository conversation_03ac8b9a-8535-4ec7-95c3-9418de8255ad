package max_fully_earned_charges_rule_v1

import (
	"cmp"
	"context"
	"fmt"
	"math"
	"slices"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	common_proto "nirvanatech.com/nirvana/common-go/proto"
	slices_util "nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	plugins_common "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

var allPolicyNames = []ptypes.PolicyName{
	ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
	ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
	ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
}

type ruleTestEnv struct {
	fx.In
	Deps Deps
}

type ruleTestSuite struct {
	suite.Suite
	ctx   context.Context
	env   ruleTestEnv
	fxapp *fxtest.App
	rule  *Rule
}

func TestRule(t *testing.T) {
	suite.Run(t, new(ruleTestSuite))
}

func (s *ruleTestSuite) SetupSuite() {
	s.ctx = context.Background()

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
	)
}

func (s *ruleTestSuite) SetupTest() {
	s.rule = NewRule(s.env.Deps)
}

func (s *ruleTestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *ruleTestSuite) buildBundleChunks(chunkCount int) []*ptypes.BundleSpec_ChunkSpec {
	startDate := time.Now()
	var bundleChunks []*ptypes.BundleSpec_ChunkSpec
	for i := 0; i < chunkCount; i++ {
		endDate := startDate.AddDate(0, 0, 1)
		bundleChunks = append(bundleChunks, &ptypes.BundleSpec_ChunkSpec{
			ChunkId: fmt.Sprintf("chunk%d", i),
			Dates: &common_proto.Interval{
				Start: timestamppb.New(startDate),
				End:   timestamppb.New(endDate),
			},
		})
		startDate = endDate
	}

	return bundleChunks
}

func (s *ruleTestSuite) buildSpecifiedRegularAIs(low, high int, suffix string) []*ptypes.SpecifiedRegularAdditionalInsured {
	var specifiedAIs []*ptypes.SpecifiedRegularAdditionalInsured
	for j := low; j < high; j++ {
		specifiedAIs = append(specifiedAIs, &ptypes.SpecifiedRegularAdditionalInsured{
			Id: fmt.Sprintf("AI-Regular-%d-%s", j, suffix),
		})
	}
	return specifiedAIs
}

func (s *ruleTestSuite) buildSpecifiedPNCAIs(
	low,
	high int,
	suffix string,
) []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured {
	var specifiedAIs []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured
	for j := low; j < high; j++ {
		specifiedAIs = append(specifiedAIs, &ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
			Id: fmt.Sprintf("AI-PNC-%d-%s", j, suffix),
		})
	}
	return specifiedAIs
}

func (s *ruleTestSuite) buildSpecifiedWOSs(low, high int, suffix string) []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation {
	var specifiedWOSs []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation
	for j := low; j < high; j++ {
		specifiedWOSs = append(specifiedWOSs, &ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
			Id: fmt.Sprintf("WOS-%d-%s", j, suffix),
		})
	}

	return specifiedWOSs
}

func (s *ruleTestSuite) TestApply_WithNilNextPluginFn() {
	input := &plugins_common.PluginChainInput{}

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().Regexp("nextPluginApplyFn can't be nil", err.Error())
	s.Require().Nil(output)
}

// TestApply_1:
// - One policy
// - Specified Xs are less or equal than the maximum
func (s *ruleTestSuite) TestApply_1() {
	for _, policyName := range allPolicyNames {
		for totalChunksCount := 1; totalChunksCount <= 2; totalChunksCount++ {
			for totalSpecifiedCount := 0; totalSpecifiedCount <= maxFullyEarnedChargeCount; totalSpecifiedCount++ {
				testName := fmt.Sprintf(
					"With %s, %d chunks and %d specified X",
					policyName,
					totalChunksCount,
					totalSpecifiedCount,
				)
				s.Run(testName, func() {
					bundleChunks := s.buildBundleChunks(totalChunksCount)

					specifiedPerChunk := int(math.Ceil(float64(totalSpecifiedCount) / float64(totalChunksCount)))

					var policyChunks []*ptypes.PolicySpec_ChunkSpec
					start := 0
					end := specifiedPerChunk
					for i := 0; i < totalChunksCount; i++ {
						currentChunkSpecifiedRegularAIs := s.buildSpecifiedRegularAIs(start, end, "")
						currentChunkSpecifiedPNCAIs := s.buildSpecifiedPNCAIs(start, end, "")
						currentChunkSpecifiedWOSs := s.buildSpecifiedWOSs(start, end, "")

						slices_util.Shuffle(currentChunkSpecifiedRegularAIs)
						slices_util.Shuffle(currentChunkSpecifiedPNCAIs)
						slices_util.Shuffle(currentChunkSpecifiedWOSs)

						policyChunks = append(policyChunks, &ptypes.PolicySpec_ChunkSpec{
							ChunkId:                            bundleChunks[i].ChunkId,
							SpecifiedRegularAdditionalInsureds: currentChunkSpecifiedRegularAIs,
							SpecifiedPrimaryAndNonContributoryAdditionalInsureds: currentChunkSpecifiedPNCAIs,
							SpecifiedThirdPartiesWithWOS:                         currentChunkSpecifiedWOSs,
						})

						start = end
						end = int(math.Min(float64(end+specifiedPerChunk), float64(totalSpecifiedCount)))
					}

					request := &ptypes.Request{
						BundleSpec: &ptypes.BundleSpec{
							ChunkSpecs: bundleChunks,
						},
						PolicySpecs: []*ptypes.PolicySpec{
							{
								PolicyNumber: "policy1",
								PolicyName:   policyName,
								ChunkSpecs:   policyChunks,
							},
						},
					}

					expectedRequest := proto.Clone(request).(*ptypes.Request)

					mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
					mockNextPluginApplyFn := func(_ context.Context, input *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
						actualRequest, err := input.GetPriceRequest()
						s.Require().NoError(err)
						s.Require().EqualExportedValues(expectedRequest, actualRequest)
						return mockOutput, nil
					}
					err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
					s.Require().NoError(err)

					output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: request})
					s.Require().NoError(err)
					s.Require().Equal(mockOutput, output)
				})
			}
		}
	}
}

// TestApply_2:
// - One policy
// - Specified Xs are more than the maximum
func (s *ruleTestSuite) TestApply_2() {
	for _, policyName := range allPolicyNames {
		for totalChunksCount := 1; totalChunksCount <= 2; totalChunksCount++ {
			for totalSpecifiedCount := maxFullyEarnedChargeCount + 1; totalSpecifiedCount <= maxFullyEarnedChargeCount+2; totalSpecifiedCount++ {
				testName := fmt.Sprintf(
					"With %s, %d chunks and %d specified X",
					policyName,
					totalChunksCount,
					totalSpecifiedCount,
				)
				s.Run(testName, func() {
					bundleChunks := s.buildBundleChunks(totalChunksCount)

					var policyChunks []*ptypes.PolicySpec_ChunkSpec
					var expectedPolicyChunks []*ptypes.PolicySpec_ChunkSpec

					specifiedPerChunk := int(math.Floor(float64(totalSpecifiedCount) / float64(totalChunksCount)))

					start := 0
					end := specifiedPerChunk
					for i := 0; i < totalChunksCount; i++ {
						currentChunkSpecifiedRegularAIs := s.buildSpecifiedRegularAIs(start, end, "")
						currentChunkSpecifiedPNCAIs := s.buildSpecifiedPNCAIs(start, end, "")
						currentChunkSpecifiedWOSs := s.buildSpecifiedWOSs(start, end, "")

						currentChunkExpectedSpecifiedRegularAIs := slices.Clone(currentChunkSpecifiedRegularAIs)
						currentChunkExpectedSpecifiedPNCAIs := slices.Clone(currentChunkSpecifiedPNCAIs)
						currentChunkExpectedSpecifiedWOSs := slices.Clone(currentChunkSpecifiedWOSs)
						if end > maxFullyEarnedChargeCount {
							maxIdx := maxFullyEarnedChargeCount - start
							currentChunkExpectedSpecifiedRegularAIs = slices.Clone(currentChunkExpectedSpecifiedRegularAIs[:maxIdx])
							currentChunkExpectedSpecifiedPNCAIs = slices.Clone(currentChunkExpectedSpecifiedPNCAIs[:maxIdx])
							currentChunkExpectedSpecifiedWOSs = slices.Clone(currentChunkSpecifiedWOSs[:maxIdx])
						}

						slices_util.Shuffle(currentChunkSpecifiedRegularAIs)
						slices_util.Shuffle(currentChunkSpecifiedPNCAIs)
						slices_util.Shuffle(currentChunkSpecifiedWOSs)

						slices.SortFunc(
							currentChunkExpectedSpecifiedRegularAIs,
							func(ai1, ai2 *ptypes.SpecifiedRegularAdditionalInsured) int {
								return cmp.Compare(
									slices.Index(currentChunkSpecifiedRegularAIs, ai1),
									slices.Index(currentChunkSpecifiedRegularAIs, ai2),
								)
							},
						)
						slices.SortFunc(
							currentChunkExpectedSpecifiedPNCAIs,
							func(ai1, ai2 *ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured) int {
								return cmp.Compare(
									slices.Index(currentChunkSpecifiedPNCAIs, ai1),
									slices.Index(currentChunkSpecifiedPNCAIs, ai2),
								)
							},
						)
						slices.SortFunc(
							currentChunkExpectedSpecifiedWOSs,
							func(wos1, wos2 *ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation) int {
								return cmp.Compare(
									slices.Index(currentChunkSpecifiedWOSs, wos1),
									slices.Index(currentChunkSpecifiedWOSs, wos2),
								)
							},
						)

						chunkID := bundleChunks[i].ChunkId
						policyChunk := &ptypes.PolicySpec_ChunkSpec{
							ChunkId:                            chunkID,
							SpecifiedRegularAdditionalInsureds: currentChunkSpecifiedRegularAIs,
							SpecifiedPrimaryAndNonContributoryAdditionalInsureds: currentChunkSpecifiedPNCAIs,
							SpecifiedThirdPartiesWithWOS:                         currentChunkSpecifiedWOSs,
						}
						expectedPolicyChunk := &ptypes.PolicySpec_ChunkSpec{
							ChunkId:                            chunkID,
							SpecifiedRegularAdditionalInsureds: currentChunkExpectedSpecifiedRegularAIs,
							SpecifiedPrimaryAndNonContributoryAdditionalInsureds: currentChunkExpectedSpecifiedPNCAIs,
							SpecifiedThirdPartiesWithWOS:                         currentChunkExpectedSpecifiedWOSs,
						}

						policyChunks = append(policyChunks, policyChunk)
						expectedPolicyChunks = append(expectedPolicyChunks, expectedPolicyChunk)

						start = end
						end = start + specifiedPerChunk
						if i == totalChunksCount-1 {
							end = totalSpecifiedCount + 1
						}
					}

					request := &ptypes.Request{
						BundleSpec: &ptypes.BundleSpec{
							ChunkSpecs: bundleChunks,
						},
						PolicySpecs: []*ptypes.PolicySpec{
							{
								PolicyNumber: "policy1",
								PolicyName:   policyName,
								ChunkSpecs:   policyChunks,
							},
						},
					}
					expectedRequest := &ptypes.Request{
						BundleSpec: &ptypes.BundleSpec{
							ChunkSpecs: bundleChunks,
						},
						PolicySpecs: []*ptypes.PolicySpec{
							{
								PolicyNumber: "policy1",
								PolicyName:   policyName,
								ChunkSpecs:   expectedPolicyChunks,
							},
						},
					}

					mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
					mockNextPluginApplyFn := func(_ context.Context, input *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
						actualRequest, err := input.GetPriceRequest()
						s.Require().NoError(err)
						s.Require().EqualExportedValues(expectedRequest, actualRequest)
						return mockOutput, nil
					}
					err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
					s.Require().NoError(err)

					output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: request})
					s.Require().NoError(err)
					s.Require().Equal(mockOutput, output)
				})
			}
		}
	}
}

// TestApply_3:
// - Three policies: MC, GL, MTC
// - One chunk
// - Specified Xs in MC are more than the maximum
func (s *ruleTestSuite) TestApply_3() {
	bundleChunks := s.buildBundleChunks(1)

	mcChunkSpecifiedRegularAIs := s.buildSpecifiedRegularAIs(0, maxFullyEarnedChargeCount, "ca")
	mcChunkSpecifiedPNCAIs := s.buildSpecifiedPNCAIs(0, maxFullyEarnedChargeCount, "ca")
	mcCurrentChunkSpecifiedWOSs := s.buildSpecifiedWOSs(0, maxFullyEarnedChargeCount, "ca")

	glChunkSpecifiedRegularAIs := s.buildSpecifiedRegularAIs(0, 1, "gl")
	glChunkSpecifiedPNCAIs := s.buildSpecifiedPNCAIs(0, 1, "gl")
	glCurrentChunkSpecifiedWOSs := s.buildSpecifiedWOSs(0, 1, "gl")

	mtcChunkSpecifiedRegularAIs := s.buildSpecifiedRegularAIs(0, 1, "im")
	mtcChunkSpecifiedPNCAIs := s.buildSpecifiedPNCAIs(0, 1, "im")
	mtcCurrentChunkSpecifiedWOSs := s.buildSpecifiedWOSs(0, 1, "im")

	chunkID := bundleChunks[0].ChunkId

	request := &ptypes.Request{
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: bundleChunks,
		},
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{
						ChunkId:                            chunkID,
						SpecifiedRegularAdditionalInsureds: mcChunkSpecifiedRegularAIs,
						SpecifiedPrimaryAndNonContributoryAdditionalInsureds: mcChunkSpecifiedPNCAIs,
						SpecifiedThirdPartiesWithWOS:                         mcCurrentChunkSpecifiedWOSs,
					},
				},
			},
			{
				PolicyNumber: "policy2",
				PolicyName:   ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{
						ChunkId:                            chunkID,
						SpecifiedRegularAdditionalInsureds: glChunkSpecifiedRegularAIs,
						SpecifiedPrimaryAndNonContributoryAdditionalInsureds: glChunkSpecifiedPNCAIs,
						SpecifiedThirdPartiesWithWOS:                         glCurrentChunkSpecifiedWOSs,
					},
				},
			},
			{
				PolicyNumber: "policy3",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{
						ChunkId:                            chunkID,
						SpecifiedRegularAdditionalInsureds: mtcChunkSpecifiedRegularAIs,
						SpecifiedPrimaryAndNonContributoryAdditionalInsureds: mtcChunkSpecifiedPNCAIs,
						SpecifiedThirdPartiesWithWOS:                         mtcCurrentChunkSpecifiedWOSs,
					},
				},
			},
		},
	}

	expectedRequest := proto.Clone(request).(*ptypes.Request)
	glChunk := expectedRequest.PolicySpecs[1].ChunkSpecs[0]
	glChunk.SpecifiedRegularAdditionalInsureds = nil
	glChunk.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
	glChunk.SpecifiedThirdPartiesWithWOS = nil
	mtcChunk := expectedRequest.PolicySpecs[2].ChunkSpecs[0]
	mtcChunk.SpecifiedRegularAdditionalInsureds = nil
	mtcChunk.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
	mtcChunk.SpecifiedThirdPartiesWithWOS = nil

	mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
	mockNextPluginApplyFn := func(_ context.Context, input *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		actualRequest, err := input.GetPriceRequest()
		s.Require().NoError(err)
		s.Require().EqualExportedValues(expectedRequest, actualRequest)
		return mockOutput, nil
	}
	err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: request})
	s.Require().NoError(err)
	s.Require().Equal(mockOutput, output)
}

// TestApply_4:
// - Two policies: GL, MTC
// - One chunk
// - Specified Xs in GL are more than the maximum
func (s *ruleTestSuite) TestApply_4() {
	bundleChunks := s.buildBundleChunks(1)

	glChunkSpecifiedRegularAIs := s.buildSpecifiedRegularAIs(0, maxFullyEarnedChargeCount, "gl")
	glChunkSpecifiedPNCAIs := s.buildSpecifiedPNCAIs(0, maxFullyEarnedChargeCount, "gl")
	glCurrentChunkSpecifiedWOSs := s.buildSpecifiedWOSs(0, maxFullyEarnedChargeCount, "gl")

	mtcChunkSpecifiedRegularAIs := s.buildSpecifiedRegularAIs(0, 1, "im")
	mtcChunkSpecifiedPNCAIs := s.buildSpecifiedPNCAIs(0, 1, "im")
	mtcCurrentChunkSpecifiedWOSs := s.buildSpecifiedWOSs(0, 1, "im")

	chunkID := bundleChunks[0].ChunkId

	request := &ptypes.Request{
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: bundleChunks,
		},
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{
						ChunkId:                            chunkID,
						SpecifiedRegularAdditionalInsureds: glChunkSpecifiedRegularAIs,
						SpecifiedPrimaryAndNonContributoryAdditionalInsureds: glChunkSpecifiedPNCAIs,
						SpecifiedThirdPartiesWithWOS:                         glCurrentChunkSpecifiedWOSs,
					},
				},
			},
			{
				PolicyNumber: "policy2",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{
						ChunkId:                            chunkID,
						SpecifiedRegularAdditionalInsureds: mtcChunkSpecifiedRegularAIs,
						SpecifiedPrimaryAndNonContributoryAdditionalInsureds: mtcChunkSpecifiedPNCAIs,
						SpecifiedThirdPartiesWithWOS:                         mtcCurrentChunkSpecifiedWOSs,
					},
				},
			},
		},
	}

	expectedRequest := proto.Clone(request).(*ptypes.Request)
	mtcChunk := expectedRequest.PolicySpecs[1].ChunkSpecs[0]
	mtcChunk.SpecifiedRegularAdditionalInsureds = nil
	mtcChunk.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
	mtcChunk.SpecifiedThirdPartiesWithWOS = nil

	mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
	mockNextPluginApplyFn := func(_ context.Context, input *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		actualRequest, err := input.GetPriceRequest()
		s.Require().NoError(err)
		s.Require().EqualExportedValues(expectedRequest, actualRequest)
		return mockOutput, nil
	}
	err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: request})
	s.Require().NoError(err)
	s.Require().Equal(mockOutput, output)
}
