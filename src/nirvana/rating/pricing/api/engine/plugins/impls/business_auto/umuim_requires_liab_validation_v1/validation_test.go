package umuim_requires_liab_validation_v1

import (
	"context"
	"testing"

	"github.com/cockroachdb/errors"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/infra/fx/testloader"
	plugins_common "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

type validationTestEnv struct {
	fx.In

	Deps Deps
}

type validationTestSuite struct {
	suite.Suite

	ctx        context.Context
	ctrl       *gomock.Controller
	env        validationTestEnv
	fxapp      *fxtest.App
	validation *Validation
}

func TestValidation(t *testing.T) {
	suite.Run(t, new(validationTestSuite))
}

func (s *validationTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.ctrl = gomock.NewController(s.T())

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
	)
}

func (s *validationTestSuite) SetupTest() {
	s.validation = NewValidation(s.env.Deps)
}

func (s *validationTestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *validationTestSuite) TestApply_WithNilNextPluginApplyFn() {
	output, err := s.validation.Apply(s.ctx, nil)
	s.Require().Error(err)
	s.Require().Regexp("nextPluginApplyFn can't be nil", err.Error())
	s.Require().Nil(output)
}

func (s *validationTestSuite) TestApply_WithErrorFromNextPlugin() {
	input := &plugins_common.PluginChainInput{
		PolicyNumber: "policy123",
		Request: &ptypes.Request{
			PolicySpecs: []*ptypes.PolicySpec{
				{
					PolicyNumber: "policy123",
					ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
						{
							ChunkId: "chunkID-1",
							Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
								BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{},
							},
						},
					},
				},
			},
		},
	}

	mockNextPluginError := errors.New("mock error from next plugin")
	mockNextPluginApplyFn := func(ctx context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		s.Require().Equal(input, inp)
		return nil, mockNextPluginError
	}
	err := s.validation.SetNextPluginApplyFn(mockNextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.validation.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().ErrorIs(err, mockNextPluginError)
	s.Require().Nil(output)
}

func (s *validationTestSuite) TestApply_WithUMUIM_WithLiab() {
	input := &plugins_common.PluginChainInput{
		PolicyNumber: "policy-1",
		Request: &ptypes.Request{
			PolicySpecs: []*ptypes.PolicySpec{
				{
					// Policy is valid
					PolicyNumber: "policy-1",
					ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
						{
							ChunkId: "chunkID-1",
							SubCoverages: []ptypes.SubCoverageType{
								ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
								ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
								ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
								ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
								ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
								ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
							},
							Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
								BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{},
							},
						},
					},
				},
				{
					// Policy is not valid, but it's not the one that is currently being
					// processed by the plugin chain, so it should not cause an error.
					PolicyNumber: "policy-2",
					ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
						{
							ChunkId: "chunkID-1",
							SubCoverages: []ptypes.SubCoverageType{
								ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
								ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
								ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
								ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
							},
							Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
								BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{},
							},
						},
					},
				},
			},
		},
	}

	mockPluginChainOutput := plugins_common.PluginChainOutput{
		"chunkID-1": &plugins_common.ChunkOutput{},
		"chunkID-2": &plugins_common.ChunkOutput{},
	}
	mockNextPluginApplyFn := func(ctx context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		s.Require().Equal(input, inp)
		return mockPluginChainOutput, nil
	}
	err := s.validation.SetNextPluginApplyFn(mockNextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.validation.Apply(s.ctx, input)
	s.Require().NoError(err)
	s.Require().Equal(mockPluginChainOutput, output)
}

func (s *validationTestSuite) TestApply_WithUMUIM_WithoutLiab() {
	type testCase struct {
		name    string
		subCovs []ptypes.SubCoverageType
	}

	testCases := []testCase{
		{
			name: "UMUIM without BI or PD",
			subCovs: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
			},
		},
		{
			name: "UMUIM with BI, but without PD",
			subCovs: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
			},
		},
		{
			name: "UMUIM with PD, but without BI",
			subCovs: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
			},
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			input := &plugins_common.PluginChainInput{
				PolicyNumber: "policy-1",
				Request: &ptypes.Request{
					PolicySpecs: []*ptypes.PolicySpec{
						{
							PolicyNumber: "policy-1",
							ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
								{
									ChunkId: "chunkID-1",
									Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
										BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{},
									},
								},
								{
									ChunkId:      "chunkID-2",
									SubCoverages: tc.subCovs,
									Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
										BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{},
									},
								},
							},
						},
					},
				},
			}

			mockNextPluginApplyFn := func(ctx context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				s.Require().Fail("should not have reached here")
				return nil, nil
			}
			err := s.validation.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.validation.Apply(s.ctx, input)
			s.Require().Error(err)
			s.Require().Regexp("UMUIM requires Liab", err.Error())
			s.Require().Regexp("chunkID-2", err.Error())
			s.Require().Nil(output)
		})
	}
}