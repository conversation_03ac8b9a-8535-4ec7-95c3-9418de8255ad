load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "med_pay_and_pip_mutual_exclusivity_validation_v1",
    srcs = ["validation.go"],
    importpath = "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/med_pay_and_pip_mutual_exclusivity_validation_v1",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/rating/pricing/api/engine/plugins/common",
        "//nirvana/rating/pricing/api/ptypes/programs/business_auto",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "med_pay_and_pip_mutual_exclusivity_validation_v1_test",
    srcs = ["validation_test.go"],
    embed = [":med_pay_and_pip_mutual_exclusivity_validation_v1"],
    deps = [
        "//nirvana/infra/fx/testloader",
        "//nirvana/rating/pricing/api/engine/plugins/common",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
