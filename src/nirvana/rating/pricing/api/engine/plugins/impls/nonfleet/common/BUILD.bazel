load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "common",
    srcs = ["fully_earned_charges_utils.go"],
    importpath = "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/common",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/slice_utils",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_cockroachdb_errors//:errors",
    ],
)

go_test(
    name = "common_test",
    srcs = ["fully_earned_charges_utils_test.go"],
    embed = [":common"],
    deps = [
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_stretchr_testify//require",
    ],
)
