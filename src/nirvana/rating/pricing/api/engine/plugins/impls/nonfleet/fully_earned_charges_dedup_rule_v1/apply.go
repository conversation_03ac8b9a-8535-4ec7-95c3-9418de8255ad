package fully_earned_charges_dedup_rule_v1

import (
	"context"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	nf_common "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

// The apply method assumes that the data was already
// validated.
//
// For instance, it assumes that AIs and TP with WOS
// are unique within a policy chunk.
//
// Note: this rule assumes that AI types are valid
// (no unspecified value). We don't validate this
// here, because it is assumed that the engine already
// validated it.
func (r *Rule) apply(
	ctx context.Context,
	input *common.PluginChainInput,
) (common.PluginChainOutput, error) {
	if r.nextPluginApplyFn == nil {
		return nil, errors.New("nextPluginApplyFn can't be nil")
	}

	req, err := input.GetPriceRequest()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get price request from plugin chain input")
	}

	bundleChunks := req.BundleSpec.ChunkSpecs

	policySpecs := req.PolicySpecs

	mcPolicyChunksMap, err := nf_common.BuildPolicyChunksMap(
		policySpecs,
		ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
	)
	if err != nil {
		return nil, err
	}

	glPolicyChunksMap, err := nf_common.BuildPolicyChunksMap(
		policySpecs,
		ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
	)
	if err != nil {
		return nil, err
	}

	mtcPolicyChunksMap, err := nf_common.BuildPolicyChunksMap(
		policySpecs,
		ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
	)
	if err != nil {
		return nil, err
	}

	shouldPriceBlanketRegularAI := true
	shouldPriceBlanketPNCAI := true
	shouldPriceBlanketWOS := true

	pricedSpecifiedRegularAI := make(map[string]bool)
	pricedSpecifiedPNCAI := make(map[string]bool)
	pricedSpecifiedWOS := make(map[string]bool)

	for i, bundleChunk := range bundleChunks {
		if bundleChunk == nil {
			return nil, errors.Newf("nil bundle chunk found at index %d", i)
		}

		chunkID := bundleChunk.ChunkId

		mcChunk := mcPolicyChunksMap[chunkID]
		glChunk := glPolicyChunksMap[chunkID]
		mtcChunk := mtcPolicyChunksMap[chunkID]

		// The time interval between non-contiguous chunks (i.e., chunks for which
		// there's a gap in between) represents a period of time in which the policy
		// was "paused". When the policy is reinstated, we should charge it as if it
		// was just starting.
		//
		// This means charging again for any modifiers that were previously priced.
		//
		// To achieve this, we re-start the variables that we use to track which
		// modifiers were already priced for. Re-starting these variables "forces"
		// the algorithm to price the modifiers again.
		if i > 0 {
			prevDates := bundleChunks[i-1].Dates
			currentDates := bundleChunk.Dates
			if prevDates == nil || currentDates == nil {
				return nil, errors.Newf("missing dates for bundle chunks at index %d", i)
			}

			if !(prevDates.End.AsTime().Equal(currentDates.Start.AsTime())) {
				shouldPriceBlanketRegularAI = true
				shouldPriceBlanketPNCAI = true
				shouldPriceBlanketWOS = true
				pricedSpecifiedRegularAI = make(map[string]bool)
				pricedSpecifiedPNCAI = make(map[string]bool)
				pricedSpecifiedWOS = make(map[string]bool)
			}
		}

		shouldPriceBlanketRegularAI = r.handleBlanketModifier(
			mcChunk,
			glChunk,
			mtcChunk,
			shouldPriceBlanketRegularAI,
			r.checkBlanketRegularAIIsPresent,
			r.removeBlanketRegularAI,
		)
		shouldPriceBlanketPNCAI = r.handleBlanketModifier(
			mcChunk,
			glChunk,
			mtcChunk,
			shouldPriceBlanketPNCAI,
			r.checkBlanketPNCAIIsPresent,
			r.removeBlanketPNCAI,
		)
		shouldPriceBlanketWOS = r.handleBlanketModifier(
			mcChunk,
			glChunk,
			mtcChunk,
			shouldPriceBlanketWOS,
			r.checkBlanketWOSIsPresent,
			r.removeBlanketWOS,
		)

		r.handleSpecifiedModifier(
			mcChunk,
			glChunk,
			mtcChunk,
			pricedSpecifiedRegularAI,
			nf_common.GetSpecifiedRegularAIIDs,
			nf_common.RemoveSpecifiedRegularAIID,
		)
		r.handleSpecifiedModifier(
			mcChunk,
			glChunk,
			mtcChunk,
			pricedSpecifiedPNCAI,
			nf_common.GetSpecifiedPNCAIIDs,
			nf_common.RemoveSpecifiedPNCAIID,
		)
		r.handleSpecifiedModifier(
			mcChunk,
			glChunk,
			mtcChunk,
			pricedSpecifiedWOS,
			nf_common.GetSpecifiedWOSIDs,
			nf_common.RemoveSpecifiedWOSID,
		)
	}

	return r.nextPluginApplyFn(ctx, input)
}

// handleBlanketModifier receives a boolean flag representing if the blanket modifier
// should be priced, a blanket modifier (AI or WOS), and three policy chunks (one for
// each policy). It removes the blanket modifier from some chunks according to dedup
// logic, and returns whether the blanket modifier should be priced in the next chunk.
func (r *Rule) handleBlanketModifier(
	mcChunk *ptypes.PolicySpec_ChunkSpec,
	glChunk *ptypes.PolicySpec_ChunkSpec,
	mtcChunk *ptypes.PolicySpec_ChunkSpec,
	shouldPriceModifier bool,
	checkModifierIsPresentFn func(*ptypes.PolicySpec_ChunkSpec) bool,
	removeModifierFn func(*ptypes.PolicySpec_ChunkSpec),
) bool {
	isModifierPresentForMCPolicy := checkModifierIsPresentFn(mcChunk)
	isModifierPresentForGLPolicy := checkModifierIsPresentFn(glChunk)
	isModifierPresentForMTCPolicy := checkModifierIsPresentFn(mtcChunk)

	// First we check if modifiers should be removed for the current chunks.
	if shouldPriceModifier {
		if isModifierPresentForMCPolicy {
			if isModifierPresentForGLPolicy {
				removeModifierFn(glChunk)
			}
			if isModifierPresentForMTCPolicy {
				removeModifierFn(mtcChunk)
			}
		} else if isModifierPresentForGLPolicy {
			if isModifierPresentForMTCPolicy {
				removeModifierFn(mtcChunk)
			}
		}
	} else {
		if isModifierPresentForMCPolicy {
			removeModifierFn(mcChunk)
		}
		if isModifierPresentForGLPolicy {
			removeModifierFn(glChunk)
		}
		if isModifierPresentForMTCPolicy {
			removeModifierFn(mtcChunk)
		}
	}

	// Then we return whether the modifier should be priced in the next chunk
	// (which only happens if the modifier is not present in any of the current chunks).
	return !isModifierPresentForMCPolicy && !isModifierPresentForGLPolicy && !isModifierPresentForMTCPolicy
}

func (r *Rule) checkBlanketRegularAIIsPresent(chunk *ptypes.PolicySpec_ChunkSpec) bool {
	return chunk != nil && chunk.BlanketRegularAdditionalInsured != nil
}

func (r *Rule) checkBlanketPNCAIIsPresent(chunk *ptypes.PolicySpec_ChunkSpec) bool {
	return chunk != nil && chunk.BlanketPrimaryAndNonContributoryAdditionalInsured != nil
}

func (r *Rule) checkBlanketWOSIsPresent(chunk *ptypes.PolicySpec_ChunkSpec) bool {
	return chunk != nil && chunk.BlanketThirdPartyWithWaiverOfSubrogation != nil
}

func (r *Rule) removeBlanketRegularAI(chunk *ptypes.PolicySpec_ChunkSpec) {
	if chunk != nil {
		chunk.BlanketRegularAdditionalInsured = nil
	}
}

func (r *Rule) removeBlanketPNCAI(chunk *ptypes.PolicySpec_ChunkSpec) {
	if chunk != nil {
		chunk.BlanketPrimaryAndNonContributoryAdditionalInsured = nil
	}
}

func (r *Rule) removeBlanketWOS(chunk *ptypes.PolicySpec_ChunkSpec) {
	if chunk != nil {
		chunk.BlanketThirdPartyWithWaiverOfSubrogation = nil
	}
}

// handleSpecifiedModifier receives a map representing which specified AIs/WOSs
// are already priced and three policy chunks (one for each policy).
// It removes the specified AIs/WOSs from either chunk according to dedup logic, and
// also modifies the map to keep track of the priced AI/WOSs.
func (r *Rule) handleSpecifiedModifier(
	mcChunk *ptypes.PolicySpec_ChunkSpec,
	glChunk *ptypes.PolicySpec_ChunkSpec,
	mtcChunk *ptypes.PolicySpec_ChunkSpec,
	pricedIDs map[string]bool,
	getIDsFn func(*ptypes.PolicySpec_ChunkSpec) []string,
	removeIDFn func(*ptypes.PolicySpec_ChunkSpec, string),
) {
	mcIDs := make([]string, 0)
	glIDs := make([]string, 0)
	mtcIDs := make([]string, 0)

	if mcChunk != nil {
		mcIDs = getIDsFn(mcChunk)
	}
	if glChunk != nil {
		glIDs = getIDsFn(glChunk)
	}
	if mtcChunk != nil {
		mtcIDs = getIDsFn(mtcChunk)
	}

	allIDs := make(map[string]bool)
	for _, id := range mcIDs {
		allIDs[id] = true
	}
	for _, id := range glIDs {
		allIDs[id] = true
	}
	for _, id := range mtcIDs {
		allIDs[id] = true
	}

	for id := range allIDs {
		isCurrentIDPresentForMCPolicy := slice_utils.Contains(mcIDs, id)
		isCurrentIDPresentForGLPolicy := slice_utils.Contains(glIDs, id)
		isCurrentIDPresentForMTCPolicy := slice_utils.Contains(mtcIDs, id)

		shouldPriceCurrentID := false
		if _, ok := pricedIDs[id]; !ok {
			shouldPriceCurrentID = true
		}

		// First we check if the ID should be removed for the current chunks.
		if shouldPriceCurrentID {
			if isCurrentIDPresentForMCPolicy {
				if isCurrentIDPresentForGLPolicy {
					removeIDFn(glChunk, id)
				}
				if isCurrentIDPresentForMTCPolicy {
					removeIDFn(mtcChunk, id)
				}
			} else if isCurrentIDPresentForGLPolicy {
				if isCurrentIDPresentForMTCPolicy {
					removeIDFn(mtcChunk, id)
				}
			}
		} else {
			if isCurrentIDPresentForMCPolicy {
				removeIDFn(mcChunk, id)
			}
			if isCurrentIDPresentForGLPolicy {
				removeIDFn(glChunk, id)
			}
			if isCurrentIDPresentForMTCPolicy {
				removeIDFn(mtcChunk, id)
			}
		}

		// Then we update the map to keep track of the priced AI/WOSs.
		if !isCurrentIDPresentForMCPolicy && !isCurrentIDPresentForGLPolicy && !isCurrentIDPresentForMTCPolicy {
			delete(pricedIDs, id)
		} else {
			pricedIDs[id] = true
		}
	}

	// We also need to update the map based on IDs that were priced before,
	// but are not present in the current chunks.
	for id := range pricedIDs {
		if _, ok := allIDs[id]; !ok {
			delete(pricedIDs, id)
		}
	}
}
