package common

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

type specifiedParty interface {
	GetId() string
}

// BuildPolicyChunksMap builds a map where the keys
// are chunk IDs and values are the actual chunks.
//
// We do this because it helps us to quickly get
// a policy chunk for a given chunk ID (instead of
// searching within a slice).
func BuildPolicyChunksMap(
	policySpecs []*ptypes.PolicySpec,
	policyName ptypes.PolicyName,
) (map[string]*ptypes.PolicySpec_ChunkSpec, error) {
	filteredPolicySpecs := filterPolicySpecs(policySpecs, policyName)

	chunksMap := make(map[string]*ptypes.PolicySpec_ChunkSpec)
	for _, policySpec := range filteredPolicySpecs {
		for _, chunk := range policySpec.ChunkSpecs {
			chunkID := chunk.ChunkId
			if _, ok := chunksMap[chunkID]; ok {
				return nil, errors.Newf(
					"duplicate chunk ID %s found for policy %s",
					chunkID,
					policySpec.PolicyNumber,
				)
			}
			chunksMap[chunk.ChunkId] = chunk
		}
	}
	return chunksMap, nil
}

// filterPolicySpecs filters policy specs based on
// policy name.
//
// Note that a policy name can be repeated across different
// bundle chunks, but within a bundle chunk, policy names
// must be unique.
//
// The former can happen, for example, in the case of policies
// that have a shorter duration than the bundle overall duration.
//
// In other words, you could have something like:
// - Chunk 1 (3 months):
//   - MTC Policy 1
//   - GL Policy 1
//
// - Chunk 2 (3 months):
//   - MTC Policy 1
//   - GL Policy 2
//
// In that example, the bundle and the MTC policy have a
// duration of 6 months, but there are two GL policies
// with a duration of 3 months each.
func filterPolicySpecs(
	policySpecs []*ptypes.PolicySpec,
	policyName ptypes.PolicyName,
) []*ptypes.PolicySpec {
	return slice_utils.Filter(policySpecs, func(policySpec *ptypes.PolicySpec) bool {
		return policyName == policySpec.PolicyName
	})
}

func GetSpecifiedRegularAIIDs(chunk *ptypes.PolicySpec_ChunkSpec) []string {
	if chunk == nil {
		return nil
	}
	return getIDs(
		slice_utils.Map(
			chunk.SpecifiedRegularAdditionalInsureds,
			func(ai *ptypes.SpecifiedRegularAdditionalInsured) specifiedParty {
				return ai
			},
		),
	)
}

func GetSpecifiedPNCAIIDs(chunk *ptypes.PolicySpec_ChunkSpec) []string {
	if chunk == nil {
		return nil
	}
	return getIDs(
		slice_utils.Map(
			chunk.SpecifiedPrimaryAndNonContributoryAdditionalInsureds,
			func(ai *ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured) specifiedParty {
				return ai
			},
		),
	)
}

func GetSpecifiedWOSIDs(chunk *ptypes.PolicySpec_ChunkSpec) []string {
	if chunk == nil {
		return nil
	}
	return getIDs(
		slice_utils.Map(
			chunk.SpecifiedThirdPartiesWithWOS,
			func(tp *ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation) specifiedParty {
				return tp
			},
		),
	)
}

func getIDs(parties []specifiedParty) []string {
	return slice_utils.Map(
		parties,
		func(party specifiedParty) string {
			return party.GetId()
		},
	)
}

func RemoveSpecifiedRegularAIID(
	chunk *ptypes.PolicySpec_ChunkSpec,
	targetID string,
) {
	if chunk == nil {
		return
	}

	chunk.SpecifiedRegularAdditionalInsureds = slice_utils.Filter(
		chunk.SpecifiedRegularAdditionalInsureds,
		func(insured *ptypes.SpecifiedRegularAdditionalInsured) bool {
			return insured.Id != targetID
		},
	)
}

func RemoveSpecifiedPNCAIID(
	chunk *ptypes.PolicySpec_ChunkSpec,
	targetID string,
) {
	if chunk == nil {
		return
	}

	chunk.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = slice_utils.Filter(
		chunk.SpecifiedPrimaryAndNonContributoryAdditionalInsureds,
		func(insured *ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured) bool {
			return insured.Id != targetID
		},
	)
}

func RemoveSpecifiedWOSID(
	chunk *ptypes.PolicySpec_ChunkSpec,
	targetID string,
) {
	if chunk == nil {
		return
	}

	chunk.SpecifiedThirdPartiesWithWOS = slice_utils.Filter(
		chunk.SpecifiedThirdPartiesWithWOS,
		func(insured *ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation) bool {
			return insured.Id != targetID
		},
	)
}
