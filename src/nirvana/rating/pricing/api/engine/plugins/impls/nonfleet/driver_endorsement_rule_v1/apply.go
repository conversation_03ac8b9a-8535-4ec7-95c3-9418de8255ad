package driver_endorsement_rule_v1

import (
	"context"
	"math"

	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/proto"

	"nirvanatech.com/nirvana/common-go/log"
	plugins_common "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

const maxAllowedPremiumVariationForDriversChanges = 0.2 // 20%

// apply assumes that input data is valid (e.g., the number of
// chunks in ChunksSpecs is the same as the number of chunks in
// the PolicySpec and the BundleSpec).
func (r *Rule) apply(
	ctx context.Context,
	input *plugins_common.PluginChainInput,
) (plugins_common.PluginChainOutput, error) {
	if input == nil {
		return nil, errors.New("input can't be nil")
	}

	if r.nextPluginApplyFn == nil {
		return nil, errors.New("nextPluginApplyFn can't be nil")
	}

	request, err := input.GetPriceRequest()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get request")
	}

	if request.BundleSpec == nil {
		return nil, errors.Newf("bundleSpec can't be nil")
	}

	policySpec, err := input.GetPolicySpec()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get policy spec")
	}

	policyNumber := policySpec.GetPolicyNumber()

	policyChunkSpecs := policySpec.ChunkSpecs
	bundleChunkSpecs := request.BundleSpec.ChunkSpecs

	// We sort all slice-like fields inside the bundle
	// and policy chunk specs so that we can then compare
	// them accurately.
	for _, chunkSpec := range policyChunkSpecs {
		chunkSpec.Sort()
	}
	for _, chunkSpec := range bundleChunkSpecs {
		chunkSpec.Sort()
	}

	outputs := make(plugins_common.PluginChainOutput)

	var lastRatedDrivers []*ptypes.NonFleet_Driver

	// We create a map to look up a bundle chunk spec
	// based on its ID efficiently.
	bundleChunkSpecsMap := make(map[string]*ptypes.BundleSpec_ChunkSpec)
	for _, bc := range bundleChunkSpecs {
		bundleChunkSpecsMap[bc.ChunkId] = bc
	}

	for currPolicyChunkSpecIdx := range policyChunkSpecs {
		var err error
		var chunkOutput *plugins_common.ChunkOutput

		currPolicyChunkSpec := policyChunkSpecs[currPolicyChunkSpecIdx]
		currChunkID := currPolicyChunkSpec.ChunkId

		if currPolicyChunkSpec.GetNonFleetPolicyChunkSpecData() == nil {
			return nil, errors.Newf(
				"NF policy chunk spec data not found (chunkID=%s, policyNumber=%s)",
				currChunkID,
				policyNumber,
			)
		}

		currBundleChunkSpec, ok := bundleChunkSpecsMap[currChunkID]
		if !ok {
			return nil, errors.Newf(
				"bundle chunk spec not found (chunkID=%s, policyNumber=%s)",
				currChunkID,
				policyNumber,
			)
		}

		if currBundleChunkSpec.GetNonFleetBundleChunkSpecData() == nil {
			return nil, errors.Newf(
				"NF bundle chunk spec data not found (chunkID=%s, policyNumber=%s)",
				currChunkID,
				policyNumber,
			)
		}

		currDrivers := currPolicyChunkSpec.GetNonFleetPolicyChunkSpecData().Drivers

		prevPolicyChunkIdx := currPolicyChunkSpecIdx - 1
		var prevPolicyChunkSpec *ptypes.PolicySpec_ChunkSpec
		var prevChunkID string
		var prevBundleChunkSpec *ptypes.BundleSpec_ChunkSpec

		// At this point, all three "prev" variables should be set if currPolicyChunkSpecIdx > 0,
		// assuming that the input data is correct.
		var isFirstChunk bool
		if currPolicyChunkSpecIdx == 0 {
			isFirstChunk = true
		} else {
			prevPolicyChunkSpec = policyChunkSpecs[prevPolicyChunkIdx]
			prevChunkID = prevPolicyChunkSpec.ChunkId

			prevBundleChunkSpec = bundleChunkSpecsMap[prevChunkID]
			if !(prevBundleChunkSpec.Dates.End.AsTime().Equal(currBundleChunkSpec.Dates.Start.AsTime())) {
				isFirstChunk = true
			}
		}

		if isFirstChunk {
			chunkOutput, err = r.getOutputForChunk(ctx, currChunkID, input)
			if err != nil {
				return nil, errors.Wrapf(
					err,
					"failed to get output (chunkID=%s, policyNumber=%s)",
					currChunkID,
					policyNumber,
				)
			}

			lastRatedDrivers = currDrivers
		} else if r.driversHaveChanged(prevPolicyChunkSpec, currPolicyChunkSpec) {
			currPolicyChunkSpec.GetNonFleetPolicyChunkSpecData().Drivers = lastRatedDrivers

			baseChunkOutput, err := r.getOutputForChunk(ctx, currChunkID, input)
			if err != nil {
				return nil, errors.Wrapf(
					err,
					"failed to get output for last rated drivers (chunkID=%s, policyNumber=%s)",
					currChunkID,
					policyNumber,
				)
			}

			baseSubTotalPremium, err := r.getSubTotalPremium(baseChunkOutput)
			if err != nil {
				return nil, err
			}

			currPolicyChunkSpec.GetNonFleetPolicyChunkSpecData().Drivers = currDrivers

			proposedChunkOutput, err := r.getOutputForChunk(ctx, currChunkID, input)
			if err != nil {
				return nil, errors.Wrapf(
					err,
					"failed to get output for new drivers (chunkID=%s, policyNumber=%s)",
					currChunkID,
					policyNumber,
				)
			}

			proposedSubTotalPremium, err := r.getSubTotalPremium(proposedChunkOutput)
			if err != nil {
				return nil, err
			}

			var ruleApplied bool

			if math.Abs((proposedSubTotalPremium/baseSubTotalPremium)-1) > maxAllowedPremiumVariationForDriversChanges {
				chunkOutput = proposedChunkOutput
				lastRatedDrivers = currDrivers
				ruleApplied = true
			} else {
				chunkOutput = baseChunkOutput
			}

			err = r.setPluginMetadata(chunkOutput, baseSubTotalPremium, proposedSubTotalPremium)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to set plugin metadata (chunkID=%s)", currChunkID)
			}

			log.Info(
				log.ContextWithFields(
					ctx,
					log.String("policyNumber", policyNumber),
					log.Int("currPolicyChunkSpecIdx", currPolicyChunkSpecIdx),
					log.String("currChunkID", currChunkID),
					log.Float64("baseSubTotalPremium", baseSubTotalPremium),
					log.Float64("proposedSubTotalPremium", proposedSubTotalPremium),
					log.Any("lastRatedDrivers", lastRatedDrivers),
					log.Bool("ruleApplied", ruleApplied),
				),
				"Chunk with driver related changes detected",
			)
		} else if r.otherInputsHaveChanged(prevBundleChunkSpec, currBundleChunkSpec, prevPolicyChunkSpec, currPolicyChunkSpec) {
			currPolicyChunkSpec.GetNonFleetPolicyChunkSpecData().Drivers = lastRatedDrivers

			chunkOutput, err = r.getOutputForChunk(ctx, currChunkID, input)
			if err != nil {
				return nil, errors.Wrapf(
					err,
					"failed to get output for non driver related changes (chunkID=%s, policyNumber=%s)",
					currChunkID,
					policyNumber,
				)
			}

			// We restore the previous drivers.
			currPolicyChunkSpec.GetNonFleetPolicyChunkSpecData().Drivers = currDrivers

			subTotalPremium, err := r.getSubTotalPremium(chunkOutput)
			if err != nil {
				return nil, err
			}

			log.Info(
				log.ContextWithFields(
					ctx,
					log.String("policyNumber", policyNumber),
					log.Int("currPolicyChunkSpecIdx", currPolicyChunkSpecIdx),
					log.String("currChunkID", currChunkID),
					log.Float64("subTotalPremium", subTotalPremium),
					log.Any("lastRatedDrivers", lastRatedDrivers),
				),
				"Chunk with non driver related changes detected (and no driver related changes)",
			)
		} else {
			// Here we append the same output as the
			// one obtained in the previous iteration.
			chunkOutput, err = outputs[prevChunkID].Copy()
			if err != nil {
				return nil, errors.Wrapf(
					err,
					"failed to copy chunk output for chunkID %s (policyNumber=%s)",
					prevChunkID,
					policyNumber,
				)
			}

			subTotalPremium, err := r.getSubTotalPremium(chunkOutput)
			if err != nil {
				return nil, err
			}

			log.Info(
				log.ContextWithFields(
					ctx,
					log.String("policyNumber", policyNumber),
					log.Int("currPolicyChunkSpecIdx", currPolicyChunkSpecIdx),
					log.String("currChunkID", currChunkID),
					log.Float64("subTotalPremium", subTotalPremium),
					log.Any("lastRatedDrivers", lastRatedDrivers),
				),
				"Chunk without changes detected",
			)
		}

		outputs[currChunkID] = chunkOutput
	}

	return outputs, nil
}

func (r *Rule) driversHaveChanged(prevChunk, currChunk *ptypes.PolicySpec_ChunkSpec) bool {
	prevDrivers := prevChunk.GetNonFleetPolicyChunkSpecData().Drivers
	currDrivers := currChunk.GetNonFleetPolicyChunkSpecData().Drivers

	if len(prevDrivers) != len(currDrivers) {
		return true
	}

	for i := range currDrivers {
		if !proto.Equal(currDrivers[i], prevDrivers[i]) {
			return true
		}
	}

	return false
}

func (r *Rule) otherInputsHaveChanged(
	prevBundleChunk, currBundleChunk *ptypes.BundleSpec_ChunkSpec,
	prevPolicyChunk, currPolicyChunk *ptypes.PolicySpec_ChunkSpec,
) bool {
	// We set chunk ID, chunk dates, drivers and artifact config
	// to nil/empty, to avoid comparing them.
	currBundleChunkID := currBundleChunk.ChunkId
	prevBundleChunkID := prevBundleChunk.ChunkId
	currBundleChunk.ChunkId = ""
	prevBundleChunk.ChunkId = ""

	currPolicyChunkID := currPolicyChunk.ChunkId
	prevPolicyChunkID := prevPolicyChunk.ChunkId
	currPolicyChunk.ChunkId = ""
	prevPolicyChunk.ChunkId = ""

	currBundleChunkDates := currBundleChunk.Dates
	prevBundleChunkDates := prevBundleChunk.Dates
	currBundleChunk.Dates = nil
	prevBundleChunk.Dates = nil

	currDrivers := currPolicyChunk.GetNonFleetPolicyChunkSpecData().Drivers
	prevDrivers := prevPolicyChunk.GetNonFleetPolicyChunkSpecData().Drivers
	currPolicyChunk.GetNonFleetPolicyChunkSpecData().Drivers = nil
	prevPolicyChunk.GetNonFleetPolicyChunkSpecData().Drivers = nil

	currArtifactConfig := currPolicyChunk.ArtifactConfig
	prevArtifactConfig := prevPolicyChunk.ArtifactConfig
	currPolicyChunk.ArtifactConfig = nil
	prevPolicyChunk.ArtifactConfig = nil

	retval := !proto.Equal(currPolicyChunk, prevPolicyChunk) || !proto.Equal(currBundleChunk, prevBundleChunk)

	// And then we restore the changed fields.
	currBundleChunk.ChunkId = currBundleChunkID
	prevBundleChunk.ChunkId = prevBundleChunkID

	currPolicyChunk.ChunkId = currPolicyChunkID
	prevPolicyChunk.ChunkId = prevPolicyChunkID

	currBundleChunk.Dates = currBundleChunkDates
	prevBundleChunk.Dates = prevBundleChunkDates

	currPolicyChunk.GetNonFleetPolicyChunkSpecData().Drivers = currDrivers
	prevPolicyChunk.GetNonFleetPolicyChunkSpecData().Drivers = prevDrivers

	currPolicyChunk.ArtifactConfig = currArtifactConfig
	prevPolicyChunk.ArtifactConfig = prevArtifactConfig

	return retval
}

func (r *Rule) getOutputForChunk(
	ctx context.Context,
	chunkID string,
	input *plugins_common.PluginChainInput,
) (*plugins_common.ChunkOutput, error) {
	// Note: we refetch the policy spec instance, because this function is
	// responsible to modify it before calling RateML. It wouldn't be correct
	// for this function to assume that the policy spec received as an input
	// is the right spec to modify. That would leak the responsibility that
	// this function has, so we do it this way (albeit it means duplicating
	// a bit of code and doing something twice).
	policySpec, err := input.GetPolicySpec()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get policy spec")
	}

	allChunks := policySpec.ChunkSpecs

	currChunk := ptypes.FindChunkSpec(allChunks, chunkID)
	if currChunk == nil {
		return nil, errors.Newf("chunk not found for chunkID %s", chunkID)
	}

	// We pass the "next plugin" only one policy chunk (the
	// one that we are currently processing). We do this so
	// that the "next plugin" only calculates the result for
	// the chunk we need instead of calculating results for
	// all chunks, when we only care about one of them.
	//
	// This assumes that this rule/plugin is the last one in
	// the chain that, in order to price chunk i-th, needs
	// to be aware of other chunks (e.g., chunk i-1-th).
	policySpec.ChunkSpecs = []*ptypes.PolicySpec_ChunkSpec{currChunk}

	pluginChainOutput, err := r.nextPluginApplyFn(ctx, input)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to call next plugin for chunk %s", chunkID)
	}

	// We restore the original chunks before returning.
	policySpec.ChunkSpecs = allChunks

	return pluginChainOutput[chunkID], err
}

// getSubTotalPremium retrieves the sub-total premium from the chunk output,
// which corresponds to the premium of all charges that are not surcharges or
// fee charges.
func (r *Rule) getSubTotalPremium(chunkOutput *plugins_common.ChunkOutput) (float64, error) {
	if chunkOutput == nil {
		return 0, errors.New("chunk output can't be nil")
	}

	var subTotalPremium decimal.Decimal
	for _, ch := range chunkOutput.Charges {
		if ch.IsFullyEarnedCharge() || ch.IsSurcharge() {
			continue
		}

		if !ch.HasAmountBasedBillingDetails() {
			return 0, errors.Newf("charge does not have amount based billing details (charge=%+v)", ch)
		}

		amount, err := ch.Calculate(nil)
		if err != nil {
			return 0, errors.Wrapf(err, "failed to calculate charge amount (charge=%+v)", ch)
		}

		subTotalPremium = subTotalPremium.Add(amount)
	}

	premiumAsFloat, _ := subTotalPremium.Float64()

	return premiumAsFloat, nil
}

func (r *Rule) setPluginMetadata(
	chunkOutput *plugins_common.ChunkOutput,
	baseSubTotalPremium float64,
	proposedSubTotalPremium float64,
) error {
	if chunkOutput == nil {
		return errors.New("chunk output can't be nil")
	}

	if chunkOutput.Metadata == nil {
		chunkOutput.Metadata = &ptypes.ChunkOutput_Metadata{
			PluginsMetadata: &ptypes.PluginsMetadata{},
		}
	}

	if chunkOutput.Metadata.PluginsMetadata == nil {
		chunkOutput.Metadata.PluginsMetadata = &ptypes.PluginsMetadata{}
	}

	chunkOutput.Metadata.PluginsMetadata.DriverEndorsementRuleV1Metadata = &ptypes.DriverEndorsementRuleV1Metadata{
		Threshold:               maxAllowedPremiumVariationForDriversChanges,
		BaseSubTotalPremium:     baseSubTotalPremium,
		ProposedSubTotalPremium: proposedSubTotalPremium,
	}

	return nil
}
