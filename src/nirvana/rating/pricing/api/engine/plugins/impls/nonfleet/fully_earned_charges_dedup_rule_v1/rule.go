package fully_earned_charges_dedup_rule_v1

import (
	"context"

	"go.uber.org/fx"

	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
)

type Deps struct {
	fx.In
}

// The Rule struct implements the logic regarding the
// deduplication of fully earned charges. There are essentially
// two subrules to this:
//  1. If multiple consecutive chunks contain the same fully earned
//     charge, we should only keep the first one of them.
//     This is done because fully earned charges are not prorated,
//     like other charges.
//  2. For a given chunk, if a fully earned charge is included in
//     multiple policies (e.g., Motor Carrier (MC) policy,
//     General Liability (GL) policy, Motor Truck Cargo (MTC)
//     policy, we should only charge it once.
//
// More details of the rule can be found in the tests.
//
// Note #1: as of 16/05/2025, we have six different types of
// fully earned charges: blanket/specific x AI/AI-PNC/WOS. We apply
// the dedup algorithm to each type separately/individually.
//
// Note #2: the MC policy is also known as the AL+APD policy.
//
// Note #3: we assume that all "MC-like" policies should be
// treated as part of one group, independent of the carrier.
// And the same is done for all "GL-like" or "MTC-like" policies.
// This means, among other things, that a customer can't have
// multiple CA/GL/MTC policies at the same time (i.e., same
// chunk) with different carriers.
type Rule struct {
	deps Deps

	nextPluginApplyFn common.PluginApplyFn
}

var _ common.PluginI = (*Rule)(nil)

func (r *Rule) Apply(
	ctx context.Context,
	input *common.PluginChainInput,
) (common.PluginChainOutput, error) {
	return r.apply(ctx, input)
}

func (r *Rule) SetNextPluginApplyFn(nextPluginApplyFn common.PluginApplyFn) error {
	r.nextPluginApplyFn = nextPluginApplyFn
	return nil
}

func NewRule(deps Deps) *Rule {
	return &Rule{deps: deps}
}
