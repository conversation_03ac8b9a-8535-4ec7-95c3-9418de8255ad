package common

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

var allPolicyNames = []ptypes.PolicyName{
	ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
	ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
	ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
}

func Test_BuildPolicyChunksMap(t *testing.T) {
	for i, policyName := range allPolicyNames {
		t.Run(fmt.Sprintf("With duplicated chunk ID across same policy (%s)", policyName), func(tt *testing.T) {
			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
							{
								ChunkId: "chunk1",
							},
						},
					},
				},
			}
			output, err := BuildPolicyChunksMap(req.PolicySpecs, policyName)
			require.Error(tt, err)
			require.Regexp(tt, "duplicate chunk ID", err.Error())
			require.Nil(tt, output)
		})

		t.Run(fmt.Sprintf("With duplicated chunk ID across policies with same name (%s)", policyName), func(tt *testing.T) {
			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
						},
					},
					{
						PolicyNumber: "policy2",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
						},
					},
				},
			}
			output, err := BuildPolicyChunksMap(req.PolicySpecs, policyName)
			require.Error(tt, err)
			require.Regexp(tt, "duplicate chunk ID", err.Error())
			require.Nil(tt, output)
		})

		t.Run(fmt.Sprintf("With duplicated chunk ID across policies with different names"), func(tt *testing.T) {
			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
						},
					},
					{
						PolicyNumber: "policy2",
						PolicyName:   ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
						},
					},
					{
						PolicyNumber: "policy3",
						PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
						},
					},
				},
			}

			policySpecs := req.GetPolicySpecs()
			mockOutput := map[string]*ptypes.PolicySpec_ChunkSpec{
				"chunk1": policySpecs[i].ChunkSpecs[0],
			}
			output, err := BuildPolicyChunksMap(req.PolicySpecs, policyName)
			require.NoError(tt, err)
			require.Equal(tt, mockOutput, output)
		})

		t.Run(fmt.Sprintf("With no duplicated chunk ID"), func(tt *testing.T) {
			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
						},
						{
							ChunkId: "chunk2",
						},
						{
							ChunkId: "chunk3",
						},
						{
							ChunkId: "chunk4",
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
							{
								ChunkId: "chunk3",
							},
						},
					},
					{
						PolicyNumber: "policy2",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk2",
							},
							{
								ChunkId: "chunk4",
							},
						},
					},
				},
			}

			policySpecs := req.GetPolicySpecs()
			mockOutput := map[string]*ptypes.PolicySpec_ChunkSpec{
				"chunk1": policySpecs[0].ChunkSpecs[0],
				"chunk2": policySpecs[1].ChunkSpecs[0],
				"chunk3": policySpecs[0].ChunkSpecs[1],
				"chunk4": policySpecs[1].ChunkSpecs[1],
			}
			output, err := BuildPolicyChunksMap(req.PolicySpecs, policyName)
			require.NoError(tt, err)
			require.Equal(tt, mockOutput, output)
		})
	}
}

func Test_GetSpecifiedX(t *testing.T) {
	// With nil chunk
	var chunk *ptypes.PolicySpec_ChunkSpec

	regularIDs := GetSpecifiedRegularAIIDs(chunk)
	require.Empty(t, regularIDs)

	pncIDs := GetSpecifiedPNCAIIDs(chunk)
	require.Empty(t, pncIDs)

	wosIDs := GetSpecifiedWOSIDs(chunk)
	require.Empty(t, wosIDs)

	// With nil specified X arrays.
	// We don't test the case when the arrays are empty because
	// the behavior is the same as when they are nil.
	chunk = &ptypes.PolicySpec_ChunkSpec{}

	regularIDs = GetSpecifiedRegularAIIDs(chunk)
	require.Empty(t, regularIDs)

	pncIDs = GetSpecifiedPNCAIIDs(chunk)
	require.Empty(t, pncIDs)

	wosIDs = GetSpecifiedWOSIDs(chunk)
	require.Empty(t, wosIDs)

	// With specified AIs
	chunk = &ptypes.PolicySpec_ChunkSpec{
		ChunkId: "chunk1",
		SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
			{
				Id: "AI-1",
			},
			{
				Id: "AI-3",
			},
		},
		SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
			{
				Id: "AI-2",
			},
			{
				Id: "AI-4",
			},
		},
		SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
			{
				Id: "WOS-1",
			},
			{
				Id: "WOS-2",
			},
		},
	}

	regularIDs = GetSpecifiedRegularAIIDs(chunk)
	require.ElementsMatch(t, []string{"AI-1", "AI-3"}, regularIDs)

	pncIDs = GetSpecifiedPNCAIIDs(chunk)
	require.ElementsMatch(t, []string{"AI-2", "AI-4"}, pncIDs)

	wosIDs = GetSpecifiedWOSIDs(chunk)
	require.ElementsMatch(t, []string{"WOS-1", "WOS-2"}, wosIDs)
}

func Test_RemoveSpecifiedX(t *testing.T) {
	// With nil chunk
	var chunk *ptypes.PolicySpec_ChunkSpec
	RemoveSpecifiedRegularAIID(chunk, "some-id")
	RemoveSpecifiedPNCAIID(chunk, "some-id")
	RemoveSpecifiedWOSID(chunk, "some-id")

	// With nil specified X arrays.
	// We test that it doesn't panic.
	// We don't test the case when the arrays are empty because
	// the behavior is the same as when they are nil.
	chunk = &ptypes.PolicySpec_ChunkSpec{}
	RemoveSpecifiedRegularAIID(chunk, "some-id")
	RemoveSpecifiedPNCAIID(chunk, "some-id")
	RemoveSpecifiedWOSID(chunk, "some-id")

	// With specified X to remove.
	chunk = &ptypes.PolicySpec_ChunkSpec{
		ChunkId: "chunk1",
		SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
			{
				Id: "ID-1",
			},
		},
		SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
			{
				Id: "ID-1",
			},
		},
		SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
			{
				Id: "ID-1",
			},
		},
	}

	require.NotEmpty(t, chunk.SpecifiedRegularAdditionalInsureds)
	RemoveSpecifiedRegularAIID(chunk, "ID-1")
	require.Empty(t, chunk.SpecifiedRegularAdditionalInsureds)

	require.NotEmpty(t, chunk.SpecifiedPrimaryAndNonContributoryAdditionalInsureds)
	RemoveSpecifiedPNCAIID(chunk, "ID-1")
	require.Empty(t, chunk.SpecifiedPrimaryAndNonContributoryAdditionalInsureds)

	require.NotEmpty(t, chunk.SpecifiedThirdPartiesWithWOS)
	RemoveSpecifiedWOSID(chunk, "ID-1")
	require.Empty(t, chunk.SpecifiedThirdPartiesWithWOS)
}
