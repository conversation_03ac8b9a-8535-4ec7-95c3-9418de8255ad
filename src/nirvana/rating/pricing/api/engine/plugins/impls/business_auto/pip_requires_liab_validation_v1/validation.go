package pip_requires_liab_validation_v1

import (
	"context"

	"github.com/cockroachdb/errors"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/business_auto"
)

type Deps struct {
	fx.In
}

type Validation struct {
	deps Deps

	nextPluginApplyFn common.PluginApplyFn
}

var _ common.PluginI = (*Validation)(nil)

func (v *Validation) Apply(
	ctx context.Context,
	input *common.PluginChainInput,
) (common.PluginChainOutput, error) {
	if v.nextPluginApplyFn == nil {
		return nil, errors.New("nextPluginApplyFn can't be nil")
	}

	policySpec, err := input.GetPolicySpec()
	if err != nil {
		return nil, err
	}

	for _, policyChunkSpec := range policySpec.ChunkSpecs {
		pipIsPresent := policyChunkSpec.IsSubCoverageGroupPresent(business_auto.PIPSubCoverageGroup)
		if !pipIsPresent {
			continue
		}

		if !policyChunkSpec.IsSubCoverageGroupPresent(business_auto.LiabSubCoverageGroup) {
			return nil, errors.Newf(
				"PIP requires Liab (chunkID=%s)",
				policyChunkSpec.ChunkId,
			)
		}
	}

	return v.nextPluginApplyFn(ctx, input)
}

func (v *Validation) SetNextPluginApplyFn(nextPluginApplyFn common.PluginApplyFn) error {
	v.nextPluginApplyFn = nextPluginApplyFn
	return nil
}

func NewValidation(deps Deps) *Validation {
	return &Validation{deps: deps}
}
