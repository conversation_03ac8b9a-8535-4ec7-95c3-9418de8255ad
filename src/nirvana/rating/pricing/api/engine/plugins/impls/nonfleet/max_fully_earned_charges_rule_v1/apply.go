package max_fully_earned_charges_rule_v1

import (
	"context"
	"slices"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	nf_common "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

const maxFullyEarnedChargeCount = 5

// The apply method assumes that the data was already
// validated.
//
// For instance, it assumes that AIs and TP with WOS
// are unique within a policy chunk.
//
// Note: this rule assumes that AI types are valid
// (no unspecified value). We don't validate this
// here, because it is assumed that the engine already
// validated it.
func (r *Rule) apply(
	ctx context.Context,
	p *common.PluginChainInput,
) (common.PluginChainOutput, error) {
	if r.nextPluginApplyFn == nil {
		return nil, errors.New("nextPluginApplyFn can't be nil")
	}

	req, err := p.GetPriceRequest()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get price request from plugin chain input")
	}

	bundleChunks := req.BundleSpec.ChunkSpecs

	policySpecs := req.PolicySpecs

	mcPolicyChunksMap, err := nf_common.BuildPolicyChunksMap(
		policySpecs,
		ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
	)
	if err != nil {
		return nil, err
	}

	glPolicyChunksMap, err := nf_common.BuildPolicyChunksMap(
		policySpecs,
		ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
	)
	if err != nil {
		return nil, err
	}

	mtcPolicyChunksMap, err := nf_common.BuildPolicyChunksMap(
		policySpecs,
		ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
	)
	if err != nil {
		return nil, err
	}

	seenRegularAI := make(map[string]struct{})
	seenPNCAI := make(map[string]struct{})
	seenWOS := make(map[string]struct{})

	for i, bundleChunk := range bundleChunks {
		if bundleChunk == nil {
			return nil, errors.Newf("nil bundle chunk found at index %d", i)
		}

		chunkID := bundleChunk.ChunkId
		policyChunks := []*ptypes.PolicySpec_ChunkSpec{
			mcPolicyChunksMap[chunkID],
			glPolicyChunksMap[chunkID],
			mtcPolicyChunksMap[chunkID],
		}

		for _, pChunk := range policyChunks {
			if pChunk == nil {
				continue
			}

			r.handleSpecifiedModifier(
				pChunk,
				seenRegularAI,
				r.getSpecifiedRegularAIIDs,
				nf_common.RemoveSpecifiedRegularAIID,
			)
			r.handleSpecifiedModifier(
				pChunk,
				seenPNCAI,
				r.getSpecifiedPNCAIIDs,
				nf_common.RemoveSpecifiedPNCAIID,
			)
			r.handleSpecifiedModifier(
				pChunk,
				seenWOS,
				r.getSpecifiedWOSIDs,
				nf_common.RemoveSpecifiedWOSID,
			)
		}
	}

	return r.nextPluginApplyFn(ctx, p)
}

func (r *Rule) handleSpecifiedModifier(
	chunk *ptypes.PolicySpec_ChunkSpec,
	seenIDs map[string]struct{},
	getIDsFn func(*ptypes.PolicySpec_ChunkSpec) []string,
	removeIDFn func(*ptypes.PolicySpec_ChunkSpec, string),
) {
	for _, id := range getIDsFn(chunk) {
		if len(seenIDs) < maxFullyEarnedChargeCount {
			seenIDs[id] = struct{}{}
			continue
		}
		removeIDFn(chunk, id)
	}
}

func (r *Rule) getSpecifiedRegularAIIDs(chunk *ptypes.PolicySpec_ChunkSpec) []string {
	return slices.Sorted(slices.Values(nf_common.GetSpecifiedRegularAIIDs(chunk)))
}

func (r *Rule) getSpecifiedPNCAIIDs(chunk *ptypes.PolicySpec_ChunkSpec) []string {
	return slices.Sorted(slices.Values(nf_common.GetSpecifiedPNCAIIDs(chunk)))
}

func (r *Rule) getSpecifiedWOSIDs(chunk *ptypes.PolicySpec_ChunkSpec) []string {
	return slices.Sorted(slices.Values(nf_common.GetSpecifiedWOSIDs(chunk)))
}
