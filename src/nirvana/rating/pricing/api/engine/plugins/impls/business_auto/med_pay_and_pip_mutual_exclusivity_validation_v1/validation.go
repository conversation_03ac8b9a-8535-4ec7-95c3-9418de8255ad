package med_pay_and_pip_mutual_exclusivity_validation_v1

import (
	"context"

	"github.com/cockroachdb/errors"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/business_auto"
)

type Deps struct {
	fx.In
}

// The Validation checks that there are no policy chunk specs
// that have both MedPay and PIP.
//
// This is only validated in some states, so this plugin is not
// declared for all models.
type Validation struct {
	deps Deps

	nextPluginApplyFn common.PluginApplyFn
}

var _ common.PluginI = (*Validation)(nil)

func (v *Validation) Apply(
	ctx context.Context,
	input *common.PluginChainInput,
) (common.PluginChainOutput, error) {
	if v.nextPluginApplyFn == nil {
		return nil, errors.New("nextPluginApplyFn can't be nil")
	}

	policySpec, err := input.GetPolicySpec()
	if err != nil {
		return nil, err
	}

	for _, policyChunkSpec := range policySpec.ChunkSpecs {
		medPayIsPresent := policyChunkSpec.IsSubCoverageGroupPresent(business_auto.MedPaySubCoverageGroup)
		pipIsPresent := policyChunkSpec.IsSubCoverageGroupPresent(business_auto.PIPSubCoverageGroup)

		if medPayIsPresent && pipIsPresent {
			return nil, errors.Newf(
				"MedPay and PIP are mutually exclusive (chunkID=%s)",
				policyChunkSpec.ChunkId,
			)
		}
	}

	return v.nextPluginApplyFn(ctx, input)
}

func (v *Validation) SetNextPluginApplyFn(nextPluginApplyFn common.PluginApplyFn) error {
	v.nextPluginApplyFn = nextPluginApplyFn
	return nil
}

func NewValidation(deps Deps) *Validation {
	return &Validation{deps: deps}
}
