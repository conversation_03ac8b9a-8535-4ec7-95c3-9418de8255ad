package charges_proration_v1

import (
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"google.golang.org/protobuf/types/known/timestamppb"

	common_proto "nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

type chargesProraterTestEnv struct {
	fx.In

	Prorater ChargesProraterI
}

type chargesProraterTestSuite struct {
	suite.Suite

	env   chargesProraterTestEnv
	fxApp *fxtest.App
}

func TestChargesProrater(t *testing.T) {
	suite.Run(t, new(chargesProraterTestSuite))
}

func (s *chargesProraterTestSuite) SetupSuite() {
	s.fxApp = testloader.RequireStart(s.T(), &s.env)
}

func (s *chargesProraterTestSuite) TearDownSuite() {
	s.fxApp.RequireStop()
}

func (s *chargesProraterTestSuite) Test_ProrateCharges_WhenPolicyHasZeroDuration() {
	policyDates := &common_proto.Interval{}
	chunksDates := map[string]*common_proto.Interval{}
	originalChunksCharges := map[string][]*ptypes.Charge{}

	proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
	s.Require().Error(err)
	s.Require().Regexp("policy duration is zero", err.Error())
	s.Require().Nil(proratedChunksCharges)
}

func (s *chargesProraterTestSuite) Test_ProrateCharges_WhenChunkDurationIsMissing() {
	policyDates := &common_proto.Interval{
		Start: timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime()),
		End:   timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2025").ToTime()),
	}
	chunksDates := map[string]*common_proto.Interval{}
	originalChunksCharges := map[string][]*ptypes.Charge{
		"chunkID2": {},
	}

	proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
	s.Require().Error(err)
	s.Require().Regexp("duration not found for chunk chunkID2", err.Error())
	s.Require().Nil(proratedChunksCharges)
}

func (s *chargesProraterTestSuite) Test_ProrateCharges_WithNegativePremiumForProratableCharge() {
	policyDates := &common_proto.Interval{
		Start: timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime()),
		End:   timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2025").ToTime()),
	}

	chunkID := "chunkID"

	chunksDates := map[string]*common_proto.Interval{
		chunkID: policyDates,
	}
	originalChunksCharges := map[string][]*ptypes.Charge{
		chunkID: {
			s.newProratableBaseChargeWithChargeableSubCoverageGroup(
				chunksDates,
				chunkID,
				"-1000",
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
			),
		},
	}

	proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
	s.Require().Error(err)
	s.Require().Regexp("negative premium for charge", err.Error())
	s.Require().Nil(proratedChunksCharges)
}

// Test_ProrateCharges_WithOneChunk_WithChargesThatShouldNotBeProrated
// tests the case with charges that should not be prorated (i.e., fee
// charges and rate-based charges, as of 13/01/2025).
func (s *chargesProraterTestSuite) Test_ProrateCharges_WithOneChunk_WithChargesThatShouldNotBeProrated() {
	// Policy has 360 days
	policyStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime())
	policyEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "26/12/2024").ToTime())
	policyDates := &common_proto.Interval{
		Start: policyStartDate,
		End:   policyEndDate,
	}

	chunkID := "chunkID"

	// Chunk has only 120 days (not entire policy duration)
	chunkStartDate := policyStartDate
	chunkEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "30/04/2024").ToTime())

	chunksDates := map[string]*common_proto.Interval{
		chunkID: {
			Start: chunkStartDate,
			End:   chunkEndDate,
		},
	}

	originalChunksCharges := map[string][]*ptypes.Charge{
		chunkID: {
			s.newBlanketRegularAICharge(chunksDates, chunkID, "100"),
			s.newBlanketPrimaryAndNonContributoryAICharge(chunksDates, chunkID, "100"),
			s.newBlanketWOSCharge(chunksDates, chunkID, "100"),
			s.newSpecifiedRegularAICharge(chunksDates, chunkID, "100"),
			s.newSpecifiedPrimaryAndNonContributoryAICharge(chunksDates, chunkID, "100"),
			s.newSpecifiedWOSCharge(chunksDates, chunkID, "100"),
			s.newFeeChargeSurplusTaxSurcharge(chunksDates, chunkID, "100"),
			s.newFeeChargeStampingFeeSurcharge(chunksDates, chunkID, "100"),
			s.newMCCASurcharge(chunksDates, chunkID, "100"),
			s.newChargeWithRateBasedBillingDetails(chunksDates, chunkID, "100"),
		},
	}

	// `expectedChunksCharges` is an exact deep copy of `originalChunksCharges`
	// as no proration should have been applied.
	expectedChunksCharges := make(map[string][]*ptypes.Charge)
	for id, charges := range originalChunksCharges {
		expectedChunksCharges[id] = make([]*ptypes.Charge, len(charges))
		for i, charge := range charges {
			expectedChunksCharges[id][i] = charge.Copy()
		}
	}

	proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
	s.Require().NoError(err)
	s.Require().Equal(expectedChunksCharges, proratedChunksCharges)
}

func (s *chargesProraterTestSuite) Test_ProrateCharges_WithOneChunk_WithInexactProration() {
	// Policy has 360 days
	policyStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime())
	policyEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "26/12/2024").ToTime())
	policyDates := &common_proto.Interval{
		Start: policyStartDate,
		End:   policyEndDate,
	}

	chunkID := "chunkID"

	// Chunk has only 115 days (not entire policy duration)
	chunkStartDate := policyStartDate
	chunkEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "25/04/2024").ToTime())

	chunksDates := map[string]*common_proto.Interval{
		chunkID: {
			Start: chunkStartDate,
			End:   chunkEndDate,
		},
	}

	// We use two different chargeable sub-coverages in order to create two different groups of charges.
	chunkCharge1 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID,
		"100",
		ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
	)
	chunkCharge2 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID,
		"110",
		ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
	)
	chunkCharge3 := s.newProratableSurchargeWithChargeablePolicy(
		chunksDates,
		chunkID,
		"123.45",
	)

	originalChunksCharges := map[string][]*ptypes.Charge{
		chunkID: {
			chunkCharge1,
			chunkCharge2,
			chunkCharge3,
		},
	}

	chunkCharge1Copy := chunkCharge1.Copy()
	chunkCharge2Copy := chunkCharge2.Copy()
	chunkCharge3Copy := chunkCharge3.Copy()

	/*
		acc = 0

		chunk 1:
		  - proratedPremium := 100 * (115 / 360) = 31.94444444
		  - combinedPremium = proratedPremium + acc = 31.94444444
		  - roundedPremium = round(combinedPremium) = 32
		  - acc = combinedPremium - roundedPremium = -0.05555556
	*/
	chunkCharge1Copy.GetAmountBasedBillingDetails().Amount = "32"

	/*
		acc = 0

		chunk 1:
		  - proratedPremium := 110 * (115 / 360) = 35.13888889
		  - combinedPremium = proratedPremium + acc = 35.13888889
		  - roundedPremium = round(combinedPremium) = 35
		  - acc = combinedPremium - roundedPremium = 0.13888889
	*/
	chunkCharge2Copy.GetAmountBasedBillingDetails().Amount = "35"

	/*
		acc = 0

		chunk 1:
		  - proratedPremium := 123.45 * (115 / 360) = 39.43541667
		  - combinedPremium = proratedPremium + acc = 39.43541667
		  - roundedPremium = round(combinedPremium) = 39.44
		  - acc = combinedPremium - roundedPremium = -0.00458333
	*/
	chunkCharge3Copy.GetAmountBasedBillingDetails().Amount = "39.44"

	expectedChunksCharges := map[string][]*ptypes.Charge{
		chunkID: {
			chunkCharge1Copy,
			chunkCharge2Copy,
			chunkCharge3Copy,
		},
	}

	proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
	s.Require().NoError(err)
	s.Require().Equal(len(expectedChunksCharges), len(proratedChunksCharges))
	s.Require().Equal(len(expectedChunksCharges[chunkID]), len(proratedChunksCharges[chunkID]))
	for i, charge := range proratedChunksCharges[chunkID] {
		s.Require().EqualExportedValues(expectedChunksCharges[chunkID][i], charge)
	}
}

// Test_ProrateCharges_WithOneChunk_WhenRoundedPremiumIsZero tests
// the case when the premium after rounding is zero.
func (s *chargesProraterTestSuite) Test_ProrateCharges_WithOneChunk_WhenRoundedPremiumIsZero() {
	// Policy has 360 days
	policyStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime())
	policyEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "26/12/2024").ToTime())
	policyDates := &common_proto.Interval{
		Start: policyStartDate,
		End:   policyEndDate,
	}

	chunkID := "chunkID"

	// Chunk has only 175 days (not entire policy duration)
	chunkStartDate := policyStartDate
	chunkEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "24/06/2024").ToTime())

	chunksDates := map[string]*common_proto.Interval{
		chunkID: {
			Start: chunkStartDate,
			End:   chunkEndDate,
		},
	}

	// We use two different chargeable sub-coverages in order to create two different groups of charges.
	chunkCharge1 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID,
		"1",
		ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
	)
	chunkCharge2 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID,
		"2",
		ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
	)

	originalChunksCharges := map[string][]*ptypes.Charge{
		chunkID: {
			chunkCharge1,
			chunkCharge2,
		},
	}

	chunkCharge1Copy := chunkCharge1.Copy()
	chunkCharge2Copy := chunkCharge2.Copy()

	// Charge 2 should be kept because: 175/360 * 2 = ~0.9722 -> 1
	/*
		acc = 0

		chunk 1:
		  - proratedPremium := 1 * (175 / 360) = 0.48611111
		  - combinedPremium = proratedPremium + acc = 0.48611111
		  - roundedPremium = round(combinedPremium) = 0
		  - acc = combinedPremium - roundedPremium = 0.48611111
	*/
	chunkCharge1Copy.GetAmountBasedBillingDetails().Amount = "0"

	/*
		acc = 0

		chunk 1:
		  - proratedPremium := 2 * (175 / 360) = 0.97222222
		  - combinedPremium = proratedPremium + acc = 0.97222222
		  - roundedPremium = round(combinedPremium) = 1
		  - acc = combinedPremium - roundedPremium = -0.027777778
	*/
	chunkCharge2Copy.GetAmountBasedBillingDetails().Amount = "1"

	expectedChunksCharges := map[string][]*ptypes.Charge{
		chunkID: {
			chunkCharge2Copy,
		},
	}

	proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
	s.Require().NoError(err)
	s.Require().Equal(len(expectedChunksCharges), len(proratedChunksCharges))
	s.Require().Equal(len(expectedChunksCharges[chunkID]), len(proratedChunksCharges[chunkID]))
	s.Require().EqualExportedValues(expectedChunksCharges[chunkID][0], proratedChunksCharges[chunkID][0])
}

func (s *chargesProraterTestSuite) Test_ProrateCharges_WithMultipleChunk_WhenRoundedPremiumIsZero() {
	// Policy has 360 days
	policyStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime())
	policyEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "26/12/2024").ToTime())
	policyDates := &common_proto.Interval{
		Start: policyStartDate,
		End:   policyEndDate,
	}

	chunkID1 := "chunkID1"
	chunkID2 := "chunkID2"

	// Chunk 1 has 175 days
	chunk1StartDate := policyStartDate
	chunk1EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "24/06/2024").ToTime())

	// Chunk 2 has 175 days
	chunk2StartDate := chunk1EndDate
	chunk2EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "16/12/2024").ToTime())

	chunksDates := map[string]*common_proto.Interval{
		chunkID1: {
			Start: chunk1StartDate,
			End:   chunk1EndDate,
		},
		chunkID2: {
			Start: chunk2StartDate,
			End:   chunk2EndDate,
		},
	}

	subCov := ptypes.SubCoverageType_SubCoverageType_BodilyInjury
	chunk1Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(chunksDates, chunkID1, "1", subCov)
	chunk2Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(chunksDates, chunkID2, "1", subCov)

	originalChunksCharges := map[string][]*ptypes.Charge{
		chunkID1: {
			chunk1Charge,
		},
		chunkID2: {
			chunk2Charge,
		},
	}

	chunk1ChargeCopy := chunk1Charge.Copy()
	chunk2ChargeCopy := chunk2Charge.Copy()

	/*
		acc = 0

		chunk 1:
		  - proratedPremium := 1 * (175 / 360) = 0.48611111
		  - combinedPremium = proratedPremium + acc = 0.48611111
		  - roundedPremium = round(combinedPremium) = 0
		  - acc = combinedPremium - roundedPremium = 0.48611111

		chunk 2:
		  - proratedPremium := 1 * (175 / 360) = 0.48611111
		  - combinedPremium = proratedPremium + acc = 0.97222222
		  - roundedPremium = round(combinedPremium) = 1
		  - acc = combinedPremium - roundedPremium = -0.02777778
	*/
	chunk1ChargeCopy.GetAmountBasedBillingDetails().Amount = "0"
	chunk2ChargeCopy.GetAmountBasedBillingDetails().Amount = "1"

	expectedChunksCharges := map[string][]*ptypes.Charge{
		chunkID1: {},
		chunkID2: {
			chunk2ChargeCopy,
		},
	}

	proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
	s.Require().NoError(err)
	s.Require().Equal(len(expectedChunksCharges), len(proratedChunksCharges))
	s.Require().Equal(len(expectedChunksCharges[chunkID1]), len(proratedChunksCharges[chunkID1]))
	s.Require().Equal(len(expectedChunksCharges[chunkID2]), len(proratedChunksCharges[chunkID2]))
	s.Require().EqualExportedValues(expectedChunksCharges[chunkID2][0], proratedChunksCharges[chunkID2][0])
}

// Test_ProrateCharges_WithMultipleChunks_WithExactProration tests
// the case when prorating charge premiums results in integer values
// (i.e., dollar amounts). So that there are no accumulated cents passed on
// to the next chunk.
func (s *chargesProraterTestSuite) Test_ProrateCharges_WithMultipleChunks_WithExactProration() {
	// Policy has 360 days
	policyStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime())
	policyEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "26/12/2024").ToTime())
	policyDates := &common_proto.Interval{
		Start: policyStartDate,
		End:   policyEndDate,
	}

	chunkID1 := "chunkID1"
	chunkID2 := "chunkID2"
	chunkID3 := "chunkID3"

	// Chunk 1 has 60 days
	chunk1StartDate := policyStartDate
	chunk1EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/03/2024").ToTime())

	// Chunk 2 has 120 days
	chunk2StartDate := chunk1EndDate
	chunk2EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "29/06/2024").ToTime())

	// Chunk 3 has 180 days
	chunk3StartDate := chunk2EndDate
	chunk3EndDate := policyEndDate

	chunksDates := map[string]*common_proto.Interval{
		chunkID1: {
			Start: chunk1StartDate,
			End:   chunk1EndDate,
		},
		chunkID2: {
			Start: chunk2StartDate,
			End:   chunk2EndDate,
		},
		chunkID3: {
			Start: chunk3StartDate,
			End:   chunk3EndDate,
		},
	}

	subCov := ptypes.SubCoverageType_SubCoverageType_BodilyInjury
	chunk1Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID1,
		"360",
		subCov,
	)
	chunk2Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID2,
		"240",
		subCov,
	)
	chunk3Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID3,
		"100",
		subCov,
	)

	originalChunksCharges := map[string][]*ptypes.Charge{
		chunkID1: {
			chunk1Charge,
		},
		chunkID2: {
			chunk2Charge,
		},
		chunkID3: {
			chunk3Charge,
		},
	}

	chunk1ChargeCopy := chunk1Charge.Copy()
	chunk2ChargeCopy := chunk2Charge.Copy()
	chunk3ChargeCopy := chunk3Charge.Copy()

	/*
		acc = 0

		chunk 1:
		  - proratedPremium := 360 * (60 / 360) = 60
		  - combinedPremium = proratedPremium + acc = 60
		  - roundedPremium = round(combinedPremium) = 60
		  - acc = combinedPremium - roundedPremium = 0

		chunk 2:
		  - proratedPremium := 240 * (120 / 360) = 80
		  - combinedPremium = proratedPremium + acc = 80
		  - roundedPremium = round(combinedPremium) = 80
		  - acc = combinedPremium - roundedPremium = 0

		chunk 3:
		  - proratedPremium := 100 * (180 / 360) = 50
		  - combinedPremium = proratedPremium + acc = 50
		  - roundedPremium = round(combinedPremium) = 50
		  - acc = combinedPremium - roundedPremium = 0
	*/
	chunk1ChargeCopy.GetAmountBasedBillingDetails().Amount = "60"
	chunk2ChargeCopy.GetAmountBasedBillingDetails().Amount = "80"
	chunk3ChargeCopy.GetAmountBasedBillingDetails().Amount = "50"

	expectedChunksCharges := map[string][]*ptypes.Charge{
		chunkID1: {
			chunk1ChargeCopy,
		},
		chunkID2: {
			chunk2ChargeCopy,
		},
		chunkID3: {
			chunk3ChargeCopy,
		},
	}

	proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
	s.Require().NoError(err)
	s.Require().Equal(len(expectedChunksCharges), len(proratedChunksCharges))
	for chunkID, chunkCharges := range proratedChunksCharges {
		s.Require().Equal(len(expectedChunksCharges[chunkID]), len(chunkCharges))
		s.Require().EqualExportedValues(expectedChunksCharges[chunkID][0], chunkCharges[0])
	}
}

// Test_ProrateCharges_WithMultipleChunks_WithInexactProration_1 test
// the case when prorating results in premiums that contain decimals
// (i.e., cents or even fractions of cents). Therefore, there are
// accumulated cents that are passed on to the next chunk.
//
// We have other tests that do the same, but with a different combination of inputs.
func (s *chargesProraterTestSuite) Test_ProrateCharges_WithMultipleChunks_WithInexactProration_1() {
	subCovGroups := [][]ptypes.SubCoverageType{
		{
			ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
		},
		{
			ptypes.SubCoverageType_SubCoverageType_Collision,
			ptypes.SubCoverageType_SubCoverageType_Comprehensive,
		},
	}

	for _, subCovs := range subCovGroups {
		// Policy has 360 days
		policyStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime())
		policyEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "26/12/2024").ToTime())
		policyDates := &common_proto.Interval{
			Start: policyStartDate,
			End:   policyEndDate,
		}

		chunkID1 := "chunkID1"
		chunkID2 := "chunkID2"
		chunkID3 := "chunkID3"

		// Chunk 1 has 120 days
		chunk1StartDate := policyStartDate
		chunk1EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "30/04/2024").ToTime())

		// Chunk 2 has 120 days
		chunk2StartDate := chunk1EndDate
		chunk2EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "28/08/2024").ToTime())

		// Chunk 3 has 120 days
		chunk3StartDate := chunk2EndDate
		chunk3EndDate := policyEndDate

		chunksDates := map[string]*common_proto.Interval{
			chunkID1: {
				Start: chunk1StartDate,
				End:   chunk1EndDate,
			},
			chunkID2: {
				Start: chunk2StartDate,
				End:   chunk2EndDate,
			},
			chunkID3: {
				Start: chunk3StartDate,
				End:   chunk3EndDate,
			},
		}

		chunk1Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
			chunksDates,
			chunkID1,
			"100",
			subCovs...,
		)
		chunk2Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
			chunksDates,
			chunkID2,
			"100",
			subCovs...,
		)
		chunk3Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
			chunksDates,
			chunkID3,
			"100",
			subCovs...,
		)

		originalChunksCharges := map[string][]*ptypes.Charge{
			chunkID1: {
				chunk1Charge,
			},
			chunkID2: {
				chunk2Charge,
			},
			chunkID3: {
				chunk3Charge,
			},
		}

		chunk1ChargeCopy := chunk1Charge.Copy()
		chunk2ChargeCopy := chunk2Charge.Copy()
		chunk3ChargeCopy := chunk3Charge.Copy()

		/*
			acc = 0

			chunk 1:
			  - proratedPremium := 100 * (120 / 360) = 33.33333333
			  - combinedPremium = proratedPremium + acc = 33.33333333
			  - roundedPremium = round(combinedPremium) = 33
			  - acc = combinedPremium - roundedPremium = 0.33333333

			chunk 2:
			  - proratedPremium := 100 * (120 / 360) = 33.33333333
			  - combinedPremium = proratedPremium + acc = 33.66666666
			  - roundedPremium = round(combinedPremium) = 34
			  - acc = combinedPremium - roundedPremium = -0.33333334

			chunk 3:
			  - proratedPremium := 100 * (120 / 360) = 33.33333333
			  - combinedPremium = proratedPremium + acc = 32.99999999
			  - roundedPremium = round(combinedPremium) = 33
			  - acc = combinedPremium - roundedPremium = 0.00000001
		*/
		chunk1ChargeCopy.GetAmountBasedBillingDetails().Amount = "33"
		chunk2ChargeCopy.GetAmountBasedBillingDetails().Amount = "34"
		chunk3ChargeCopy.GetAmountBasedBillingDetails().Amount = "33"

		expectedChunksCharges := map[string][]*ptypes.Charge{
			chunkID1: {
				chunk1ChargeCopy,
			},
			chunkID2: {
				chunk2ChargeCopy,
			},
			chunkID3: {
				chunk3ChargeCopy,
			},
		}

		proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
		s.Require().NoError(err)
		s.Require().Equal(len(expectedChunksCharges), len(proratedChunksCharges))
		for chunkID, chunkCharges := range proratedChunksCharges {
			s.Require().Equal(len(expectedChunksCharges[chunkID]), len(chunkCharges))
			s.Require().EqualExportedValues(expectedChunksCharges[chunkID][0], chunkCharges[0])
		}
	}
}

// Test_ProrateCharges_WithMultipleChunks_WithInexactProration_2 is similar
// to Test_ProrateCharges_WithMultipleChunks_WithInexactProration_1, but here
// chunks have different durations (so the ratio applied is not the same for all).
func (s *chargesProraterTestSuite) Test_ProrateCharges_WithMultipleChunks_WithInexactProration_2() {
	subCovGroups := [][]ptypes.SubCoverageType{
		{
			ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
		},
		{
			ptypes.SubCoverageType_SubCoverageType_Collision,
			ptypes.SubCoverageType_SubCoverageType_Comprehensive,
		},
	}

	for _, subCovs := range subCovGroups {
		// Policy has 360 days
		policyStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime())
		policyEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "26/12/2024").ToTime())
		policyDates := &common_proto.Interval{
			Start: policyStartDate,
			End:   policyEndDate,
		}

		chunkID1 := "chunkID1"
		chunkID2 := "chunkID2"
		chunkID3 := "chunkID3"

		// Chunk 1 has 110 days
		chunk1StartDate := policyStartDate
		chunk1EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "20/04/2024").ToTime())

		// Chunk 2 has 145 days
		chunk2StartDate := chunk1EndDate
		chunk2EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "12/09/2024").ToTime())

		// Chunk 3 has 105 days
		chunk3StartDate := chunk2EndDate
		chunk3EndDate := policyEndDate

		chunksDates := map[string]*common_proto.Interval{
			chunkID1: {
				Start: chunk1StartDate,
				End:   chunk1EndDate,
			},
			chunkID2: {
				Start: chunk2StartDate,
				End:   chunk2EndDate,
			},
			chunkID3: {
				Start: chunk3StartDate,
				End:   chunk3EndDate,
			},
		}

		chunk1Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
			chunksDates,
			chunkID1,
			"100",
			subCovs...,
		)
		chunk2Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
			chunksDates,
			chunkID2,
			"100",
			subCovs...,
		)
		chunk3Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
			chunksDates,
			chunkID3,
			"100",
			subCovs...,
		)

		originalChunksCharges := map[string][]*ptypes.Charge{
			chunkID1: {
				chunk1Charge,
			},
			chunkID2: {
				chunk2Charge,
			},
			chunkID3: {
				chunk3Charge,
			},
		}

		chunk1ChargeCopy := chunk1Charge.Copy()
		chunk2ChargeCopy := chunk2Charge.Copy()
		chunk3ChargeCopy := chunk3Charge.Copy()

		/*
			acc = 0

			chunk 1:
			  - proratedPremium := 100 * (110 / 360) = 30.55555556
			  - combinedPremium = proratedPremium + acc = 30.55555556
			  - roundedPremium = round(combinedPremium) = 31
			  - acc = combinedPremium - roundedPremium = -0.44444444

			chunk 2:
			  - proratedPremium := 100 * (145 / 360) = 40.27777778
			  - combinedPremium = proratedPremium + acc = 39.83333334
			  - roundedPremium = round(combinedPremium) = 40
			  - acc = combinedPremium - roundedPremium = -0.16666666

			chunk 3:
			  - proratedPremium := 100 * (105 / 360) = 29.16666667
			  - combinedPremium = proratedPremium + acc = 29.00000001
			  - roundedPremium = round(combinedPremium) = 29
			  - acc = combinedPremium - roundedPremium = 0.00000001
		*/
		chunk1ChargeCopy.GetAmountBasedBillingDetails().Amount = "31"
		chunk2ChargeCopy.GetAmountBasedBillingDetails().Amount = "40"
		chunk3ChargeCopy.GetAmountBasedBillingDetails().Amount = "29"

		expectedChunksCharges := map[string][]*ptypes.Charge{
			chunkID1: {
				chunk1ChargeCopy,
			},
			chunkID2: {
				chunk2ChargeCopy,
			},
			chunkID3: {
				chunk3ChargeCopy,
			},
		}

		proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
		s.Require().NoError(err)
		s.Require().Equal(len(expectedChunksCharges), len(proratedChunksCharges))
		for chunkID, chunkCharges := range proratedChunksCharges {
			s.Require().Equal(len(expectedChunksCharges[chunkID]), len(chunkCharges))
			s.Require().EqualExportedValues(expectedChunksCharges[chunkID][0], chunkCharges[0])
		}
	}
}

// Test_ProrateCharges_WithMultipleChunks_WithInexactProration_3 is similar
// to Test_ProrateCharges_WithMultipleChunks_WithInexactProration_1, but here
// there are multiple groups of charges.
func (s *chargesProraterTestSuite) Test_ProrateCharges_WithMultipleChunks_WithInexactProration_3() {
	// Policy has 50 days
	policyStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime())
	policyEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "20/02/2024").ToTime())
	policyDates := &common_proto.Interval{
		Start: policyStartDate,
		End:   policyEndDate,
	}

	chunkID1 := "chunkID1"
	chunkID2 := "chunkID2"
	chunkID3 := "chunkID3"
	chunkID4 := "chunkID4"
	chunkID5 := "chunkID5"

	// Chunk 1 has 10 days
	chunk1StartDate := policyStartDate
	chunk1EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "11/01/2024").ToTime())

	// Chunk 2 has 10 days
	chunk2StartDate := chunk1EndDate
	chunk2EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "21/01/2024").ToTime())

	// Chunk 3 has 10 days
	chunk3StartDate := chunk2EndDate
	chunk3EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "31/01/2024").ToTime())

	// Chunk 4 has 10 days
	chunk4StartDate := chunk3EndDate
	chunk4EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "10/02/2024").ToTime())

	// Chunk 5 has 10 days
	chunk5StartDate := chunk4EndDate
	chunk5EndDate := policyEndDate

	chunksDates := map[string]*common_proto.Interval{
		chunkID1: {
			Start: chunk1StartDate,
			End:   chunk1EndDate,
		},
		chunkID2: {
			Start: chunk2StartDate,
			End:   chunk2EndDate,
		},
		chunkID3: {
			Start: chunk3StartDate,
			End:   chunk3EndDate,
		},
		chunkID4: {
			Start: chunk4StartDate,
			End:   chunk4EndDate,
		},
		chunkID5: {
			Start: chunk5StartDate,
			End:   chunk5EndDate,
		},
	}

	// This is the first "group" of similar charges
	subCovs1 := []ptypes.SubCoverageType{ptypes.SubCoverageType_SubCoverageType_BodilyInjury}
	chunk1Charge1 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID1,
		"51.5",
		subCovs1...,
	)
	chunk2Charge1 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID2,
		"59",
		subCovs1...,
	)
	chunk3Charge1 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID3,
		"61.5",
		subCovs1...,
	)
	chunk4Charge1 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID4,
		"73",
		subCovs1...,
	)
	chunk5Charge1 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID5,
		"67",
		subCovs1...,
	)

	// This is the second "group" of similar charges
	subCovs2 := []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
		ptypes.SubCoverageType_SubCoverageType_Collision,
	}
	chunk1Charge2 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID1,
		"53",
		subCovs2...,
	)
	chunk2Charge2 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID2,
		"53",
		subCovs2...,
	)
	chunk3Charge2 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID3,
		"102",
		subCovs2...,
	)
	chunk4Charge2 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID4,
		"76.5",
		subCovs2...,
	)
	chunk5Charge2 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID5,
		"215.5",
		subCovs2...,
	)

	// This is the third "group" of similar charges.
	chunk1Charge3 := s.newProratableSurchargeWithChargeablePolicy(
		chunksDates,
		chunkID1,
		"51.63",
	)
	chunk2Charge3 := s.newProratableSurchargeWithChargeablePolicy(
		chunksDates,
		chunkID2,
		"59.34",
	)
	chunk3Charge3 := s.newProratableSurchargeWithChargeablePolicy(
		chunksDates,
		chunkID3,
		"61.29",
	)
	chunk4Charge3 := s.newProratableSurchargeWithChargeablePolicy(
		chunksDates,
		chunkID4,
		"73.87",
	)
	chunk5Charge3 := s.newProratableSurchargeWithChargeablePolicy(
		chunksDates,
		chunkID5,
		"67.90",
	)

	originalChunksCharges := map[string][]*ptypes.Charge{
		chunkID1: {
			chunk1Charge1,
			chunk1Charge2,
			chunk1Charge3,
		},
		chunkID2: {
			chunk2Charge1,
			chunk2Charge2,
			chunk2Charge3,
		},
		chunkID3: {
			chunk3Charge1,
			chunk3Charge2,
			chunk3Charge3,
		},
		chunkID4: {
			chunk4Charge1,
			chunk4Charge2,
			chunk4Charge3,
		},
		chunkID5: {
			chunk5Charge1,
			chunk5Charge2,
			chunk5Charge3,
		},
	}

	chunk1Charge1Copy := chunk1Charge1.Copy()
	chunk2Charge1Copy := chunk2Charge1.Copy()
	chunk3Charge1Copy := chunk3Charge1.Copy()
	chunk4Charge1Copy := chunk4Charge1.Copy()
	chunk5Charge1Copy := chunk5Charge1.Copy()

	chunk1Charge2Copy := chunk1Charge2.Copy()
	chunk2Charge2Copy := chunk2Charge2.Copy()
	chunk3Charge2Copy := chunk3Charge2.Copy()
	chunk4Charge2Copy := chunk4Charge2.Copy()
	chunk5Charge2Copy := chunk5Charge2.Copy()

	chunk1Charge3Copy := chunk1Charge3.Copy()
	chunk2Charge3Copy := chunk2Charge3.Copy()
	chunk3Charge3Copy := chunk3Charge3.Copy()
	chunk4Charge3Copy := chunk4Charge3.Copy()
	chunk5Charge3Copy := chunk5Charge3.Copy()

	/*
		acc = 0

		chunk 1:
		  - proratedPremium := 51.5 * (10 / 50) = 10.3
		  - combinedPremium = proratedPremium + acc = 10.3
		  - roundedPremium = round(combinedPremium) = 10
		  - acc = combinedPremium - roundedPremium = 0.3

		chunk 2:
		  - proratedPremium := 59 * (10 / 50) = 11.8
		  - combinedPremium = proratedPremium + acc = 12.1
		  - roundedPremium = round(combinedPremium) = 12
		  - acc = combinedPremium - roundedPremium = 0.1

		chunk 3:
		  - proratedPremium := 61.5 * (10 / 50) = 12.3
		  - combinedPremium = proratedPremium + acc = 12.4
		  - roundedPremium = round(combinedPremium) = 12
		  - acc = combinedPremium - roundedPremium = 0.4

		chunk 4:
		  - proratedPremium := 73 * (10 / 50) = 14.6
		  - combinedPremium = proratedPremium + acc = 15
		  - roundedPremium = round(combinedPremium) = 15
		  - acc = combinedPremium - roundedPremium = 0

		chunk 5:
		  - proratedPremium := 67 * (10 / 50) = 13.4
		  - combinedPremium = proratedPremium + acc = 13.4
		  - roundedPremium = round(combinedPremium) = 13
		  - acc = combinedPremium - roundedPremium = 0.4
	*/
	chunk1Charge1Copy.GetAmountBasedBillingDetails().Amount = "10"
	chunk2Charge1Copy.GetAmountBasedBillingDetails().Amount = "12"
	chunk3Charge1Copy.GetAmountBasedBillingDetails().Amount = "12"
	chunk4Charge1Copy.GetAmountBasedBillingDetails().Amount = "15"
	chunk5Charge1Copy.GetAmountBasedBillingDetails().Amount = "13"

	/*
		acc = 0

		chunk 1:
		  - proratedPremium := 53 * (10 / 50) = 10.6
		  - combinedPremium = proratedPremium + acc = 10.6
		  - roundedPremium = round(combinedPremium) = 11
		  - acc = combinedPremium - roundedPremium = -0.4

		chunk 2:
		  - proratedPremium := 53 * (10 / 50) = 10.6
		  - combinedPremium = proratedPremium + acc = 10.2
		  - roundedPremium = round(combinedPremium) = 10
		  - acc = combinedPremium - roundedPremium = 0.2

		chunk 3:
		  - proratedPremium := 102 * (10 / 50) = 20.4
		  - combinedPremium = proratedPremium + acc = 20.6
		  - roundedPremium = round(combinedPremium) = 21
		  - acc = combinedPremium - roundedPremium = -0.4

		chunk 4:
		  - proratedPremium := 76.5 * (10 / 50) = 15.3
		  - combinedPremium = proratedPremium + acc = 14.9
		  - roundedPremium = round(combinedPremium) = 15
		  - acc = combinedPremium - roundedPremium = -0.1

		chunk 5:
		  - proratedPremium := 215.5 * (10 / 50) = 43.1
		  - combinedPremium = proratedPremium + acc = 43
		  - roundedPremium = round(combinedPremium) = 43
		  - acc = combinedPremium - roundedPremium = 0
	*/
	chunk1Charge2Copy.GetAmountBasedBillingDetails().Amount = "11"
	chunk2Charge2Copy.GetAmountBasedBillingDetails().Amount = "10"
	chunk3Charge2Copy.GetAmountBasedBillingDetails().Amount = "21"
	chunk4Charge2Copy.GetAmountBasedBillingDetails().Amount = "15"
	chunk5Charge2Copy.GetAmountBasedBillingDetails().Amount = "43"

	/*
		acc = 0

		chunk 1:
		  - proratedPremium := 51.63 * (10 / 50) = 10.326
		  - combinedPremium = proratedPremium + acc = 10.326
		  - roundedPremium = round(combinedPremium) = 10.33
		  - acc = combinedPremium - roundedPremium = -0.004

		chunk 2:
		  - proratedPremium := 59.34 * (10 / 50) = 11.868
		  - combinedPremium = proratedPremium + acc = 11.864
		  - roundedPremium = round(combinedPremium) = 11.86
		  - acc = combinedPremium - roundedPremium = 0.004

		chunk 3:
		  - proratedPremium := 61.29 * (10 / 50) = 12.258
		  - combinedPremium = proratedPremium + acc = 12.262
		  - roundedPremium = round(combinedPremium) = 12.26
		  - acc = combinedPremium - roundedPremium = 0.002

		chunk 4:
		  - proratedPremium := 73.87 * (10 / 50) = 14.774
		  - combinedPremium = proratedPremium + acc = 14.776
		  - roundedPremium = round(combinedPremium) = 14.78
		  - acc = combinedPremium - roundedPremium = -0.004

		chunk 5:
		  - proratedPremium := 67.90 * (10 / 50) = 13.58
		  - combinedPremium = proratedPremium + acc = 13.576
		  - roundedPremium = round(combinedPremium) = 13.58
		  - acc = combinedPremium - roundedPremium = -0.004
	*/
	chunk1Charge3Copy.GetAmountBasedBillingDetails().Amount = "10.33"
	chunk2Charge3Copy.GetAmountBasedBillingDetails().Amount = "11.86"
	chunk3Charge3Copy.GetAmountBasedBillingDetails().Amount = "12.26"
	chunk4Charge3Copy.GetAmountBasedBillingDetails().Amount = "14.78"
	chunk5Charge3Copy.GetAmountBasedBillingDetails().Amount = "13.58"

	expectedChunksCharges := map[string][]*ptypes.Charge{
		chunkID1: {
			chunk1Charge1Copy,
			chunk1Charge2Copy,
			chunk1Charge3Copy,
		},
		chunkID2: {
			chunk2Charge1Copy,
			chunk2Charge2Copy,
			chunk2Charge3Copy,
		},
		chunkID3: {
			chunk3Charge1Copy,
			chunk3Charge2Copy,
			chunk3Charge3Copy,
		},
		chunkID4: {
			chunk4Charge1Copy,
			chunk4Charge2Copy,
			chunk4Charge3Copy,
		},
		chunkID5: {
			chunk5Charge1Copy,
			chunk5Charge2Copy,
			chunk5Charge3Copy,
		},
	}

	proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
	s.Require().NoError(err)
	s.Require().Equal(len(expectedChunksCharges), len(proratedChunksCharges))
	for chunkID, chunkCharges := range proratedChunksCharges {
		s.Require().Equal(len(expectedChunksCharges[chunkID]), len(chunkCharges))
		for i, charge := range chunkCharges {
			s.Require().EqualExportedValues(expectedChunksCharges[chunkID][i], charge)
		}
	}
}

func (s *chargesProraterTestSuite) Test_ProrateCharges_WithMultipleChunks_WhenChunksAreNotContiguousAndTotalDurationIsLessThanPolicyDuration() {
	// Policy has 360 days
	policyStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime())
	policyEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "26/12/2024").ToTime())
	policyDates := &common_proto.Interval{
		Start: policyStartDate,
		End:   policyEndDate,
	}

	chunkID1 := "chunkID1"
	chunkID2 := "chunkID2"
	chunkID3 := "chunkID3"

	// Chunk 1 has 10 days
	chunk1StartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "11/01/2024").ToTime())
	chunk1EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "21/01/2024").ToTime())

	// Chunk 2 has 10 days
	chunk2StartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "11/02/2024").ToTime())
	chunk2EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "21/02/2024").ToTime())

	// Chunk 3 has 10 days
	chunk3StartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "11/03/2024").ToTime())
	chunk3EndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "21/03/2024").ToTime())

	chunksDates := map[string]*common_proto.Interval{
		chunkID1: {
			Start: chunk1StartDate,
			End:   chunk1EndDate,
		},
		chunkID2: {
			Start: chunk2StartDate,
			End:   chunk2EndDate,
		},
		chunkID3: {
			Start: chunk3StartDate,
			End:   chunk3EndDate,
		},
	}

	subCov := ptypes.SubCoverageType_SubCoverageType_BodilyInjury
	chunk1Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID1,
		"100",
		subCov,
	)
	chunk2Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID2,
		"100",
		subCov,
	)
	chunk3Charge := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID3,
		"100",
		subCov,
	)

	originalChunksCharges := map[string][]*ptypes.Charge{
		chunkID1: {
			chunk1Charge,
		},
		chunkID2: {
			chunk2Charge,
		},
		chunkID3: {
			chunk3Charge,
		},
	}

	chunk1ChargeCopy := chunk1Charge.Copy()
	chunk2ChargeCopy := chunk2Charge.Copy()
	chunk3ChargeCopy := chunk3Charge.Copy()

	/*
		acc = 0

		chunk 1:
		  - proratedPremium := 100 * (10 / 360) = 2.77777778
		  - combinedPremium = proratedPremium + acc = 2.77777778
		  - roundedPremium = round(combinedPremium) = 3
		  - acc = combinedPremium - roundedPremium = -0.22222222

		chunk 2:
		  - proratedPremium := 100 * (10 / 360) = 2.77777778
		  - combinedPremium = proratedPremium + acc = 2.55555556
		  - roundedPremium = round(combinedPremium) = 3
		  - acc = combinedPremium - roundedPremium = -0.44444444

		chunk 3:
		  - proratedPremium :=  100 * (10 / 360) = 2.77777778
		  - combinedPremium = proratedPremium + acc = 2.33333334
		  - roundedPremium = round(combinedPremium) = 2
		  - acc = combinedPremium - roundedPremium = 0.33333334
	*/
	chunk1ChargeCopy.GetAmountBasedBillingDetails().Amount = "3"
	chunk2ChargeCopy.GetAmountBasedBillingDetails().Amount = "3"
	chunk3ChargeCopy.GetAmountBasedBillingDetails().Amount = "2"

	expectedChunksCharges := map[string][]*ptypes.Charge{
		chunkID1: {
			chunk1ChargeCopy,
		},
		chunkID2: {
			chunk2ChargeCopy,
		},
		chunkID3: {
			chunk3ChargeCopy,
		},
	}

	proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
	s.Require().NoError(err)
	s.Require().Equal(len(expectedChunksCharges), len(proratedChunksCharges))
	for chunkID, chunkCharges := range proratedChunksCharges {
		s.Require().Equal(len(expectedChunksCharges[chunkID]), len(chunkCharges))
		s.Require().EqualExportedValues(expectedChunksCharges[chunkID][0], chunkCharges[0])
	}
}

// Test_ProrateCharges_WithMultipleChunks_WithZeroDurations tests the
// case when we have zero duration chunks (and also chunks whose dates
// have a time part, besides the date part).
//
// When calculating the effective duration of the chunk, we remove
// the time part of the dates. This is a temporary workaround to
// support 1-minute chunks and interpret them as zero duration chunks.
//
// Note that charges that aren't prorated should not be filtered out
// from zero duration chunks.
func (s *chargesProraterTestSuite) Test_ProrateCharges_WithMultipleChunks_WithZeroDurations() {
	// Policy has 360 days
	policyStartDate := timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC))
	policyEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "26/12/2024").ToTime())
	policyDates := &common_proto.Interval{
		Start: policyStartDate,
		End:   policyEndDate,
	}

	chunkID1 := "chunkID1"
	chunkID2 := "chunkID2"
	chunkID3 := "chunkID3"
	chunkID4 := "chunkID4"

	// Chunk 1 has zero duration
	chunk1StartDate := policyStartDate
	chunk1EndDate := timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC))

	// Chunk 2 has a duration of 1 minute (but an effective duration of zero)
	chunk2StartDate := policyStartDate
	chunk2EndDate := timestamppb.New(time.Date(2024, 1, 1, 0, 1, 0, 0, time.UTC))

	// Chunk 3 has a duration of 23 hours, 59 minutes and 59 seconds (but an effective duration of zero)
	chunk3StartDate := policyStartDate
	chunk3EndDate := timestamppb.New(time.Date(2024, 1, 1, 23, 59, 59, 0, time.UTC))

	// Chunk 4 has a duration of 2 seconds (but an effective duration of 1 day)
	chunk4StartDate := timestamppb.New(time.Date(2024, 1, 1, 23, 59, 59, 0, time.UTC))
	chunk4EndDate := timestamppb.New(time.Date(2024, 1, 2, 0, 0, 1, 0, time.UTC))

	chunksDates := map[string]*common_proto.Interval{
		chunkID1: {
			Start: chunk1StartDate,
			End:   chunk1EndDate,
		},
		chunkID2: {
			Start: chunk2StartDate,
			End:   chunk2EndDate,
		},
		chunkID3: {
			Start: chunk3StartDate,
			End:   chunk3EndDate,
		},
		chunkID4: {
			Start: chunk4StartDate,
			End:   chunk4EndDate,
		},
	}

	// In chunk 1, charges 1 and 2 are fee charges, and
	// therefore are not prorated. Despite chunk 1 having
	// zero duration, they are still included in the final
	// output. On the other hand, charge 3 should be prorated,
	// and in consequence shouldn't appear in the final output.
	chunk1Charge1 := s.newBlanketWOSCharge(chunksDates, chunkID1, "1")
	chunk1Charge2 := s.newBlanketRegularAICharge(chunksDates, chunkID1, "2")
	chunk1Charge3 := s.newBlanketPrimaryAndNonContributoryAICharge(chunksDates, chunkID1, "3")
	chunk1Charge4 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID1,
		"100000000000",
		ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
	)

	// Same thing happens for chunk 2, because it has
	// an effective duration of zero (even though it
	// technically has a duration of 1 minute).
	chunk2Charge1 := s.newSpecifiedWOSCharge(chunksDates, chunkID2, "4")
	chunk2Charge2 := s.newSpecifiedRegularAICharge(chunksDates, chunkID2, "5")
	chunk2Charge3 := s.newSpecifiedPrimaryAndNonContributoryAICharge(chunksDates, chunkID2, "6")
	chunk2Charge4 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID2,
		"100000000000",
		ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
	)

	// In chunk 3, charge 1 should be prorated (by construction),
	// and in consequence shouldn't be included in the final output
	// (because the chunk has an effective duration of zero).
	//
	// On the other hand, charge 2, 3, 4 and 5 should not be prorated
	// (charge 2 is a rate-based charge and the others are special surcharges).
	// Therefore, despite chunk 3 having an effective duration of zero,
	// these other charges should still be included in the final output.
	chunk3Charge1 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID3,
		"100000000000",
		ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
	)
	chunk3Charge2 := s.newChargeWithRateBasedBillingDetails(
		chunksDates,
		chunkID3,
		"0.5",
	)
	chunk3Charge3 := s.newFeeChargeSurplusTaxSurcharge(
		chunksDates,
		chunkID3,
		"0.5",
	)
	chunk3Charge4 := s.newFeeChargeStampingFeeSurcharge(
		chunksDates,
		chunkID3,
		"0.5",
	)
	chunk3Charge5 := s.newMCCASurcharge(
		chunksDates,
		chunkID3,
		"0.5",
	)

	chunk4Charge1 := s.newProratableBaseChargeWithChargeableSubCoverageGroup(
		chunksDates,
		chunkID4,
		"360",
		ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
	)

	originalChunksCharges := map[string][]*ptypes.Charge{
		chunkID1: {
			chunk1Charge1,
			chunk1Charge2,
			chunk1Charge3,
			chunk1Charge4,
		},
		chunkID2: {
			chunk2Charge1,
			chunk2Charge2,
			chunk2Charge3,
			chunk2Charge4,
		},
		chunkID3: {
			chunk3Charge1,
			chunk3Charge2,
			chunk3Charge3,
			chunk3Charge4,
			chunk3Charge5,
		},
		chunkID4: {
			chunk4Charge1,
		},
	}

	chunk4Charge1Copy := chunk4Charge1.Copy()

	/*
		acc = 0

		chunk 1: because this is a zero-duration chunk,
		         it's like there was no charge corresponding
				 to the group of chunk4Charge1.

		chunk 2: same as before.

		chunk 3: same as before.

		chunk 4:
		  - proratedPremium := 360 * (1 / 360) = 1
		  - combinedPremium = proratedPremium + acc = 1
		  - roundedPremium = round(combinedPremium) = 1
		  - acc = combinedPremium - roundedPremium = 0
	*/
	chunk4Charge1Copy.GetAmountBasedBillingDetails().Amount = "1"

	expectedChunksCharges := map[string][]*ptypes.Charge{
		chunkID1: {
			chunk1Charge1,
			chunk1Charge2,
			chunk1Charge3,
		},
		chunkID2: {
			chunk2Charge1,
			chunk2Charge2,
			chunk2Charge3,
		},
		chunkID3: {
			chunk3Charge2,
			chunk3Charge3,
			chunk3Charge4,
			chunk3Charge5,
		},
		chunkID4: {
			chunk4Charge1Copy,
		},
	}

	proratedChunksCharges, err := s.env.Prorater.ProrateCharges(policyDates, chunksDates, originalChunksCharges)
	s.Require().NoError(err)
	s.Require().Equal(len(expectedChunksCharges), len(proratedChunksCharges))
	for chunkID, chunkCharges := range proratedChunksCharges {
		s.Require().Equalf(len(expectedChunksCharges[chunkID]), len(chunkCharges), "chunkID: %s", chunkID)
		for i, charge := range chunkCharges {
			s.Require().EqualExportedValues(expectedChunksCharges[chunkID][i], charge)
		}
	}
}

// We add a different chunk ID, date and distributions, to
// test the group ID is being created correctly (omitting
// those fields).
func (s *chargesProraterTestSuite) newProratableBaseChargeWithChargeableSubCoverageGroup(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	premium string,
	subCovs ...ptypes.SubCoverageType,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails(premium, chunksDates[chunkID].Start).
		WithChargeableSubCoverageGroup(subCovs...).
		WithDistributions(
			ptypes.NewChargeDistribution(
				ptypes.Charge_DistributionType_Unspecified,
				ptypes.NewChargeDistributionItem(chunkID, ""),
			),
		).
		Build()
}

func (s *chargesProraterTestSuite) newProratableSurchargeWithChargeablePolicy(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	premium string,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithAmountBasedBillingDetails(premium, chunksDates[chunkID].Start).
		WithDefaultChargeablePolicy_TestOnly().
		WithDefaultSurchargeType_TestOnly().
		Build()
}

func (s *chargesProraterTestSuite) newFeeChargeSurplusTaxSurcharge(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	premium string,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithAmountBasedBillingDetails(premium, chunksDates[chunkID].Start).
		WithDefaultChargeablePolicy_TestOnly().
		WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
		Build()
}

func (s *chargesProraterTestSuite) newMCCASurcharge(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	premium string,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithAmountBasedBillingDetails(premium, chunksDates[chunkID].Start).
		WithDefaultChargeablePolicy_TestOnly().
		WithMCCASurchargeType().
		Build()
}

func (s *chargesProraterTestSuite) newFeeChargeStampingFeeSurcharge(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	premium string,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithAmountBasedBillingDetails(premium, chunksDates[chunkID].Start).
		WithDefaultChargeablePolicy_TestOnly().
		WithStampingFeeFromFullyEarnedPremiumSurchargeType().
		Build()
}

func (s *chargesProraterTestSuite) newChargeWithRateBasedBillingDetails(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	rate string,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithRateBasedBillingDetails(
			rate,
			chunksDates[chunkID],
			ptypes.RateBasis_RateBasis_Unspecified,
		).
		Build()
}

func (s *chargesProraterTestSuite) newBlanketRegularAICharge(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	premium string,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithBaseChargeTypeWithBlanketRegularAdditionalInsured().
		WithAmountBasedBillingDetails(premium, chunksDates[chunkID].Start).
		Build()
}

func (s *chargesProraterTestSuite) newBlanketPrimaryAndNonContributoryAICharge(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	premium string,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().
		WithAmountBasedBillingDetails(premium, chunksDates[chunkID].Start).
		Build()
}

func (s *chargesProraterTestSuite) newBlanketWOSCharge(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	premium string,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithBaseChargeTypeWithBlanketWaiverOfSubrogation().
		WithAmountBasedBillingDetails(premium, chunksDates[chunkID].Start).
		Build()
}

func (s *chargesProraterTestSuite) newSpecifiedRegularAICharge(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	premium string,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().
		WithAmountBasedBillingDetails(premium, chunksDates[chunkID].Start).
		Build()
}

func (s *chargesProraterTestSuite) newSpecifiedPrimaryAndNonContributoryAICharge(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	premium string,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().
		WithAmountBasedBillingDetails(premium, chunksDates[chunkID].Start).
		Build()
}

func (s *chargesProraterTestSuite) newSpecifiedWOSCharge(
	chunksDates map[string]*common_proto.Interval,
	chunkID string,
	premium string,
) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().
		WithAmountBasedBillingDetails(premium, chunksDates[chunkID].Start).
		Build()
}
