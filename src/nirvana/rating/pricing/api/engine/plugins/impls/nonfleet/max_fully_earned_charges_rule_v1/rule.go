package max_fully_earned_charges_rule_v1

import (
	"context"

	"go.uber.org/fx"

	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
)

type Deps struct {
	fx.In
}

// The Rule struct implements the logic regarding the
// limit imposed to fully earned charges in terms of their count.
//
// More concretely, we only charge for the first five fully earned
// charges of a given combination.
//   - Combinations are: Blanket/Specified x AI/AI-PNC/WOS.
//
// This is a bundle-level rule, not a policy-level rule.
// Meaning we have one "counter" for each combination that
// is shared between all policies in the bundle.
//
// In case that there are multiple candidates for the fifth
// specified X, we use the following selection algorithm:
//   - Choose first from MC, then GL then MTC.
//   - Within a given policy, choose based on ID alphabetical
//     order.
//
// This rule assumes it is placed after the fully earned charges
// dedup rule.
type Rule struct {
	deps Deps

	nextPluginApplyFn common.PluginApplyFn
}

var _ common.PluginI = (*Rule)(nil)

func (r *Rule) Apply(
	ctx context.Context,
	input *common.PluginChainInput,
) (common.PluginChainOutput, error) {
	return r.apply(ctx, input)
}

func (r *Rule) SetNextPluginApplyFn(nextPluginApplyFn common.PluginApplyFn) error {
	r.nextPluginApplyFn = nextPluginApplyFn
	return nil
}

func NewRule(deps Deps) *Rule {
	return &Rule{deps: deps}
}
