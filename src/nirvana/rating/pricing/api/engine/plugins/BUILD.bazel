load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "plugins",
    srcs = [
        "exclusiveness_map.go",
        "factories_map.go",
        "fx.go",
        "helper.go",
        "helper_mock.go",
        "order_map.go",
        "supported_map.go",
    ],
    importpath = "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/rating/pricing/api/engine/plugins/common",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/bi_and_pd_require_joint_selection_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/coll_and_umpd_mutual_exclusivity_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/coll_requires_comp_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/hired_auto_liab_requires_liab_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/hired_auto_pd_requires_comp_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/hired_auto_pd_requires_hired_auto_liab_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/hired_auto_pd_requires_liab_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/med_pay_and_pip_mutual_exclusivity_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/med_pay_requires_liab_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/medical_expense_benefits_requires_liab_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/no_specified_x_modifier_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/non_owned_vehicle_requires_liab_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/only_ba_policy_is_supported_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/pip_requires_liab_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/rental_requires_pd_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/same_pd_combination_for_all_vehicles_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/same_per_occurrence_limit_for_umbi_and_uimbi_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/towing_requires_pd_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/towing_restricted_to_certain_vehicles_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/uimbi_requires_liab_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umbi_is_required_if_liab_is_present_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umbi_requires_liab_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umpd_requires_liab_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umpd_requires_umbi_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umpd_restricted_to_ppt_vehicles_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umuim_requires_liab_validation_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/common/charges_proration_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/common/mcca_surcharge_dedup_rule_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/common/metrics_reporting_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/common/rateml_artifact_upload_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/driver_endorsement_rule_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/fully_earned_charges_dedup_rule_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/max_fully_earned_charges_rule_v1",
        "//nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/same_per_occurrence_limit_for_hired_auto_and_liab_validation_v1",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/rtypes",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "plugins_test",
    srcs = [
        "helper_test.go",
        "supported_map_test.go",
    ],
    embed = [":plugins"],
    deps = [
        "//nirvana/common-go/us_states",
        "//nirvana/infra/fx/testloader",
        "//nirvana/rating/pricing/api/engine/plugins/common",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/rtypes",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
