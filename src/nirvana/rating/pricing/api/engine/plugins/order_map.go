package plugins

import "nirvanatech.com/nirvana/rating/pricing/api/ptypes"

type OrderList []ptypes.PluginID

// OrderMap maps PluginIDs to their index in the OrderList.
type OrderMap map[ptypes.PluginID]int

// orderList holds all active plugins in the order they should
// be executed. This order is important because plugins might depend
// on the output of other plugins. For instance, the MaxFeeChargesRule
// must run after the FeeChargesDedupeRule).
//
// There might also be performance reasons. For example, the
// DriverEndorsementRule. This rule must be placed after all other
// plugins, due to a performance optimization hack we have in place.
// You can read more about this in the rule's documentation.
//
// In some cases, the order in which two plugins are run doesn't really
// matter. When adding a new plugin like this, pick a place arbitrarily
// and stick to it. This list essentially represents a "master" order
// of the plugins.
//
// Note that some plugins will never be run together, because they belong
// to different programs.
//
// DO NOT MAKE CHANGES THAT MODIFY THIS ORDER. ONLY ADD NEW PLUGINS.
var orderList = OrderList{
	ptypes.PluginID_PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1,
	ptypes.PluginID_PluginID_CollRequiresCompValidation_V1,
	ptypes.PluginID_PluginID_SamePDCombinationForAllVehiclesValidation_V1,
	ptypes.PluginID_PluginID_UMPDRestrictedToPPTVehiclesValidation_V1,
	ptypes.PluginID_PluginID_CollAndUMPDMutualExclusivityValidation_V1,
	ptypes.PluginID_PluginID_TowingRestrictedToCertainVehiclesValidation_V1,
	ptypes.PluginID_PluginID_NoSpecifiedXModifierValidation_V1,
	ptypes.PluginID_PluginID_HiredAutoPDRequiresCompValidation_V1,
	ptypes.PluginID_PluginID_UMBIIsRequiredIfLiabIsPresentValidation_V1,
	ptypes.PluginID_PluginID_TowingRequiresPDValidation_V1,
	ptypes.PluginID_PluginID_RentalRequiresPDValidation_V1,
	ptypes.PluginID_PluginID_MedPayRequiresLiabValidation_V1,
	ptypes.PluginID_PluginID_MedicalExpenseBenefitsRequiresLiabValidation_V1,
	ptypes.PluginID_PluginID_UMBIRequiresLiabValidation_V1,
	ptypes.PluginID_PluginID_UMPDRequiresLiabValidation_V1,
	ptypes.PluginID_PluginID_UIMBIRequiresLiabValidation_V1,
	ptypes.PluginID_PluginID_HiredAutoLiabRequiresLiabValidation_V1,
	ptypes.PluginID_PluginID_HiredAutoPDRequiresLiabValidation_V1,
	ptypes.PluginID_PluginID_NonOwnedVehicleRequiresLiabValidation_V1,
	ptypes.PluginID_PluginID_BIAndPDRequireJointSelectionValidation_V1,
	ptypes.PluginID_PluginID_OnlyBAPolicyIsSupportedValidation_V1,
	ptypes.PluginID_PluginID_UMPDRequiresUMBIValidation_V1,
	ptypes.PluginID_PluginID_SamePerOccurrenceLimitForUMBIAndUIMBIValidation_V1,
	ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1,
	ptypes.PluginID_PluginID_PIPRequiresLiabValidation_V1,
	ptypes.PluginID_PluginID_UMUIMRequiresLiabValidation_V1,
	ptypes.PluginID_PluginID_MedPayAndPIPMutualExclusivityValidation_V1,
	ptypes.PluginID_PluginID_MetricsReporting_V1,
	ptypes.PluginID_PluginID_ChargesProration_V1,
	ptypes.PluginID_PluginID_RateMLArtifactUpload_V1,
	ptypes.PluginID_PluginID_LossHistoryExperiment_V1,
	ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1,
	ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1,
	ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1,
	ptypes.PluginID_PluginID_DriverEndorsementRule_V1,
}

func newOrderMap(orderList OrderList) OrderMap {
	orderMap := make(OrderMap)
	for i, pluginID := range orderList {
		orderMap[pluginID] = i
	}
	return orderMap
}
