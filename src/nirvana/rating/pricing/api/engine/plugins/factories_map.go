package plugins

import (
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/med_pay_and_pip_mutual_exclusivity_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/pip_requires_liab_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umuim_requires_liab_validation_v1"

	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/bi_and_pd_require_joint_selection_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/coll_and_umpd_mutual_exclusivity_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/coll_requires_comp_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/hired_auto_liab_requires_liab_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/hired_auto_pd_requires_comp_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/hired_auto_pd_requires_hired_auto_liab_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/hired_auto_pd_requires_liab_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/med_pay_requires_liab_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/medical_expense_benefits_requires_liab_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/no_specified_x_modifier_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/non_owned_vehicle_requires_liab_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/only_ba_policy_is_supported_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/rental_requires_pd_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/same_pd_combination_for_all_vehicles_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/same_per_occurrence_limit_for_umbi_and_uimbi_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/towing_requires_pd_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/towing_restricted_to_certain_vehicles_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/uimbi_requires_liab_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umbi_is_required_if_liab_is_present_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umbi_requires_liab_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umpd_requires_liab_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umpd_requires_umbi_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/business_auto/umpd_restricted_to_ppt_vehicles_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/common/charges_proration_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/common/mcca_surcharge_dedup_rule_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/common/metrics_reporting_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/common/rateml_artifact_upload_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/driver_endorsement_rule_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/fully_earned_charges_dedup_rule_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/max_fully_earned_charges_rule_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/same_per_occurrence_limit_for_hired_auto_and_liab_validation_v1"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

type Factory func() common.PluginI

type FactoriesMap map[ptypes.PluginID]Factory

type factoriesMapDeps struct {
	fx.In

	MetricsReportingV1Deps                                    metrics_reporting_v1.Deps
	MCCASurchargeDedupRuleV1Deps                              mcca_surcharge_dedup_rule_v1.Deps
	RateMLArtifactUploadV1Deps                                rateml_artifact_upload_v1.Deps
	DriverEndorsementRuleV1Deps                               driver_endorsement_rule_v1.Deps
	FeeChargesDedupRuleV1Deps                                 fully_earned_charges_dedup_rule_v1.Deps
	MaxFeesChargesRuleV1Deps                                  max_fully_earned_charges_rule_v1.Deps
	HiredAutoPDRequiresHiredAutoLiabValidationV1Deps          hired_auto_pd_requires_hired_auto_liab_validation_v1.Deps
	CollRequiresCompValidationV1Deps                          coll_requires_comp_validation_v1.Deps
	SamePdCombinationForAllVehiclesValidationV1Deps           same_pd_combination_for_all_vehicles_validation_v1.Deps
	UMPDRestrictedToPPTVehiclesValidationV1Deps               umpd_restricted_to_ppt_vehicles_validation_v1.Deps
	CollAndUMPDMutualExclusivityValidationV1Deps              coll_and_umpd_mutual_exclusivity_validation_v1.Deps
	TowingRestrictedToCertainVehiclesValidationV1Deps         towing_restricted_to_certain_vehicles_validation_v1.Deps
	NoSpecifiedModifiersValidationV1Deps                      no_specified_x_modifier_validation_v1.Deps
	HiredAutoPDRequiresCompValidationV1Deps                   hired_auto_pd_requires_comp_validation_v1.Deps
	UMBIIsRequiredIfLiabIsPresentValidationV1Deps             umbi_is_required_if_liab_is_present_validation_v1.Deps
	TowingRequiresPDValidationV1Deps                          towing_requires_pd_validation_v1.Deps
	RentalRequiresPDValidationV1Deps                          rental_requires_pd_validation_v1.Deps
	MedPayRequiresLiabValidationV1Deps                        med_pay_requires_liab_validation_v1.Deps
	MedicalExpenseBenefitsRequiresLiabValidationV1Deps        medical_expense_benefits_requires_liab_validation_v1.Deps
	UMBIRequiresLiabValidationV1Deps                          umbi_requires_liab_validation_v1.Deps
	UMPDRequiresLiabValidationV1Deps                          umpd_requires_liab_validation_v1.Deps
	UIMBIRequiresLiabValidationV1Deps                         uimbi_requires_liab_validation_v1.Deps
	HiredAutoLiabRequiresLiabValidationV1Deps                 hired_auto_liab_requires_liab_validation_v1.Deps
	HiredAutoPDRequiresLiabValidationV1Deps                   hired_auto_pd_requires_liab_validation_v1.Deps
	NonOwnedVehicleRequiresLiabValidationV1Deps               non_owned_vehicle_requires_liab_validation_v1.Deps
	BIAndPDRequireJointSelectionValidationV1Deps              bi_and_pd_require_joint_selection_validation_v1.Deps
	OnlyBAPolicyIsSupportedValidationV1Deps                   only_ba_policy_is_supported_validation_v1.Deps
	UMPDRequiresUMBIValidationV1Deps                          umpd_requires_umbi_validation_v1.Deps
	SamePerOccurrenceLimitForUMBIAndUIMBIValidationV1Deps     same_per_occurrence_limit_for_umbi_and_uimbi_validation_v1.Deps
	ChargesProrationV1Deps                                    charges_proration_v1.Deps
	SamePerOccurrenceLimitForHiredAutoAndLiabValidationV1Deps same_per_occurrence_limit_for_hired_auto_and_liab_validation_v1.Deps
	PIPRequiresLiabValidationV1Deps                           pip_requires_liab_validation_v1.Deps
	UMUIMRequiresLiabValidationV1Deps                         umuim_requires_liab_validation_v1.Deps
	MedPayAndPIPMutualExclusivityValidationV1Deps             med_pay_and_pip_mutual_exclusivity_validation_v1.Deps
}

func newFactoriesMap(deps factoriesMapDeps) FactoriesMap {
	//nolint:exhaustive
	return FactoriesMap{
		ptypes.PluginID_PluginID_MetricsReporting_V1: func() common.PluginI {
			return metrics_reporting_v1.NewPlugin(deps.MetricsReportingV1Deps)
		},
		ptypes.PluginID_PluginID_RateMLArtifactUpload_V1: func() common.PluginI {
			return rateml_artifact_upload_v1.NewPlugin(deps.RateMLArtifactUploadV1Deps)
		},
		ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1: func() common.PluginI {
			return fully_earned_charges_dedup_rule_v1.NewRule(deps.FeeChargesDedupRuleV1Deps)
		},
		ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1: func() common.PluginI {
			return max_fully_earned_charges_rule_v1.NewRule(deps.MaxFeesChargesRuleV1Deps)
		},
		ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1: func() common.PluginI {
			return mcca_surcharge_dedup_rule_v1.NewRule(deps.MCCASurchargeDedupRuleV1Deps)
		},
		ptypes.PluginID_PluginID_DriverEndorsementRule_V1: func() common.PluginI {
			return driver_endorsement_rule_v1.NewRule(deps.DriverEndorsementRuleV1Deps)
		},
		ptypes.PluginID_PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1: func() common.PluginI {
			return hired_auto_pd_requires_hired_auto_liab_validation_v1.NewValidation(deps.HiredAutoPDRequiresHiredAutoLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_CollRequiresCompValidation_V1: func() common.PluginI {
			return coll_requires_comp_validation_v1.NewValidation(deps.CollRequiresCompValidationV1Deps)
		},
		ptypes.PluginID_PluginID_SamePDCombinationForAllVehiclesValidation_V1: func() common.PluginI {
			return same_pd_combination_for_all_vehicles_validation_v1.NewValidation(deps.SamePdCombinationForAllVehiclesValidationV1Deps)
		},
		ptypes.PluginID_PluginID_UMPDRestrictedToPPTVehiclesValidation_V1: func() common.PluginI {
			return umpd_restricted_to_ppt_vehicles_validation_v1.NewValidation(deps.UMPDRestrictedToPPTVehiclesValidationV1Deps)
		},
		ptypes.PluginID_PluginID_CollAndUMPDMutualExclusivityValidation_V1: func() common.PluginI {
			return coll_and_umpd_mutual_exclusivity_validation_v1.NewValidation(deps.CollAndUMPDMutualExclusivityValidationV1Deps)
		},
		ptypes.PluginID_PluginID_TowingRestrictedToCertainVehiclesValidation_V1: func() common.PluginI {
			return towing_restricted_to_certain_vehicles_validation_v1.NewValidation(deps.TowingRestrictedToCertainVehiclesValidationV1Deps)
		},
		ptypes.PluginID_PluginID_NoSpecifiedXModifierValidation_V1: func() common.PluginI {
			return no_specified_x_modifier_validation_v1.NewValidation(deps.NoSpecifiedModifiersValidationV1Deps)
		},
		ptypes.PluginID_PluginID_HiredAutoPDRequiresCompValidation_V1: func() common.PluginI {
			return hired_auto_pd_requires_comp_validation_v1.NewValidation(deps.HiredAutoPDRequiresCompValidationV1Deps)
		},
		ptypes.PluginID_PluginID_UMBIIsRequiredIfLiabIsPresentValidation_V1: func() common.PluginI {
			return umbi_is_required_if_liab_is_present_validation_v1.NewValidation(deps.UMBIIsRequiredIfLiabIsPresentValidationV1Deps)
		},
		ptypes.PluginID_PluginID_TowingRequiresPDValidation_V1: func() common.PluginI {
			return towing_requires_pd_validation_v1.NewValidation(deps.TowingRequiresPDValidationV1Deps)
		},
		ptypes.PluginID_PluginID_RentalRequiresPDValidation_V1: func() common.PluginI {
			return rental_requires_pd_validation_v1.NewValidation(deps.RentalRequiresPDValidationV1Deps)
		},
		ptypes.PluginID_PluginID_MedPayRequiresLiabValidation_V1: func() common.PluginI {
			return med_pay_requires_liab_validation_v1.NewValidation(deps.MedPayRequiresLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_MedicalExpenseBenefitsRequiresLiabValidation_V1: func() common.PluginI {
			return medical_expense_benefits_requires_liab_validation_v1.NewValidation(deps.MedicalExpenseBenefitsRequiresLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_UMBIRequiresLiabValidation_V1: func() common.PluginI {
			return umbi_requires_liab_validation_v1.NewValidation(deps.UMBIRequiresLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_UMPDRequiresLiabValidation_V1: func() common.PluginI {
			return umpd_requires_liab_validation_v1.NewValidation(deps.UMPDRequiresLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_UIMBIRequiresLiabValidation_V1: func() common.PluginI {
			return uimbi_requires_liab_validation_v1.NewValidation(deps.UIMBIRequiresLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_HiredAutoLiabRequiresLiabValidation_V1: func() common.PluginI {
			return hired_auto_liab_requires_liab_validation_v1.NewValidation(deps.HiredAutoLiabRequiresLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_HiredAutoPDRequiresLiabValidation_V1: func() common.PluginI {
			return hired_auto_pd_requires_liab_validation_v1.NewValidation(deps.HiredAutoPDRequiresLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_NonOwnedVehicleRequiresLiabValidation_V1: func() common.PluginI {
			return non_owned_vehicle_requires_liab_validation_v1.NewValidation(deps.NonOwnedVehicleRequiresLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_BIAndPDRequireJointSelectionValidation_V1: func() common.PluginI {
			return bi_and_pd_require_joint_selection_validation_v1.NewValidation(deps.BIAndPDRequireJointSelectionValidationV1Deps)
		},
		ptypes.PluginID_PluginID_OnlyBAPolicyIsSupportedValidation_V1: func() common.PluginI {
			return only_ba_policy_is_supported_validation_v1.NewValidation(deps.OnlyBAPolicyIsSupportedValidationV1Deps)
		},
		ptypes.PluginID_PluginID_UMPDRequiresUMBIValidation_V1: func() common.PluginI {
			return umpd_requires_umbi_validation_v1.NewValidation(deps.UMPDRequiresUMBIValidationV1Deps)
		},
		ptypes.PluginID_PluginID_SamePerOccurrenceLimitForUMBIAndUIMBIValidation_V1: func() common.PluginI {
			return same_per_occurrence_limit_for_umbi_and_uimbi_validation_v1.NewValidation(deps.SamePerOccurrenceLimitForUMBIAndUIMBIValidationV1Deps)
		},
		ptypes.PluginID_PluginID_ChargesProration_V1: func() common.PluginI {
			return charges_proration_v1.NewPlugin(deps.ChargesProrationV1Deps)
		},
		ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1: func() common.PluginI {
			return same_per_occurrence_limit_for_hired_auto_and_liab_validation_v1.NewValidation(deps.SamePerOccurrenceLimitForHiredAutoAndLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_LossHistoryExperiment_V1: func() common.PluginI {
			// TODO: implement after migrating Fleet
			panic("LossHistoryExperiment plugin is not implemented yet")
		},
		ptypes.PluginID_PluginID_PIPRequiresLiabValidation_V1: func() common.PluginI {
			return pip_requires_liab_validation_v1.NewValidation(deps.PIPRequiresLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_UMUIMRequiresLiabValidation_V1: func() common.PluginI {
			return umuim_requires_liab_validation_v1.NewValidation(deps.UMUIMRequiresLiabValidationV1Deps)
		},
		ptypes.PluginID_PluginID_MedPayAndPIPMutualExclusivityValidation_V1: func() common.PluginI {
			return med_pay_and_pip_mutual_exclusivity_validation_v1.NewValidation(deps.MedPayAndPIPMutualExclusivityValidationV1Deps)
		},
	}
}
