package ptypes

import (
	"cmp"
	"slices"

	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/proto"
)

func NewChargeBuilder() *ChargeBuilder {
	return &ChargeBuilder{
		charge: &Charge{},
	}
}

type ChargeBuilder struct {
	charge *Charge
}

func (cb *ChargeBuilder) WithDefaultAmountBasedBillingDetails_TestOnly() *ChargeBuilder {
	return cb.WithAmountBasedBillingDetails("100.0", timestamppb.Now())
}

func (cb *ChargeBuilder) WithAmountBasedBillingDetails(premium string, date *timestamppb.Timestamp) *ChargeBuilder {
	return cb.WithBillingDetails(NewChargeAmountBasedBillingDetails(premium, date))
}

func (cb *ChargeBuilder) WithDefaultRateBasedBillingDetails_TestOnly() *ChargeBuilder {
	return cb.WithRateBasedBillingDetails(
		"0.785",
		&proto.Interval{
			Start: timestamppb.Now(),
			End:   timestamppb.Now(),
		},
		RateBasis_RateBasis_Unspecified,
	)
}

func (cb *ChargeBuilder) WithRateBasedBillingDetails(
	rate string,
	dates *proto.Interval,
	rateBasis RateBasis,
) *ChargeBuilder {
	return cb.WithBillingDetails(NewChargeRateBasedBillingDetails(rate, dates, rateBasis))
}

func (cb *ChargeBuilder) WithBillingDetails(billingDetails isCharge_BillingDetails) *ChargeBuilder {
	cb.charge.BillingDetails = billingDetails
	return cb
}

func (cb *ChargeBuilder) WithBaseChargeTypeWithoutExtraInfo() *ChargeBuilder {
	return cb.WithChargeType(NewChargeBaseChargeWithNoExtraInfo())
}

func (cb *ChargeBuilder) WithBaseChargeTypeWithBlanketRegularAdditionalInsured() *ChargeBuilder {
	return cb.WithChargeType(NewChargeBaseChargeWithBlanketRegularAdditionalInsured())
}

func (cb *ChargeBuilder) WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured() *ChargeBuilder {
	return cb.WithChargeType(NewChargeBaseChargeWithBlanketPrimaryAndNonContributoryAdditionalInsured())
}

func (cb *ChargeBuilder) WithBaseChargeTypeWithBlanketWaiverOfSubrogation() *ChargeBuilder {
	return cb.WithChargeType(NewChargeBaseChargeWithBlanketWaiverOfSubrogation())
}

func (cb *ChargeBuilder) WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly() *ChargeBuilder {
	return cb.WithBaseChargeTypeWithSpecifiedRegularAdditionalInsured("123")
}

func (cb *ChargeBuilder) WithBaseChargeTypeWithSpecifiedRegularAdditionalInsured(
	additionalInsuredID string,
) *ChargeBuilder {
	return cb.WithChargeType(NewChargeBaseChargeWithSpecifiedRegularAdditionalInsured(additionalInsuredID))
}

func (cb *ChargeBuilder) WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly() *ChargeBuilder {
	return cb.WithBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured("123")
}

func (cb *ChargeBuilder) WithBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured(
	additionalInsuredID string,
) *ChargeBuilder {
	return cb.WithChargeType(NewChargeBaseChargeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured(additionalInsuredID))
}

func (cb *ChargeBuilder) WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly() *ChargeBuilder {
	return cb.WithBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation("123")
}

func (cb *ChargeBuilder) WithBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation(
	thirdPartyID string,
) *ChargeBuilder {
	return cb.WithChargeType(NewChargeBaseChargeWithSpecifiedThirdPartyWithWaiverOfSubrogation(thirdPartyID))
}

func (cb *ChargeBuilder) WithDefaultSurchargeType_TestOnly() *ChargeBuilder {
	return cb.WithSurchargeType(Surcharge_Type_Unspecified)
}

func (cb *ChargeBuilder) WithNCRFSurchargeType() *ChargeBuilder {
	return cb.WithSurchargeType(Surcharge_Type_NCRF)
}

func (cb *ChargeBuilder) WithMCCASurchargeType() *ChargeBuilder {
	return cb.WithSurchargeType(Surcharge_Type_MCCA)
}

func (cb *ChargeBuilder) WithStampingFeeSurchargeType() *ChargeBuilder {
	return cb.WithSurchargeType(Surcharge_Type_STAMPING_FEE)
}

func (cb *ChargeBuilder) WithSurplusTaxSurchargeType() *ChargeBuilder {
	return cb.WithSurchargeType(Surcharge_Type_SURPLUS_TAX)
}

func (cb *ChargeBuilder) WithStampingFeeFromFullyEarnedPremiumSurchargeType() *ChargeBuilder {
	return cb.WithSurchargeType(Surcharge_Type_FEE_CHARGE_STAMPING_FEE)
}

func (cb *ChargeBuilder) WithSurplusTaxFromFullyEarnedPremiumSurchargeType() *ChargeBuilder {
	return cb.WithSurchargeType(Surcharge_Type_FEE_CHARGE_SURPLUS_TAX)
}

func (cb *ChargeBuilder) WithSurchargeType(surchargeType Surcharge_Type) *ChargeBuilder {
	return cb.WithChargeType(NewChargeSurcharge(surchargeType))
}

func (cb *ChargeBuilder) WithManualEndorsementChargeType() *ChargeBuilder {
	return cb.WithChargeType(NewChargeManualEndorsementCharge())
}

func (cb *ChargeBuilder) WithChargeType(chargeType isCharge_Type) *ChargeBuilder {
	cb.charge.Type = chargeType
	return cb
}

func (cb *ChargeBuilder) WithDefaultChargeableSubCoverageGroup_TestOnly() *ChargeBuilder {
	return cb.WithChargeableSubCoverageGroup(SubCoverageType_SubCoverageType_Unspecified)
}

func (cb *ChargeBuilder) WithChargeableSubCoverageGroup(subCoverageTypes ...SubCoverageType) *ChargeBuilder {
	return cb.WithChargedItem(NewChargeChargedSubCoverageGroup(subCoverageTypes...))
}

func (cb *ChargeBuilder) WithDefaultChargeablePolicy_TestOnly() *ChargeBuilder {
	return cb.WithChargeablePolicy("policyNumber")
}

func (cb *ChargeBuilder) WithChargeablePolicy(policyNumber string) *ChargeBuilder {
	return cb.WithChargedItem(NewChargeChargedPolicy(policyNumber))
}

func (cb *ChargeBuilder) WithChargedItem(chargedItem isCharge_ChargedItem) *ChargeBuilder {
	cb.charge.ChargedItem = chargedItem
	return cb
}

func (cb *ChargeBuilder) WithDefaultDistribution_TestOnly() *ChargeBuilder {
	return cb.WithDistribution(
		Charge_DistributionType_Unspecified,
		[]*Charge_DistributionItem{
			NewChargeDistributionItem("1", "0.6"),
			NewChargeDistributionItem("2", "0.4"),
		},
	)
}

func (cb *ChargeBuilder) WithDistribution(
	distributionType Charge_DistributionType,
	items []*Charge_DistributionItem,
) *ChargeBuilder {
	slices.SortFunc(items, func(i1, i2 *Charge_DistributionItem) int {
		return cmp.Compare(i1.Id, i2.Id)
	})

	distribution := NewChargeDistribution(distributionType, items...)

	cb.charge.Distributions = append(cb.charge.Distributions, distribution)
	return cb
}

func (cb *ChargeBuilder) WithDistributions(distributions ...*Charge_Distribution) *ChargeBuilder {
	for _, distribution := range distributions {
		if distribution == nil {
			continue
		}

		slices.SortFunc(distribution.Items, func(i1, i2 *Charge_DistributionItem) int {
			return cmp.Compare(i1.Id, i2.Id)
		})
	}

	slices.SortFunc(distributions, func(d1, d2 *Charge_Distribution) int {
		return cmp.Compare(d1.Type, d2.Type)
	})

	cb.charge.Distributions = distributions
	return cb
}

func (cb *ChargeBuilder) Build() *Charge {
	return cb.charge
}
