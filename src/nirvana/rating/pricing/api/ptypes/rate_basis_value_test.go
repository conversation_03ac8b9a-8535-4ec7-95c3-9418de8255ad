package ptypes

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func Test_HasMilesValue(t *testing.T) {
	var v *RateBasisValue
	require.NotPanics(t, func() { require.False(t, v.HasMilesValue()) })

	v = &RateBasisValue{}
	require.False(t, v.HasMilesValue())

	v = &RateBasisValue{Value: &RateBasisValue_Miles{}}
	require.True(t, v.HasMilesValue())

	v = &RateBasisValue{Value: &RateBasisValue_TivDays{}}
	require.False(t, v.HasMilesValue())

	v = &RateBasisValue{Value: &RateBasisValue_Premium{}}
	require.False(t, v.Has<PERSON>ilesValue())
}

func Test_HasTIVDaysValue(t *testing.T) {
	var v *RateBasisValue
	require.NotPanics(t, func() { require.False(t, v.HasTIVDaysValue()) })

	v = &RateBasisValue{}
	require.False(t, v.HasTIVDaysValue())

	v = &RateBasisValue{Value: &RateBasisValue_Miles{}}
	require.False(t, v.HasTIVDaysValue())

	v = &RateBasisValue{Value: &RateBasisValue_TivDays{}}
	require.True(t, v.HasTIVDaysValue())

	v = &RateBasisValue{Value: &RateBasisValue_Premium{}}
	require.False(t, v.HasTIVDaysValue())
}

func Test_HasPremiumValue(t *testing.T) {
	var v *RateBasisValue
	require.NotPanics(t, func() { require.False(t, v.HasPremiumValue()) })

	v = &RateBasisValue{}
	require.False(t, v.HasPremiumValue())

	v = &RateBasisValue{Value: &RateBasisValue_Miles{}}
	require.False(t, v.HasPremiumValue())

	v = &RateBasisValue{Value: &RateBasisValue_TivDays{}}
	require.False(t, v.HasPremiumValue())

	v = &RateBasisValue{Value: &RateBasisValue_Premium{}}
	require.True(t, v.HasPremiumValue())
}
