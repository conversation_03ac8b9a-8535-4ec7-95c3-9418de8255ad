package ptypes

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestIsChargeRefundable_WithNilCharge(t *testing.T) {
	// With nil charge
	res, err := IsChargeRefundable(nil)
	require.Error(t, err)
	require.<PERSON>exp(t, "charge was nil", err.Error())
	require.False(t, res)

	// With nil charge type
	ch := NewChargeBuilder().Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown billing details", err.Error())
	require.False(t, res)
}

func TestIsChargeRefundable_WithAmountBasedBillingDetails(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		Build()
	res, err := IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown type for charge with amount-based billing details", err.Error())
	require.False(t, res)

	// With NCRF surcharge
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithNCRFSurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.True(t, res)

	// With MCCA surcharge
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithMCCASurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.False(t, res)

	// With Surplus Tax surcharge
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithSurplusTaxSurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.True(t, res)

	// WithSurplus Tax from fully earned premium surcharge
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.False(t, res)

	// With Stamping Fee surcharge
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithStampingFeeSurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.True(t, res)

	// With Stamping Fee from fully earned premium surcharge
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithStampingFeeFromFullyEarnedPremiumSurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.False(t, res)

	// With invalid surcharge
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithDefaultSurchargeType_TestOnly().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown surcharge", err.Error())
	require.False(t, res)

	// With base charge with no extra info and chargeable sub-coverage
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithBaseChargeTypeWithoutExtraInfo().
		WithDefaultChargeableSubCoverageGroup_TestOnly().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.True(t, res)

	// With base charge with BRAI extra info
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithBaseChargeTypeWithBlanketRegularAdditionalInsured().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.False(t, res)

	// With base charge with BPNCAI extra info
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.False(t, res)

	// With base charge with BWOS extra info
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithBaseChargeTypeWithBlanketWaiverOfSubrogation().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.False(t, res)

	// With base charge with SRAI extra info
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.False(t, res)

	// With base charge with SPNCAI extra info
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.False(t, res)

	// With base charge with SWOS extra info
	ch = NewChargeBuilder().
		WithDefaultAmountBasedBillingDetails_TestOnly().
		WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.False(t, res)
}

func TestIsChargeRefundable_WithRateBasedBillingDetails(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		Build()
	res, err := IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown type for charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With NCRF surcharge
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithNCRFSurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown type for charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With MCCA surcharge
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithMCCASurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown type for charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With Stamping Fee surcharge
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithStampingFeeSurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown type for charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With Stamping Fee from fully earned premium surcharge
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithStampingFeeFromFullyEarnedPremiumSurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown type for charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With Surplus Tax surcharge
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithSurplusTaxSurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown type for charge with rate-based billing details", err.Error())
	require.False(t, res)

	// WithSurplus Tax from fully earned premium surcharge
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithSurplusTaxFromFullyEarnedPremiumSurchargeType().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown type for charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With invalid surcharge
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithDefaultSurchargeType_TestOnly().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown type for charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With base charge with no extra info and chargeable sub-coverage
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithBaseChargeTypeWithoutExtraInfo().
		WithDefaultChargeableSubCoverageGroup_TestOnly().
		Build()
	res, err = IsChargeRefundable(ch)
	require.NoError(t, err)
	require.True(t, res)

	// With base charge with BRAI extra info
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithBaseChargeTypeWithBlanketRegularAdditionalInsured().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown charged item for base charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With base charge with BPNCAI extra info
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown charged item for base charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With base charge with BWOS extra info
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithBaseChargeTypeWithBlanketWaiverOfSubrogation().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown charged item for base charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With base charge with SRAI extra info
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown charged item for base charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With base charge with SPNCAI extra info
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown charged item for base charge with rate-based billing details", err.Error())
	require.False(t, res)

	// With base charge with SWOS extra info
	ch = NewChargeBuilder().
		WithDefaultRateBasedBillingDetails_TestOnly().
		WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().
		Build()
	res, err = IsChargeRefundable(ch)
	require.Error(t, err)
	require.Regexp(t, "unknown charged item for base charge with rate-based billing details", err.Error())
	require.False(t, res)
}
