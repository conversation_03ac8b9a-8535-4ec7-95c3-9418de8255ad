load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "pricing_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/rating/pricing/api/ptypes",
    proto = "//proto/pricing:ptypes_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/business-auto/model/proto",
        "//nirvana/common-go/proto",
        "//nirvana/fleet/model",
        "//nirvana/nonfleet/model",
    ],
)

# keep
go_proto_link(
    name = "pricing_go_proto_link",
    dep = ":pricing_go_proto",
    version = "v1",
)

go_library(
    name = "ptypes",
    srcs = [
        "bundle_spec_chunk_spec.go",
        "business_auto_bundle_chunk_spec_data.go",
        "business_auto_policy_chunk_spec_data.go",
        "business_auto_vehicle.go",
        "charge.go",
        "charge_builder.go",
        "chunk_output_metadata.go",
        "chunk_output_program_specific_metadata_mock.go",
        "combined_deductible_spec.go",
        "deductible_spec.go",
        "experience_rating_modification.go",
        "find_utils.go",
        "interfaces.go",
        "limit_spec.go",
        "loss_free_modification.go",
        "new.go",
        "nonfleet_bundle_chunk_spec_data.go",
        "nonfleet_continuous_coverage_record.go",
        "nonfleet_policy_chunk_spec_data.go",
        "nonfleet_underwriter_input.go",
        "policy_spec_chunk_spec.go",
        "rate_basis_value.go",
        "refundability.go",
        "request.go",
        "schedule_modification.go",
        "sort_utils.go",
        "sub_coverage_group.go",
        "sub_coverage_type.go",
    ],
    embed = [":pricing_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/rating/pricing/api/ptypes",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_shopspring_decimal//:decimal",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "ptypes_test",
    srcs = [
        "bundle_spec_chunk_spec_test.go",
        "business_auto_policy_chunk_spec_data_test.go",
        "charge_test.go",
        "combined_deductible_spec_test.go",
        "deductible_spec_test.go",
        "experience_rating_modification_test.go",
        "find_utils_test.go",
        "limit_spec_test.go",
        "loss_free_modification_test.go",
        "nonfleet_bundle_chunk_spec_data_test.go",
        "nonfleet_continuous_coverage_record_test.go",
        "nonfleet_policy_chunk_spec_data_test.go",
        "nonfleet_underwriter_input_test.go",
        "policy_spec_chunk_spec_test.go",
        "rate_basis_value_test.go",
        "refundability_test.go",
        "request_test.go",
        "schedule_modification_test.go",
        "sub_coverage_group_test.go",
        "sub_coverage_type_test.go",
    ],
    embed = [":ptypes"],
    deps = [
        "//nirvana/nonfleet/model",
        "@com_github_shopspring_decimal//:decimal",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
    ],
)
