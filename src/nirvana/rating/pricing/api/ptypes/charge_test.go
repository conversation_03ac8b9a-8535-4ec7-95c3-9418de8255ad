package ptypes

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/require"
)

func TestCalculate_WithInvalidBillingDetails(t *testing.T) {
	ch := NewChargeBuilder().Build()
	premium, err := ch.Calculate(nil)
	require.Error(t, err)
	require.Regexp(t, "unknown charge billing details", err.Error())
	require.Equal(t, decimal.Zero, premium)
}

func TestCalculate_WithAmountBasedBillingDetails(t *testing.T) {
	ch := NewChargeBuilder().WithDefaultAmountBasedBillingDetails_TestOnly().Build()

	// With non nil rate basis value
	premium, err := ch.Calculate(&RateBasisValue{})
	require.Error(t, err)
	require.Regexp(t, "invalid rate basis value for charge with amount based billing details", err.Error())
	require.Equal(t, decimal.Zero, premium)

	// With invalid amount
	ch = NewChargeBuilder().WithAmountBasedBillingDetails("not-numeric", nil).Build()
	premium, err = ch.Calculate(nil)
	require.Error(t, err)
	require.Regexp(t, "failed to parse amount", err.Error())
	require.Equal(t, decimal.Zero, premium)

	// With success
	ch = NewChargeBuilder().WithAmountBasedBillingDetails("978.12345", nil).Build()
	premium, err = ch.Calculate(nil)
	expected := decimal.NewFromFloat(978.12345)
	require.NoError(t, err)
	require.Equal(t, expected, premium)
}

func TestCalculate_WithRateBasedBillingDetails(t *testing.T) {
	ch := NewChargeBuilder().WithDefaultRateBasedBillingDetails_TestOnly().Build()

	// With nil rate basis value
	premium, err := ch.Calculate(nil)
	require.Error(t, err)
	require.Regexp(t, "invalid rate basis value for charge with rate based billing details", err.Error())
	require.Equal(t, decimal.Zero, premium)

	// With non-miles rate basis value
	premium, err = ch.Calculate(&RateBasisValue{})
	require.Error(t, err)
	require.Regexp(t, "invalid rate basis value for charge with rate based billing details", err.Error())
	require.Equal(t, decimal.Zero, premium)
}

func TestCalculate_WithRateBasedBillingDetails_WithMilesBasisValue(t *testing.T) {
	// With different charge's basis value (unspecified).
	ch := NewChargeBuilder().
		WithRateBasedBillingDetails("", nil, RateBasis_RateBasis_Unspecified).
		Build()
	premium, err := ch.Calculate(NewRateBasisValueWithMiles(9876))
	require.Error(t, err)
	require.Regexp(t, "charge's rate basis is not miles", err.Error())
	require.Equal(t, decimal.Zero, premium)

	// With invalid rate (non-numeric)
	ch = NewChargeBuilder().
		WithRateBasedBillingDetails("not-numeric", nil, RateBasis_RateBasis_Miles).
		Build()
	premium, err = ch.Calculate(NewRateBasisValueWithMiles(9876))
	require.Error(t, err)
	require.Regexp(t, "failed to parse rate", err.Error())
	require.Equal(t, decimal.Zero, premium)

	// With success
	ch = NewChargeBuilder().
		WithRateBasedBillingDetails("1.345", nil, RateBasis_RateBasis_Miles).
		Build()
	premium, err = ch.Calculate(NewRateBasisValueWithMiles(9876))
	expected := decimal.NewFromFloat(13283.22)
	require.NoError(t, err)
	require.True(t, expected.Equal(premium))
}

func TestHasAmountBasedBillingDetails(t *testing.T) {
	// With nil billing details
	ch := NewChargeBuilder().Build()
	require.False(t, ch.HasAmountBasedBillingDetails())

	// With Amount-based billing details
	ch = NewChargeBuilder().WithDefaultAmountBasedBillingDetails_TestOnly().Build()
	require.True(t, ch.HasAmountBasedBillingDetails())

	// With Rate-Based billing details
	ch = NewChargeBuilder().WithDefaultRateBasedBillingDetails_TestOnly().Build()
	require.False(t, ch.HasAmountBasedBillingDetails())
}

func TestHasRateBasedBillingDetails(t *testing.T) {
	// With nil billing details
	ch := NewChargeBuilder().Build()
	require.False(t, ch.HasRateBasedBillingDetails())

	// With Amount-based billing details
	ch = NewChargeBuilder().WithDefaultAmountBasedBillingDetails_TestOnly().Build()
	require.False(t, ch.HasRateBasedBillingDetails())

	// With Rate-Based billing details
	ch = NewChargeBuilder().WithDefaultRateBasedBillingDetails_TestOnly().Build()
	require.True(t, ch.HasRateBasedBillingDetails())
}

func TestIsBaseCharge(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.IsBaseCharge())

	// With base charge type with no extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.True(t, ch.IsBaseCharge())

	// With base charge type with BRAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketRegularAdditionalInsured().Build()
	require.True(t, ch.IsBaseCharge())

	// With base charge type with BPNCAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().Build()
	require.True(t, ch.IsBaseCharge())

	// With base charge type with BWOS extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketWaiverOfSubrogation().Build()
	require.True(t, ch.IsBaseCharge())

	// With base charge type with SRAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().Build()
	require.True(t, ch.IsBaseCharge())

	// With base charge type with SPNCAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().Build()
	require.True(t, ch.IsBaseCharge())

	// With base charge type with SWOS extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().Build()
	require.True(t, ch.IsBaseCharge())

	// With surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.IsBaseCharge())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.IsBaseCharge())
}

func TestIsSurcharge(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.IsSurcharge())

	// With base charge type
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.IsSurcharge())

	// With surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.True(t, ch.IsSurcharge())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.IsSurcharge())
}

func TestIsManualEndorsementCharge(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.IsManualEndorsementCharge())

	// With base charge type
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.IsManualEndorsementCharge())

	// With surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.IsManualEndorsementCharge())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.True(t, ch.IsManualEndorsementCharge())
}

func TestIsNCRFSurcharge(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.IsNCRFSurcharge())

	// With base charge type
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.IsNCRFSurcharge())

	// With unspecified surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.IsNCRFSurcharge())

	// With NCRF surcharge
	ch = NewChargeBuilder().WithNCRFSurchargeType().Build()
	require.True(t, ch.IsNCRFSurcharge())

	// With MCCA surcharge
	ch = NewChargeBuilder().WithMCCASurchargeType().Build()
	require.False(t, ch.IsNCRFSurcharge())

	// With Stamping Fee surcharge
	ch = NewChargeBuilder().WithStampingFeeSurchargeType().Build()
	require.False(t, ch.IsNCRFSurcharge())

	// With Surplus Tax surcharge
	ch = NewChargeBuilder().WithSurplusTaxSurchargeType().Build()
	require.False(t, ch.IsNCRFSurcharge())

	// With Stamping Fee surcharge from fully earned premium
	ch = NewChargeBuilder().WithStampingFeeFromFullyEarnedPremiumSurchargeType().Build()
	require.False(t, ch.IsNCRFSurcharge())

	// With Surplus Tax surcharge from fully earned premium
	ch = NewChargeBuilder().WithSurplusTaxFromFullyEarnedPremiumSurchargeType().Build()
	require.False(t, ch.IsNCRFSurcharge())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.IsNCRFSurcharge())
}

func TestIsMCCASurcharge(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.IsMCCASurcharge())

	// With base charge type
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.IsMCCASurcharge())

	// With unspecified surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.IsMCCASurcharge())

	// With NCRF surcharge
	ch = NewChargeBuilder().WithNCRFSurchargeType().Build()
	require.False(t, ch.IsMCCASurcharge())

	// With MCCA surcharge
	ch = NewChargeBuilder().WithMCCASurchargeType().Build()
	require.True(t, ch.IsMCCASurcharge())

	// With Stamping Fee surcharge
	ch = NewChargeBuilder().WithStampingFeeSurchargeType().Build()
	require.False(t, ch.IsMCCASurcharge())

	// With Surplus Tax surcharge
	ch = NewChargeBuilder().WithSurplusTaxSurchargeType().Build()
	require.False(t, ch.IsMCCASurcharge())

	// With Stamping Fee surcharge from fully earned premium
	ch = NewChargeBuilder().WithStampingFeeFromFullyEarnedPremiumSurchargeType().Build()
	require.False(t, ch.IsMCCASurcharge())

	// With Surplus Tax surcharge from fully earned premium
	ch = NewChargeBuilder().WithSurplusTaxFromFullyEarnedPremiumSurchargeType().Build()
	require.False(t, ch.IsMCCASurcharge())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.IsMCCASurcharge())
}

func TestIsStampingFeeSurcharge(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.IsStampingFeeSurcharge())

	// With base charge type
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.IsStampingFeeSurcharge())

	// With unspecified surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.IsStampingFeeSurcharge())

	// With NCRF surcharge
	ch = NewChargeBuilder().WithNCRFSurchargeType().Build()
	require.False(t, ch.IsStampingFeeSurcharge())

	// With MCCA surcharge
	ch = NewChargeBuilder().WithMCCASurchargeType().Build()
	require.False(t, ch.IsStampingFeeSurcharge())

	// With Stamping Fee surcharge
	ch = NewChargeBuilder().WithStampingFeeSurchargeType().Build()
	require.True(t, ch.IsStampingFeeSurcharge())

	// With Surplus Tax surcharge
	ch = NewChargeBuilder().WithSurplusTaxSurchargeType().Build()
	require.False(t, ch.IsStampingFeeSurcharge())

	// With Stamping Fee surcharge from fully earned premium
	ch = NewChargeBuilder().WithStampingFeeFromFullyEarnedPremiumSurchargeType().Build()
	require.False(t, ch.IsStampingFeeSurcharge())

	// With Surplus Tax surcharge from fully earned premium
	ch = NewChargeBuilder().WithSurplusTaxFromFullyEarnedPremiumSurchargeType().Build()
	require.False(t, ch.IsStampingFeeSurcharge())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.IsStampingFeeSurcharge())
}

func TestIsSurplusTaxSurcharge(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.IsSurplusTaxSurcharge())

	// With base charge type
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.IsSurplusTaxSurcharge())

	// With unspecified surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.IsSurplusTaxSurcharge())

	// With NCRF surcharge
	ch = NewChargeBuilder().WithNCRFSurchargeType().Build()
	require.False(t, ch.IsSurplusTaxSurcharge())

	// With MCCA surcharge
	ch = NewChargeBuilder().WithMCCASurchargeType().Build()
	require.False(t, ch.IsSurplusTaxSurcharge())

	// With Stamping Fee surcharge
	ch = NewChargeBuilder().WithStampingFeeSurchargeType().Build()
	require.False(t, ch.IsSurplusTaxSurcharge())

	// With Surplus Tax surcharge
	ch = NewChargeBuilder().WithSurplusTaxSurchargeType().Build()
	require.True(t, ch.IsSurplusTaxSurcharge())

	// With Stamping Fee surcharge from fully earned premium
	ch = NewChargeBuilder().WithStampingFeeFromFullyEarnedPremiumSurchargeType().Build()
	require.False(t, ch.IsSurplusTaxSurcharge())

	// With Surplus Tax surcharge from fully earned premium
	ch = NewChargeBuilder().WithSurplusTaxFromFullyEarnedPremiumSurchargeType().Build()
	require.False(t, ch.IsSurplusTaxSurcharge())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.IsSurplusTaxSurcharge())
}

func TestIsFeeChargeStampingFeeSurcharge(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.IsStampingFeeSurchargeFromFullyEarnedPremium())

	// With base charge type
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.IsStampingFeeSurchargeFromFullyEarnedPremium())

	// With unspecified surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.IsStampingFeeSurchargeFromFullyEarnedPremium())

	// With NCRF surcharge
	ch = NewChargeBuilder().WithNCRFSurchargeType().Build()
	require.False(t, ch.IsStampingFeeSurchargeFromFullyEarnedPremium())

	// With MCCA surcharge
	ch = NewChargeBuilder().WithMCCASurchargeType().Build()
	require.False(t, ch.IsStampingFeeSurchargeFromFullyEarnedPremium())

	// With Stamping Fee surcharge
	ch = NewChargeBuilder().WithStampingFeeSurchargeType().Build()
	require.False(t, ch.IsStampingFeeSurchargeFromFullyEarnedPremium())

	// With Surplus Tax surcharge
	ch = NewChargeBuilder().WithSurplusTaxSurchargeType().Build()
	require.False(t, ch.IsStampingFeeSurchargeFromFullyEarnedPremium())

	// With Stamping Fee surcharge from fully earned premium
	ch = NewChargeBuilder().WithStampingFeeFromFullyEarnedPremiumSurchargeType().Build()
	require.True(t, ch.IsStampingFeeSurchargeFromFullyEarnedPremium())

	// With Surplus Tax surcharge from fully earned premium
	ch = NewChargeBuilder().WithSurplusTaxFromFullyEarnedPremiumSurchargeType().Build()
	require.False(t, ch.IsStampingFeeSurchargeFromFullyEarnedPremium())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.IsStampingFeeSurchargeFromFullyEarnedPremium())
}

func TestIsFeeChargeSurplusTaxSurcharge(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.IsSurplusTaxSurchargeFromFullyEarnedPremium())

	// With base charge type
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.IsSurplusTaxSurchargeFromFullyEarnedPremium())

	// With unspecified surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.IsSurplusTaxSurchargeFromFullyEarnedPremium())

	// With NCRF surcharge
	ch = NewChargeBuilder().WithNCRFSurchargeType().Build()
	require.False(t, ch.IsSurplusTaxSurchargeFromFullyEarnedPremium())

	// With MCCA surcharge
	ch = NewChargeBuilder().WithMCCASurchargeType().Build()
	require.False(t, ch.IsSurplusTaxSurchargeFromFullyEarnedPremium())

	// With Stamping Fee surcharge
	ch = NewChargeBuilder().WithStampingFeeSurchargeType().Build()
	require.False(t, ch.IsSurplusTaxSurchargeFromFullyEarnedPremium())

	// With Surplus Tax surcharge
	ch = NewChargeBuilder().WithSurplusTaxSurchargeType().Build()
	require.False(t, ch.IsSurplusTaxSurchargeFromFullyEarnedPremium())

	// With Stamping Fee surcharge from fully earned premium
	ch = NewChargeBuilder().WithStampingFeeFromFullyEarnedPremiumSurchargeType().Build()
	require.False(t, ch.IsSurplusTaxSurchargeFromFullyEarnedPremium())

	// With Surplus Tax surcharge from fully earned premium
	ch = NewChargeBuilder().WithSurplusTaxFromFullyEarnedPremiumSurchargeType().Build()
	require.True(t, ch.IsSurplusTaxSurchargeFromFullyEarnedPremium())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.IsSurplusTaxSurchargeFromFullyEarnedPremium())
}

func TestAppliesToSubCoverageGroup(t *testing.T) {
	// With nil charge chargeable item
	ch := NewChargeBuilder().Build()
	require.False(t, ch.AppliesToSubCoverageGroup())

	// With sub-coverage chargeable item
	ch = NewChargeBuilder().WithDefaultChargeableSubCoverageGroup_TestOnly().Build()
	require.True(t, ch.AppliesToSubCoverageGroup())

	// With chargeable policy item
	ch = NewChargeBuilder().WithDefaultChargeablePolicy_TestOnly().Build()
	require.False(t, ch.AppliesToSubCoverageGroup())

	// With manual endorsement charge type (no charged item)
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.AppliesToSubCoverageGroup())
}

func TestAppliesToPolicy(t *testing.T) {
	// With nil charge chargeable item
	ch := NewChargeBuilder().Build()
	require.False(t, ch.AppliesToPolicy())

	// With sub-coverage chargeable item
	ch = NewChargeBuilder().WithDefaultChargeableSubCoverageGroup_TestOnly().Build()
	require.False(t, ch.AppliesToPolicy())

	// With chargeable policy item
	ch = NewChargeBuilder().WithDefaultChargeablePolicy_TestOnly().Build()
	require.True(t, ch.AppliesToPolicy())

	// With manual endorsement charge type (no charged item)
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.AppliesToPolicy())
}

func TestAppliesToBlanketRegularAdditionalInsured(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.AppliesToBlanketRegularAdditionalInsured())

	// With base charge type with no extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.AppliesToBlanketRegularAdditionalInsured())

	// With base charge type with BRAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketRegularAdditionalInsured().Build()
	require.True(t, ch.AppliesToBlanketRegularAdditionalInsured())

	// With base charge type with BPNCAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().Build()
	require.False(t, ch.AppliesToBlanketRegularAdditionalInsured())

	// With base charge type with BWOS extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketWaiverOfSubrogation().Build()
	require.False(t, ch.AppliesToBlanketRegularAdditionalInsured())

	// With base charge type with SRAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketRegularAdditionalInsured())

	// With base charge type with SPNCAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketRegularAdditionalInsured())

	// With base charge type with SWOS extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketRegularAdditionalInsured())

	// With surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketRegularAdditionalInsured())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.AppliesToBlanketRegularAdditionalInsured())
}

func TestAppliesToBlanketPrimaryAndNonContributoryAdditionalInsured(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with no extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with BRAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketRegularAdditionalInsured().Build()
	require.False(t, ch.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with BPNCAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().Build()
	require.True(t, ch.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with BWOS extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketWaiverOfSubrogation().Build()
	require.False(t, ch.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with SRAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with SPNCAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with SWOS extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured())

	// With surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured())
}

func TestAppliesToBlanketWaiverOfSubrogation(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.AppliesToBlanketWaiverOfSubrogation())

	// With base charge type with no extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.AppliesToBlanketWaiverOfSubrogation())

	// With base charge type with BRAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketRegularAdditionalInsured().Build()
	require.False(t, ch.AppliesToBlanketWaiverOfSubrogation())

	// With base charge type with BPNCAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().Build()
	require.False(t, ch.AppliesToBlanketWaiverOfSubrogation())

	// With base charge type with BWOS extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketWaiverOfSubrogation().Build()
	require.True(t, ch.AppliesToBlanketWaiverOfSubrogation())

	// With base charge type with SRAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketWaiverOfSubrogation())

	// With base charge type with SPNCAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketWaiverOfSubrogation())

	// With base charge type with SWOS extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketWaiverOfSubrogation())

	// With surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.AppliesToBlanketWaiverOfSubrogation())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.AppliesToBlanketWaiverOfSubrogation())
}

func TestAppliesToSpecifiedRegularAdditionalInsured(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.AppliesToSpecifiedRegularAdditionalInsured())

	// With base charge type with no extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.AppliesToSpecifiedRegularAdditionalInsured())

	// With base charge type with BRAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketRegularAdditionalInsured().Build()
	require.False(t, ch.AppliesToSpecifiedRegularAdditionalInsured())

	// With base charge type with BPNCAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().Build()
	require.False(t, ch.AppliesToSpecifiedRegularAdditionalInsured())

	// With base charge type with BWOS extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketWaiverOfSubrogation().Build()
	require.False(t, ch.AppliesToSpecifiedRegularAdditionalInsured())

	// With base charge type with SRAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().Build()
	require.True(t, ch.AppliesToSpecifiedRegularAdditionalInsured())

	// With base charge type with SPNCAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().Build()
	require.False(t, ch.AppliesToSpecifiedRegularAdditionalInsured())

	// With base charge type with SWOS extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().Build()
	require.False(t, ch.AppliesToSpecifiedRegularAdditionalInsured())

	// With surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.AppliesToSpecifiedRegularAdditionalInsured())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.AppliesToSpecifiedRegularAdditionalInsured())
}

func TestAppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with no extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with BRAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketRegularAdditionalInsured().Build()
	require.False(t, ch.AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with BPNCAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().Build()
	require.False(t, ch.AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with BWOS extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketWaiverOfSubrogation().Build()
	require.False(t, ch.AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with SRAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().Build()
	require.False(t, ch.AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with SPNCAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().Build()
	require.True(t, ch.AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured())

	// With base charge type with SWOS extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().Build()
	require.False(t, ch.AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured())

	// With surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured())
}

func TestAppliesToSpecifiedThirdPartyWithWaiverOfSubrogation(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation())

	// With base charge type with no extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation())

	// With base charge type with BRAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketRegularAdditionalInsured().Build()
	require.False(t, ch.AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation())

	// With base charge type with BPNCAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().Build()
	require.False(t, ch.AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation())

	// With base charge type with BWOS extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketWaiverOfSubrogation().Build()
	require.False(t, ch.AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation())

	// With base charge type with SRAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().Build()
	require.False(t, ch.AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation())

	// With base charge type with SPNCAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().Build()
	require.False(t, ch.AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation())

	// With base charge type with SWOS extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().Build()
	require.True(t, ch.AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation())

	// With surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation())
}

func TestIsFeeCharge(t *testing.T) {
	// With nil charge type
	ch := NewChargeBuilder().Build()
	require.False(t, ch.IsFullyEarnedCharge())

	// With base charge type with no extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithoutExtraInfo().Build()
	require.False(t, ch.IsFullyEarnedCharge())

	// With base charge type with BRAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketRegularAdditionalInsured().Build()
	require.True(t, ch.IsFullyEarnedCharge())

	// With base charge type with BPNCAI extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured().Build()
	require.True(t, ch.IsFullyEarnedCharge())

	// With base charge type with BWOS extra info
	ch = NewChargeBuilder().WithBaseChargeTypeWithBlanketWaiverOfSubrogation().Build()
	require.True(t, ch.IsFullyEarnedCharge())

	// With base charge type with SRAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedRegularAdditionalInsured_TestOnly().Build()
	require.True(t, ch.IsFullyEarnedCharge())

	// With base charge type with SPNCAI extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured_TestOnly().Build()
	require.True(t, ch.IsFullyEarnedCharge())

	// With base charge type with SWOS extra info
	ch = NewChargeBuilder().WithDefaultBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation_TestOnly().Build()
	require.True(t, ch.IsFullyEarnedCharge())

	// With surcharge type
	ch = NewChargeBuilder().WithDefaultSurchargeType_TestOnly().Build()
	require.False(t, ch.IsFullyEarnedCharge())

	// With manual endorsement charge type
	ch = NewChargeBuilder().WithManualEndorsementChargeType().Build()
	require.False(t, ch.IsFullyEarnedCharge())
}
