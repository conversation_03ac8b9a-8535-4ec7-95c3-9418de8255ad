// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pricing/charges.proto

package ptypes

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	proto "nirvanatech.com/nirvana/common-go/proto"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RateBasis int32

const (
	RateBasis_RateBasis_Unspecified RateBasis = 0
	RateBasis_RateBasis_Miles       RateBasis = 1
	RateBasis_RateBasis_TIV_Days    RateBasis = 2
	RateBasis_RateBasis_Premium     RateBasis = 3
)

// Enum value maps for RateBasis.
var (
	RateBasis_name = map[int32]string{
		0: "RateBasis_Unspecified",
		1: "RateBasis_Miles",
		2: "RateBasis_TIV_Days",
		3: "RateBasis_Premium",
	}
	RateBasis_value = map[string]int32{
		"RateBasis_Unspecified": 0,
		"RateBasis_Miles":       1,
		"RateBasis_TIV_Days":    2,
		"RateBasis_Premium":     3,
	}
)

func (x RateBasis) Enum() *RateBasis {
	p := new(RateBasis)
	*p = x
	return p
}

func (x RateBasis) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RateBasis) Descriptor() protoreflect.EnumDescriptor {
	return file_pricing_charges_proto_enumTypes[0].Descriptor()
}

func (RateBasis) Type() protoreflect.EnumType {
	return &file_pricing_charges_proto_enumTypes[0]
}

func (x RateBasis) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RateBasis.Descriptor instead.
func (RateBasis) EnumDescriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{0}
}

type Charge_DistributionType int32

const (
	Charge_DistributionType_Unspecified Charge_DistributionType = 0
	Charge_DistributionType_Vehicle     Charge_DistributionType = 1
)

// Enum value maps for Charge_DistributionType.
var (
	Charge_DistributionType_name = map[int32]string{
		0: "DistributionType_Unspecified",
		1: "DistributionType_Vehicle",
	}
	Charge_DistributionType_value = map[string]int32{
		"DistributionType_Unspecified": 0,
		"DistributionType_Vehicle":     1,
	}
)

func (x Charge_DistributionType) Enum() *Charge_DistributionType {
	p := new(Charge_DistributionType)
	*p = x
	return p
}

func (x Charge_DistributionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Charge_DistributionType) Descriptor() protoreflect.EnumDescriptor {
	return file_pricing_charges_proto_enumTypes[1].Descriptor()
}

func (Charge_DistributionType) Type() protoreflect.EnumType {
	return &file_pricing_charges_proto_enumTypes[1]
}

func (x Charge_DistributionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Charge_DistributionType.Descriptor instead.
func (Charge_DistributionType) EnumDescriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{0, 0}
}

type Surcharge_Type int32

const (
	Surcharge_Type_Unspecified             Surcharge_Type = 0
	Surcharge_Type_NCRF                    Surcharge_Type = 1
	Surcharge_Type_MCCA                    Surcharge_Type = 2
	Surcharge_Type_STAMPING_FEE            Surcharge_Type = 3
	Surcharge_Type_SURPLUS_TAX             Surcharge_Type = 4
	Surcharge_Type_FEE_CHARGE_STAMPING_FEE Surcharge_Type = 5
	Surcharge_Type_FEE_CHARGE_SURPLUS_TAX  Surcharge_Type = 6
)

// Enum value maps for Surcharge_Type.
var (
	Surcharge_Type_name = map[int32]string{
		0: "Type_Unspecified",
		1: "Type_NCRF",
		2: "Type_MCCA",
		3: "Type_STAMPING_FEE",
		4: "Type_SURPLUS_TAX",
		5: "Type_FEE_CHARGE_STAMPING_FEE",
		6: "Type_FEE_CHARGE_SURPLUS_TAX",
	}
	Surcharge_Type_value = map[string]int32{
		"Type_Unspecified":             0,
		"Type_NCRF":                    1,
		"Type_MCCA":                    2,
		"Type_STAMPING_FEE":            3,
		"Type_SURPLUS_TAX":             4,
		"Type_FEE_CHARGE_STAMPING_FEE": 5,
		"Type_FEE_CHARGE_SURPLUS_TAX":  6,
	}
)

func (x Surcharge_Type) Enum() *Surcharge_Type {
	p := new(Surcharge_Type)
	*p = x
	return p
}

func (x Surcharge_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Surcharge_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pricing_charges_proto_enumTypes[2].Descriptor()
}

func (Surcharge_Type) Type() protoreflect.EnumType {
	return &file_pricing_charges_proto_enumTypes[2]
}

func (x Surcharge_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Surcharge_Type.Descriptor instead.
func (Surcharge_Type) EnumDescriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{11, 0}
}

type Charge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Distributions []*Charge_Distribution `protobuf:"bytes,2,rep,name=distributions,proto3" json:"distributions,omitempty"`
	// Types that are assignable to BillingDetails:
	//
	//	*Charge_AmountBasedBillingDetails
	//	*Charge_RateBasedBillingDetails
	BillingDetails isCharge_BillingDetails `protobuf_oneof:"billingDetails"`
	// Types that are assignable to Type:
	//
	//	*Charge_BaseCharge
	//	*Charge_Surcharge
	//	*Charge_ManualEndorsementCharge
	Type isCharge_Type `protobuf_oneof:"type"`
	// Types that are assignable to ChargedItem:
	//
	//	*Charge_ChargedSubCoverageGroup
	//	*Charge_ChargedPolicy
	ChargedItem isCharge_ChargedItem `protobuf_oneof:"chargedItem"`
}

func (x *Charge) Reset() {
	*x = Charge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Charge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Charge) ProtoMessage() {}

func (x *Charge) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Charge.ProtoReflect.Descriptor instead.
func (*Charge) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{0}
}

func (x *Charge) GetDistributions() []*Charge_Distribution {
	if x != nil {
		return x.Distributions
	}
	return nil
}

func (m *Charge) GetBillingDetails() isCharge_BillingDetails {
	if m != nil {
		return m.BillingDetails
	}
	return nil
}

func (x *Charge) GetAmountBasedBillingDetails() *AmountBasedBillingDetails {
	if x, ok := x.GetBillingDetails().(*Charge_AmountBasedBillingDetails); ok {
		return x.AmountBasedBillingDetails
	}
	return nil
}

func (x *Charge) GetRateBasedBillingDetails() *RateBasedBillingDetails {
	if x, ok := x.GetBillingDetails().(*Charge_RateBasedBillingDetails); ok {
		return x.RateBasedBillingDetails
	}
	return nil
}

func (m *Charge) GetType() isCharge_Type {
	if m != nil {
		return m.Type
	}
	return nil
}

func (x *Charge) GetBaseCharge() *BaseCharge {
	if x, ok := x.GetType().(*Charge_BaseCharge); ok {
		return x.BaseCharge
	}
	return nil
}

func (x *Charge) GetSurcharge() *Surcharge {
	if x, ok := x.GetType().(*Charge_Surcharge); ok {
		return x.Surcharge
	}
	return nil
}

func (x *Charge) GetManualEndorsementCharge() *ManualEndorsementCharge {
	if x, ok := x.GetType().(*Charge_ManualEndorsementCharge); ok {
		return x.ManualEndorsementCharge
	}
	return nil
}

func (m *Charge) GetChargedItem() isCharge_ChargedItem {
	if m != nil {
		return m.ChargedItem
	}
	return nil
}

func (x *Charge) GetChargedSubCoverageGroup() *ChargeableSubCoverageGroup {
	if x, ok := x.GetChargedItem().(*Charge_ChargedSubCoverageGroup); ok {
		return x.ChargedSubCoverageGroup
	}
	return nil
}

func (x *Charge) GetChargedPolicy() *ChargeablePolicy {
	if x, ok := x.GetChargedItem().(*Charge_ChargedPolicy); ok {
		return x.ChargedPolicy
	}
	return nil
}

type isCharge_BillingDetails interface {
	isCharge_BillingDetails()
}

type Charge_AmountBasedBillingDetails struct {
	AmountBasedBillingDetails *AmountBasedBillingDetails `protobuf:"bytes,3,opt,name=amountBasedBillingDetails,proto3,oneof"`
}

type Charge_RateBasedBillingDetails struct {
	RateBasedBillingDetails *RateBasedBillingDetails `protobuf:"bytes,4,opt,name=rateBasedBillingDetails,proto3,oneof"`
}

func (*Charge_AmountBasedBillingDetails) isCharge_BillingDetails() {}

func (*Charge_RateBasedBillingDetails) isCharge_BillingDetails() {}

type isCharge_Type interface {
	isCharge_Type()
}

type Charge_BaseCharge struct {
	BaseCharge *BaseCharge `protobuf:"bytes,5,opt,name=baseCharge,proto3,oneof"`
}

type Charge_Surcharge struct {
	Surcharge *Surcharge `protobuf:"bytes,6,opt,name=surcharge,proto3,oneof"`
}

type Charge_ManualEndorsementCharge struct {
	ManualEndorsementCharge *ManualEndorsementCharge `protobuf:"bytes,9,opt,name=manualEndorsementCharge,proto3,oneof"`
}

func (*Charge_BaseCharge) isCharge_Type() {}

func (*Charge_Surcharge) isCharge_Type() {}

func (*Charge_ManualEndorsementCharge) isCharge_Type() {}

type isCharge_ChargedItem interface {
	isCharge_ChargedItem()
}

type Charge_ChargedSubCoverageGroup struct {
	ChargedSubCoverageGroup *ChargeableSubCoverageGroup `protobuf:"bytes,7,opt,name=chargedSubCoverageGroup,proto3,oneof"`
}

type Charge_ChargedPolicy struct {
	ChargedPolicy *ChargeablePolicy `protobuf:"bytes,8,opt,name=chargedPolicy,proto3,oneof"`
}

func (*Charge_ChargedSubCoverageGroup) isCharge_ChargedItem() {}

func (*Charge_ChargedPolicy) isCharge_ChargedItem() {}

type AmountBasedBillingDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date             *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Amount           string                 `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	UnmodifiedAmount *string                `protobuf:"bytes,3,opt,name=unmodifiedAmount,proto3,oneof" json:"unmodifiedAmount,omitempty"`
}

func (x *AmountBasedBillingDetails) Reset() {
	*x = AmountBasedBillingDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmountBasedBillingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmountBasedBillingDetails) ProtoMessage() {}

func (x *AmountBasedBillingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmountBasedBillingDetails.ProtoReflect.Descriptor instead.
func (*AmountBasedBillingDetails) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{1}
}

func (x *AmountBasedBillingDetails) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *AmountBasedBillingDetails) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *AmountBasedBillingDetails) GetUnmodifiedAmount() string {
	if x != nil && x.UnmodifiedAmount != nil {
		return *x.UnmodifiedAmount
	}
	return ""
}

type RateBasedBillingDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dates          *proto.Interval `protobuf:"bytes,1,opt,name=dates,proto3" json:"dates,omitempty"`
	Rate           string          `protobuf:"bytes,2,opt,name=rate,proto3" json:"rate,omitempty"`
	UnmodifiedRate *string         `protobuf:"bytes,3,opt,name=unmodifiedRate,proto3,oneof" json:"unmodifiedRate,omitempty"`
	RateBasis      RateBasis       `protobuf:"varint,4,opt,name=rateBasis,proto3,enum=pricing.RateBasis" json:"rateBasis,omitempty"`
}

func (x *RateBasedBillingDetails) Reset() {
	*x = RateBasedBillingDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateBasedBillingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateBasedBillingDetails) ProtoMessage() {}

func (x *RateBasedBillingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateBasedBillingDetails.ProtoReflect.Descriptor instead.
func (*RateBasedBillingDetails) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{2}
}

func (x *RateBasedBillingDetails) GetDates() *proto.Interval {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *RateBasedBillingDetails) GetRate() string {
	if x != nil {
		return x.Rate
	}
	return ""
}

func (x *RateBasedBillingDetails) GetUnmodifiedRate() string {
	if x != nil && x.UnmodifiedRate != nil {
		return *x.UnmodifiedRate
	}
	return ""
}

func (x *RateBasedBillingDetails) GetRateBasis() RateBasis {
	if x != nil {
		return x.RateBasis
	}
	return RateBasis_RateBasis_Unspecified
}

type RateBasisValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*RateBasisValue_Miles
	//	*RateBasisValue_TivDays
	//	*RateBasisValue_Premium
	Value isRateBasisValue_Value `protobuf_oneof:"value"`
}

func (x *RateBasisValue) Reset() {
	*x = RateBasisValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateBasisValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateBasisValue) ProtoMessage() {}

func (x *RateBasisValue) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateBasisValue.ProtoReflect.Descriptor instead.
func (*RateBasisValue) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{3}
}

func (m *RateBasisValue) GetValue() isRateBasisValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *RateBasisValue) GetMiles() int64 {
	if x, ok := x.GetValue().(*RateBasisValue_Miles); ok {
		return x.Miles
	}
	return 0
}

func (x *RateBasisValue) GetTivDays() int64 {
	if x, ok := x.GetValue().(*RateBasisValue_TivDays); ok {
		return x.TivDays
	}
	return 0
}

func (x *RateBasisValue) GetPremium() int64 {
	if x, ok := x.GetValue().(*RateBasisValue_Premium); ok {
		return x.Premium
	}
	return 0
}

type isRateBasisValue_Value interface {
	isRateBasisValue_Value()
}

type RateBasisValue_Miles struct {
	Miles int64 `protobuf:"varint,1,opt,name=miles,proto3,oneof"`
}

type RateBasisValue_TivDays struct {
	TivDays int64 `protobuf:"varint,2,opt,name=tivDays,proto3,oneof"`
}

type RateBasisValue_Premium struct {
	Premium int64 `protobuf:"varint,3,opt,name=premium,proto3,oneof"`
}

func (*RateBasisValue_Miles) isRateBasisValue_Value() {}

func (*RateBasisValue_TivDays) isRateBasisValue_Value() {}

func (*RateBasisValue_Premium) isRateBasisValue_Value() {}

type BaseCharge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to ExtraInfo:
	//
	//	*BaseCharge_BlanketRegularAdditionalInsuredInfo
	//	*BaseCharge_BlanketPrimaryAndNonContributoryAdditionalInsuredInfo
	//	*BaseCharge_BlanketWaiverOfSubrogationInfo
	//	*BaseCharge_SpecifiedRegularAdditionalInsuredInfo
	//	*BaseCharge_SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo
	//	*BaseCharge_SpecifiedThirdPartyWithWaiverOfSubrogationInfo
	ExtraInfo isBaseCharge_ExtraInfo `protobuf_oneof:"extraInfo"`
}

func (x *BaseCharge) Reset() {
	*x = BaseCharge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseCharge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseCharge) ProtoMessage() {}

func (x *BaseCharge) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseCharge.ProtoReflect.Descriptor instead.
func (*BaseCharge) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{4}
}

func (m *BaseCharge) GetExtraInfo() isBaseCharge_ExtraInfo {
	if m != nil {
		return m.ExtraInfo
	}
	return nil
}

func (x *BaseCharge) GetBlanketRegularAdditionalInsuredInfo() *BlanketRegularAdditionalInsuredInfo {
	if x, ok := x.GetExtraInfo().(*BaseCharge_BlanketRegularAdditionalInsuredInfo); ok {
		return x.BlanketRegularAdditionalInsuredInfo
	}
	return nil
}

func (x *BaseCharge) GetBlanketPrimaryAndNonContributoryAdditionalInsuredInfo() *BlanketPrimaryAndNonContributoryAdditionalInsuredInfo {
	if x, ok := x.GetExtraInfo().(*BaseCharge_BlanketPrimaryAndNonContributoryAdditionalInsuredInfo); ok {
		return x.BlanketPrimaryAndNonContributoryAdditionalInsuredInfo
	}
	return nil
}

func (x *BaseCharge) GetBlanketWaiverOfSubrogationInfo() *BlanketWaiverOfSubrogationInfo {
	if x, ok := x.GetExtraInfo().(*BaseCharge_BlanketWaiverOfSubrogationInfo); ok {
		return x.BlanketWaiverOfSubrogationInfo
	}
	return nil
}

func (x *BaseCharge) GetSpecifiedRegularAdditionalInsuredInfo() *SpecifiedRegularAdditionalInsuredInfo {
	if x, ok := x.GetExtraInfo().(*BaseCharge_SpecifiedRegularAdditionalInsuredInfo); ok {
		return x.SpecifiedRegularAdditionalInsuredInfo
	}
	return nil
}

func (x *BaseCharge) GetSpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo() *SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo {
	if x, ok := x.GetExtraInfo().(*BaseCharge_SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo); ok {
		return x.SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo
	}
	return nil
}

func (x *BaseCharge) GetSpecifiedThirdPartyWithWaiverOfSubrogationInfo() *SpecifiedThirdPartyWithWaiverOfSubrogationInfo {
	if x, ok := x.GetExtraInfo().(*BaseCharge_SpecifiedThirdPartyWithWaiverOfSubrogationInfo); ok {
		return x.SpecifiedThirdPartyWithWaiverOfSubrogationInfo
	}
	return nil
}

type isBaseCharge_ExtraInfo interface {
	isBaseCharge_ExtraInfo()
}

type BaseCharge_BlanketRegularAdditionalInsuredInfo struct {
	BlanketRegularAdditionalInsuredInfo *BlanketRegularAdditionalInsuredInfo `protobuf:"bytes,1,opt,name=blanketRegularAdditionalInsuredInfo,proto3,oneof"`
}

type BaseCharge_BlanketPrimaryAndNonContributoryAdditionalInsuredInfo struct {
	BlanketPrimaryAndNonContributoryAdditionalInsuredInfo *BlanketPrimaryAndNonContributoryAdditionalInsuredInfo `protobuf:"bytes,2,opt,name=blanketPrimaryAndNonContributoryAdditionalInsuredInfo,proto3,oneof"`
}

type BaseCharge_BlanketWaiverOfSubrogationInfo struct {
	BlanketWaiverOfSubrogationInfo *BlanketWaiverOfSubrogationInfo `protobuf:"bytes,3,opt,name=blanketWaiverOfSubrogationInfo,proto3,oneof"`
}

type BaseCharge_SpecifiedRegularAdditionalInsuredInfo struct {
	SpecifiedRegularAdditionalInsuredInfo *SpecifiedRegularAdditionalInsuredInfo `protobuf:"bytes,4,opt,name=specifiedRegularAdditionalInsuredInfo,proto3,oneof"`
}

type BaseCharge_SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo struct {
	SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo *SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo `protobuf:"bytes,5,opt,name=specifiedPrimaryAndNonContributoryAdditionalInsuredInfo,proto3,oneof"`
}

type BaseCharge_SpecifiedThirdPartyWithWaiverOfSubrogationInfo struct {
	SpecifiedThirdPartyWithWaiverOfSubrogationInfo *SpecifiedThirdPartyWithWaiverOfSubrogationInfo `protobuf:"bytes,6,opt,name=specifiedThirdPartyWithWaiverOfSubrogationInfo,proto3,oneof"`
}

func (*BaseCharge_BlanketRegularAdditionalInsuredInfo) isBaseCharge_ExtraInfo() {}

func (*BaseCharge_BlanketPrimaryAndNonContributoryAdditionalInsuredInfo) isBaseCharge_ExtraInfo() {}

func (*BaseCharge_BlanketWaiverOfSubrogationInfo) isBaseCharge_ExtraInfo() {}

func (*BaseCharge_SpecifiedRegularAdditionalInsuredInfo) isBaseCharge_ExtraInfo() {}

func (*BaseCharge_SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo) isBaseCharge_ExtraInfo() {}

func (*BaseCharge_SpecifiedThirdPartyWithWaiverOfSubrogationInfo) isBaseCharge_ExtraInfo() {}

type BlanketRegularAdditionalInsuredInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BlanketRegularAdditionalInsuredInfo) Reset() {
	*x = BlanketRegularAdditionalInsuredInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlanketRegularAdditionalInsuredInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlanketRegularAdditionalInsuredInfo) ProtoMessage() {}

func (x *BlanketRegularAdditionalInsuredInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlanketRegularAdditionalInsuredInfo.ProtoReflect.Descriptor instead.
func (*BlanketRegularAdditionalInsuredInfo) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{5}
}

type BlanketPrimaryAndNonContributoryAdditionalInsuredInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BlanketPrimaryAndNonContributoryAdditionalInsuredInfo) Reset() {
	*x = BlanketPrimaryAndNonContributoryAdditionalInsuredInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlanketPrimaryAndNonContributoryAdditionalInsuredInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlanketPrimaryAndNonContributoryAdditionalInsuredInfo) ProtoMessage() {}

func (x *BlanketPrimaryAndNonContributoryAdditionalInsuredInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlanketPrimaryAndNonContributoryAdditionalInsuredInfo.ProtoReflect.Descriptor instead.
func (*BlanketPrimaryAndNonContributoryAdditionalInsuredInfo) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{6}
}

type BlanketWaiverOfSubrogationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BlanketWaiverOfSubrogationInfo) Reset() {
	*x = BlanketWaiverOfSubrogationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlanketWaiverOfSubrogationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlanketWaiverOfSubrogationInfo) ProtoMessage() {}

func (x *BlanketWaiverOfSubrogationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlanketWaiverOfSubrogationInfo.ProtoReflect.Descriptor instead.
func (*BlanketWaiverOfSubrogationInfo) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{7}
}

type SpecifiedRegularAdditionalInsuredInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalInsuredId string `protobuf:"bytes,1,opt,name=additionalInsuredId,proto3" json:"additionalInsuredId,omitempty"`
}

func (x *SpecifiedRegularAdditionalInsuredInfo) Reset() {
	*x = SpecifiedRegularAdditionalInsuredInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpecifiedRegularAdditionalInsuredInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecifiedRegularAdditionalInsuredInfo) ProtoMessage() {}

func (x *SpecifiedRegularAdditionalInsuredInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecifiedRegularAdditionalInsuredInfo.ProtoReflect.Descriptor instead.
func (*SpecifiedRegularAdditionalInsuredInfo) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{8}
}

func (x *SpecifiedRegularAdditionalInsuredInfo) GetAdditionalInsuredId() string {
	if x != nil {
		return x.AdditionalInsuredId
	}
	return ""
}

type SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalInsuredId string `protobuf:"bytes,1,opt,name=additionalInsuredId,proto3" json:"additionalInsuredId,omitempty"`
}

func (x *SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo) Reset() {
	*x = SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo) ProtoMessage() {}

func (x *SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo.ProtoReflect.Descriptor instead.
func (*SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{9}
}

func (x *SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo) GetAdditionalInsuredId() string {
	if x != nil {
		return x.AdditionalInsuredId
	}
	return ""
}

type SpecifiedThirdPartyWithWaiverOfSubrogationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ThirdPartyId string `protobuf:"bytes,2,opt,name=thirdPartyId,proto3" json:"thirdPartyId,omitempty"`
}

func (x *SpecifiedThirdPartyWithWaiverOfSubrogationInfo) Reset() {
	*x = SpecifiedThirdPartyWithWaiverOfSubrogationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpecifiedThirdPartyWithWaiverOfSubrogationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecifiedThirdPartyWithWaiverOfSubrogationInfo) ProtoMessage() {}

func (x *SpecifiedThirdPartyWithWaiverOfSubrogationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecifiedThirdPartyWithWaiverOfSubrogationInfo.ProtoReflect.Descriptor instead.
func (*SpecifiedThirdPartyWithWaiverOfSubrogationInfo) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{10}
}

func (x *SpecifiedThirdPartyWithWaiverOfSubrogationInfo) GetThirdPartyId() string {
	if x != nil {
		return x.ThirdPartyId
	}
	return ""
}

type Surcharge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type Surcharge_Type `protobuf:"varint,1,opt,name=type,proto3,enum=pricing.Surcharge_Type" json:"type,omitempty"`
}

func (x *Surcharge) Reset() {
	*x = Surcharge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Surcharge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Surcharge) ProtoMessage() {}

func (x *Surcharge) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Surcharge.ProtoReflect.Descriptor instead.
func (*Surcharge) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{11}
}

func (x *Surcharge) GetType() Surcharge_Type {
	if x != nil {
		return x.Type
	}
	return Surcharge_Type_Unspecified
}

type ManualEndorsementCharge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ManualEndorsementCharge) Reset() {
	*x = ManualEndorsementCharge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualEndorsementCharge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualEndorsementCharge) ProtoMessage() {}

func (x *ManualEndorsementCharge) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualEndorsementCharge.ProtoReflect.Descriptor instead.
func (*ManualEndorsementCharge) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{12}
}

type ChargeableSubCoverageGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group *SubCoverageGroup `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
}

func (x *ChargeableSubCoverageGroup) Reset() {
	*x = ChargeableSubCoverageGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChargeableSubCoverageGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChargeableSubCoverageGroup) ProtoMessage() {}

func (x *ChargeableSubCoverageGroup) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChargeableSubCoverageGroup.ProtoReflect.Descriptor instead.
func (*ChargeableSubCoverageGroup) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{13}
}

func (x *ChargeableSubCoverageGroup) GetGroup() *SubCoverageGroup {
	if x != nil {
		return x.Group
	}
	return nil
}

type ChargeablePolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PolicyNumber string `protobuf:"bytes,1,opt,name=policyNumber,proto3" json:"policyNumber,omitempty"`
}

func (x *ChargeablePolicy) Reset() {
	*x = ChargeablePolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChargeablePolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChargeablePolicy) ProtoMessage() {}

func (x *ChargeablePolicy) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChargeablePolicy.ProtoReflect.Descriptor instead.
func (*ChargeablePolicy) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{14}
}

func (x *ChargeablePolicy) GetPolicyNumber() string {
	if x != nil {
		return x.PolicyNumber
	}
	return ""
}

type Charge_Distribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  Charge_DistributionType    `protobuf:"varint,1,opt,name=type,proto3,enum=pricing.Charge_DistributionType" json:"type,omitempty"`
	Items []*Charge_DistributionItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *Charge_Distribution) Reset() {
	*x = Charge_Distribution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Charge_Distribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Charge_Distribution) ProtoMessage() {}

func (x *Charge_Distribution) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Charge_Distribution.ProtoReflect.Descriptor instead.
func (*Charge_Distribution) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Charge_Distribution) GetType() Charge_DistributionType {
	if x != nil {
		return x.Type
	}
	return Charge_DistributionType_Unspecified
}

func (x *Charge_Distribution) GetItems() []*Charge_DistributionItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type Charge_DistributionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Fraction string `protobuf:"bytes,2,opt,name=fraction,proto3" json:"fraction,omitempty"`
}

func (x *Charge_DistributionItem) Reset() {
	*x = Charge_DistributionItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_charges_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Charge_DistributionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Charge_DistributionItem) ProtoMessage() {}

func (x *Charge_DistributionItem) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_charges_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Charge_DistributionItem.ProtoReflect.Descriptor instead.
func (*Charge_DistributionItem) Descriptor() ([]byte, []int) {
	return file_pricing_charges_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Charge_DistributionItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Charge_DistributionItem) GetFraction() string {
	if x != nil {
		return x.Fraction
	}
	return ""
}

var File_pricing_charges_proto protoreflect.FileDescriptor

var file_pricing_charges_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x75,
	0x62, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xbc, 0x07, 0x0a, 0x06, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x42, 0x0a, 0x0d,
	0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x62, 0x0a, 0x19, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x64, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x19, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x61, 0x73, 0x65, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x5c, 0x0a, 0x17, 0x72, 0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x64, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e,
	0x52, 0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x17, 0x72, 0x61, 0x74, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x35, 0x0a, 0x0a, 0x62, 0x61, 0x73, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x48, 0x01, 0x52, 0x0a, 0x62,
	0x61, 0x73, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x73, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x48, 0x01, 0x52, 0x09, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x5c, 0x0a,
	0x17, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x45,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x48, 0x01, 0x52, 0x17, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x5f, 0x0a, 0x17, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x61, 0x62, 0x6c,
	0x65, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x48, 0x02, 0x52, 0x17, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x53, 0x75, 0x62, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x41, 0x0a, 0x0d,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x48, 0x02,
	0x52, 0x0d, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x1a,
	0x7c, 0x0a, 0x0c, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x34, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x3e, 0x0a,
	0x10, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x52, 0x0a,
	0x10, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x10,
	0x01, 0x42, 0x10, 0x0a, 0x0e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x42, 0x06, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x4a, 0x04, 0x08, 0x01, 0x10, 0x02,
	0x22, 0xa9, 0x01, 0x0a, 0x19, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x64,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2e,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x10, 0x75, 0x6e, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x10, 0x75, 0x6e, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x75, 0x6e, 0x6d, 0x6f,
	0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc7, 0x01, 0x0a,
	0x17, 0x52, 0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x65, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x72, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x0e, 0x75, 0x6e, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e,
	0x75, 0x6e, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x30, 0x0a, 0x09, 0x72, 0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x69, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x52,
	0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x69, 0x73, 0x52, 0x09, 0x72, 0x61, 0x74, 0x65, 0x42, 0x61,
	0x73, 0x69, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x75, 0x6e, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x22, 0x69, 0x0a, 0x0e, 0x52, 0x61, 0x74, 0x65, 0x42, 0x61,
	0x73, 0x69, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x16, 0x0a, 0x05, 0x6d, 0x69, 0x6c, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05, 0x6d, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x1a, 0x0a, 0x07, 0x74, 0x69, 0x76, 0x44, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x00, 0x52, 0x07, 0x74, 0x69, 0x76, 0x44, 0x61, 0x79, 0x73, 0x12, 0x1a, 0x0a, 0x07,
	0x70, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52,
	0x07, 0x70, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0xb4, 0x07, 0x0a, 0x0a, 0x42, 0x61, 0x73, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x12, 0x80, 0x01, 0x0a, 0x23, 0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74,
	0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x23,
	0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0xb6, 0x01, 0x0a, 0x35, 0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6c,
	0x61, 0x6e, 0x6b, 0x65, 0x74, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x35, 0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x50, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x71, 0x0a, 0x1e,
	0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53,
	0x75, 0x62, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42,
	0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75,
	0x62, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52,
	0x1e, 0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66,
	0x53, 0x75, 0x62, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x86, 0x01, 0x0a, 0x25, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x52, 0x65, 0x67,
	0x75, 0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x48,
	0x00, 0x52, 0x25, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x52, 0x65, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0xbc, 0x01, 0x0a, 0x37, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64,
	0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x50, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x37,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f,
	0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0xa1, 0x01, 0x0a, 0x2e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x57,
	0x69, 0x74, 0x68, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x57, 0x69,
	0x74, 0x68, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x2e, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79,
	0x57, 0x69, 0x74, 0x68, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72,
	0x6f, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x0b, 0x0a, 0x09, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x25, 0x0a, 0x23, 0x42, 0x6c, 0x61, 0x6e,
	0x6b, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x37, 0x0a, 0x35, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x6f, 0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x20, 0x0a, 0x1e, 0x42, 0x6c, 0x61, 0x6e,
	0x6b, 0x65, 0x74, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x59, 0x0a, 0x25, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x65, 0x64, 0x49, 0x64, 0x22, 0x6b, 0x0a, 0x37, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x30, 0x0a, 0x13, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64,
	0x49, 0x64, 0x22, 0x54, 0x0a, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54,
	0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x57, 0x61, 0x69,
	0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72,
	0x74, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x68, 0x69, 0x72,
	0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x49, 0x64, 0x22, 0xe5, 0x01, 0x0a, 0x09, 0x53, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0xaa, 0x01, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x43, 0x52, 0x46, 0x10,
	0x01, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x43, 0x43, 0x41, 0x10, 0x02,
	0x12, 0x15, 0x0a, 0x11, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x49, 0x4e,
	0x47, 0x5f, 0x46, 0x45, 0x45, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x53, 0x55, 0x52, 0x50, 0x4c, 0x55, 0x53, 0x5f, 0x54, 0x41, 0x58, 0x10, 0x04, 0x12, 0x20, 0x0a,
	0x1c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x45, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x45, 0x45, 0x10, 0x05, 0x12,
	0x1f, 0x0a, 0x1b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x45, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52,
	0x47, 0x45, 0x5f, 0x53, 0x55, 0x52, 0x50, 0x4c, 0x55, 0x53, 0x5f, 0x54, 0x41, 0x58, 0x10, 0x06,
	0x22, 0x19, 0x0a, 0x17, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x22, 0x4d, 0x0a, 0x1a, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2f, 0x0a, 0x05, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x36, 0x0a, 0x10, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x22,
	0x0a, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x2a, 0x6a, 0x0a, 0x09, 0x52, 0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x69, 0x73, 0x12,
	0x19, 0x0a, 0x15, 0x52, 0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x69, 0x73, 0x5f, 0x55, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x61,
	0x74, 0x65, 0x42, 0x61, 0x73, 0x69, 0x73, 0x5f, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x10, 0x01, 0x12,
	0x16, 0x0a, 0x12, 0x52, 0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x69, 0x73, 0x5f, 0x54, 0x49, 0x56,
	0x5f, 0x44, 0x61, 0x79, 0x73, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x61, 0x74, 0x65, 0x42,
	0x61, 0x73, 0x69, 0x73, 0x5f, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x10, 0x03, 0x42, 0x33,
	0x5a, 0x31, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pricing_charges_proto_rawDescOnce sync.Once
	file_pricing_charges_proto_rawDescData = file_pricing_charges_proto_rawDesc
)

func file_pricing_charges_proto_rawDescGZIP() []byte {
	file_pricing_charges_proto_rawDescOnce.Do(func() {
		file_pricing_charges_proto_rawDescData = protoimpl.X.CompressGZIP(file_pricing_charges_proto_rawDescData)
	})
	return file_pricing_charges_proto_rawDescData
}

var file_pricing_charges_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_pricing_charges_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_pricing_charges_proto_goTypes = []interface{}{
	(RateBasis)(0),                              // 0: pricing.RateBasis
	(Charge_DistributionType)(0),                // 1: pricing.Charge.DistributionType
	(Surcharge_Type)(0),                         // 2: pricing.Surcharge.Type
	(*Charge)(nil),                              // 3: pricing.Charge
	(*AmountBasedBillingDetails)(nil),           // 4: pricing.AmountBasedBillingDetails
	(*RateBasedBillingDetails)(nil),             // 5: pricing.RateBasedBillingDetails
	(*RateBasisValue)(nil),                      // 6: pricing.RateBasisValue
	(*BaseCharge)(nil),                          // 7: pricing.BaseCharge
	(*BlanketRegularAdditionalInsuredInfo)(nil), // 8: pricing.BlanketRegularAdditionalInsuredInfo
	(*BlanketPrimaryAndNonContributoryAdditionalInsuredInfo)(nil),   // 9: pricing.BlanketPrimaryAndNonContributoryAdditionalInsuredInfo
	(*BlanketWaiverOfSubrogationInfo)(nil),                          // 10: pricing.BlanketWaiverOfSubrogationInfo
	(*SpecifiedRegularAdditionalInsuredInfo)(nil),                   // 11: pricing.SpecifiedRegularAdditionalInsuredInfo
	(*SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo)(nil), // 12: pricing.SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo
	(*SpecifiedThirdPartyWithWaiverOfSubrogationInfo)(nil),          // 13: pricing.SpecifiedThirdPartyWithWaiverOfSubrogationInfo
	(*Surcharge)(nil),                  // 14: pricing.Surcharge
	(*ManualEndorsementCharge)(nil),    // 15: pricing.ManualEndorsementCharge
	(*ChargeableSubCoverageGroup)(nil), // 16: pricing.ChargeableSubCoverageGroup
	(*ChargeablePolicy)(nil),           // 17: pricing.ChargeablePolicy
	(*Charge_Distribution)(nil),        // 18: pricing.Charge.Distribution
	(*Charge_DistributionItem)(nil),    // 19: pricing.Charge.DistributionItem
	(*timestamppb.Timestamp)(nil),      // 20: google.protobuf.Timestamp
	(*proto.Interval)(nil),             // 21: common.Interval
	(*SubCoverageGroup)(nil),           // 22: pricing.SubCoverageGroup
}
var file_pricing_charges_proto_depIdxs = []int32{
	18, // 0: pricing.Charge.distributions:type_name -> pricing.Charge.Distribution
	4,  // 1: pricing.Charge.amountBasedBillingDetails:type_name -> pricing.AmountBasedBillingDetails
	5,  // 2: pricing.Charge.rateBasedBillingDetails:type_name -> pricing.RateBasedBillingDetails
	7,  // 3: pricing.Charge.baseCharge:type_name -> pricing.BaseCharge
	14, // 4: pricing.Charge.surcharge:type_name -> pricing.Surcharge
	15, // 5: pricing.Charge.manualEndorsementCharge:type_name -> pricing.ManualEndorsementCharge
	16, // 6: pricing.Charge.chargedSubCoverageGroup:type_name -> pricing.ChargeableSubCoverageGroup
	17, // 7: pricing.Charge.chargedPolicy:type_name -> pricing.ChargeablePolicy
	20, // 8: pricing.AmountBasedBillingDetails.date:type_name -> google.protobuf.Timestamp
	21, // 9: pricing.RateBasedBillingDetails.dates:type_name -> common.Interval
	0,  // 10: pricing.RateBasedBillingDetails.rateBasis:type_name -> pricing.RateBasis
	8,  // 11: pricing.BaseCharge.blanketRegularAdditionalInsuredInfo:type_name -> pricing.BlanketRegularAdditionalInsuredInfo
	9,  // 12: pricing.BaseCharge.blanketPrimaryAndNonContributoryAdditionalInsuredInfo:type_name -> pricing.BlanketPrimaryAndNonContributoryAdditionalInsuredInfo
	10, // 13: pricing.BaseCharge.blanketWaiverOfSubrogationInfo:type_name -> pricing.BlanketWaiverOfSubrogationInfo
	11, // 14: pricing.BaseCharge.specifiedRegularAdditionalInsuredInfo:type_name -> pricing.SpecifiedRegularAdditionalInsuredInfo
	12, // 15: pricing.BaseCharge.specifiedPrimaryAndNonContributoryAdditionalInsuredInfo:type_name -> pricing.SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo
	13, // 16: pricing.BaseCharge.specifiedThirdPartyWithWaiverOfSubrogationInfo:type_name -> pricing.SpecifiedThirdPartyWithWaiverOfSubrogationInfo
	2,  // 17: pricing.Surcharge.type:type_name -> pricing.Surcharge.Type
	22, // 18: pricing.ChargeableSubCoverageGroup.group:type_name -> pricing.SubCoverageGroup
	1,  // 19: pricing.Charge.Distribution.type:type_name -> pricing.Charge.DistributionType
	19, // 20: pricing.Charge.Distribution.items:type_name -> pricing.Charge.DistributionItem
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_pricing_charges_proto_init() }
func file_pricing_charges_proto_init() {
	if File_pricing_charges_proto != nil {
		return
	}
	file_pricing_sub_coverages_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pricing_charges_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Charge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmountBasedBillingDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateBasedBillingDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateBasisValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseCharge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlanketRegularAdditionalInsuredInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlanketPrimaryAndNonContributoryAdditionalInsuredInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlanketWaiverOfSubrogationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpecifiedRegularAdditionalInsuredInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpecifiedThirdPartyWithWaiverOfSubrogationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Surcharge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualEndorsementCharge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChargeableSubCoverageGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChargeablePolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Charge_Distribution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_charges_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Charge_DistributionItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pricing_charges_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Charge_AmountBasedBillingDetails)(nil),
		(*Charge_RateBasedBillingDetails)(nil),
		(*Charge_BaseCharge)(nil),
		(*Charge_Surcharge)(nil),
		(*Charge_ManualEndorsementCharge)(nil),
		(*Charge_ChargedSubCoverageGroup)(nil),
		(*Charge_ChargedPolicy)(nil),
	}
	file_pricing_charges_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_pricing_charges_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_pricing_charges_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*RateBasisValue_Miles)(nil),
		(*RateBasisValue_TivDays)(nil),
		(*RateBasisValue_Premium)(nil),
	}
	file_pricing_charges_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*BaseCharge_BlanketRegularAdditionalInsuredInfo)(nil),
		(*BaseCharge_BlanketPrimaryAndNonContributoryAdditionalInsuredInfo)(nil),
		(*BaseCharge_BlanketWaiverOfSubrogationInfo)(nil),
		(*BaseCharge_SpecifiedRegularAdditionalInsuredInfo)(nil),
		(*BaseCharge_SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo)(nil),
		(*BaseCharge_SpecifiedThirdPartyWithWaiverOfSubrogationInfo)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricing_charges_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pricing_charges_proto_goTypes,
		DependencyIndexes: file_pricing_charges_proto_depIdxs,
		EnumInfos:         file_pricing_charges_proto_enumTypes,
		MessageInfos:      file_pricing_charges_proto_msgTypes,
	}.Build()
	File_pricing_charges_proto = out.File
	file_pricing_charges_proto_rawDesc = nil
	file_pricing_charges_proto_goTypes = nil
	file_pricing_charges_proto_depIdxs = nil
}
