package ptypes

import (
	"github.com/cockroachdb/errors"
)

/*
TODO: Determine which are the BaseCharges and Surcharges for Fleet, and whether they are refundable or not.
*/

// IsChargeRefundable assumes that a given base charge or surcharge will
// maintain its refundability constant across model versions.
//
// This might not always be true, as regulators could declare that from
// a certain date onwards, a certain surcharge changes its refundability.
// In case this happens, we should model this as a new "type" of charge.
//
// Additionally, we might also change the refundability of a base charge,
// as these are defined by us (not regulators). Although we do follow
// the industry standards.
func IsChargeRefundable(c *Charge) (bool, error) {
	if c == nil {
		return false, errors.New("charge was nil")
	}

	if c.Has<PERSON>mountBasedBillingDetails() {
		if c.IsSurcharge() {
			if c.IsNCRFSurcharge() {
				return true, nil
			} else if c.IsMCCASurcharge() {
				return false, nil
			} else if c.IsSurplusTaxSurcharge() {
				return true, nil
			} else if c.IsSurplusTaxSurchargeFromFullyEarnedPremium() {
				return false, nil
			} else if c.IsStampingFeeSurcharge() {
				return true, nil
			} else if c.IsStampingFeeSurchargeFromFullyEarnedPremium() {
				return false, nil
			}
			return false, errors.Newf("unknown surcharge: %+v", c.GetSurcharge())
		} else if c.IsBaseCharge() {
			if c.IsFullyEarnedCharge() {
				return false, nil
			}
			return true, nil
		}
		return false, errors.Newf("unknown type for charge with amount-based billing details: %T", c.GetType())
	} else if c.HasRateBasedBillingDetails() {
		if c.IsBaseCharge() {
			if c.AppliesToSubCoverageGroup() {
				return true, nil
			}
			return false, errors.Newf(
				"unknown charged item for base charge with rate-based billing details: %T",
				c.GetChargedItem(),
			)
		}
		return false, errors.Newf("unknown type for charge with rate-based billing details: %T", c.GetType())
	}
	return false, errors.Newf("unknown billing details: %T", c.GetBillingDetails())
}
