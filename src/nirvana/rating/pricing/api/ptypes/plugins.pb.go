// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pricing/plugins.proto

package ptypes

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PluginID int32

const (
	PluginID_PluginID_Unspecified                                            PluginID = 0
	PluginID_PluginID_LossHistoryExperiment_V1                               PluginID = 1
	PluginID_PluginID_DriverEndorsementRule_V1                               PluginID = 2
	PluginID_PluginID_RateMLArtifactUpload_V1                                PluginID = 3
	PluginID_PluginID_MetricsReporting_V1                                    PluginID = 4
	PluginID_PluginID_FullyEarnedChargesDedupRule_V1                         PluginID = 5
	PluginID_PluginID_MaxFullyEarnedChargesRule_V1                           PluginID = 6
	PluginID_PluginID_MCCASurchargeDedupRule_V1                              PluginID = 7
	PluginID_PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1          PluginID = 8
	PluginID_PluginID_CollRequiresCompValidation_V1                          PluginID = 9
	PluginID_PluginID_SamePDCombinationForAllVehiclesValidation_V1           PluginID = 10
	PluginID_PluginID_UMPDRestrictedToPPTVehiclesValidation_V1               PluginID = 11
	PluginID_PluginID_CollAndUMPDMutualExclusivityValidation_V1              PluginID = 12
	PluginID_PluginID_TowingRestrictedToCertainVehiclesValidation_V1         PluginID = 13
	PluginID_PluginID_NoSpecifiedXModifierValidation_V1                      PluginID = 14
	PluginID_PluginID_HiredAutoPDRequiresCompValidation_V1                   PluginID = 15
	PluginID_PluginID_UMBIIsRequiredIfLiabIsPresentValidation_V1             PluginID = 16
	PluginID_PluginID_TowingRequiresPDValidation_V1                          PluginID = 17
	PluginID_PluginID_RentalRequiresPDValidation_V1                          PluginID = 18
	PluginID_PluginID_MedPayRequiresLiabValidation_V1                        PluginID = 19
	PluginID_PluginID_UMBIRequiresLiabValidation_V1                          PluginID = 20
	PluginID_PluginID_UMPDRequiresLiabValidation_V1                          PluginID = 21
	PluginID_PluginID_UIMBIRequiresLiabValidation_V1                         PluginID = 22
	PluginID_PluginID_HiredAutoLiabRequiresLiabValidation_V1                 PluginID = 23
	PluginID_PluginID_HiredAutoPDRequiresLiabValidation_V1                   PluginID = 24
	PluginID_PluginID_NonOwnedVehicleRequiresLiabValidation_V1               PluginID = 25
	PluginID_PluginID_BIAndPDRequireJointSelectionValidation_V1              PluginID = 26
	PluginID_PluginID_OnlyBAPolicyIsSupportedValidation_V1                   PluginID = 27
	PluginID_PluginID_UMPDRequiresUMBIValidation_V1                          PluginID = 28
	PluginID_PluginID_SamePerOccurrenceLimitForUMBIAndUIMBIValidation_V1     PluginID = 29
	PluginID_PluginID_ChargesProration_V1                                    PluginID = 30
	PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1 PluginID = 31
	PluginID_PluginID_MedicalExpenseBenefitsRequiresLiabValidation_V1        PluginID = 32
	PluginID_PluginID_PIPRequiresLiabValidation_V1                           PluginID = 33
	PluginID_PluginID_UMUIMRequiresLiabValidation_V1                         PluginID = 34
	PluginID_PluginID_MedPayAndPIPMutualExclusivityValidation_V1             PluginID = 35
	PluginID_PluginID_MockPlugin_V1                                          PluginID = 1000
	PluginID_PluginID_MockPlugin_V2                                          PluginID = 1001
	PluginID_PluginID_MockPlugin_V3                                          PluginID = 1002
	PluginID_PluginID_MockPlugin_V4                                          PluginID = 1003
	PluginID_PluginID_MockPlugin_V5                                          PluginID = 1004
)

// Enum value maps for PluginID.
var (
	PluginID_name = map[int32]string{
		0:    "PluginID_Unspecified",
		1:    "PluginID_LossHistoryExperiment_V1",
		2:    "PluginID_DriverEndorsementRule_V1",
		3:    "PluginID_RateMLArtifactUpload_V1",
		4:    "PluginID_MetricsReporting_V1",
		5:    "PluginID_FullyEarnedChargesDedupRule_V1",
		6:    "PluginID_MaxFullyEarnedChargesRule_V1",
		7:    "PluginID_MCCASurchargeDedupRule_V1",
		8:    "PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1",
		9:    "PluginID_CollRequiresCompValidation_V1",
		10:   "PluginID_SamePDCombinationForAllVehiclesValidation_V1",
		11:   "PluginID_UMPDRestrictedToPPTVehiclesValidation_V1",
		12:   "PluginID_CollAndUMPDMutualExclusivityValidation_V1",
		13:   "PluginID_TowingRestrictedToCertainVehiclesValidation_V1",
		14:   "PluginID_NoSpecifiedXModifierValidation_V1",
		15:   "PluginID_HiredAutoPDRequiresCompValidation_V1",
		16:   "PluginID_UMBIIsRequiredIfLiabIsPresentValidation_V1",
		17:   "PluginID_TowingRequiresPDValidation_V1",
		18:   "PluginID_RentalRequiresPDValidation_V1",
		19:   "PluginID_MedPayRequiresLiabValidation_V1",
		20:   "PluginID_UMBIRequiresLiabValidation_V1",
		21:   "PluginID_UMPDRequiresLiabValidation_V1",
		22:   "PluginID_UIMBIRequiresLiabValidation_V1",
		23:   "PluginID_HiredAutoLiabRequiresLiabValidation_V1",
		24:   "PluginID_HiredAutoPDRequiresLiabValidation_V1",
		25:   "PluginID_NonOwnedVehicleRequiresLiabValidation_V1",
		26:   "PluginID_BIAndPDRequireJointSelectionValidation_V1",
		27:   "PluginID_OnlyBAPolicyIsSupportedValidation_V1",
		28:   "PluginID_UMPDRequiresUMBIValidation_V1",
		29:   "PluginID_SamePerOccurrenceLimitForUMBIAndUIMBIValidation_V1",
		30:   "PluginID_ChargesProration_V1",
		31:   "PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1",
		32:   "PluginID_MedicalExpenseBenefitsRequiresLiabValidation_V1",
		33:   "PluginID_PIPRequiresLiabValidation_V1",
		34:   "PluginID_UMUIMRequiresLiabValidation_V1",
		35:   "PluginID_MedPayAndPIPMutualExclusivityValidation_V1",
		1000: "PluginID_MockPlugin_V1",
		1001: "PluginID_MockPlugin_V2",
		1002: "PluginID_MockPlugin_V3",
		1003: "PluginID_MockPlugin_V4",
		1004: "PluginID_MockPlugin_V5",
	}
	PluginID_value = map[string]int32{
		"PluginID_Unspecified":                                            0,
		"PluginID_LossHistoryExperiment_V1":                               1,
		"PluginID_DriverEndorsementRule_V1":                               2,
		"PluginID_RateMLArtifactUpload_V1":                                3,
		"PluginID_MetricsReporting_V1":                                    4,
		"PluginID_FullyEarnedChargesDedupRule_V1":                         5,
		"PluginID_MaxFullyEarnedChargesRule_V1":                           6,
		"PluginID_MCCASurchargeDedupRule_V1":                              7,
		"PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1":          8,
		"PluginID_CollRequiresCompValidation_V1":                          9,
		"PluginID_SamePDCombinationForAllVehiclesValidation_V1":           10,
		"PluginID_UMPDRestrictedToPPTVehiclesValidation_V1":               11,
		"PluginID_CollAndUMPDMutualExclusivityValidation_V1":              12,
		"PluginID_TowingRestrictedToCertainVehiclesValidation_V1":         13,
		"PluginID_NoSpecifiedXModifierValidation_V1":                      14,
		"PluginID_HiredAutoPDRequiresCompValidation_V1":                   15,
		"PluginID_UMBIIsRequiredIfLiabIsPresentValidation_V1":             16,
		"PluginID_TowingRequiresPDValidation_V1":                          17,
		"PluginID_RentalRequiresPDValidation_V1":                          18,
		"PluginID_MedPayRequiresLiabValidation_V1":                        19,
		"PluginID_UMBIRequiresLiabValidation_V1":                          20,
		"PluginID_UMPDRequiresLiabValidation_V1":                          21,
		"PluginID_UIMBIRequiresLiabValidation_V1":                         22,
		"PluginID_HiredAutoLiabRequiresLiabValidation_V1":                 23,
		"PluginID_HiredAutoPDRequiresLiabValidation_V1":                   24,
		"PluginID_NonOwnedVehicleRequiresLiabValidation_V1":               25,
		"PluginID_BIAndPDRequireJointSelectionValidation_V1":              26,
		"PluginID_OnlyBAPolicyIsSupportedValidation_V1":                   27,
		"PluginID_UMPDRequiresUMBIValidation_V1":                          28,
		"PluginID_SamePerOccurrenceLimitForUMBIAndUIMBIValidation_V1":     29,
		"PluginID_ChargesProration_V1":                                    30,
		"PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1": 31,
		"PluginID_MedicalExpenseBenefitsRequiresLiabValidation_V1":        32,
		"PluginID_PIPRequiresLiabValidation_V1":                           33,
		"PluginID_UMUIMRequiresLiabValidation_V1":                         34,
		"PluginID_MedPayAndPIPMutualExclusivityValidation_V1":             35,
		"PluginID_MockPlugin_V1":                                          1000,
		"PluginID_MockPlugin_V2":                                          1001,
		"PluginID_MockPlugin_V3":                                          1002,
		"PluginID_MockPlugin_V4":                                          1003,
		"PluginID_MockPlugin_V5":                                          1004,
	}
)

func (x PluginID) Enum() *PluginID {
	p := new(PluginID)
	*p = x
	return p
}

func (x PluginID) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PluginID) Descriptor() protoreflect.EnumDescriptor {
	return file_pricing_plugins_proto_enumTypes[0].Descriptor()
}

func (PluginID) Type() protoreflect.EnumType {
	return &file_pricing_plugins_proto_enumTypes[0]
}

func (x PluginID) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PluginID.Descriptor instead.
func (PluginID) EnumDescriptor() ([]byte, []int) {
	return file_pricing_plugins_proto_rawDescGZIP(), []int{0}
}

type PluginsMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DriverEndorsementRuleV1Metadata *DriverEndorsementRuleV1Metadata `protobuf:"bytes,1,opt,name=driverEndorsementRuleV1Metadata,proto3,oneof" json:"driverEndorsementRuleV1Metadata,omitempty"`
}

func (x *PluginsMetadata) Reset() {
	*x = PluginsMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_plugins_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PluginsMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginsMetadata) ProtoMessage() {}

func (x *PluginsMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_plugins_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginsMetadata.ProtoReflect.Descriptor instead.
func (*PluginsMetadata) Descriptor() ([]byte, []int) {
	return file_pricing_plugins_proto_rawDescGZIP(), []int{0}
}

func (x *PluginsMetadata) GetDriverEndorsementRuleV1Metadata() *DriverEndorsementRuleV1Metadata {
	if x != nil {
		return x.DriverEndorsementRuleV1Metadata
	}
	return nil
}

type DriverEndorsementRuleV1Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Threshold               float64 `protobuf:"fixed64,1,opt,name=threshold,proto3" json:"threshold,omitempty"`
	BaseSubTotalPremium     float64 `protobuf:"fixed64,2,opt,name=baseSubTotalPremium,proto3" json:"baseSubTotalPremium,omitempty"`
	ProposedSubTotalPremium float64 `protobuf:"fixed64,3,opt,name=proposedSubTotalPremium,proto3" json:"proposedSubTotalPremium,omitempty"`
}

func (x *DriverEndorsementRuleV1Metadata) Reset() {
	*x = DriverEndorsementRuleV1Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_plugins_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DriverEndorsementRuleV1Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverEndorsementRuleV1Metadata) ProtoMessage() {}

func (x *DriverEndorsementRuleV1Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_plugins_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverEndorsementRuleV1Metadata.ProtoReflect.Descriptor instead.
func (*DriverEndorsementRuleV1Metadata) Descriptor() ([]byte, []int) {
	return file_pricing_plugins_proto_rawDescGZIP(), []int{1}
}

func (x *DriverEndorsementRuleV1Metadata) GetThreshold() float64 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *DriverEndorsementRuleV1Metadata) GetBaseSubTotalPremium() float64 {
	if x != nil {
		return x.BaseSubTotalPremium
	}
	return 0
}

func (x *DriverEndorsementRuleV1Metadata) GetProposedSubTotalPremium() float64 {
	if x != nil {
		return x.ProposedSubTotalPremium
	}
	return 0
}

var File_pricing_plugins_proto protoreflect.FileDescriptor

var file_pricing_plugins_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x22, 0xae, 0x01, 0x0a, 0x0f, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x77, 0x0a, 0x1f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x45, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x56, 0x31, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x45, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x56, 0x31, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1f, 0x64, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65,
	0x56, 0x31, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x88, 0x01, 0x01, 0x42, 0x22, 0x0a,
	0x20, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x56, 0x31, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x22, 0xab, 0x01, 0x0a, 0x1f, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x45, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x56, 0x31, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x12, 0x30, 0x0a, 0x13, 0x62, 0x61, 0x73, 0x65, 0x53, 0x75, 0x62, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x13, 0x62, 0x61, 0x73, 0x65, 0x53, 0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72,
	0x65, 0x6d, 0x69, 0x75, 0x6d, 0x12, 0x38, 0x0a, 0x17, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65,
	0x64, 0x53, 0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x64,
	0x53, 0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x2a,
	0xee, 0x0e, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x14,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x49, 0x44, 0x5f, 0x4c, 0x6f, 0x73, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x45, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x56, 0x31, 0x10, 0x01, 0x12, 0x25, 0x0a,
	0x21, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x5f,
	0x56, 0x31, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44,
	0x5f, 0x52, 0x61, 0x74, 0x65, 0x4d, 0x4c, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x56, 0x31, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x56, 0x31, 0x10, 0x04, 0x12, 0x2b, 0x0a, 0x27,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x46, 0x75, 0x6c, 0x6c, 0x79, 0x45, 0x61,
	0x72, 0x6e, 0x65, 0x64, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x44, 0x65, 0x64, 0x75, 0x70,
	0x52, 0x75, 0x6c, 0x65, 0x5f, 0x56, 0x31, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x4d, 0x61, 0x78, 0x46, 0x75, 0x6c, 0x6c, 0x79, 0x45, 0x61,
	0x72, 0x6e, 0x65, 0x64, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x5f,
	0x56, 0x31, 0x10, 0x06, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44,
	0x5f, 0x4d, 0x43, 0x43, 0x41, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x44, 0x65,
	0x64, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x5f, 0x56, 0x31, 0x10, 0x07, 0x12, 0x3a, 0x0a, 0x36,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x48, 0x69, 0x72, 0x65, 0x64, 0x41, 0x75,
	0x74, 0x6f, 0x50, 0x44, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x48, 0x69, 0x72, 0x65,
	0x64, 0x41, 0x75, 0x74, 0x6f, 0x4c, 0x69, 0x61, 0x62, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x08, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x49, 0x44, 0x5f, 0x43, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x73, 0x43, 0x6f, 0x6d, 0x70, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x56, 0x31, 0x10, 0x09, 0x12, 0x39, 0x0a, 0x35, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44,
	0x5f, 0x53, 0x61, 0x6d, 0x65, 0x50, 0x44, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x0a, 0x12,
	0x35, 0x0a, 0x31, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x55, 0x4d, 0x50, 0x44,
	0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x65, 0x64, 0x54, 0x6f, 0x50, 0x50, 0x54, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x56, 0x31, 0x10, 0x0b, 0x12, 0x36, 0x0a, 0x32, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x49, 0x44, 0x5f, 0x43, 0x6f, 0x6c, 0x6c, 0x41, 0x6e, 0x64, 0x55, 0x4d, 0x50, 0x44, 0x4d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x69, 0x74, 0x79, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x0c, 0x12, 0x3b,
	0x0a, 0x37, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x54, 0x6f, 0x77, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x65, 0x64, 0x54, 0x6f, 0x43, 0x65, 0x72,
	0x74, 0x61, 0x69, 0x6e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x0d, 0x12, 0x2e, 0x0a, 0x2a, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x4e, 0x6f, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x58, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x0e, 0x12, 0x31, 0x0a, 0x2d, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x48, 0x69, 0x72, 0x65, 0x64, 0x41, 0x75, 0x74,
	0x6f, 0x50, 0x44, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x0f, 0x12, 0x37,
	0x0a, 0x33, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x55, 0x4d, 0x42, 0x49, 0x49,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x66, 0x4c, 0x69, 0x61, 0x62, 0x49,
	0x73, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x10, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x49, 0x44, 0x5f, 0x54, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x73, 0x50, 0x44, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56,
	0x31, 0x10, 0x11, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f,
	0x52, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x50, 0x44,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x12, 0x12,
	0x2c, 0x0a, 0x28, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x4d, 0x65, 0x64, 0x50,
	0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x4c, 0x69, 0x61, 0x62, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x13, 0x12, 0x2a, 0x0a,
	0x26, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x55, 0x4d, 0x42, 0x49, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x4c, 0x69, 0x61, 0x62, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x14, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x55, 0x4d, 0x50, 0x44, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x73, 0x4c, 0x69, 0x61, 0x62, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x56, 0x31, 0x10, 0x15, 0x12, 0x2b, 0x0a, 0x27, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x44, 0x5f, 0x55, 0x49, 0x4d, 0x42, 0x49, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x4c,
	0x69, 0x61, 0x62, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31,
	0x10, 0x16, 0x12, 0x33, 0x0a, 0x2f, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x48,
	0x69, 0x72, 0x65, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x4c, 0x69, 0x61, 0x62, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x73, 0x4c, 0x69, 0x61, 0x62, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x17, 0x12, 0x31, 0x0a, 0x2d, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x49, 0x44, 0x5f, 0x48, 0x69, 0x72, 0x65, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x50, 0x44, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x4c, 0x69, 0x61, 0x62, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x18, 0x12, 0x35, 0x0a, 0x31, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x4e, 0x6f, 0x6e, 0x4f, 0x77, 0x6e, 0x65, 0x64, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x4c, 0x69,
	0x61, 0x62, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10,
	0x19, 0x12, 0x36, 0x0a, 0x32, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x42, 0x49,
	0x41, 0x6e, 0x64, 0x50, 0x44, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x4a, 0x6f, 0x69, 0x6e,
	0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x1a, 0x12, 0x31, 0x0a, 0x2d, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x4f, 0x6e, 0x6c, 0x79, 0x42, 0x41, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x49, 0x73, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x1b, 0x12, 0x2a, 0x0a, 0x26,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x55, 0x4d, 0x50, 0x44, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x73, 0x55, 0x4d, 0x42, 0x49, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x1c, 0x12, 0x3f, 0x0a, 0x3b, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x49, 0x44, 0x5f, 0x53, 0x61, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x4f, 0x63, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x55, 0x4d,
	0x42, 0x49, 0x41, 0x6e, 0x64, 0x55, 0x49, 0x4d, 0x42, 0x49, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x1d, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x50, 0x72, 0x6f,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x1e, 0x12, 0x43, 0x0a, 0x3f, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x53, 0x61, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x4f,
	0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x46, 0x6f,
	0x72, 0x48, 0x69, 0x72, 0x65, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x6e, 0x64, 0x4c, 0x69, 0x61,
	0x62, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x1f,
	0x12, 0x3c, 0x0a, 0x38, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x4d, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x4c, 0x69, 0x61, 0x62, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x20, 0x12, 0x29,
	0x0a, 0x25, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x50, 0x49, 0x50, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x4c, 0x69, 0x61, 0x62, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x21, 0x12, 0x2b, 0x0a, 0x27, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x55, 0x4d, 0x55, 0x49, 0x4d, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x73, 0x4c, 0x69, 0x61, 0x62, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x56, 0x31, 0x10, 0x22, 0x12, 0x37, 0x0a, 0x33, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x49, 0x44, 0x5f, 0x4d, 0x65, 0x64, 0x50, 0x61, 0x79, 0x41, 0x6e, 0x64, 0x50, 0x49, 0x50, 0x4d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0x23, 0x12,
	0x1b, 0x0a, 0x16, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x4d, 0x6f, 0x63, 0x6b,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x56, 0x31, 0x10, 0xe8, 0x07, 0x12, 0x1b, 0x0a, 0x16,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x4d, 0x6f, 0x63, 0x6b, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x5f, 0x56, 0x32, 0x10, 0xe9, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f, 0x4d, 0x6f, 0x63, 0x6b, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x56, 0x33, 0x10, 0xea, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x49, 0x44, 0x5f, 0x4d, 0x6f, 0x63, 0x6b, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x56, 0x34,
	0x10, 0xeb, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x5f,
	0x4d, 0x6f, 0x63, 0x6b, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x56, 0x35, 0x10, 0xec, 0x07,
	0x42, 0x33, 0x5a, 0x31, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x72, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pricing_plugins_proto_rawDescOnce sync.Once
	file_pricing_plugins_proto_rawDescData = file_pricing_plugins_proto_rawDesc
)

func file_pricing_plugins_proto_rawDescGZIP() []byte {
	file_pricing_plugins_proto_rawDescOnce.Do(func() {
		file_pricing_plugins_proto_rawDescData = protoimpl.X.CompressGZIP(file_pricing_plugins_proto_rawDescData)
	})
	return file_pricing_plugins_proto_rawDescData
}

var file_pricing_plugins_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pricing_plugins_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pricing_plugins_proto_goTypes = []interface{}{
	(PluginID)(0),                           // 0: pricing.PluginID
	(*PluginsMetadata)(nil),                 // 1: pricing.PluginsMetadata
	(*DriverEndorsementRuleV1Metadata)(nil), // 2: pricing.DriverEndorsementRuleV1Metadata
}
var file_pricing_plugins_proto_depIdxs = []int32{
	2, // 0: pricing.PluginsMetadata.driverEndorsementRuleV1Metadata:type_name -> pricing.DriverEndorsementRuleV1Metadata
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pricing_plugins_proto_init() }
func file_pricing_plugins_proto_init() {
	if File_pricing_plugins_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pricing_plugins_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PluginsMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_plugins_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DriverEndorsementRuleV1Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pricing_plugins_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricing_plugins_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pricing_plugins_proto_goTypes,
		DependencyIndexes: file_pricing_plugins_proto_depIdxs,
		EnumInfos:         file_pricing_plugins_proto_enumTypes,
		MessageInfos:      file_pricing_plugins_proto_msgTypes,
	}.Build()
	File_pricing_plugins_proto = out.File
	file_pricing_plugins_proto_rawDesc = nil
	file_pricing_plugins_proto_goTypes = nil
	file_pricing_plugins_proto_depIdxs = nil
}
