package ptypes

import (
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/proto"
)

func NewRateBasisValueWithMiles(miles int64) *RateBasisValue {
	return &RateBasisValue{
		Value: &RateBasisValue_Miles{
			Miles: miles,
		},
	}
}

func NewRateBasisValueWithTIVDays(tivDays int64) *RateBasisValue {
	return &RateBasisValue{
		Value: &RateBasisValue_TivDays{
			TivDays: tivDays,
		},
	}
}

func NewRateBasisValueWithPremium(premium int64) *RateBasisValue {
	return &RateBasisValue{
		Value: &RateBasisValue_Premium{
			Premium: premium,
		},
	}
}

func NewChargeBaseChargeWithNoExtraInfo() *Charge_BaseCharge {
	return NewChargeBaseCharge(nil)
}

func NewChargeBaseChargeWithBlanketRegularAdditionalInsured() *Charge_BaseCharge {
	return NewChargeBaseCharge(
		&BaseCharge_BlanketRegularAdditionalInsuredInfo{
			BlanketRegularAdditionalInsuredInfo: &BlanketRegularAdditionalInsuredInfo{},
		},
	)
}

func NewChargeBaseChargeWithBlanketPrimaryAndNonContributoryAdditionalInsured() *Charge_BaseCharge {
	return NewChargeBaseCharge(
		&BaseCharge_BlanketPrimaryAndNonContributoryAdditionalInsuredInfo{
			BlanketPrimaryAndNonContributoryAdditionalInsuredInfo: &BlanketPrimaryAndNonContributoryAdditionalInsuredInfo{},
		},
	)
}

func NewChargeBaseChargeWithBlanketWaiverOfSubrogation() *Charge_BaseCharge {
	return NewChargeBaseCharge(
		&BaseCharge_BlanketWaiverOfSubrogationInfo{
			BlanketWaiverOfSubrogationInfo: &BlanketWaiverOfSubrogationInfo{},
		},
	)
}

func NewChargeBaseChargeWithSpecifiedRegularAdditionalInsured(additionalInsuredID string) *Charge_BaseCharge {
	return NewChargeBaseCharge(
		&BaseCharge_SpecifiedRegularAdditionalInsuredInfo{
			SpecifiedRegularAdditionalInsuredInfo: &SpecifiedRegularAdditionalInsuredInfo{
				AdditionalInsuredId: additionalInsuredID,
			},
		},
	)
}

func NewChargeBaseChargeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured(
	additionalInsuredID string,
) *Charge_BaseCharge {
	return NewChargeBaseCharge(
		&BaseCharge_SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo{
			SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo: &SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo{
				AdditionalInsuredId: additionalInsuredID,
			},
		},
	)
}

func NewChargeBaseChargeWithSpecifiedThirdPartyWithWaiverOfSubrogation(thirdPartyID string) *Charge_BaseCharge {
	return NewChargeBaseCharge(
		&BaseCharge_SpecifiedThirdPartyWithWaiverOfSubrogationInfo{
			SpecifiedThirdPartyWithWaiverOfSubrogationInfo: &SpecifiedThirdPartyWithWaiverOfSubrogationInfo{
				ThirdPartyId: thirdPartyID,
			},
		},
	)
}

func NewChargeBaseCharge(extraInfo isBaseCharge_ExtraInfo) *Charge_BaseCharge {
	return &Charge_BaseCharge{
		BaseCharge: &BaseCharge{
			ExtraInfo: extraInfo,
		},
	}
}

func NewChargeSurchargeWithNCRFType() *Charge_Surcharge {
	return &Charge_Surcharge{
		Surcharge: &Surcharge{
			Type: Surcharge_Type_NCRF,
		},
	}
}

func NewChargeSurchargeWithMCCAType() *Charge_Surcharge {
	return &Charge_Surcharge{
		Surcharge: &Surcharge{
			Type: Surcharge_Type_MCCA,
		},
	}
}

func NewChargeSurchargeWithStampingFee() *Charge_Surcharge {
	return &Charge_Surcharge{
		Surcharge: &Surcharge{
			Type: Surcharge_Type_STAMPING_FEE,
		},
	}
}

func NewChargeSurchargeWithSurplusTax() *Charge_Surcharge {
	return &Charge_Surcharge{
		Surcharge: &Surcharge{
			Type: Surcharge_Type_SURPLUS_TAX,
		},
	}
}

func NewChargeSurchargeWithStampingFeeFromFullyEarnedPremium() *Charge_Surcharge {
	return &Charge_Surcharge{
		Surcharge: &Surcharge{
			Type: Surcharge_Type_FEE_CHARGE_STAMPING_FEE,
		},
	}
}

func NewChargeSurchargeWithSurplusTaxFromFullyEarnedPremium() *Charge_Surcharge {
	return &Charge_Surcharge{
		Surcharge: &Surcharge{
			Type: Surcharge_Type_FEE_CHARGE_SURPLUS_TAX,
		},
	}
}

func NewChargeSurcharge(surchargeType Surcharge_Type) *Charge_Surcharge {
	return &Charge_Surcharge{
		Surcharge: &Surcharge{
			Type: surchargeType,
		},
	}
}

func NewChargeManualEndorsementCharge() *Charge_ManualEndorsementCharge {
	return &Charge_ManualEndorsementCharge{
		ManualEndorsementCharge: &ManualEndorsementCharge{},
	}
}

func NewChargeChargedSubCoverageGroup(subCoverageTypes ...SubCoverageType) *Charge_ChargedSubCoverageGroup {
	return &Charge_ChargedSubCoverageGroup{
		ChargedSubCoverageGroup: &ChargeableSubCoverageGroup{
			Group: &SubCoverageGroup{
				SubCoverages: subCoverageTypes,
			},
		},
	}
}

func NewChargeChargedPolicy(policyNumber string) *Charge_ChargedPolicy {
	return &Charge_ChargedPolicy{
		ChargedPolicy: &ChargeablePolicy{
			PolicyNumber: policyNumber,
		},
	}
}

func NewChargeAmountBasedBillingDetails(
	amount string,
	date *timestamppb.Timestamp,
) *Charge_AmountBasedBillingDetails {
	return &Charge_AmountBasedBillingDetails{
		AmountBasedBillingDetails: &AmountBasedBillingDetails{
			Amount: amount,
			Date:   date,
		},
	}
}

func NewChargeRateBasedBillingDetails(
	rate string,
	dates *proto.Interval,
	rateBasis RateBasis,
) *Charge_RateBasedBillingDetails {
	return &Charge_RateBasedBillingDetails{
		RateBasedBillingDetails: &RateBasedBillingDetails{
			Rate:      rate,
			Dates:     dates,
			RateBasis: rateBasis,
		},
	}
}

func NewChargeDistributionItem(id, fraction string) *Charge_DistributionItem {
	return &Charge_DistributionItem{
		Id:       id,
		Fraction: fraction,
	}
}

func NewChargeDistribution(
	distributionType Charge_DistributionType,
	items ...*Charge_DistributionItem,
) *Charge_Distribution {
	return &Charge_Distribution{
		Type:  distributionType,
		Items: items,
	}
}
