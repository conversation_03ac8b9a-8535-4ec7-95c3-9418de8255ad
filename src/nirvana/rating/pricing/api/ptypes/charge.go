package ptypes

import (
	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

/*
Calculate returns the premium to charge for a given rate basis value.

The exact behavior depends on the billing details of the charge and the rate basis.
Note that a charge expects a specific rate basis, defined on its billing details.

The billing details of a charge also determine the logic used to calculate the premium.

NOTE: as of Sep 16th 2024, only AmountBasedBillingDetails are supported at the moment.
In the future we plan to add RateBasedBillingDetails (mostly for Fleet).

[Amount-based billing details]
Charges with this type of billing details expect a nil rate basis value.
In this case, the charge will have a single date (inside its billing details).
Billing needs to determine if the charge should be considered for the billing
period they are calculating, based on the charge's date.

Note that in this case the premium to be charged is stored inside the billing
details object (within the charge), and it is returned as is. This means that
Billing could obtain the premium to charge without needing to call the Calculate
method. But we decided to support AmountBasedBillingDetails any ways, in order
to keep the interface consistent with future charge types.

[Rate-based billing details]
Charges with this type of billing details will have a rate basis (inside its
billing details). There might be different kinds of rate bases (e.g. TIV, miles, etc.).

In this case, the billing details will also have a date interval. Billing can use this
field to calculate the rate basis value that needs to be passed to the Calculate method.

The billing period might not match exactly with the charge's date interval. If
there's no overlap between the two intervals, billing should not consider this
charge. If there is an overlap, billing should calculate the rate basis value
for the intersection and then pass it to the Calculate method.

Note that in this case, the rate basis value will not be nil as in the case
of an AmountBasedBillingDetails).

For example, let's say we have an ongoing rate-based charge with:
- A rate of $100/mile
- A start date of 03/01/2024
- An end date of 06/01/2024
All of this information is stored in the Charge message (particularly inside
the RateBasedBillingDetails message).

Note that in the example, the charge's interval is 3 months.

Now, let's say that Billing want to calculate how much to charge for the period
of 01/01/2024 to 05/01/2024. Note that the billing period length is 5 months.

First, Billing should calculate the intersection between these two intervals,
which is 2 months (from 03/01/2024 to 05/01/2024).

Then, it needs to calculate the rate basis value for this intersection.
Let's say that the rate basis value is 5000 miles. This value is passed
to the Calculate method, which will multiply the rate ($100/mile) by the
value received (5000 miles) to get the premium to charge: $500,000.
*/
func (c *Charge) Calculate(rateBasisValue *RateBasisValue) (decimal.Decimal, error) {
	if c.HasAmountBasedBillingDetails() {
		billingDetails := c.GetAmountBasedBillingDetails()
		if rateBasisValue == nil {
			amount, err := decimal.NewFromString(billingDetails.Amount)
			if err != nil {
				return decimal.Zero, errors.Wrap(err, "failed to parse amount")
			}

			return amount, nil
		}

		return decimal.Zero, errors.Newf(
			"invalid rate basis value for charge with amount based billing details: %T",
			rateBasisValue.GetValue(),
		)
	} else if c.HasRateBasedBillingDetails() {
		billingDetails := c.GetRateBasedBillingDetails()
		if rateBasisValue.HasMilesValue() {
			chargeRateBasis := billingDetails.RateBasis
			if chargeRateBasis != RateBasis_RateBasis_Miles {
				return decimal.Zero, errors.Newf(
					"charge's rate basis is not miles, is: %v",
					chargeRateBasis,
				)
			}

			rate, err := decimal.NewFromString(billingDetails.Rate)
			if err != nil {
				return decimal.Zero, errors.Wrap(err, "failed to parse rate")
			}

			miles := decimal.NewFromInt(rateBasisValue.GetMiles())
			premium := rate.Mul(miles)

			return premium, nil
		}

		return decimal.Zero, errors.Newf(
			"invalid rate basis value for charge with rate based billing details: %T",
			rateBasisValue.GetValue(),
		)
	}

	return decimal.Zero, errors.Newf("unknown charge billing details: %T", c.GetBillingDetails())
}

func (c *Charge) HasAmountBasedBillingDetails() bool {
	return c.GetAmountBasedBillingDetails() != nil
}

func (c *Charge) HasRateBasedBillingDetails() bool {
	return c.GetRateBasedBillingDetails() != nil
}

func (c *Charge) IsBaseCharge() bool {
	return c.GetBaseCharge() != nil
}

func (c *Charge) IsSurcharge() bool {
	return c.GetSurcharge() != nil
}

func (c *Charge) IsManualEndorsementCharge() bool {
	return c.GetManualEndorsementCharge() != nil
}

func (c *Charge) IsNCRFSurcharge() bool {
	return c.IsSurchargeType(Surcharge_Type_NCRF)
}

func (c *Charge) IsMCCASurcharge() bool {
	return c.IsSurchargeType(Surcharge_Type_MCCA)
}

func (c *Charge) IsSurplusTaxSurcharge() bool {
	return c.IsSurchargeType(Surcharge_Type_SURPLUS_TAX)
}

func (c *Charge) IsStampingFeeSurcharge() bool {
	return c.IsSurchargeType(Surcharge_Type_STAMPING_FEE)
}

func (c *Charge) IsStampingFeeSurchargeFromFullyEarnedPremium() bool {
	return c.IsSurchargeType(Surcharge_Type_FEE_CHARGE_STAMPING_FEE)
}

func (c *Charge) IsSurplusTaxSurchargeFromFullyEarnedPremium() bool {
	return c.IsSurchargeType(Surcharge_Type_FEE_CHARGE_SURPLUS_TAX)
}

func (c *Charge) IsSurchargeType(typ Surcharge_Type) bool {
	surcharge := c.GetSurcharge()
	if surcharge == nil {
		return false
	}
	return surcharge.GetType() == typ
}

// IsFullyEarnedCharge is a helper method to determine if a charge is one of
// the charges that Nirvana charges as additional fully earned premiums,
// which are not part of the main coverage-related charges.
//
// Note that all of these "charges" are base charges, and what
// distinguishes them is the type of charged item they have.
func (c *Charge) IsFullyEarnedCharge() bool {
	return c.AppliesToBlanketRegularAdditionalInsured() ||
		c.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured() ||
		c.AppliesToBlanketWaiverOfSubrogation() ||
		c.AppliesToSpecifiedRegularAdditionalInsured() ||
		c.AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured() ||
		c.AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation()
}

func (c *Charge) AppliesToSubCoverageGroup() bool {
	return c.GetChargedSubCoverageGroup() != nil
}

func (c *Charge) AppliesToPolicy() bool {
	return c.GetChargedPolicy() != nil
}

func (c *Charge) AppliesToBlanketRegularAdditionalInsured() bool {
	return c.GetBaseCharge().GetBlanketRegularAdditionalInsuredInfo() != nil
}

func (c *Charge) AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured() bool {
	return c.GetBaseCharge().GetBlanketPrimaryAndNonContributoryAdditionalInsuredInfo() != nil
}

func (c *Charge) AppliesToBlanketWaiverOfSubrogation() bool {
	return c.GetBaseCharge().GetBlanketWaiverOfSubrogationInfo() != nil
}

func (c *Charge) AppliesToSpecifiedRegularAdditionalInsured() bool {
	return c.GetBaseCharge().GetSpecifiedRegularAdditionalInsuredInfo() != nil
}

func (c *Charge) AppliesToSpecifiedPrimaryAndNonContributoryAdditionalInsured() bool {
	return c.GetBaseCharge().GetSpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo() != nil
}

func (c *Charge) AppliesToSpecifiedThirdPartyWithWaiverOfSubrogation() bool {
	return c.GetBaseCharge().GetSpecifiedThirdPartyWithWaiverOfSubrogationInfo() != nil
}

func (c *Charge) Copy() *Charge {
	if c == nil {
		return nil
	}
	return proto.Clone(c).(*Charge)
}

// BeforeMarshalHook should be called before marshaling a Charge message
// using protojson.Marshal. This method is used by the Pricing team to
// customize the serde of Charge messages, thus allowing easier refactors
// and handling of backwards incompatible changes.
//
// The underlying message is modified in place.
func (c *Charge) BeforeMarshalHook() error {
	return nil
}

// AfterUnmarshalHook is equivalent to BeforeMarshalHook, but for the
// reverse operation. Read BeforeMarshalHook's doc for more information.
func (c *Charge) AfterUnmarshalHook() error {
	return nil
}

// MarshalCharge serializes a Charge message into a JSON byte slice.
// Pricing consumers should use this method to serialize Charge messages.
func MarshalCharge(c *Charge) ([]byte, error) {
	err := c.BeforeMarshalHook()
	if err != nil {
		return nil, err
	}

	return protojson.Marshal(c)
}

// UnmarshalCharge is equivalent to MarshalCharge, but for the reverse operation.
func UnmarshalCharge(bytes []byte) (*Charge, error) {
	ch := &Charge{}
	err := protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}.Unmarshal(bytes, ch)
	if err != nil {
		return nil, err
	}

	err = ch.AfterUnmarshalHook()
	if err != nil {
		return nil, err
	}

	return ch, nil
}
