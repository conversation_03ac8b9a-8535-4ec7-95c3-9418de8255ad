package jobs

import (
	"context"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/infra/config"
)

func ShouldTriggerLossRunParsing(
	ctx context.Context,
	cfg *config.Config,
	agencyWrapper agency.DataWrapper,
	authWrapper auth.DataWrapper,
	ffClient feature_flag_lib.Client,
	createdBy string,
	agencyId uuid.UUID,
	submissionId string,
) (bool, error) {
	if cfg.Env != config.Env_PROD {
		log.Info(ctx, "Loss run parsing is only enabled in production", log.String("submissionId", submissionId))
		return false, nil
	}
	// Check if the loss run parsing feature flag is enabled for the user who created the application
	lossRunParsingEnabled, err := fetchLossRunParsingFeatureFlag(ctx, authWrapper, ffClient, createdBy)
	if err != nil {
		log.Error(ctx, "Failed to fetch loss run parsing feature flag",
			log.String("submissionId", submissionId), log.Err(err))
		return false, errors.Wrapf(err, "failed to fetch loss run parsing feature flag for submission %s", submissionId)
	}
	if !lossRunParsingEnabled {
		log.Info(ctx, "Loss run parsing feature flag is disabled for user", log.String("createdBy", createdBy), log.String("submissionId", submissionId))
		return false, nil
	}
	// Only trigger loss run parsing for non-test agencies
	appAgency, err := agencyWrapper.FetchAgency(ctx, agencyId)
	if err != nil {
		return false, errors.Wrapf(err, "failed to fetch agency for submission %s and agencyId %s", submissionId, agencyId.String())
	}
	if appAgency.IsTestAgency {
		return false, nil
	}
	return true, nil
}

func fetchLossRunParsingFeatureFlag(
	ctx context.Context,
	authWrapper auth.DataWrapper,
	ffClient feature_flag_lib.Client,
	createdBy string,
) (bool, error) {
	// Check if the createdBy is a valid UUID
	createdByUUID, err := uuid.Parse(createdBy)
	if err != nil {
		return false, errors.Wrapf(err, "invalid UUID format for createdBy: %s", createdBy)
	}

	// Fetch the user from the auth wrapper
	user, err := authWrapper.FetchAuthzUser(ctx, createdByUUID)
	if err != nil {
		return false, errors.Wrap(err, "failed to fetch user for loss run parsing feature flag")
	}

	// Check if the feature flag is enabled for the user
	lossRunParsingEnabled, err := ffClient.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(*user),
		feature_flag_lib.FeaturePibitLossRunParsing,
		false,
	)
	if err != nil {
		return false, errors.Wrap(err, "failed to fetch loss run parsing feature flag")
	}
	return lossRunParsingEnabled, nil
}

func ShouldTriggerLossRunAggregation(
	ctx context.Context,
	applicationId string,
	parsedLossRunWrapper pibit.DataWrapper,
	appWrapper application.DataWrapper,
) (bool, error) {
	submissions, err := appWrapper.GetSubmissionsByAppId(ctx, applicationId)
	if err != nil {
		return false, errors.Wrapf(err, "failed to fetch submissions for application %s", applicationId)
	}
	submissionIds := slice_utils.Map(submissions, func(submission application.SubmissionObject) string {
		return submission.ID
	})
	docs, err := parsedLossRunWrapper.GetDocumentsBySubmissionIds(ctx, submissionIds)
	if err != nil {
		return false, errors.Wrapf(err, "failed to fetch documents for submission %s", applicationId)
	}
	allReceived := true
	for _, doc := range docs {
		if doc.Status == pibit.DocumentStatusSent {
			allReceived = false
			break
		}
	}
	return allReceived, nil
}
