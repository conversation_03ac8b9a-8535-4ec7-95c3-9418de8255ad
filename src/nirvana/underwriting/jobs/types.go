package jobs

import (
	"encoding/json"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/infra/config"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/db-api/db_wrappers/app_review_widget"

	"nirvanatech.com/nirvana/jobber/jtypes"
)

const (
	GenerateWidgetsForAppReview            = "RunGenerateWidgetsForAppReview"
	GenerateAppReviewWidget                = "RunGenerateAppReviewWidget"
	GenerateExperiments                    = "GenerateExperiments"
	BackFillAppReviewWidget                = "RunBackFillAppReviewWidget"
	GenerateAppReviewWidgetData            = "RunGenerateAppReviewWidgetData"
	RepullAppReviewWidget                  = "RunRepullAppReviewWidget"
	GenerateAppetiteFactors                = "GenerateAppetiteFactors"
	RefreshFleetUnderwritingUsecases       = "RefreshFleetUnderwritingUsecases"
	CancellationScoreSnowflakePostgresSync = "RunCancellationScoreSnowflakePostgresSync"
	GenerateMstReferralRules               = "GenerateMstReferralRules"
	GenerateAppetiteFactorsNF              = "GenerateAppetiteFactorsNF"
	UpdateAppetiteFactorsNF                = "UpdateAppetiteFactorsNF"
	UpdateApplicationPreTelematicsStatus   = "UpdateApplicationPreTelematicsStatus"
	GenerateUwNotesWorkflow                = "GenerateUwNotesWorkflow"
)

type GenerateAppReviewWidgetsArgs struct {
	AppReviewId string
}

func (g *GenerateAppReviewWidgetsArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*g)
}

func (g *GenerateAppReviewWidgetsArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*GenerateAppReviewWidgetsArgs)(nil)

type BackFillAppReviewWidgetsArgs struct {
	AppReviewIds []uuid.UUID
	WidgetEnums  []app_review_widget.AppReviewWidget
	BackFillAll  bool
}

func (g *BackFillAppReviewWidgetsArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*g)
}

func (g *BackFillAppReviewWidgetsArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*BackFillAppReviewWidgetsArgs)(nil)

func BackFillAppReviewWidgetsUnmarshalFn(data []byte, m jtypes.MessageVersion) (*BackFillAppReviewWidgetsArgs, error) {
	var s BackFillAppReviewWidgetsArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return nil, err
	}
	return &s, nil
}

func GenerateAppReviewUnmarshalFn(data []byte, m jtypes.MessageVersion) (*GenerateAppReviewWidgetsArgs, error) {
	var s GenerateAppReviewWidgetsArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return nil, err
	}
	return &s, nil
}

type GenerateWidgetDataArgs struct {
	ReviewId string
}

func (g *GenerateWidgetDataArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*g)
}

func (g *GenerateWidgetDataArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*GenerateWidgetDataArgs)(nil)

func GenerateWidgetDataUnmarshalFn(data []byte, m jtypes.MessageVersion) (*GenerateWidgetDataArgs, error) {
	var s GenerateWidgetDataArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return nil, err
	}
	return &s, nil
}

type RefreshUnderwritingUsecasesArgs struct{}

func (g *RefreshUnderwritingUsecasesArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*g)
}

func (g *RefreshUnderwritingUsecasesArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*RefreshUnderwritingUsecasesArgs)(nil)

func RefreshUnderwritingUsecasesUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*RefreshUnderwritingUsecasesArgs, error) {
	var s RefreshUnderwritingUsecasesArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return nil, err
	}
	return &s, nil
}

type GenerateAppReviewWidgetDataArgs struct {
	Id uuid.UUID
}

func (g *GenerateAppReviewWidgetDataArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*g)
}

func (g *GenerateAppReviewWidgetDataArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*GenerateAppReviewWidgetDataArgs)(nil)

func GenerateAppReviewWidgetDataUnmarshalFn(data []byte, m jtypes.MessageVersion) (*GenerateAppReviewWidgetDataArgs, error) {
	var s GenerateAppReviewWidgetDataArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return nil, err
	}
	return &s, nil
}

type RepullWidgetArgs struct {
	Id          uuid.UUID
	WidgetEnums []app_review_widget.AppReviewWidget
}

func (g *RepullWidgetArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*g)
}

func (g *RepullWidgetArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*RepullWidgetArgs)(nil)

func RepullWidgetUnmarshalFn(data []byte, m jtypes.MessageVersion) (*RepullWidgetArgs, error) {
	var s RepullWidgetArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return nil, err
	}
	return &s, nil
}

type GenerateExperimentsArgs struct {
	AppReviewId string
	IsRollback  bool
}

func (g *GenerateExperimentsArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*g)
}

func (g *GenerateExperimentsArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*GenerateExperimentsArgs)(nil)

func GenerateExperimentsUnmarshalFn(data []byte, m jtypes.MessageVersion) (*GenerateExperimentsArgs, error) {
	var s GenerateExperimentsArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return nil, err
	}
	return &s, nil
}

type GenerateAppetiteFactorsArgs struct {
	AppReviewId string
	// IsBackfillAttempt set to true should only be used for backfills where rollback apps also need to be considered
	IsBackfillAttempt bool
	// BackfillSequenceID should be passed if IsBackfillAttempt is true. This helps us keep track of the sequencing of the backfills done.
	BackfillSequenceId *string
}

func (g *GenerateAppetiteFactorsArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*g)
}

func (g *GenerateAppetiteFactorsArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*GenerateAppetiteFactorsArgs)(nil)

func GenerateAppetiteScoreUnmarshalFn(data []byte, m jtypes.MessageVersion) (*GenerateAppetiteFactorsArgs, error) {
	var s GenerateAppetiteFactorsArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return nil, err
	}
	return &s, nil
}

type GenerateMstReferralRulesArgs struct {
	AppReviewId string
}

func (g *GenerateMstReferralRulesArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*g)
}

func (g *GenerateMstReferralRulesArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*GenerateMstReferralRulesArgs)(nil)

func GenerateMstReferralRulesArgsUnmarshalFn(data []byte, m jtypes.MessageVersion) (*GenerateMstReferralRulesArgs, error) {
	if m != 0 {
		return nil, errors.Newf("version is not valid")
	}
	var s GenerateMstReferralRulesArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return nil, err
	}
	return &s, nil
}

type UpdateApplicationPreTelematicsStatusArgs struct{}

func (g *UpdateApplicationPreTelematicsStatusArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*g)
}

func (g *UpdateApplicationPreTelematicsStatusArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*UpdateApplicationPreTelematicsStatusArgs)(nil)

func UpdateApplicationPreTelematicsStatusUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*UpdateApplicationPreTelematicsStatusArgs, error) {
	var s UpdateApplicationPreTelematicsStatusArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return nil, err
	}
	return &s, nil
}

type GenerateUwNotesMessage struct {
	ApplicationReviewID uuid.UUID
}

func (s *GenerateUwNotesMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func (s *GenerateUwNotesMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

var _ jtypes.Message = &GenerateUwNotesMessage{}

func GenerateNotesUwMessageUnmarshalFn(data []byte, m jtypes.MessageVersion) (*GenerateUwNotesMessage, error) {
	var s GenerateUwNotesMessage
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type TriggerParseLossRunJobDeps struct {
	JobberClient      quoting_jobber.Client
	Config            *config.Config
	AuthWrapper       auth.DataWrapper
	AgencyWrapper     agency.DataWrapper
	FeatureFlagClient feature_flag_lib.Client
}

type AggregateLossRunsJobDeps struct {
	ParsedLossRunsWrapper pibit.DataWrapper
	AppWrapper            application.DataWrapper
	JobberClient          quoting_jobber.Client
}

var (
	ErrShouldTriggerFalse        = errors.New("shouldTrigger for parse loss runs job is false")
	ErrShouldTriggerErrored      = errors.New("error occurred while determining if job should be triggered")
	TagKeyReason                 = "reason"
	TagKeySource                 = "source"
	TagValueShouldTriggerErrored = "should_trigger_errored"
	TagValueJobTriggerErrored    = "job_trigger_errored"
	TagValueJobRunStuck          = "job_run_stuck"
	TagValueSubmission           = "submission"
	TagValueDocumentUpload       = "document_upload"
	TagValueEscalation           = "escalation"
)
