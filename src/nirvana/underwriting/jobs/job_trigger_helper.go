package jobs

import (
	"context"
	pagerduty2 "github.com/PagerDuty/go-pagerduty"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/str_utils"
	admitted "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	insurance_eng "nirvanatech.com/nirvana/insurance-core/monitoring"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/quoting/jobs"
)

var ErrTriggerPDFailed = errors.New("failed to trigger pager duty for portfolio monitoring job failure")

func TriggerInsurancePortfolioMonitoringJobs(
	ctx context.Context,
	applicationReviewWrapper uw.ApplicationReviewWrapper,
	insuranceEngPDClient insurance_eng.PagerDutyClient,
	quotingJobber quoting_jobber.Client,
	appId string,
	client Client,
	source TriggerSource,
	programType policyenums.ProgramType,
) error {
	// Triggering the jobs for all the application reviews of the given application.
	// We are doing this to ensure that the experiments gets updated for all the app reviews
	// of the given application once we receive consent for TSP connection
	if programType != policyenums.ProgramTypeFleet {
		return nil
	}
	appReviews, err := applicationReviewWrapper.GetAllReviewsForApp(ctx, appId)
	if err != nil {
		return errors.Wrapf(err, "Failed to find App Review for application: %s", appId)
	}
	for _, appReview := range appReviews {
		// only trigger jobs for non-terminal states (thus, also avoiding stale app reviews)
		if _, ok := uw.GetNonTerminalApplicationReviewStateMap()[appReview.State]; !ok {
			continue
		}
		pdDetails := map[string]any{
			"Application ID":        appReview.ApplicationID,
			"Application Review ID": appReview.Id,
			// easy to identify on an event of on-call ticket
			"Source": source.String(),
		}
		if err := TriggerGenerateExperimentsJob(
			ctx, quotingJobber, appReview.Id, source, false); err != nil {
			// just log the error event and generate a PD for it
			log.Error(ctx, "Failed to add GenerateExperiment job", log.Err(err))
			pdDetails["Error"] = err
			pdTriggerErr := TriggerPagerDutyForGenerateExperimentJobFailure(
				ctx, insuranceEngPDClient, appReview.Application.AgencyID, appReview.Id, client,
				pdDetails, err,
			)
			if pdTriggerErr != nil {
				return errors.Wrapf(err, "Failed to trigger PD for id: %s", appReview.ApplicationID)
			}
		}
	}
	return nil
}

func TriggerGenerateExperimentsJob(
	ctx context.Context,
	jobberClient quoting_jobber.Client,
	reviewId string,
	source TriggerSource,
	IsRollback bool,
) error {
	jobRunId, err := jobberClient.AddJobRun(ctx,
		jobber.NewAddJobRunParams(
			GenerateExperiments,
			&GenerateExperimentsArgs{
				AppReviewId: reviewId,
				IsRollback:  IsRollback,
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	if err != nil {
		return errors.Wrap(err, "failed to add GenerateExperiment job run")
	}
	log.Info(ctx, "successfully added GenerateExperiment job run", log.String("appReviewId", reviewId),
		log.Stringer("source", source), log.Stringer("jobRunId", jobRunId))
	return nil
}

func TriggerGenerateAppetiteFactorsForApplication(
	ctx context.Context,
	applicationReviewWrapper uw.ApplicationReviewWrapper,
	insuranceEngPDClient insurance_eng.PagerDutyClient,
	quotingJobber quoting_jobber.Client,
	appId string,
	client Client,
	source TriggerSource,
	programType policyenums.ProgramType,
) error {
	if programType != policyenums.ProgramTypeFleet {
		return nil
	}
	appReviews, err := applicationReviewWrapper.GetAllReviewsForApp(ctx, appId)
	if err != nil {
		return errors.Wrapf(err, "failed to find App Review for application: %s", appId)
	}
	for _, appReview := range appReviews {
		// only trigger jobs for non-terminal states (thus, also avoiding stale app reviews)
		if _, ok := uw.GetNonTerminalApplicationReviewStateMap()[appReview.State]; !ok {
			continue
		}
		pdDetails := map[string]any{
			"Application ID":        appReview.ApplicationID,
			"Application Review ID": appReview.Id,
			// easy to identify on an event of on-call ticket
			"Source": source.String(),
		}
		if _, err := TriggerGenerateAppetiteFactorsJob(ctx, quotingJobber, appReview.Id, source); err != nil {
			// just log the error event and generate a PD for it
			log.Error(ctx, "failed to add GenerateAppetiteFactors job", log.Err(err))
			pdDetails["Error"] = err
			pdTriggerErr := TriggerPagerDutyForGenerateAppetiteFactorsJobFailure(
				ctx, insuranceEngPDClient, appReview.Application.AgencyID, appReview.Id, client,
				pdDetails, err,
			)

			if pdTriggerErr != nil {
				return errors.Wrapf(err, "failed to trigger PD for id: %s", appReview.ApplicationID)
			}
		}
	}
	return nil
}

func TriggerGenerateAppetiteFactorsJob(
	ctx context.Context,
	jobberClient quoting_jobber.Client,
	reviewId string,
	source TriggerSource,
) (*jtypes.JobRunId, error) {
	jobRunId, err := jobberClient.AddJobRun(ctx,
		jobber.NewAddJobRunParams(
			GenerateAppetiteFactors,
			&GenerateAppetiteFactorsArgs{
				AppReviewId: reviewId,
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to add GenerateAppetiteFactors job run")
	}
	log.Info(ctx, "successfully added GenerateAppetiteFactors job run", log.String("appReviewId", reviewId),
		log.Stringer("source", source), log.Stringer("jobRunId", jobRunId))
	return &jobRunId, nil
}

func TriggerGenerateAppetiteFactorsNFForApplication(
	ctx context.Context,
	applicationReviewWrapper nf_app_review.Wrapper,
	admittedAppWrapper admitted.AdmittedAppWrapper,
	insuranceEngPDClient insurance_eng.PagerDutyClient,
	quotingJobber quoting_jobber.Client,
	appId string,
	client Client,
	source TriggerSource,
) error {
	appReviews, err := applicationReviewWrapper.GetAllReviewsForApp(ctx, appId)
	if err != nil {
		return errors.Wrapf(err, "failed to find App Review for application: %s", appId)
	}
	for _, appReview := range appReviews {
		// only trigger jobs for non-terminal states (thus, also avoiding stale app reviews)
		if _, ok := nf_app_review.GetNonTerminalAppReviewStateMap()[appReview.GetAppState()]; !ok {
			continue
		}
		app, err := admittedAppWrapper.GetAppById(ctx, appReview.GetApplicationID())
		if err != nil {
			return errors.Wrapf(err, "cannot find application for id %s", appReview.GetApplicationID())
		}
		pdDetails := map[string]any{
			"Application ID":        appReview.GetApplicationID(),
			"Application Review ID": appReview.GetID(),
			// easy to identify on an event of on-call ticket
			"Source": source.String(),
		}
		if _, err := TriggerGenerateAppetiteFactorsNFJob(ctx, quotingJobber, appReview.GetID().String(), source); err != nil {
			// just log the error event and generate a PD for it
			log.Error(ctx, "failed to add GenerateAppetiteFactorsNF job", log.Err(err))
			pdDetails["Error"] = err
			pdTriggerErr := TriggerPagerDutyForGenerateAppetiteFactorsNFJobFailure(
				ctx, insuranceEngPDClient, app.AgencyID, appReview.GetID().String(), client,
				pdDetails, err,
			)

			if pdTriggerErr != nil {
				return errors.Wrapf(err, "failed to trigger PD for id: %s", appReview.GetApplicationID())
			}
		}
	}
	return nil
}

func TriggerGenerateAppetiteFactorsNFJob(
	ctx context.Context,
	jobberClient quoting_jobber.Client,
	reviewId string,
	source TriggerSource,
) (*jtypes.JobRunId, error) {
	jobRunId, err := jobberClient.AddJobRun(
		ctx,
		jobber.NewAddJobRunParams(
			GenerateAppetiteFactorsNF,
			&GenerateAppetiteFactorsArgs{
				AppReviewId: reviewId,
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to add GenerateAppetiteFactorsNF job run for app review %s, source %s", reviewId, source)
	}
	log.Info(ctx, "successfully added GenerateAppetiteFactorsNF job run", log.String("appReviewId", reviewId),
		log.Stringer("source", source), log.Stringer("jobRunId", jobRunId))
	return &jobRunId, nil
}

func TriggerPagerDutyForGenerateExperimentJobFailure(
	ctx context.Context,
	pdClient insurance_eng.PagerDutyClient,
	agencyID uuid.UUID,
	appReviewID string,
	client Client,
	pdDetails map[string]any,
	err error,
) error {
	pdErr := pdClient.TriggerAlert(ctx, &insurance_eng.TriggerPagerDutyAlertRequest{
		Event: pagerduty2.V2Event{
			Action:   "trigger",
			DedupKey: appReviewID + "_" + GenerateExperiments,
			Client:   client.String(),
			Payload: &pagerduty2.V2Payload{
				Summary:  "generate experiments job failed for " + appReviewID,
				Source:   client.String(),
				Severity: "error",
				Details:  pdDetails,
			},
		},
		AgencyID:    agencyID,
		ProgramType: policyenums.ProgramTypeFleet,
		Err:         err,
	})
	if pdErr != nil {
		return errors.Mark(pdErr, ErrTriggerPDFailed)
	}

	return nil
}

func TriggerPagerDutyForGenerateAppetiteFactorsJobFailure(
	ctx context.Context,
	pdClient insurance_eng.PagerDutyClient,
	agencyID uuid.UUID,
	appReviewID string,
	client Client,
	pdDetails map[string]any,
	err error,
) error {
	pdErr := pdClient.TriggerAlert(ctx, &insurance_eng.TriggerPagerDutyAlertRequest{
		Event: pagerduty2.V2Event{
			Action:   "trigger",
			DedupKey: appReviewID + "_" + GenerateAppetiteFactors,
			Client:   client.String(),
			Payload: &pagerduty2.V2Payload{
				Summary:  "add generate appetite factors job failed for " + appReviewID,
				Source:   client.String(),
				Severity: "error",
				Details:  pdDetails,
			},
		},
		AgencyID: agencyID,
		Err:      err,
	})
	if pdErr != nil {
		return errors.Mark(pdErr, ErrTriggerPDFailed)
	}

	return nil
}

func TriggerPagerDutyForGenerateAppetiteFactorsNFJobFailure(
	ctx context.Context,
	pdClient insurance_eng.PagerDutyClient,
	agencyID uuid.UUID,
	appReviewID string,
	client Client,
	pdDetails map[string]any,
	err error,
) error {
	// todo remove alert, send a metric instead
	pdErr := pdClient.TriggerAlert(ctx, &insurance_eng.TriggerPagerDutyAlertRequest{
		Event: pagerduty2.V2Event{
			Action:   "trigger",
			DedupKey: appReviewID + "_" + GenerateAppetiteFactorsNF,
			Client:   client.String(),
			Payload: &pagerduty2.V2Payload{
				Summary:  "add generate appetite factors nf job failed for " + appReviewID,
				Source:   client.String(),
				Severity: "error",
				Details:  pdDetails,
			},
		},
		AgencyID: agencyID,
		Err:      err,
	})
	if pdErr != nil {
		return errors.Mark(pdErr, ErrTriggerPDFailed)
	}

	return nil
}

func TriggerGenerateMstReferralRulesJob(
	ctx context.Context,
	jobberClient quoting_jobber.Client,
	reviewId string,
	source TriggerSource,
) (*jtypes.JobRunId, error) {
	jobRunId, err := jobberClient.AddJobRun(
		ctx,
		jobber.NewAddJobRunParams(
			GenerateMstReferralRules,
			&GenerateMstReferralRulesArgs{
				AppReviewId: reviewId,
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	if err != nil {
		return nil, errors.Wrapf(err,
			"failed to add GenerateMstReferralRules job run for app review %s, source %s", reviewId, source)
	}
	log.Info(ctx, "successfully added GenerateMstReferralRules job run", log.String("appReviewId", reviewId),
		log.Stringer("source", source), log.Stringer("jobRunId", jobRunId))
	return &jobRunId, nil
}

func TriggerParseLossRunsJob(
	ctx context.Context,
	applicationId string,
	submissionId string,
	agencyId uuid.UUID,
	createdBy string,
	deps *TriggerParseLossRunJobDeps,
	creatorUUID *uuid.UUID,
) (*jtypes.JobRunId, error) {
	log.Info(ctx, "Attempting to add TriggerParseLossRunsJob job", log.String("submissionID", submissionId), log.String("applicationId", applicationId))
	shouldTrigger, err := ShouldTriggerLossRunParsing(ctx, deps.Config, deps.AgencyWrapper, deps.AuthWrapper, deps.FeatureFlagClient, createdBy, agencyId, submissionId)
	if err != nil {
		log.Error(ctx, "Error occurred while determining whether job run should be triggered", log.Err(err))
		return nil, ErrShouldTriggerErrored
	}
	if !shouldTrigger {
		log.Info(ctx, "skipping TriggerParseLossRuns job run", log.String("applicationID", applicationId),
			log.String("submissionID", submissionId), log.Stringer("agencyID", agencyId),
			log.String("createdBy", createdBy))
		return nil, ErrShouldTriggerFalse
	}
	params := jobber.NewAddJobRunParams(
		jobs.TriggerParseLossRuns,
		&jobs.TriggerParseLossRunsArgs{
			ApplicationId: applicationId,
			SubmissionId:  submissionId,
			AgencyId:      agencyId,
		},
		jtypes.NewMetadata(jtypes.Immediate),
	)
	if creatorUUID != nil {
		params = params.WithCreatorUUID(*creatorUUID)
	}
	jobRunId, err := deps.JobberClient.AddJobRun(
		ctx,
		params,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to add TriggerParseLossRuns job run for application %s", applicationId)
	}
	log.Info(ctx, "successfully added TriggerParseLossRuns job run", log.String("applicationID", applicationId),
		log.Stringer("jobRunId", jobRunId))
	return &jobRunId, nil
}

func TriggerLossRunAggregation(
	ctx context.Context,
	applicationId string,
	deps *AggregateLossRunsJobDeps,
) (*jtypes.JobRunId, error) {
	log.Info(ctx, "Attempting to add LossRunAggregation job", log.String("applicationID", applicationId))
	shouldTrigger, err := ShouldTriggerLossRunAggregation(ctx, applicationId, deps.ParsedLossRunsWrapper, deps.AppWrapper)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to determine shouldTrigger (loss run aggregation) for application %s", applicationId)
	}
	if !shouldTrigger {
		log.Info(ctx, "skipping LossRunAggregation job", log.String("applicationID", applicationId))
		return nil, ErrShouldTriggerFalse
	}
	jobRunId, err := deps.JobberClient.AddJobRun(ctx, jobber.NewAddJobRunParams(
		jobs.AggregateLossRuns,
		&jobs.AggregateLossRunsArgs{
			ApplicationId: applicationId,
		},
		jtypes.NewMetadata(jtypes.Immediate),
	))
	if err != nil {
		return nil, errors.Wrapf(err, "failed to add AggregateLossRuns job run for application %s", applicationId)
	}
	log.Info(ctx, "successfully added AggregateLossRuns job run", log.String("applicationId", applicationId))
	return &jobRunId, nil
}

func EmitFailedMetricForJob(
	ctx context.Context,
	client statsd.Statter,
	jobName string,
	additionalTags ...statsd.Tag,
) {
	statName := GetJobFailureStatKey(jobName)
	tags := append([]statsd.Tag{{"success", "false"}}, additionalTags...)
	err := client.Inc(
		statName, 1, 1, tags...,
	)
	if err != nil {
		log.Error(ctx, "failed to emit metric", log.String("metricName", statName))
	}
}

func GetJobFailureStatKey(jobName string) string {
	return str_utils.ToSnakeCase(jobName) + ".add_job.count"
}

//go:generate go run github.com/dmarkham/enumer -type=Client -json -trimprefix=Client
type Client int

const (
	ClientAgents Client = iota
	ClientUnderwriter
)

//go:generate go run github.com/dmarkham/enumer -type=TriggerSource -json -trimprefix=TriggerSource -transform=whitespace
type TriggerSource int

const (
	TriggerSourceHandlerTelematicsConnection TriggerSource = iota
	TriggerSourceHandlerTelematicsConnectionComplete
	TriggerSourceJobConnectDataProvider
	TriggerSourceJobGenerateWidgets
	TriggerSourceHandlerUpdateAccountGrade
	TriggerSourceHandlerRollbackAppReview
	TriggerSourceAppReviewWidgetApplyOverrides
	TriggerSourceDataPipelineStateUpdate
	TriggerSourceEffectiveDateUpdate
	TriggerSourceUpdateAppetiteFactorsJob
	TriggerSourceSafetyScoreWidgetUpdate // DEPRECATED
	TriggerSourceGenerateAppetiteFactorsJob
	TriggerSourceUpdateSubmissionForUW
	TriggerSourceHandlerPutVinVisibilityChecklist
	TriggerSourceHandlerUpdateAssignees
	TriggerSourceHandlerDriverUpdate
	TriggerSourceHandlerDriverRecordUpdate
	TriggerSourceRefreshFleetUnderwritingUsecases
)
