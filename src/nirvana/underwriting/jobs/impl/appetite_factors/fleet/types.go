package fleet

import (
	"github.com/volatiletech/null/v8"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw/appetite_factors"
	"nirvanatech.com/nirvana/underwriting/appetite_factors/appetite_score_rubric"

	rubric "nirvanatech.com/nirvana/common-go/application-util/appetite_factors/recommended_action_rubric"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/appetite_factor"
)

type RecommendedActionGeneratorInput struct {
	LogicResolverFactory appetite_score_rubric.LogicResolverFactory
	AppReview            *uw.ApplicationReview
	AppetiteScore        appetite_factor.AppetiteScore
	CurrentSafetyScore   *appetite_factor.SafetyScore
	TspKind              *telematics.TSP
	IsTspPremier         *bool
	NumOfPU              null.Int32
	// Deprecated
	IsCRatingRecent              bool
	VinVisibilityVal             *underwriting.ApplicationReviewVinVisibility
	IsChecklistCompleted         bool
	HazardZone                   rubric.HazardZone
	IsRiskScoreElementNotPresent bool // If RiskScore element was not present in feature records table row
	// Deprecated
	IsLossesBurnRateGreaterThan20K bool
	AppetiteGuidelinesInput        rubric.AppetiteGuidelinesInput
	version                        *appetite_factors.Version
}
