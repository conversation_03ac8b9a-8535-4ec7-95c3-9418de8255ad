load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "fleet",
    srcs = [
        "helpers.go",
        "types.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/jobs/impl/appetite_factors/fleet",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/application-util/appetite_factors/recommended_action_rubric",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/risk_score_utils",
        "//nirvana/common-go/rule_engine",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/db-api/db_wrappers/uw/appetite_factors",
        "//nirvana/feature_store",
        "//nirvana/features/hazard_zones",
        "//nirvana/openapi-specs/components/underwriting",
        "//nirvana/telematics",
        "//nirvana/telematics/connections",
        "//nirvana/underwriting/app_review",
        "//nirvana/underwriting/app_review/widgets/safety",
        "//nirvana/underwriting/appetite_factors/appetite_score_rubric",
        "//nirvana/underwriting/appetite_guidelines/guidelines",
        "//nirvana/underwriting/appetite_guidelines/models",
        "//nirvana/underwriting/rule-engine/appetite_factors/appetite_factor",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
    ],
)

go_test(
    name = "fleet_test",
    srcs = ["helpers_test.go"],
    embed = [":fleet"],
    deps = [
        "//nirvana/common-go/application-util/appetite_factors/recommended_action_rubric",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/rule_engine",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/db-api/db_wrappers/uw/appetite_factors",
        "//nirvana/infra/fx/testfixtures/appetite_guidelines_fixture",
        "//nirvana/infra/fx/testfixtures/application_review_fixture",
        "//nirvana/infra/fx/testfixtures/datagov_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/openapi-specs/components/underwriting",
        "//nirvana/telematics",
        "//nirvana/telematics/connections",
        "//nirvana/telematics/connections/oauth",
        "//nirvana/underwriting/appetite_factors/appetite_score_rubric",
        "//nirvana/underwriting/appetite_factors/dsclient/dsv2",
        "//nirvana/underwriting/appetite_guidelines/guidelines",
        "//nirvana/underwriting/appetite_guidelines/models",
        "//nirvana/underwriting/jobs/impl/appetite_factors",
        "//nirvana/underwriting/rule-engine/appetite_factors/appetite_factor",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
    ],
)
