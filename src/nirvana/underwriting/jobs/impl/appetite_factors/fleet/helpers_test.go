package fleet

import (
	"context"
	"testing"
	"time"

	"nirvanatech.com/nirvana/infra/fx/testfixtures/appetite_guidelines_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/datagov_fixture"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw/appetite_factors"

	"nirvanatech.com/nirvana/underwriting/appetite_factors/appetite_score_rubric"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"

	rubric "nirvanatech.com/nirvana/common-go/application-util/appetite_factors/recommended_action_rubric"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/rule_engine"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	"nirvanatech.com/nirvana/telematics"
	tsp_connections "nirvanatech.com/nirvana/telematics/connections"
	"nirvanatech.com/nirvana/telematics/connections/oauth"
	"nirvanatech.com/nirvana/underwriting/appetite_factors/dsclient/dsv2"
	appetite_factors2 "nirvanatech.com/nirvana/underwriting/jobs/impl/appetite_factors"
	"nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/appetite_factor"
)

func TestGetRecommendedAction(t *testing.T) {
	fleetResolver := appetite_score_rubric.NewLogicResolver(appetite_score_rubric.LogicResolverParams{
		DSCalculatorV2: &appetite_score_rubric.AppetiteFactorsDataScienceCalculatorV2{
			DSClientV2: &dsv2.MockDSClientV2{},
		},
	})

	logicResolverFactory := appetite_score_rubric.NewLogicResolverFactory(
		appetite_score_rubric.LogicResolverFactoryParams{
			FleetResolver: fleetResolver,
		},
	)
	resolver, err := logicResolverFactory.GetLogicResolver(enums.ProgramTypeFleet)
	require.NoError(t, err)
	latestVersion := resolver.GetLatestVersion()
	var hazardZone rubric.HazardZone
	tests := []struct {
		name                   string
		appReview              *uw.ApplicationReview
		appetiteScore          appetite_factor.AppetiteScore
		currentSafetyScore     *appetite_factor.SafetyScore
		isTspPremier           *bool
		numOfPU                null.Int32
		isChecklistCompleted   bool
		vinVisibilityVal       *underwriting.ApplicationReviewVinVisibility
		tspKind                *telematics.TSP
		shouldApplyPricingExp  bool
		expectedAction         appetite_factor.RecommendedAction
		expectedSupposedAction *appetite_factor.RecommendedAction
		expectedVersion        *appetite_factors.Version
		expectedReason         *appetite_factor.RecommendedActionReason
	}{
		{
			name: "Renewal Application",
			appReview: &uw.ApplicationReview{
				Application: application.Application{
					RenewalMetadata: &application.RenewalMetadata{},
				},
			},
			expectedAction:  appetite_factor.RecommendedActionNotApplicableForRenewal,
			expectedVersion: nil,
		},
		{
			name: "Non-Renewal Application with < 10 PUs",
			appReview: &uw.ApplicationReview{
				Application: application.Application{},
			},
			numOfPU:         null.Int32From(9),
			expectedAction:  appetite_factor.RecommendedActionNotApplicableForNonFleet,
			expectedVersion: nil,
		},
		{
			name: "If the recommended action is decline then irrespective of the vin visibility it'll be decline",
			appReview: &uw.ApplicationReview{
				EffectiveDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				RecommendedActionInfo: &uw.RecommendedActionInfo{
					Version: &latestVersion,
				},
			},
			tspKind:              pointer_utils.ToPointer(telematics.TSPOaneELD),
			appetiteScore:        appetite_factor.AppetiteScorePreferred,
			expectedAction:       appetite_factor.RecommendedActionDecline,
			isTspPremier:         pointer_utils.ToPointer(false),
			expectedVersion:      &appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			isChecklistCompleted: true,
			vinVisibilityVal: &underwriting.ApplicationReviewVinVisibility{
				VisibleAndOverlappingVinCountPercentage: 100,
			},
		},
		{
			name: "If the vin visibility is already greater than 60% then it'll be the original action",
			appReview: &uw.ApplicationReview{
				EffectiveDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				RecommendedActionInfo: &uw.RecommendedActionInfo{
					Version: &latestVersion,
				},
			},
			tspKind:              pointer_utils.ToPointer(telematics.TSPOmnitracs),
			appetiteScore:        appetite_factor.AppetiteScorePreferred,
			expectedAction:       appetite_factor.RecommendedActionQuote,
			isTspPremier:         pointer_utils.ToPointer(false),
			expectedVersion:      &appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			isChecklistCompleted: false,
			vinVisibilityVal: &underwriting.ApplicationReviewVinVisibility{
				VisibleAndOverlappingVinCountPercentage: 76,
			},
			expectedReason: nil,
		},
		{
			name: "If vin visibility is between 20% and 60%, don't alter the base recommendation (regardless of checklist)",
			appReview: &uw.ApplicationReview{
				EffectiveDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				RecommendedActionInfo: &uw.RecommendedActionInfo{
					Version: &latestVersion,
				},
			},
			tspKind:              pointer_utils.ToPointer(telematics.TSPOmnitracs),
			appetiteScore:        appetite_factor.AppetiteScorePreferred,
			expectedAction:       appetite_factor.RecommendedActionQuote,
			isTspPremier:         pointer_utils.ToPointer(false),
			expectedVersion:      &appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			isChecklistCompleted: false,
			vinVisibilityVal: &underwriting.ApplicationReviewVinVisibility{
				VisibleAndOverlappingVinCountPercentage: 55,
			},
			expectedReason: nil,
		},
		{
			name: "If vin visibility is between 20% and 60% with completed checklist, don't alter the base recommendation",
			appReview: &uw.ApplicationReview{
				EffectiveDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				RecommendedActionInfo: &uw.RecommendedActionInfo{
					Version: &latestVersion,
				},
			},
			tspKind:              pointer_utils.ToPointer(telematics.TSPOmnitracs),
			appetiteScore:        appetite_factor.AppetiteScorePreferred,
			expectedAction:       appetite_factor.RecommendedActionQuote,
			isTspPremier:         pointer_utils.ToPointer(false),
			expectedVersion:      &appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			isChecklistCompleted: true,
			vinVisibilityVal: &underwriting.ApplicationReviewVinVisibility{
				VisibleAndOverlappingVinCountPercentage: 59,
			},
			expectedReason: nil,
		},
		{
			name: "If vin visibility is below 20%, set to Pending (out of appetite) regardless of checklist status",
			appReview: &uw.ApplicationReview{
				EffectiveDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				RecommendedActionInfo: &uw.RecommendedActionInfo{
					Version: &latestVersion,
				},
			},
			tspKind:              pointer_utils.ToPointer(telematics.TSPOmnitracs),
			appetiteScore:        appetite_factor.AppetiteScorePreferred,
			expectedAction:       appetite_factor.RecommendedActionPending,
			isTspPremier:         pointer_utils.ToPointer(false),
			expectedVersion:      &appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			isChecklistCompleted: true, // Even with completed checklist
			vinVisibilityVal: &underwriting.ApplicationReviewVinVisibility{
				VisibleAndOverlappingVinCountPercentage: 15,
			},
			expectedReason: pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonVinVisibilityLessThanThreshold),
		},
		{
			name: "If vin visibility is below 20% and checklist not completed, still set to Pending",
			appReview: &uw.ApplicationReview{
				EffectiveDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				RecommendedActionInfo: &uw.RecommendedActionInfo{
					Version: &latestVersion,
				},
			},
			tspKind:              pointer_utils.ToPointer(telematics.TSPOmnitracs),
			appetiteScore:        appetite_factor.AppetiteScorePreferred,
			expectedAction:       appetite_factor.RecommendedActionPending,
			isTspPremier:         pointer_utils.ToPointer(false),
			expectedVersion:      &appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			isChecklistCompleted: false,
			vinVisibilityVal: &underwriting.ApplicationReviewVinVisibility{
				VisibleAndOverlappingVinCountPercentage: 15,
			},
			expectedReason: pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonVinVisibilityLessThanThreshold),
		},
		{
			name: "Non-premier TSP, good vin visibility, num of PUs > 100",
			appReview: &uw.ApplicationReview{
				EffectiveDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				RecommendedActionInfo: &uw.RecommendedActionInfo{
					Version: &latestVersion,
				},
			},
			tspKind:              pointer_utils.ToPointer(telematics.TSPOmnitracs),
			appetiteScore:        appetite_factor.AppetiteScorePreferred,
			expectedAction:       appetite_factor.RecommendedActionDecline,
			isTspPremier:         pointer_utils.ToPointer(false),
			expectedVersion:      &appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			isChecklistCompleted: false,
			numOfPU:              null.Int32From(101),
			vinVisibilityVal: &underwriting.ApplicationReviewVinVisibility{
				VisibleAndOverlappingVinCountPercentage: 65,
			},
			expectedReason: pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonGTHundredFleetSizeWithNonPremier),
		},
		{
			name: "Non-premier TSP, good vin visibility, num of PUs in [50, 100], then action should be quote",
			appReview: &uw.ApplicationReview{
				EffectiveDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				RecommendedActionInfo: &uw.RecommendedActionInfo{
					Version: &latestVersion,
				},
			},
			tspKind:              pointer_utils.ToPointer(telematics.TSPOmnitracs),
			appetiteScore:        appetite_factor.AppetiteScorePreferred,
			expectedAction:       appetite_factor.RecommendedActionNeutral,
			isTspPremier:         pointer_utils.ToPointer(false),
			expectedVersion:      &appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			isChecklistCompleted: false,
			numOfPU:              null.Int32From(50),
			vinVisibilityVal: &underwriting.ApplicationReviewVinVisibility{
				VisibleAndOverlappingVinCountPercentage: 65,
			},
			expectedSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			expectedReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier),
		},
		{
			name: "Non-premier TSP with visibility between 20-60%, original action should be preserved",
			appReview: &uw.ApplicationReview{
				EffectiveDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				RecommendedActionInfo: &uw.RecommendedActionInfo{
					Version: &latestVersion,
				},
			},
			tspKind:              pointer_utils.ToPointer(telematics.TSPOmnitracs),
			appetiteScore:        appetite_factor.AppetiteScorePreferred,
			expectedAction:       appetite_factor.RecommendedActionNeutral,
			isTspPremier:         pointer_utils.ToPointer(false),
			expectedVersion:      &appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			isChecklistCompleted: false,
			numOfPU:              null.Int32From(50),
			vinVisibilityVal: &underwriting.ApplicationReviewVinVisibility{
				VisibleAndOverlappingVinCountPercentage: 45,
			},
			expectedSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			expectedReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier),
		},
		{
			name: "If the recommended action is anything but decline and checklist is not completed then it'll be Quote (instead of Pending) without reason because pricing exp is active",
			appReview: &uw.ApplicationReview{
				EffectiveDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				RecommendedActionInfo: &uw.RecommendedActionInfo{
					Version: &latestVersion,
				},
			},
			tspKind:              pointer_utils.ToPointer(telematics.TSPOmnitracs),
			appetiteScore:        appetite_factor.AppetiteScorePreferred,
			expectedAction:       appetite_factor.RecommendedActionQuote,
			isTspPremier:         pointer_utils.ToPointer(false),
			expectedVersion:      &appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			isChecklistCompleted: false,
			vinVisibilityVal: &underwriting.ApplicationReviewVinVisibility{
				VisibleAndOverlappingVinCountPercentage: 55,
			},
			shouldApplyPricingExp: true,
			expectedReason:        nil,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			test := test
			t.Parallel()
			input := &RecommendedActionGeneratorInput{
				LogicResolverFactory: logicResolverFactory,
				AppReview:            test.appReview,
				AppetiteScore:        test.appetiteScore,
				CurrentSafetyScore:   test.currentSafetyScore,
				TspKind:              test.tspKind,
				IsTspPremier:         test.isTspPremier,
				NumOfPU:              test.numOfPU,
				IsCRatingRecent:      false,
				VinVisibilityVal:     test.vinVisibilityVal,
				IsChecklistCompleted: test.isChecklistCompleted,
				HazardZone:           hazardZone,
			}
			action, reason, supposedAction, version, err := GetRecommendedAction(context.TODO(), input)
			assert.NoError(t, err)
			assert.NotNil(t, action)
			assert.Equal(t, test.expectedAction, *action)
			if test.expectedSupposedAction != nil {
				assert.NotNil(t, supposedAction)
				assert.Equal(t, *test.expectedSupposedAction, *supposedAction)
			} else {
				assert.Nil(t, supposedAction)
			}
			assert.Equal(t, test.expectedVersion, version)
			if test.expectedReason != nil {
				assert.Equal(t, test.expectedReason, reason)
			}
		})
	}
}

func TestCalculateRecommendedAction(t *testing.T) {
	reviewId := uuid.New().String()
	appReview := &uw.ApplicationReview{
		Id:            reviewId,
		EffectiveDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
	}
	var (
		hz      rubric.HazardZone
		puCount null.Int32
	)
	tests := []struct {
		name               string
		appetiteScore      appetite_factor.AppetiteScore
		currentSafetyScore *appetite_factor.SafetyScore
		expectedAction     appetite_factor.RecommendedAction
		isPremier          *bool
		tspKind            *telematics.TSP
	}{
		{
			name:          "Premier TSP and Safety Score present",
			appetiteScore: appetite_factor.AppetiteScorePreferred,
			currentSafetyScore: &appetite_factor.SafetyScore{
				MarketCategory: pointer_utils.ToPointer("Target"),
			},
			expectedAction: appetite_factor.RecommendedActionStronglyQuote,
			isPremier:      pointer_utils.ToPointer(true),
		},
		{
			name:           "Premier TSP and Safety Score not available yet",
			expectedAction: appetite_factor.RecommendedActionPending,
			isPremier:      pointer_utils.ToPointer(true),
		},
		{
			name:           "TSP premier flag is nil",
			expectedAction: appetite_factor.RecommendedActionPending,
			isPremier:      nil,
		},
		{
			name:           "Exception TSP and Safety Score not present, TSP part of allowed exception TSP",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			expectedAction: appetite_factor.RecommendedActionQuote,
			isPremier:      pointer_utils.ToPointer(false),
			tspKind:        pointer_utils.ToPointer(telematics.TSPOmnitracs),
		},
		{
			name:           "Exception TSP and Safety Score not present, TSP not part of allowed exception TSP",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			expectedAction: appetite_factor.RecommendedActionDecline,
			isPremier:      pointer_utils.ToPointer(false),
			tspKind:        pointer_utils.ToPointer(telematics.TSPOaneELD),
		},
		{
			name:          "Exception TSP and Safety Score present, TSP part of allowed exception TSP",
			appetiteScore: appetite_factor.AppetiteScorePreferred,
			currentSafetyScore: &appetite_factor.SafetyScore{
				MarketCategory: pointer_utils.ToPointer("Target"),
			},
			expectedAction: appetite_factor.RecommendedActionQuote,
			isPremier:      pointer_utils.ToPointer(false),
			tspKind:        pointer_utils.ToPointer(telematics.TSPOmnitracs),
		},
	}

	fleetResolver := appetite_score_rubric.NewLogicResolver(appetite_score_rubric.LogicResolverParams{
		DSCalculatorV2: &appetite_score_rubric.AppetiteFactorsDataScienceCalculatorV2{
			DSClientV2: &dsv2.MockDSClientV2{},
		},
	})

	logicResolverFactory := appetite_score_rubric.NewLogicResolverFactory(
		appetite_score_rubric.LogicResolverFactoryParams{
			FleetResolver: fleetResolver,
		},
	)
	resolver, err := logicResolverFactory.GetLogicResolver(enums.ProgramTypeFleet)
	assert.NoError(t, err)
	version := resolver.GetLatestVersion()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt := tt
			t.Parallel()
			input := &RecommendedActionGeneratorInput{
				LogicResolverFactory: logicResolverFactory,
				AppReview:            appReview,
				AppetiteScore:        tt.appetiteScore,
				CurrentSafetyScore:   tt.currentSafetyScore,
				TspKind:              tt.tspKind,
				IsTspPremier:         tt.isPremier,
				NumOfPU:              puCount,
				IsCRatingRecent:      false,
				HazardZone:           hz,
				version:              &version,
			}
			action, _, _, err := calculateRecommendedAction(context.TODO(), input, enums.ProgramTypeFleet)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedAction, action)
		})
	}
}

func TestIsTSPPremier(t *testing.T) {
	var env struct {
		fx.In

		AppReviewFixture         *application_review_fixture.ApplicationReviewsFixture
		AppWrapper               application.DataWrapper
		ApplicationReviewWrapper uw.ApplicationReviewWrapper
		OauthConnManager         *oauth.OauthConnManager
		TSPConnManager           *tsp_connections.TSPConnManager
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.TODO()
	appReview := env.AppReviewFixture.ApplicationReview

	handleId := uuid.New()
	tspEnum := telematics.TSPSamsara

	err := env.AppWrapper.UpdateApp(ctx, appReview.ApplicationID,
		func(app application.Application) (application.Application, error) {
			app.TSPEnum = pointer_utils.ToPointer(tspEnum)
			app.TSPConnHandleId = pointer_utils.ToPointer(handleId.String())
			return app, nil
		})
	require.NoError(t, err)

	err = oauth.PreloadHandleForTest(ctx, env.OauthConnManager, handleId, tspEnum)
	require.NoError(t, err)

	updatedAppReview, err := env.ApplicationReviewWrapper.GetReview(ctx, appReview.Id)
	require.NoError(t, err)

	isPremier, tsp, err := IsTSPPremier(ctx, updatedAppReview, env.TSPConnManager)
	require.NoError(t, err)
	require.True(t, isPremier)
	require.NotNil(t, tsp)
}

func TestUpdateCurrentFactorsWithPreviousValues(t *testing.T) {
	// Some static variables for convenience
	V1K := pointer_utils.ToPointer(1000.0)
	V2K := pointer_utils.ToPointer(2000.0)
	V3K := pointer_utils.ToPointer(3000.0)

	A := pointer_utils.ToPointer("A")
	B := pointer_utils.ToPointer("B")
	C := pointer_utils.ToPointer("C")

	version1 := &appetite_factors.Version{Major: 1}
	version2 := &appetite_factors.Version{Major: 2}

	tests := []struct {
		name                  string
		prevVersion           *appetite_factors.Version
		currVersion           *appetite_factors.Version
		previousFactor        appetite_factor.AppetiteFactor
		currentFactor         appetite_factor.AppetiteFactor
		expectedCurrentFactor appetite_factor.AppetiteFactor
		expectHasDataUpdated  bool
		expectError           bool
	}{
		// New test case added explicitly to test version mismatch
		{
			name:                  "Different versions but values equal, fields still updated",
			prevVersion:           version1,
			currVersion:           version2,
			previousFactor:        NewMinMaxAppetiteFactor(nil, V1K, nil),
			currentFactor:         NewMinMaxAppetiteFactor(nil, V1K, nil),
			expectedCurrentFactor: NewMinMaxAppetiteFactor(V1K, V1K, nil),
			expectHasDataUpdated:  true, // because versions differ
		},
		{
			name:                  "Different versions and different values",
			prevVersion:           version1,
			currVersion:           version2,
			previousFactor:        NewMinMaxAppetiteFactor(nil, V2K, V2K),
			currentFactor:         NewMinMaxAppetiteFactor(nil, V1K, nil),
			expectedCurrentFactor: NewMinMaxAppetiteFactor(V2K, V1K, V2K),
			expectHasDataUpdated:  true,
		},
		{
			name:                  "MinMax - No value difference and same versions",
			prevVersion:           version1,
			currVersion:           version1,
			previousFactor:        NewMinMaxAppetiteFactor(nil, V1K, nil),
			currentFactor:         NewMinMaxAppetiteFactor(nil, V1K, nil),
			expectedCurrentFactor: NewMinMaxAppetiteFactor(V1K, V1K, nil),
			expectHasDataUpdated:  false,
		},
		{
			name:                  "MinMax - Update previous and last varied values",
			prevVersion:           version1,
			currVersion:           version1,
			previousFactor:        NewMinMaxAppetiteFactor(V2K, V1K, V3K),
			currentFactor:         NewMinMaxAppetiteFactor(nil, V1K, nil),
			expectedCurrentFactor: NewMinMaxAppetiteFactor(V1K, V1K, V2K),
			expectHasDataUpdated:  false,
		},
		{
			name:                  "MinMax - Previous with value, current without value",
			prevVersion:           version1,
			currVersion:           version1,
			previousFactor:        NewMinMaxAppetiteFactor(nil, V2K, nil),
			currentFactor:         NewMinMaxAppetiteFactor(nil, V1K, nil),
			expectedCurrentFactor: NewMinMaxAppetiteFactor(V2K, V1K, V2K),
			expectHasDataUpdated:  true, // values differ: V2K vs V1K
		},
		{
			name:                  "MinMax - Current factor has no value (nil), previous factor has value",
			prevVersion:           version1,
			currVersion:           version1,
			previousFactor:        NewMinMaxAppetiteFactor(nil, V2K, nil),
			currentFactor:         NewMinMaxAppetiteFactor(nil, nil, nil),
			expectedCurrentFactor: NewMinMaxAppetiteFactor(V2K, nil, V2K),
			expectHasDataUpdated:  true,
		},
		{
			name:                  "MinMax - Previous factor with incomplete fields",
			prevVersion:           version1,
			currVersion:           version1,
			previousFactor:        NewMinMaxAppetiteFactor(V2K, nil, V2K),
			currentFactor:         NewMinMaxAppetiteFactor(nil, V1K, nil),
			expectedCurrentFactor: NewMinMaxAppetiteFactor(nil, V1K, V2K),
			expectHasDataUpdated:  true,
		},
		{
			name:                  "Equality - No value difference and same versions",
			prevVersion:           version1,
			currVersion:           version1,
			previousFactor:        NewEqualityAppetiteFactor(nil, A, nil),
			currentFactor:         NewEqualityAppetiteFactor(nil, A, nil),
			expectedCurrentFactor: NewEqualityAppetiteFactor(A, A, nil),
			expectHasDataUpdated:  false,
		},
		{
			name:                  "Equality - Update fields with previous non-nil values",
			prevVersion:           version1,
			currVersion:           version1,
			previousFactor:        NewEqualityAppetiteFactor(B, A, C),
			currentFactor:         NewEqualityAppetiteFactor(nil, A, nil),
			expectedCurrentFactor: NewEqualityAppetiteFactor(A, A, B),
			expectHasDataUpdated:  false,
		},
		{
			name:                  "Equality - Current factor changed from previous factor",
			prevVersion:           version1,
			currVersion:           version1,
			previousFactor:        NewEqualityAppetiteFactor(nil, B, nil),
			currentFactor:         NewEqualityAppetiteFactor(nil, A, nil),
			expectedCurrentFactor: NewEqualityAppetiteFactor(B, A, B),
			expectHasDataUpdated:  true,
		},
		{
			name:                  "Equality - Current factor has nil values, previous has values",
			prevVersion:           version1,
			currVersion:           version1,
			previousFactor:        NewEqualityAppetiteFactor(nil, B, nil),
			currentFactor:         NewEqualityAppetiteFactor(nil, nil, nil),
			expectedCurrentFactor: NewEqualityAppetiteFactor(B, nil, B),
			expectHasDataUpdated:  true,
		},
		{
			name:                  "Equality - Previous has incomplete fields",
			prevVersion:           version1,
			currVersion:           version1,
			previousFactor:        NewEqualityAppetiteFactor(B, nil, B),
			currentFactor:         NewEqualityAppetiteFactor(nil, A, nil),
			expectedCurrentFactor: NewEqualityAppetiteFactor(nil, A, B),
			expectHasDataUpdated:  true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			hasDataUpdated, err := appetite_factors2.UpdateCurrentFactorsWithPreviousValues(
				[]appetite_factor.AppetiteFactor{tt.previousFactor},
				[]appetite_factor.AppetiteFactor{tt.currentFactor},
				tt.prevVersion,
				tt.currVersion)

			if tt.expectError {
				require.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.Equal(t, tt.expectHasDataUpdated, hasDataUpdated)

			if tt.currentFactor.Type() == appetite_factor.TypeMinMax {
				expectedTyped := tt.expectedCurrentFactor.(*appetite_factor.MinMaxAppetiteFactor)
				observedTyped := tt.currentFactor.(*appetite_factor.MinMaxAppetiteFactor)

				require.Equal(t, expectedTyped.Value, observedTyped.Value, "MinMax Value mismatch")
				require.Equal(t, expectedTyped.PreviousValue, observedTyped.PreviousValue, "MinMax PreviousValue mismatch")
				require.Equal(t, expectedTyped.LastVariedValue, observedTyped.LastVariedValue, "MinMax LastVariedValue mismatch")
			} else {
				expectedTyped := tt.expectedCurrentFactor.(*appetite_factor.EqualityAppetiteFactor)
				observedTyped := tt.currentFactor.(*appetite_factor.EqualityAppetiteFactor)

				require.Equal(t, expectedTyped.Value, observedTyped.Value, "Equality Value mismatch")
				require.Equal(t, expectedTyped.PreviousValue, observedTyped.PreviousValue, "Equality PreviousValue mismatch")
				require.Equal(t, expectedTyped.LastVariedValue, observedTyped.LastVariedValue, "Equality LastVariedValue mismatch")
			}
		})
	}
}

func NewMinMaxAppetiteFactor(previousValue, value, lastVariedValue *float64) appetite_factor.AppetiteFactor {
	return &appetite_factor.MinMaxAppetiteFactor{
		Base: appetite_factor.Base{
			Category: appetite_factor.CategoryALLossPerUnitPerYear,
			Type:     appetite_factor.TypeMinMax,
		},
		PreviousValue:   previousValue,
		Value:           value,
		LastVariedValue: lastVariedValue,
	}
}

func NewEqualityAppetiteFactor(previousValue, value, lastVariedValue *string) appetite_factor.AppetiteFactor {
	return &appetite_factor.EqualityAppetiteFactor{
		Base: appetite_factor.Base{
			Category: appetite_factor.CategoryDOTRating,
			Type:     appetite_factor.TypeEquality,
		},
		PreviousValue:   previousValue,
		Value:           value,
		LastVariedValue: lastVariedValue,
	}
}

func TestIsConditionalRatingRecent(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name                   string
		dotRating              rule_engine.String
		dotRatingEffDate       null.Time
		appReviewEffectiveDate time.Time
		expected               bool
	}{
		{
			name:                   "dotRating not Conditional or C",
			dotRating:              rule_engine.String{Value: "SatisfactoryRating", IsNull: false},
			dotRatingEffDate:       null.TimeFrom(time.Now().AddDate(-1, 0, 0)),
			appReviewEffectiveDate: time.Now(),
			expected:               false,
		},
		{
			name:                   "dotRating ConditionalRating, effDate within 2 years",
			dotRating:              rule_engine.String{Value: "ConditionalRating", IsNull: false},
			dotRatingEffDate:       null.TimeFrom(time.Now().AddDate(-1, 0, 0)),
			appReviewEffectiveDate: time.Now(),
			expected:               true,
		},
		{
			name:                   "dotRating C, effDate within 2 years",
			dotRating:              rule_engine.String{Value: "C", IsNull: false},
			dotRatingEffDate:       null.TimeFrom(time.Now().AddDate(-1, 0, 0)),
			appReviewEffectiveDate: time.Now(),
			expected:               true,
		},
		{
			name:                   "dotRating ConditionalRating, effDate beyond 2 years",
			dotRating:              rule_engine.String{Value: "ConditionalRating", IsNull: false},
			dotRatingEffDate:       null.TimeFrom(time.Now().AddDate(-3, 0, 0)),
			appReviewEffectiveDate: time.Now(),
			expected:               false,
		},
		{
			name:                   "dotRating C, effDate beyond 2 years",
			dotRating:              rule_engine.String{Value: "C", IsNull: false},
			dotRatingEffDate:       null.TimeFrom(time.Now().AddDate(-3, 0, 0)),
			appReviewEffectiveDate: time.Now(),
			expected:               false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt := tt
			t.Parallel()
			result := IsConditionalRatingRecent(tt.dotRating, tt.dotRatingEffDate, tt.appReviewEffectiveDate)
			if result != tt.expected {
				t.Errorf("isConditionalRatingRecent() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestValidateAndGetAppReview(t *testing.T) {
	var env struct {
		fx.In

		AppReviewFixture         *application_review_fixture.ApplicationReviewsFixture
		ApplicationReviewWrapper uw.ApplicationReviewWrapper
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.TODO()

	t.Parallel()
	tests := []struct {
		name               string
		isBackfill         bool
		backfillSequenceId *string
		expectedError      error
	}{
		{
			name:          "backfilling without sequenceId should fail",
			isBackfill:    true,
			expectedError: ErrBackfillWithoutSequenceId,
		},
		{
			name:               "backfilling with sequenceId should succeed",
			isBackfill:         true,
			backfillSequenceId: pointer_utils.ToPointer("sequenceId"),
			expectedError:      nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := ValidateAndGetApplicationReview(
				ctx,
				env.ApplicationReviewWrapper,
				env.AppReviewFixture.ApplicationReview.Id,
				tt.isBackfill,
				tt.backfillSequenceId,
			)
			if tt.expectedError != nil {
				assert.True(t, errors.Is(err, tt.expectedError))
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

func TestGenerateRecommendationActionAndReasonFromVinVisibility(t *testing.T) {
	tests := []struct {
		name                   string
		originalAction         appetite_factor.RecommendedAction
		originalReason         *appetite_factor.RecommendedActionReason
		supposedAction         *appetite_factor.RecommendedAction
		vinVisibility          *underwriting.ApplicationReviewVinVisibility
		expectedAction         appetite_factor.RecommendedAction
		expectedReason         *appetite_factor.RecommendedActionReason
		expectedSupposedAction *appetite_factor.RecommendedAction
	}{
		{
			name:                   "Vin visibility >= 60% should not change the action",
			originalAction:         appetite_factor.RecommendedActionQuote,
			originalReason:         nil,
			supposedAction:         nil,
			vinVisibility:          &underwriting.ApplicationReviewVinVisibility{VisibleAndOverlappingVinCountPercentage: 65},
			expectedAction:         appetite_factor.RecommendedActionQuote,
			expectedReason:         nil,
			expectedSupposedAction: nil,
		},
		{
			name:                   "Vin visibility between 20% and 60% should not change the action",
			originalAction:         appetite_factor.RecommendedActionQuote,
			originalReason:         nil,
			supposedAction:         nil,
			vinVisibility:          &underwriting.ApplicationReviewVinVisibility{VisibleAndOverlappingVinCountPercentage: 45},
			expectedAction:         appetite_factor.RecommendedActionQuote,
			expectedReason:         nil,
			expectedSupposedAction: nil,
		},
		{
			name:                   "Vin visibility between 20% and 60% with completed checklist should not change the action",
			originalAction:         appetite_factor.RecommendedActionQuote,
			originalReason:         nil,
			supposedAction:         nil,
			vinVisibility:          &underwriting.ApplicationReviewVinVisibility{VisibleAndOverlappingVinCountPercentage: 45},
			expectedAction:         appetite_factor.RecommendedActionQuote,
			expectedReason:         nil,
			expectedSupposedAction: nil,
		},
		{
			name:                   "Vin visibility < 20% should set action to Pending regardless of checklist",
			originalAction:         appetite_factor.RecommendedActionQuote,
			originalReason:         nil,
			supposedAction:         nil,
			vinVisibility:          &underwriting.ApplicationReviewVinVisibility{VisibleAndOverlappingVinCountPercentage: 15},
			expectedAction:         appetite_factor.RecommendedActionPending,
			expectedReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonVinVisibilityLessThanThreshold),
			expectedSupposedAction: nil,
		},
		{
			name:                   "Vin visibility < 20% with uncompleted checklist should set action to Pending",
			originalAction:         appetite_factor.RecommendedActionQuote,
			originalReason:         nil,
			supposedAction:         nil,
			vinVisibility:          &underwriting.ApplicationReviewVinVisibility{VisibleAndOverlappingVinCountPercentage: 15},
			expectedAction:         appetite_factor.RecommendedActionPending,
			expectedReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonVinVisibilityLessThanThreshold),
			expectedSupposedAction: nil,
		},
		{
			name:                   "Vin visibility doesn't matter if original action is Decline",
			originalAction:         appetite_factor.RecommendedActionDecline,
			originalReason:         nil,
			supposedAction:         nil,
			vinVisibility:          &underwriting.ApplicationReviewVinVisibility{VisibleAndOverlappingVinCountPercentage: 15},
			expectedAction:         appetite_factor.RecommendedActionDecline,
			expectedReason:         nil,
			expectedSupposedAction: nil,
		},
		{
			name:                   "Nil vin visibility doesn't change action",
			originalAction:         appetite_factor.RecommendedActionQuote,
			originalReason:         nil,
			supposedAction:         nil,
			vinVisibility:          nil,
			expectedAction:         appetite_factor.RecommendedActionQuote,
			expectedReason:         nil,
			expectedSupposedAction: nil,
		},
		{
			name:                   "Safety score market category unavailable reason doesn't change action",
			originalAction:         appetite_factor.RecommendedActionPending,
			originalReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable),
			supposedAction:         nil,
			vinVisibility:          &underwriting.ApplicationReviewVinVisibility{VisibleAndOverlappingVinCountPercentage: 15},
			expectedAction:         appetite_factor.RecommendedActionPending,
			expectedReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable),
			expectedSupposedAction: nil,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			action, reason, supposedAction, err := generateRecommendationActionAndReasonFromVinVisibility(
				test.originalAction,
				test.originalReason,
				test.supposedAction,
				test.vinVisibility,
			)

			assert.NoError(t, err)
			assert.Equal(t, test.expectedAction, *action)

			// Compare the values of the reason pointers, not the pointers themselves
			if test.expectedReason == nil {
				assert.Nil(t, reason)
			} else if reason == nil {
				assert.Fail(t, "Expected reason to be non-nil but got nil")
			} else {
				// Print out the reason values for debugging
				if test.expectedReason.String() != reason.String() {
					t.Logf("Test case: %s", test.name)
					t.Logf("Expected reason: %s (%d)", test.expectedReason.String(), *test.expectedReason)
					t.Logf("Actual reason: %s (%d)", reason.String(), *reason)
				}
				// Compare the string representation of the enum instead of the raw value
				assert.Equal(t, test.expectedReason.String(), reason.String(), "Reason string values should match")
			}

			assert.Equal(t, test.expectedSupposedAction, supposedAction)
		})
	}
}

func TestExecuteAppetiteGuidelines(t *testing.T) {
	t.Parallel()
	var env struct {
		fx.In

		*appetite_guidelines_fixture.Fixture // Load Internal/Fronting Guidelines RuleSet into DB
		*datagov_fixture.DataGovFixture      // For DOT Rating generator
		AppReviewFixture                     *application_review_fixture.ApplicationReviewsFixture
		Manager                              *guidelines.Manager
	}

	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := t.Context()

	result, err := ExecuteAppetiteGuidelines(ctx, env.Manager, env.AppReviewFixture.ApplicationReview.Id)
	require.NoError(t, err)
	require.Equal(t, true, result.IsDecline)
	require.NotNil(t, result.RuleName)
	require.Equal(t, models.RuleFleetSize, *result.RuleName)
}
