package fleet

import (
	"context"
	"database/sql"
	"time"

	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"

	uw_app_review "nirvanatech.com/nirvana/underwriting/app_review"

	"nirvanatech.com/nirvana/common-go/rule_engine"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw/appetite_factors"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/features/hazard_zones"
	"nirvanatech.com/nirvana/openapi-specs/components/underwriting"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	rubric "nirvanatech.com/nirvana/common-go/application-util/appetite_factors/recommended_action_rubric"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/risk_score_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/feature_store"
	"nirvanatech.com/nirvana/telematics"
	tsp_connections "nirvanatech.com/nirvana/telematics/connections"
	"nirvanatech.com/nirvana/underwriting/app_review/widgets/safety"
	"nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/appetite_factor"
)

var (
	ErrTerminalState             = errors.New("cannot proceed because state is not valid to generate")
	invalidType                  = appetite_factor.RecommendedAction(0)
	appetiteScoreVersion1        = appetite_factors.Version{Major: 1, Minor: 0, Patch: 0}
	noRiskScoreElementFoundMsg   = "no risk score element found even when entry was present in feature records"
	ErrNoRiskScoreElementFound   = errors.New(noRiskScoreElementFoundMsg)
	ErrBackfillWithoutSequenceId = errors.New("backfill cannot be performed without sequence ID")
)

const minNumOfPUForFleet = 10

func ValidateAndGetApplicationReview(
	ctx context.Context,
	wrapper uw.ApplicationReviewWrapper,
	appReviewId string,
	isBackFillAttempt bool,
	backfillSequenceId *string,
) (*uw.ApplicationReview, error) {
	appReview, err := wrapper.GetReview(ctx, appReviewId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get app review")
	}

	if _, ok := uw.GetNonTerminalApplicationReviewStateMap()[appReview.State]; !ok {
		return appReview, ErrTerminalState
	}

	switch {
	case isBackFillAttempt && backfillSequenceId == nil:
		return appReview, ErrBackfillWithoutSequenceId
	case isBackFillAttempt:
		return appReview, nil
	}

	return appReview, nil
}

// GetTraditionalRecommendedAction returns the recommended action for underwriting applications without incorporating
// any experiments like vin visibility, pricing, etc.
func GetTraditionalRecommendedAction(
	ctx context.Context,
	input *RecommendedActionGeneratorInput,
) (
	*appetite_factor.RecommendedAction,
	*appetite_factor.RecommendedActionReason,
	*appetite_factor.RecommendedAction,
	*appetite_factors.Version,
	error,
) {
	var recommendedAction appetite_factor.RecommendedAction
	var recommendedActionReason *appetite_factor.RecommendedActionReason
	var supposedRecommendedAction *appetite_factor.RecommendedAction
	var versionPtr *appetite_factors.Version
	var err error

	switch {

	case input.AppReview.Application.IsRenewal():
		// No action is recommended for renewals since Nirvana always quotes for a renewal
		recommendedAction = appetite_factor.RecommendedActionNotApplicableForRenewal
		return &recommendedAction, nil, nil, nil, nil
	case input.NumOfPU.Valid && input.NumOfPU.Int32 < minNumOfPUForFleet:
		// Action is not applicable if the number of PUs (Trucks + Incomplete Vehicles) is less than 10
		recommendedAction = appetite_factor.RecommendedActionNotApplicableForNonFleet
		return &recommendedAction, nil, nil, nil, nil
	default:
		// Use version 1.0.0 if the version is not provided in the AppReview DB object
		versionPtr = &appetiteScoreVersion1
		if input.AppReview.RecommendedActionInfo != nil && input.AppReview.RecommendedActionInfo.Version != nil {
			versionPtr = input.AppReview.RecommendedActionInfo.Version
		}
		input.version = versionPtr
		// Calculate the recommended action based on various factors
		recommendedAction, recommendedActionReason, supposedRecommendedAction, err = calculateRecommendedAction(ctx, input, enums.ProgramTypeFleet)
		if err != nil {
			return nil, nil, nil, nil, errors.Wrap(err, "unable to calculate recommended action")
		}
	}
	return &recommendedAction, recommendedActionReason, supposedRecommendedAction, versionPtr, nil
}

func GetRecommendedAction(
	ctx context.Context,
	input *RecommendedActionGeneratorInput,
) (*appetite_factor.RecommendedAction, *appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, *appetite_factors.Version, error) {
	// Get the recommended action without incorporating any experiments or vin visibility logic
	recommendedAction, recommendedActionReason, supposedRecommendedAction, versionPtr, err := GetTraditionalRecommendedAction(ctx, input)
	if err != nil {
		return nil, nil, nil, nil, errors.Wrap(err, "unable to get traditional recommended action")
	}

	finalAction, finalReason, finalSupposedAction, err := generateRecommendationActionAndReasonFromVinVisibility(
		*recommendedAction, recommendedActionReason, supposedRecommendedAction, input.VinVisibilityVal)
	if err != nil {
		return nil, nil, nil, nil, errors.Wrap(err, "unable to generate recommendation action and reason from vin visibility")
	}
	return finalAction, finalReason, finalSupposedAction, versionPtr, nil
}

func generateRecommendationActionAndReasonFromVinVisibility(
	recommendedAction appetite_factor.RecommendedAction,
	recommendedActionReason *appetite_factor.RecommendedActionReason,
	supposedRecommendedAction *appetite_factor.RecommendedAction,
	vinVisibilityVal *underwriting.ApplicationReviewVinVisibility,
) (*appetite_factor.RecommendedAction, *appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, error) {
	// 1. No action changes for: decline action, missing VIN visibility, safety score unavailable, or when pricing experiment applies
	// 2. Keep original action for visibility >= 20%
	// 3. Set to Pending with VinVisibilityLessThanThreshold reason for visibility < 20% (out of appetite)

	// Special cases that bypass VIN visibility logic
	if recommendedAction == appetite_factor.RecommendedActionDecline ||
		vinVisibilityVal == nil {
		return &recommendedAction, recommendedActionReason, supposedRecommendedAction, nil
	}

	// Special check for SafetyScoreMarketCategoryUnavailable reason - compare by value
	if recommendedActionReason != nil &&
		*recommendedActionReason == appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable {
		return &recommendedAction, recommendedActionReason, supposedRecommendedAction, nil
	}

	// Keep original action for visibility ≥ 20% (including threshold of 60%)
	if vinVisibilityVal.VisibleAndOverlappingVinCountPercentage >= 20 {
		return &recommendedAction, recommendedActionReason, supposedRecommendedAction, nil
	}

	// For visibility < 20%, set to Pending with VinVisibilityLessThanThreshold reason
	pendingAction := appetite_factor.RecommendedActionPending
	reason := appetite_factor.RecommendedActionReasonVinVisibilityLessThanThreshold
	return &pendingAction, &reason, nil, nil
}

func calculateRecommendedAction(
	ctx context.Context,
	input *RecommendedActionGeneratorInput,
	programType enums.ProgramType,
) (appetite_factor.RecommendedAction, *appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, error) {
	logicResolver, err := input.LogicResolverFactory.GetLogicResolver(programType)
	if err != nil {
		return 0, nil, nil, errors.Wrap(err, "unable to get logic resolver")
	}
	recommendedActionCalculator, err := logicResolver.ResolveRecommendedActionLogic(*input.version)
	if err != nil {
		return invalidType, nil, nil, errors.Wrapf(err, "unable to resolve recommended action logic for version: %s", *input.version)
	}
	/*
				There can be 5 cases:
			    1. isPremier is nil, i.e., no TSP handle ID is present yet (checked upstream) -> Pending action
			    2. isPremier is true but safety score is absent -> Pending action
			    3. isPremier is true and safety score is present -> Calculate recommended action via premier rubric
			    4. isPremier is false (i.e., exception TSP case) -> Calculate recommended action via exception rubric
			    5. When isPremier is false and safety score is present -> Calculate recommended action via exception rubric
			    Note: 5th case is adverse, when we're taking an exception TSP to premier, but we're facing it issues with
		        data or quality of safety score, then the score is not reliable and should not be considered.
	*/

	if input.IsTspPremier == nil {
		return appetite_factor.RecommendedActionPending, nil, nil, nil
	}

	var request rubric.CalculateRecommendationActionRequest

	if *input.IsTspPremier {
		// Premier TSP case
		var (
			isShortHaul    *bool
			marketCategory *string
		)
		if input.CurrentSafetyScore != nil {
			// Get IsShortHaul value from safety score if ExposeScoreTrend is false
			if input.CurrentSafetyScore.ExposeScoreTrend != nil && !*input.CurrentSafetyScore.ExposeScoreTrend {
				for _, reason := range input.CurrentSafetyScore.ExposeScoreTrendReason {
					if reason == appetite_factor.ExposeScoreTrendReasonShortHaul {
						isShortHaul = pointer_utils.ToPointer(true)
						break
					}
				}
			}
			marketCategory = input.CurrentSafetyScore.MarketCategory
		}

		request = rubric.CalculateRecommendationActionRequest{
			AppetiteScore: input.AppetiteScore, MarketCategory: marketCategory, IsException: false,
			IsCRatingRecent: input.IsCRatingRecent, IsShortHaul: isShortHaul,
			EffectiveDate: input.AppReview.EffectiveDate, ReviewID: input.AppReview.Id, HazardZone: input.HazardZone,
			NumOfPU: input.NumOfPU, IsRiskScoreElementNotPresent: input.IsRiskScoreElementNotPresent,
			IsLossesBurnRateGreaterThan20K: input.IsLossesBurnRateGreaterThan20K,
			AppetiteGuidelinesInput:        input.AppetiteGuidelinesInput,
		}
	} else {
		// Exception TSP case
		request = rubric.CalculateRecommendationActionRequest{
			AppetiteScore: input.AppetiteScore, MarketCategory: nil, IsException: true, TSPKind: input.TspKind,
			IsCRatingRecent: input.IsCRatingRecent, EffectiveDate: input.AppReview.EffectiveDate,
			ReviewID: input.AppReview.Id, HazardZone: input.HazardZone, NumOfPU: input.NumOfPU,
			IsRiskScoreElementNotPresent: false, IsLossesBurnRateGreaterThan20K: input.IsLossesBurnRateGreaterThan20K,
			AppetiteGuidelinesInput: input.AppetiteGuidelinesInput,
		}
	}

	// FYI: supposedRecommendedAction is the action that was initially calculated by the rubric,
	// but we override it because of exposure to certain risk factors  (Large fleet size for non-premier TSPs,
	// Hazard zones, etc.)
	recommendedAction, reason, supposedRecommendedAction, err := recommendedActionCalculator.CalculateRecommendedAction(ctx, request)
	if err != nil {
		return invalidType, nil, nil, errors.Wrap(err, "unable to calculate recommended action")
	}

	return recommendedAction, reason, supposedRecommendedAction, nil
}

func GetSafetyScore(
	ctx context.Context,
	appReviewWrapper uw.ApplicationReviewWrapper,
	appWrapper application.DataWrapper,
	featureStore feature_store.FeatureStore,
	handleID string,
	reviewID string,
) (*appetite_factor.SafetyScore, error) {
	riskScore, err := safety.GetRiskScoreWithVersionSelection(ctx, appReviewWrapper,
		appWrapper, featureStore, handleID, reviewID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			log.Info(ctx, "no risk score trend found even when data was available",
				log.String("tsp_conn_handle_id", handleID))
			return nil, nil //nolint:nilnil
		}
		return nil, errors.Wrap(err, "failed to get risk score trend")
	}

	if len(riskScore.RiskScoreTrend) == 0 {
		// case where safety score is present but no risk score trend is present due to certain conditions
		log.Info(ctx, noRiskScoreElementFoundMsg, log.String("tsp_conn_handle_id", handleID))
		return nil, ErrNoRiskScoreElementFound
	}

	latestRiskScore := riskScore.RiskScoreTrend[len(riskScore.RiskScoreTrend)-1]
	var safetyScore appetite_factor.SafetyScore
	safetyScore.Score = latestRiskScore.Score
	safetyScore.VinCount = latestRiskScore.VinCount
	safetyScore.ScoreType = riskScore.RiskScoreType
	safetyScore.ScoreVersion = riskScore.RiskScoreVersion
	safetyScore.UWRubricVersion = &riskScore.UWRubricVersion

	// Set window start and end times
	safetyScore.WindowStart = latestRiskScore.WindowStart
	safetyScore.WindowEnd = latestRiskScore.WindowEnd

	// Set window type
	switch latestRiskScore.WindowType {
	case risk_score_utils.WindowType3M:
		safetyScore.WindowType = appetite_factor.SafetyScoreWindowType3M
	case risk_score_utils.WindowType6M:
		safetyScore.WindowType = appetite_factor.SafetyScoreWindowType6M
	case risk_score_utils.WindowType12M:
		safetyScore.WindowType = appetite_factor.SafetyScoreWindowType12M
	default:
		return nil, errors.Errorf("unknown window type %s", latestRiskScore.WindowType)
	}

	// Set the timestamp
	safetyScore.Timestamp = latestRiskScore.Timestamp
	safetyScore.ExposeScoreTrend = latestRiskScore.ExposeScoreTrend
	if len(latestRiskScore.ExposeScoreTrendReason) > 0 {
		for _, reason := range latestRiskScore.ExposeScoreTrendReason {
			switch {
			case reason == risk_score_utils.ExposeScoreTrendReasonShortHaul:
				safetyScore.ExposeScoreTrendReason = append(safetyScore.ExposeScoreTrendReason,
					appetite_factor.ExposeScoreTrendReasonShortHaul)
			default:
				return nil, errors.Errorf("unknown expose score trend reason %s", reason)
			}
		}
	}

	if safetyScore.Score != nil {
		var marketCategory *string

		for _, val := range riskScore.UWRubric {
			if *safetyScore.Score >= float32(val.ScoreStart) && *safetyScore.Score <= float32(val.ScoreEnd) {
				marketCategory = &val.MarketCategory
				break
			}
		}

		if marketCategory == nil && len(riskScore.UWRubric) > 0 {
			return nil, errors.Newf("market category not found even when at least one market category exists")
		}

		safetyScore.MarketCategory = marketCategory
	}

	return &safetyScore, nil
}

func IsTSPPremier(
	ctx context.Context,
	appReview *uw.ApplicationReview,
	connManager *tsp_connections.TSPConnManager,
) (bool, *telematics.TSP, error) {
	handleId, err := uuid.Parse(*appReview.Application.TSPConnHandleId)
	if err != nil {
		return false, nil, errors.Wrapf(err, "failed to parse handle id: %s", *appReview.Application.TSPConnHandleId)
	}

	connProps, err := connManager.GetConnectionProperties(ctx, handleId)
	if err != nil {
		return false, nil, errors.Wrapf(
			err, "failed to get connection properties for handle id: %s", *appReview.Application.TSPConnHandleId)
	}

	return telematics.IsTelematicsIntegrationPremier(connProps, appReview.Application.CreatedAt), &connProps.TSP, nil
}

// IsConditionalRatingRecent checks if a DOT rating is conditional and
// if its effective (rating) date is within two years of the effective date of an application review.
func IsConditionalRatingRecent(dotRating rule_engine.String,
	dotRatingEffDate null.Time,
	appReviewEffectiveDate time.Time,
) bool {
	if dotRating.IsNull || !dotRatingEffDate.Valid {
		return false
	}

	if (dotRating.Value == "ConditionalRating" || dotRating.Value == "C") &&
		appReviewEffectiveDate.AddDate(-2, 0, 0).Before(dotRatingEffDate.Time) {
		return true
	}

	return false
}

func GetHazardZones(
	ctx context.Context,
	featureStore feature_store.FeatureStore,
	handleId string,
) rubric.HazardZone {
	var (
		hazardZone         rubric.HazardZone
		hazardZoneDistance hazard_zones.HazardZoneDistanceFeature
		hazardZoneDuration hazard_zones.HazardZoneDurationFeature
	)
	err := featureStore.FetchLatestFeatureValue(ctx, handleId, nil, &hazardZoneDistance)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch hazard zones distance feature",
			log.Err(err),
			log.String("tsp_conn_handle_id", handleId),
			log.Stringer("feature_name", hazardZoneDistance.Name()),
		)
	} else {
		hazardZone.DistancePercentage = pointer_utils.ToPointer(hazardZoneDistance.Percentage)
	}

	err = featureStore.FetchLatestFeatureValue(ctx, handleId, nil, &hazardZoneDuration)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch hazard zones duration feature",
			log.Err(err),
			log.String("tsp_conn_handle_id", handleId),
			log.Stringer("feature_name", hazardZoneDuration.Name()),
		)
	} else {
		hazardZone.DurationPercentage = pointer_utils.ToPointer(hazardZoneDuration.Percentage)
	}

	return hazardZone
}

func GetAppetiteScoreVersion(
	info *uw.RecommendedActionInfo,
) appetite_factors.Version {
	// Use version 1.0.0 if the version is not provided in the AppReview DB object
	version := appetiteScoreVersion1
	if info != nil && info.Version != nil {
		version = *info.Version
	}
	return version
}

func IsLossesBurnRateGreaterThanThreshold(
	ctx context.Context,
	manager uw_app_review.ReviewManager,
	appReviewId string,
) (bool, error) {
	const (
		threshold = 20_000 // 20,000 USD
	)
	lossAvg, err := manager.Losses.LossAverages.Get(ctx, appReviewId)
	if err != nil {
		return false, errors.Wrapf(err, "failed to get losses average")
	}
	for _, loss := range lossAvg.Data {
		if loss.AverageType != "Burn Rate" {
			continue
		}

		values := []*float32{
			loss.AutoLiability,
			loss.AutoPhysicalDamage,
			loss.MotorTruckCargo,
		}

		for _, val := range values {
			if val != nil && *val > threshold {
				return true, nil
			}
		}
	}

	return false, nil
}

// ExecuteAppetiteGuidelines executes the appetite guidelines for a given application review ID.
// It generates internal and fronting guidelines, retrieves the decisions, and
// returns the first decline rule found in either decision.
// Note: This is a temporary measure
func ExecuteAppetiteGuidelines(
	ctx context.Context,
	manager *guidelines.Manager,
	appReviewId string,
) (rubric.AppetiteGuidelinesInput, error) {
	zeroValueInput := rubric.AppetiteGuidelinesInput{}
	appReviewUUID, err := uuid.Parse(appReviewId)
	if err != nil {
		return zeroValueInput, errors.Wrap(err, "invalid UUID for appReviewId")
	}

	if err = manager.GenerateGuidelines(ctx,
		models.CategoryInternalGuideline, models.ProgramFleetAdmitted, appReviewUUID); err != nil {
		return zeroValueInput, errors.Wrap(err, "failed to generate internal guidelines")
	}

	if err = manager.GenerateGuidelines(ctx,
		models.CategoryFrontingGuideline, models.ProgramFleetAdmitted, appReviewUUID); err != nil {
		return zeroValueInput, errors.Wrap(err, "failed to generate fronting guidelines")
	}

	internalDecision, err := manager.GetGuidelines(ctx,
		models.CategoryInternalGuideline, models.ProgramFleetAdmitted, appReviewUUID)
	if err != nil {
		return zeroValueInput, errors.Wrap(err, "failed to get internal guidelines")
	}

	frontingDecision, err := manager.GetGuidelines(ctx,
		models.CategoryFrontingGuideline, models.ProgramFleetAdmitted, appReviewUUID)
	if err != nil {
		return zeroValueInput, errors.Wrap(err, "failed to get fronting guidelines")
	}

	// Just return first decline rule found in either internal or fronting decision

	// Internal gets priority, but not a hard requirement
	if internalDecision.Decision == models.DecisionDecline {
		outputFact, ok := internalDecision.OutputContext.(*models.OutputFact)
		if !ok {
			return zeroValueInput, errors.New("internal decision is not of type OutputFact")
		}

		for _, rule := range outputFact.Decisions {
			if rule.Decision != models.DecisionDecline {
				continue
			}
			return rubric.AppetiteGuidelinesInput{
				IsDecline: true,
				RuleName:  &rule.RuleName,
				Variant:   rule.Variant,
			}, nil
		}

		// If no decline rule found in internal decision even when decision is a decline, return error
		return zeroValueInput, errors.New("no decline rule found in internal decision")
	}

	if frontingDecision.Decision == models.DecisionDecline {
		outputFact, ok := frontingDecision.OutputContext.(*models.OutputFact)
		if !ok {
			return zeroValueInput, errors.New("fronting decision is not of type OutputFact")
		}

		for _, rule := range outputFact.Decisions {
			if rule.Decision != models.DecisionDecline {
				continue
			}
			return rubric.AppetiteGuidelinesInput{
				IsDecline: true,
				RuleName:  &rule.RuleName,
				Variant:   rule.Variant,
			}, nil
		}

		// If no decline rule found in fronting decision even when decision is a decline, return error
		return zeroValueInput, errors.New("no decline rule found in fronting decision")
	}

	return zeroValueInput, nil
}
