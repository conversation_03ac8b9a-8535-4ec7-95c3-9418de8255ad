package impl

import (
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"
	"go.uber.org/multierr"
	non_fleet_experiment "nirvanatech.com/nirvana/application/experiments/non_fleet"
	application2 "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	admitted "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/underwriting/appetite_factors/appetite_score_rubric"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines"
	"nirvanatech.com/nirvana/underwriting/clients/uw_ai"
	"nirvanatech.com/nirvana/underwriting/mst_referral"
	risk_factors_service "nirvanatech.com/nirvana/underwriting/risk_factors"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	ruleengine "nirvanatech.com/nirvana/api-server/rule-engine"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	db_api "nirvanatech.com/nirvana/db-api"
	"nirvanatech.com/nirvana/db-api/db_wrappers/app_review_widget"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	fmcsa_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/feature_store"
	"nirvanatech.com/nirvana/fmcsa/safety"
	"nirvanatech.com/nirvana/infra/config"
	insurance_eng "nirvanatech.com/nirvana/insurance-core/monitoring"
	"nirvanatech.com/nirvana/jobber/event"
	"nirvanatech.com/nirvana/jobber/registry"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels"
	common_deps "nirvanatech.com/nirvana/safety/common"
	"nirvanatech.com/nirvana/servers/telematicsv2"
	"nirvanatech.com/nirvana/servers/vehicles"
	tsp_connections "nirvanatech.com/nirvana/telematics/connections"
	uw_app_review "nirvanatech.com/nirvana/underwriting/app_review"
	"nirvanatech.com/nirvana/underwriting/app_review_widget_manager"
	"nirvanatech.com/nirvana/underwriting/common"
	"nirvanatech.com/nirvana/underwriting/rule-engine/experiments/fact"
)

type Deps struct {
	fx.In

	AppReviewWidgetWrapper              app_review_widget.DataWrapper
	ApplicationReviewWrapper            uw.ApplicationReviewWrapper
	FeatureStore                        feature_store.FeatureStore
	AuthWrapper                         auth.DataWrapper
	FmcsaWrapper                        fmcsa_wrapper.DataWrapper
	UWSafetyFetcher                     *common.UWSafetyFetcher
	RuleEngine                          *ruleengine.RuleEngine
	MetricsClient                       statsd.Statter
	Jobber                              quoting_jobber.Client
	DataStores                          common_deps.DataStores
	VehiclesServiceClient               vehicles.VehiclesServiceClient
	ApplicationWrapper                  application.DataWrapper
	TSPConnectionManager                *tsp_connections.TSPConnManager
	AppReviewWidgetManager              app_review_widget_manager.AppReviewWidgetManager
	SaferInfoFetcher                    safety.SaferInfoFetcher
	Config                              *config.Config
	Constructor                         fact.Constructor
	InsuranceEngPDClient                insurance_eng.PagerDutyClient
	AppReviewManager                    uw_app_review.ReviewManager
	FormsWrapper                        forms.FormWrapper
	TelematicsPipelineManager           telematicsv2.TelematicsPipelineManager
	Clock                               clock.Clock
	AppetiteFactorsLogicResolverFactory appetite_score_rubric.LogicResolverFactory
	EventsHandler                       events.EventsHandler
	FetcherClientFactory                data_fetching.FetcherClientFactory
	ProcessorClientFactory              data_processing.ProcessorClientFactory
	StoreManager                        store_management.StoreManager
	FeatureFlagClient                   feature_flag_lib.Client
	NirvanaDB                           db_api.NirvanaRW
	SnowflakeDsDB                       db_api.SnowflakeDsRW
	EventClient                         event.Client
	MstReferralExecutor                 *mst_referral.Executor
	ReadFromStoreInterceptorFactory     read_from_store_interceptor.Factory
	WriteToStoreInterceptorFactory      write_to_store_interceptor.Factory
	RiskFactorsClient                   risk_factors_service.RiskFactorsServiceClient
	UWAIClient                          uw_ai.UWAIService
	GuidelinesManager                   *guidelines.Manager
}

type NFDeps struct {
	fx.In

	NFApplicationReviewWrapper          nf_app_review.Wrapper
	AdmittedPanelsManager               underwriting_panels.AdmittedPanelsManager
	RuleEngine                          *ruleengine.RuleEngine
	Config                              *config.Config
	FetcherClientFactory                data_fetching.FetcherClientFactory
	FmcsaWrapper                        fmcsa_wrapper.DataWrapper
	Clock                               clock.Clock
	ApplicationReviewWrapper            uw.ApplicationReviewWrapper
	AppetiteFactorsLogicResolverFactory appetite_score_rubric.LogicResolverFactory
	MetricsClient                       statsd.Statter
	Jobber                              quoting_jobber.Client
	AppWrapper                          application2.Wrapper[*admitted.AdmittedApp]
	ExperimentManager                   *non_fleet_experiment.Manager
}

func AddUnderwritingJobsToRegistry(
	deps Deps,
	nfDeps NFDeps,
	r registry.RegistryInterface,
) (multiErr error) {
	generateAppReview, err := NewGenerateAppReview(&deps)
	multiErr = multierr.Append(multiErr, err)

	generateAppReviewWidget, err := NewGenerateAppReviewWidget(&deps)
	multiErr = multierr.Append(multiErr, err)

	generateExperiments, err := newGenerateExperiments(&deps)
	multiErr = multierr.Append(multiErr, err)

	refreshFleetUnderwritingUsecases, err := newRefreshFleetUnderwritingUsecases(&deps)
	multiErr = multierr.Append(multiErr, err)

	updateAppetiteFactorsNF, err := newUpdateAppetiteFactorsNF(&nfDeps)
	multiErr = multierr.Append(multiErr, err)

	backFillAppReviewWidget, err := newBackFillAppReviewWidgets(&deps)
	multiErr = multierr.Append(multiErr, err)

	repullWidget, err := newRePullWidgetJob(&deps)
	multiErr = multierr.Append(multiErr, err)

	generateWidgetData, err := newGenerateAppReviewWidgetData(&deps)
	multiErr = multierr.Append(multiErr, err)

	generateAppetiteFactors, err := newGenerateAppetiteFactors(&deps)
	multiErr = multierr.Append(multiErr, err)

	cancellationScoreSnowflakePostgresSync, err := NewCancellationScoreSnowflakePostgresSync(&deps)
	multiErr = multierr.Append(multiErr, err)

	generateMstReferralRules, err := newGenerateMstReferralRules(&deps)
	multiErr = multierr.Append(multiErr, err)

	generateAppetiteFactorsNF, err := newGenerateAppetiteFactorsNF(&nfDeps)
	multiErr = multierr.Append(multiErr, err)

	updateApplicationPreTelematicsStatusNF, err := newUpdateApplicationPreTelematicsStatusNF(&nfDeps)
	multiErr = multierr.Append(multiErr, err)

	generateNotes, err := newGenerateUwNotesWorkflow(&deps)
	multiErr = multierr.Append(multiErr, err)

	return multierr.Combine(multiErr,
		registry.AddJob(r, generateAppReview),
		registry.AddJob(r, generateAppReviewWidget),
		registry.AddJob(r, generateExperiments),
		registry.AddJob(r, backFillAppReviewWidget),
		registry.AddJob(r, repullWidget),
		registry.AddJob(r, generateWidgetData),
		registry.AddJob(r, generateAppetiteFactors),
		registry.AddJob(r, refreshFleetUnderwritingUsecases),
		registry.AddJob(r, cancellationScoreSnowflakePostgresSync),
		registry.AddJob(r, generateMstReferralRules),
		registry.AddJob(r, generateAppetiteFactorsNF),
		registry.AddJob(r, updateAppetiteFactorsNF),
		registry.AddJob(r, updateApplicationPreTelematicsStatusNF),
		registry.AddJob(r, generateNotes),
	)
}
