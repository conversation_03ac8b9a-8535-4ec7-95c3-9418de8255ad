load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "jobs",
    srcs = [
        "client_enumer.go",
        "job_trigger_helper.go",
        "job_trigger_util.go",
        "triggersource_enumer.go",
        "types.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/jobs",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/log",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/str_utils",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/app_review_widget",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/external/pibit",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/infra/config",
        "//nirvana/insurance-core/monitoring",
        "//nirvana/jobber",
        "//nirvana/jobber/jtypes",
        "//nirvana/quoting/jobs",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_pagerduty_go_pagerduty//:go-pagerduty",
    ],
)
