package scheduler

import (
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
)

// USERS
const (
	MarkMcDonaldEmail      = "<EMAIL>"
	KarleighSchroederEmail = "<EMAIL>"
	JoniLinhartEmail       = "<EMAIL>"
	MichaelSocratesEmail   = "<EMAIL>"
	AmandaHenselEmail      = "<EMAIL>"
	MicheleSmithEmail      = "<EMAIL>"
	JordanCruzEmail        = "<EMAIL>"
	XandreaPowellEmail     = "<EMAIL>"
	SiranWixomEmail        = "<EMAIL>"
	JosephSilvestroEmail   = "<EMAIL>"
	StephanieMakowskiEmail = "<EMAIL>"
	AbbiPorterEmail        = "<EMAIL>"
	KristinWadeEmail       = "<EMAIL>"
)

// AGENCIES
const (
	AcrisureId                    = "ad46dda5-6876-488a-9218-cc1df8ffe4a5"
	TranstarId                    = "6f22f1d7-33c1-4c48-9059-1b8d75b2b431"
	GallagherId                   = "1bc2b9fc-14cc-44e2-8fba-afb75bf2605c"
	ReliancePartnersId            = "a825b0cf-de41-421c-8d07-3689143d7981"
	HigginbothamId                = "9bb396aa-9d3c-4a1d-9956-c471abe56747"
	RiskPlacementServicesId       = "ce98c3de-a25e-4946-a935-1e403bf882d2"
	HubId                         = "fbaa8240-d8d9-4614-942f-be703e1849bf"
	AmWinsID                      = "6f0fd1fa-0bf1-4d95-ac01-be997fbee860"
	AmWinsAntuId                  = "5cfa5398-700b-4c21-bbae-797b04df6a53"
	UsiId                         = "0f756012-a480-4bb4-8cfb-100ac7acb108"
	CottinghamAndButlerId         = "088ca6fd-ac21-4939-a8dc-b131315f37cb"
	KunkelAndAssociatesId         = "fe2a6ff9-285a-4c4d-96dd-e438c33dd17d"
	CrcGroupId                    = "501ba42e-1ba6-461d-8b72-b59e81740423"
	AssuredPartnersId             = "d47ea253-0bd8-40ef-8dfa-2465c15d33dc"
	BlueRidgeSpecialtyId          = "ba427ee7-b0cd-415d-8943-90800ccfde40"
	AndreiniId                    = "48055953-a81a-4e84-8c6d-a8b76fffe458"
	BulldogId                     = "42dad72a-7396-4f3c-a61c-36ee465d35b0"
	FarrisEvansId                 = "e8ab6a63-c37f-4435-8120-c72b8e9ff936"
	GoodsId                       = "b1a974fc-81bf-4305-b54e-704314b4cfb5"
	HaylorId                      = "2da9f621-25ff-468c-a846-8db51fad277d"
	IoaId                         = "5cd4668a-e7b7-4fe1-8bc2-f63a67dbe78f"
	JjDoorhyId                    = "8896d253-1b1c-4a85-8257-292a1ddd1790"
	KeyId                         = "f6882cb0-cc8f-4559-a708-be86a63bffb6"
	OakbridgeId                   = "482816ad-20ed-4961-af66-67dbf5a408f8"
	PjcId                         = "1d03e84d-1349-4d94-8d80-b8d02b17ca20"
	SmartChoiceId                 = "d1e755b7-6258-41af-98bb-29d1d7473737"
	SwzId                         = "5d9a6097-a802-4c4b-8171-9bfa168c990c"
	TclId                         = "40a893f8-285b-43d6-b2bd-2d2ab50d6bec"
	AleraId                       = "80faaf0e-28d6-44f9-a402-68c50d1bf6df"
	SunstarId                     = "b86ac1e9-20d8-4a66-b41c-3b9ce3f572bd"
	RiceId                        = "058aedd0-c2ec-4d0e-98a0-1225c552527f"
	BondarId                      = "4d84e6bf-cbc7-4f84-ab68-f963547aa83d"
	PropelId                      = "fdd2f63d-0870-4cf2-b3b5-c2861c1204b6"
	LighthouseId                  = "641e200c-51eb-4a7c-81b2-d357c0c24993"
	AlliantId                     = "82095a8c-df9c-44ef-9f47-96f2a93a67d1"
	InsureMartId                  = "013bcf59-6d42-4a4d-8575-955624188cfd"
	TriumphId                     = "44235bf1-1b39-40e2-bd12-aa0ea9a9766b"
	MarqueeId                     = "175b2189-c2af-46b3-8986-2d60dcc69897"
	FirstLightId                  = "02c34257-5eca-4f25-864c-e89174e77324"
	LouieParkerSmithId            = "53d56561-5605-4863-96df-8bda91ad14ae"
	RushTruckId                   = "f55c332a-c91b-416c-a4c0-3aafcbe5b330"
	PointeNorthId                 = "67688b87-95b6-49fa-941b-7b7f31cbcf31"
	McGriffId                     = "03c1e67e-24a8-46a3-8be2-533af277c9a2"
	FirstPatriotId                = "930894ae-735c-4968-b010-d3525e898885"
	BrandsId                      = "eb94aa1f-35df-4984-be63-5fa612aa6c7a"
	MmaClinewoodId                = "********-8cc4-409b-a4e4-92abe8c8f14e"
	MmaMidwestId                  = "6ea06a79-2fb7-4841-b8ec-19f8c2a479f4"
	MmaNebraskaId                 = "d85db827-aeff-4160-bd4b-7fe4e6f3b40e"
	RiskPlacementServicesElPasoId = "679c671b-6426-4cdc-bc54-d5ee31d91f08"
	TruckWritersId                = "8c06e53f-1c86-4aed-9686-ccd5f34a098a"
	KeystoneId                    = "2d8196df-b80b-44ad-821f-6a9cfbbc1519"
	RTSpecialtyId                 = "370867b0-3078-4908-862e-4a2c4b8efa11"
	RTSpecialtyBurbankId          = "c3fe90e3-b968-4b66-817a-638dd24d09d6"
	RTSpecialtyDallasId           = "204ec7b3-fd62-4070-9f81-39a8e01518b8"
	RTSpecialtyWalnutCreekId      = "7487042b-9d62-46c9-84a5-c45f338a3a88"
	HatchAgencyId                 = "196808cc-ce12-491c-a017-4df9f2c69520"
	HeffernanInsuranceId          = "fb5d445a-555b-4855-bb34-052d3cfd3f30"
	LibertyInsuranceServicesIncId = "11197b1f-7efc-4ed6-a808-9e7f3e4c37ca"
)

type PreferredUnderwriter struct {
	emailId string
	active  bool
	// renewalEmailId corresponds to the email id of the preferred underwriter for renewal apps.
	// if this is null, renewals get assigned to the same underwriter as new apps as provided by emailId
	renewalEmailId *string
}

// tier1AgencyToUnderwriter is a preferred underwriter map. To be used only for fleet applications
var tier1AgencyToUnderwriter = map[string]PreferredUnderwriter{
	// Acrisure : Mark McDonald
	AcrisureId: {
		emailId:        MarkMcDonaldEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(KarleighSchroederEmail),
	},
	// Transtar : Mark McDonald
	TranstarId: {
		emailId:        MarkMcDonaldEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(JoniLinhartEmail),
	},
	// Gallagher : Mark McDonald
	GallagherId: {
		emailId:        MarkMcDonaldEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(JoniLinhartEmail),
	},
	// Reliance Partners : Michele Smith
	ReliancePartnersId: {
		emailId:        MicheleSmithEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(JoniLinhartEmail),
	},
	// HigginBotham : Joseph Silvestro
	HigginbothamId: {
		emailId:        JosephSilvestroEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(JoniLinhartEmail),
	},
	// Risk Placement Services : Jordan Cruz
	RiskPlacementServicesId: {
		emailId:        JordanCruzEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(KarleighSchroederEmail),
	},
	RiskPlacementServicesElPasoId: {
		emailId:        JordanCruzEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(KarleighSchroederEmail),
	},
	// HUB : Joseph Silvestro
	HubId: {
		emailId:        JosephSilvestroEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(JoniLinhartEmail),
	},
	// AmWINS Group Inc : Kirstin Wade
	AmWinsID: {
		emailId:        KristinWadeEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(JoniLinhartEmail),
	},
	AmWinsAntuId: {
		emailId:        KristinWadeEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(JoniLinhartEmail),
	},
	// USI : Michael Socrates
	UsiId: {
		emailId:        JosephSilvestroEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(JoniLinhartEmail),
	},
	// Cottingham & Butler : Abbi Porter
	CottinghamAndButlerId: {
		emailId:        AbbiPorterEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(JoniLinhartEmail),
	},
	// Kunkel & Associates, Inc : Mark McDonald
	KunkelAndAssociatesId: {
		emailId:        MarkMcDonaldEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(JoniLinhartEmail),
	},
	// CRC Group : Amanda Hensel
	CrcGroupId: {
		emailId:        AmandaHenselEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(KarleighSchroederEmail),
	},
	// Assured Partners : Siran Wixom
	AssuredPartnersId: {
		emailId:        SiranWixomEmail,
		active:         true,
		renewalEmailId: pointer_utils.ToPointer(JoniLinhartEmail),
	},
}

var tier2AgencyToUnderwriter = map[string]PreferredUnderwriter{
	// Blue Ridge Specialty : Siran Wixom
	BlueRidgeSpecialtyId: {
		emailId: SiranWixomEmail,
		active:  true,
	},
	// McGriff : Mark McDonald
	McGriffId: {
		emailId: MarkMcDonaldEmail,
		active:  true,
	},
	// PointeNorth : Abbi Porter
	PointeNorthId: {
		emailId: AbbiPorterEmail,
		active:  true,
	},
	// Rush Truck : Siran Wixom
	RushTruckId: {
		emailId: SiranWixomEmail,
		active:  true,
	},
	// Marsh McLennan : Abbi Porter
	MmaClinewoodId: {
		emailId: AbbiPorterEmail,
		active:  true,
	},
	MmaNebraskaId: {
		emailId: AbbiPorterEmail,
		active:  true,
	},
	MmaMidwestId: {
		emailId: AbbiPorterEmail,
		active:  true,
	},
	TruckWritersId: {
		emailId: JosephSilvestroEmail,
		active:  true,
	},
	KeystoneId: {
		emailId: JosephSilvestroEmail,
		active:  true,
	},
}

var tier3AgencyToUnderwriter = map[string]PreferredUnderwriter{
	// Louie Parker Smith : Xandrea Powell
	LouieParkerSmithId: {
		emailId: XandreaPowellEmail,
		active:  true,
	},
	// Hatch Agency : Xandrea Powell
	HatchAgencyId: {
		emailId: XandreaPowellEmail,
		active:  true,
	},
	// Heffernan Insurance : Xandrea Powell
	HeffernanInsuranceId: {
		emailId: XandreaPowellEmail,
		active:  true,
	},
}

var tier11AgencyToUnderwriter = map[string]PreferredUnderwriter{
	// Alera : Stephanie Makowski
	AleraId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Andreini : Stephanie Makowski
	AndreiniId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Bulldog : Stephanie Makowski
	BulldogId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Farris Evans : Stephanie Makowski
	FarrisEvansId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Goods : Stephanie Makowski
	GoodsId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Haylor : Stephanie Makowski
	HaylorId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Key : Stephanie Makowski
	KeyId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// PJC : Stephanie Makowski
	PjcId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// TCL : Stephanie Makowski
	TclId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// SWZ : Stephanie Makowski
	SwzId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Oakbridge : Joseph Silvestro
	OakbridgeId: {
		emailId: JosephSilvestroEmail,
		active:  true,
	},
	// Smart Choice : Stephanie Makowski
	SmartChoiceId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Sunstar : Stephanie Makowski
	SunstarId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// IOA : Stephanie Makowski
	IoaId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// JJ Doorhy : Stephanie Makowski
	JjDoorhyId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// InsureMart : Stephanie Makowski
	InsureMartId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Propel : Stephanie Makowski
	PropelId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Bondar : Stephanie Makowski
	BondarId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Lighthouse : Stephanie Makowski
	LighthouseId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Alliant : Stephanie Makowski
	AlliantId: {
		emailId: StephanieMakowskiEmail,
		active:  false,
	},
	// Triumph : Xandrea Powell
	TriumphId: {
		emailId: XandreaPowellEmail,
		active:  true,
	},
	// Rice : Stephanie Makowski
	RiceId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// First Patriot : Xandrea Powell
	FirstPatriotId: {
		emailId: XandreaPowellEmail,
		active:  true,
	},
	// Brands : Stephanie Makowski
	BrandsId: {
		emailId: StephanieMakowskiEmail,
		active:  true,
	},
	// Marquee : Xandrea Powell
	MarqueeId: {
		emailId: XandreaPowellEmail,
		active:  true,
	},
	// FirstLight : Michele Smith
	FirstLightId: {
		emailId: MicheleSmithEmail,
		active:  true,
	},
	// RT Specialty : Kristin Wade
	RTSpecialtyId: {
		emailId: KristinWadeEmail,
		active:  true,
	},
	RTSpecialtyBurbankId: {
		emailId: KristinWadeEmail,
		active:  true,
	},
	RTSpecialtyDallasId: {
		emailId: KristinWadeEmail,
		active:  true,
	},
	RTSpecialtyWalnutCreekId: {
		emailId: KristinWadeEmail,
		active:  true,
	},
	// Liberty Insurance Services Inc : Xandrea Powell
	LibertyInsuranceServicesIncId: {
		emailId: XandreaPowellEmail,
		active:  true,
	},
}

func getPreferredUwEmailForAgency(agencyId string, tier int, appType uw.ApplicationType) (string, bool) {
	var mapping map[string]PreferredUnderwriter

	// Agencies are tiered based on a mix of current traction/written premiums, assumed premiums, and footprint
	switch tier {
	case 1:
		mapping = tier1AgencyToUnderwriter
	case 2:
		mapping = tier2AgencyToUnderwriter
	case 3:
		mapping = tier3AgencyToUnderwriter
	case 11:
		mapping = tier11AgencyToUnderwriter
	default:
		return "", false
	}

	if preferredUw, ok := mapping[agencyId]; ok && preferredUw.active {
		if appType == uw.ApplicationTypeRenewal && preferredUw.renewalEmailId != nil {
			return *preferredUw.renewalEmailId, true
		}
		return preferredUw.emailId, true
	}
	return "", false
}
