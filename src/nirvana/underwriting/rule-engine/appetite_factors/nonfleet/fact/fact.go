package fact

import (
	"context"
	"math"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/equipments"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/losses"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/operations"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/safety"
	"time"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/rule_engine"
	"nirvanatech.com/nirvana/common-go/time_utils"
	admitted "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/fmcsa/basic"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/appetite_factor"
)

const (
	DriverAgeBracketLowerThreshold = 23
	DriverAgeBracketUpperThreshold = 69
)

func NewFact(env config.Env) *Fact {
	return &Fact{
		MonthsInBusiness:                  null.Float64{},
		OutsideAgeBracketDriverPercentage: null.Float64{},
		DriverTurnoverPercentage:          null.Float64{},
		AverageDriverTenureMonths:         null.Float64{},
		OutOfStateDriverPercentage:        null.Float64{},
		AverageDriverYOE:                  null.Float64{},
		MVRPointDriverPercentage:          null.Float64{},
		DOTRating:                         null.String{},
		BasicAlertCount:                   rule_engine.NullBasicAlertCount(),
		AlcoholAndDrugViolationsCount:     null.Float64{},
		TenPtInspectionViolationsPerPU:    null.Float64{},
		LapseInAuthorityCount:             null.Float64{},
		AverageEquipmentAge:               null.Float64{},
		TrailerToPURatio:                  null.Float64{},
		ThreeYearLossCount:                null.Float64{},
		MaxFloat64:                        math.MaxFloat64,
		MinFloat64:                        -math.MaxFloat64,
		clock:                             rule_engine.GetClock(env),
	}
}

type Fact struct {
	// Inputs
	MonthsInBusiness                  null.Float64
	OutsideAgeBracketDriverPercentage null.Float64 // less than 23 and greater than 69
	DriverTurnoverPercentage          null.Float64 // denotes the percentage of drivers hired within the last year
	AverageDriverTenureMonths         null.Float64
	OutOfStateDriverPercentage        null.Float64
	AverageDriverYOE                  null.Float64
	MVRPointDriverPercentage          null.Float64 // denotes percentage of drivers having at least one MVR point
	DOTRating                         null.String
	BasicAlertCount                   rule_engine.BasicAlertCount
	AlcoholAndDrugViolationsCount     null.Float64
	TenPtInspectionViolationsPerPU    null.Float64 // in the last one year
	LapseInAuthorityCount             null.Float64 // todo check how to compute this
	AverageEquipmentAge               null.Float64
	TrailerToPURatio                  null.Float64
	ThreeYearLossCount                null.Float64 // todo check if only AL should be considered
	NumOfPUs                          null.Float64

	// Constants
	MaxFloat64 float64
	MinFloat64 float64

	// Rule Engine Outputs
	AppetiteFactors []appetite_factor.AppetiteFactor

	// Dependencies
	clock clock.Clock

	// Additional context required as part of generate_appetite_factors job execution
	DOTRatingEffDate null.Time // DOT Rating rating date
}

func (fact *Fact) setMonthsInBusiness(
	ctx context.Context,
	panel *operations.OperationsPanel[*admitted.AdmittedApp],
	panelInput *base_panel.PanelInput[*admitted.AdmittedApp],
) error {
	yib := panel.YearsInBusiness(ctx, panelInput)
	yibInMonths := yib.Years*12 + yib.Months
	fact.MonthsInBusiness = null.Float64From(float64(yibInMonths))
	return nil
}

func (fact *Fact) setDriverAttributes(
	ctx context.Context,
	panel *driver.DriversPanel[*admitted.AdmittedApp],
	panelInput *base_panel.PanelInput[*admitted.AdmittedApp],
	fetcherClientFactory data_fetching.FetcherClientFactory,
	effectiveDate time.Time,
) error {
	drivers, err := panel.Drivers(ctx, panelInput, fetcherClientFactory, true)
	if err != nil {
		return errors.Wrap(err, "failed to fetch drivers")
	}
	driversUnder23Count := 0
	driversOver69Count := 0
	hiredWithinLastYearCount := 0
	outOfStateDriversCount := 0
	driverTenureMonthsSummation := 0.0
	driversYOESummation := 0
	driversWithMVRPointsCount := 0
	totalDriverCount := len(drivers)
	for _, driver := range drivers {
		age := effectiveDate.Sub(driver.DateOfBirth.Time).Hours() / (time_utils.HoursInADay * time_utils.DaysInAYear)
		if age < DriverAgeBracketLowerThreshold {
			driversUnder23Count++
		} else if age > DriverAgeBracketUpperThreshold {
			driversOver69Count++
		}
		tenureMonths := effectiveDate.Sub(driver.DateOfHire.Time).Hours() / (time_utils.HoursInADay * time_utils.AvgDaysInAMonth)
		driverTenureMonthsSummation += tenureMonths
		if driver.YearsOfExp != nil {
			driversYOESummation += *driver.YearsOfExp
		}
		if driver.DateOfHire.After(effectiveDate.AddDate(-1, 0, 0)) {
			hiredWithinLastYearCount++
		}
		if driver.IsOutOfState != nil && *driver.IsOutOfState {
			outOfStateDriversCount++
		}
		if driver.ViolationPoints != nil && *driver.ViolationPoints > 0 {
			driversWithMVRPointsCount++
		}
	}
	if totalDriverCount == 0 {
		return errors.Wrap(err, "total driver count is 0")
	}
	driversUnder23Percentage := float64(driversUnder23Count) * 100 / float64(totalDriverCount)
	driversOver69Percentage := float64(driversOver69Count) * 100 / float64(totalDriverCount)
	hiredWithinLastYearPercentage := float64(hiredWithinLastYearCount) * 100 / float64(totalDriverCount)
	outOfStateDriversPercentage := float64(outOfStateDriversCount) * 100 / float64(totalDriverCount)
	averageDriverTenureMonths := driverTenureMonthsSummation / float64(totalDriverCount)
	averageDriverYOE := float64(driversYOESummation) / float64(totalDriverCount)
	mvrPointsDriverPercentage := float64(driversWithMVRPointsCount) * 100 / float64(totalDriverCount)
	fact.OutsideAgeBracketDriverPercentage = null.Float64From(driversUnder23Percentage + driversOver69Percentage)
	fact.DriverTurnoverPercentage = null.Float64From(hiredWithinLastYearPercentage)
	fact.OutOfStateDriverPercentage = null.Float64From(outOfStateDriversPercentage)
	fact.AverageDriverTenureMonths = null.Float64From(averageDriverTenureMonths)
	fact.AverageDriverYOE = null.Float64From(averageDriverYOE)
	fact.MVRPointDriverPercentage = null.Float64From(mvrPointsDriverPercentage)
	return nil
}

func (fact *Fact) setPUCount(
	panel *equipments.EquipmentsPanel[*admitted.AdmittedApp],
	panelInput *base_panel.PanelInput[*admitted.AdmittedApp],
) error {
	actualPUCount := panel.GetActualPUCount(panelInput)
	fact.NumOfPUs = null.Float64From(float64(actualPUCount))
	return nil
}

func (fact *Fact) setDOTRating(
	ctx context.Context,
	panel *safety.SafetyPanel[*admitted.AdmittedApp],
	panelInput *base_panel.PanelInput[*admitted.AdmittedApp],
) error {
	dotRating, ratingDate, err := panel.DOTRating(ctx, panelInput)
	if err != nil {
		return errors.Wrap(err, "failed to get dotRating")
	}
	fact.DOTRating = null.StringFrom(string(dotRating))
	if ratingDate != nil {
		fact.DOTRatingEffDate = null.TimeFrom(ratingDate.Time)
	}
	return nil
}

func (fact *Fact) setBasicAlertsCount(
	ctx context.Context,
	panel *safety.SafetyPanel[*admitted.AdmittedApp],
	panelInput *base_panel.PanelInput[*admitted.AdmittedApp],
) error {
	basicThreshold, err := panel.BasicScores(ctx, panelInput)
	if err != nil {
		return errors.Wrap(err, "unable to get loss summary for review")
	}
	if basicThreshold == nil {
		return errors.New("basic threshold is nil")
	}
	basicAlertCount, scorePresent := 0, false
	for _, threshold := range basicThreshold {
		// Crash Indicator is a category weighted on other categories and looks at no unique data
		if threshold.Category != basic.CrashIndicator.Name() && threshold.Score != nil {
			scorePresent = true
			if *threshold.Score > threshold.Threshold {
				basicAlertCount += 1
			}
		}
	}
	if scorePresent {
		fact.BasicAlertCount = rule_engine.ToBasicAlertCount(float64(basicAlertCount))
	} else {
		fact.BasicAlertCount = rule_engine.ToInconclusiveBasicAlertCount()
	}
	return nil
}

func (fact *Fact) setEquipmentFields(
	panel *equipments.EquipmentsPanel[*admitted.AdmittedApp],
	panelInput *base_panel.PanelInput[*admitted.AdmittedApp],
	effectiveDate time.Time,
) error {
	equipment := panel.EquipmentDetails(panelInput, false)
	ageSummation := 0
	trailerCount := 0
	equipmentCount := len(equipment)
	if equipmentCount == 0 {
		return errors.Newf("no equipment found for input %v", panelInput)
	}
	for _, unit := range equipment {
		ageInYears := effectiveDate.Year() - unit.Year
		ageSummation += ageInYears
	}
	fact.AverageEquipmentAge = null.Float64From(float64(ageSummation) / float64(equipmentCount))

	// keeping in separate loop to allow average age to be set even if vehicle type fetch fails for some reason
	for _, unit := range equipment {
		vehicleType, err := enums.VehicleTypeString(unit.VehicleType)
		if err != nil {
			return errors.Wrapf(err, "unable to get vehicleType for VIN %s", unit.Vin)
		}
		if admitted.IsTrailer(vehicleType) {
			trailerCount++
		}
	}
	puCount := len(equipment) - trailerCount
	if puCount != 0 {
		fact.TrailerToPURatio = null.Float64From(float64(trailerCount) / float64(puCount))
	}

	return nil
}

func (fact *Fact) setThreeYearLossCount(
	panel *losses.LossesPanel[*admitted.AdmittedApp],
	panelInput *base_panel.PanelInput[*admitted.AdmittedApp],
) error {
	atFaultLosses := panel.GetLossInfo(panelInput).AtFaultAccidents
	if atFaultLosses == nil {
		return errors.New("No at fault losses found")
	}
	fact.ThreeYearLossCount = null.Float64From(float64(*atFaultLosses))
	return nil
}

func (fact *Fact) setViolationRelatedFields(
	ctx context.Context,
	safetyPanel *safety.SafetyPanel[*admitted.AdmittedApp],
	equipmentsPanel *equipments.EquipmentsPanel[*admitted.AdmittedApp],
	panelInput *base_panel.PanelInput[*admitted.AdmittedApp],
	effectiveDate time.Time,
) error {
	severeViolations, err := safetyPanel.SevereViolations(ctx, panelInput)
	if err != nil {
		return errors.Wrapf(err, "unable to get severe violations for review")
	}
	if severeViolations == nil {
		return errors.Wrap(err, "severe violations nil for review")
	}

	fact.setAlcoholAndDrugViolations(severeViolations)

	numOfPUs := equipmentsPanel.GetActualPUCount(panelInput)
	if numOfPUs == 0 {
		return errors.Newf("no pus found for input %v", panelInput)
	}

	fact.setTenPtViolations(severeViolations, effectiveDate, numOfPUs)

	return nil
}

func (fact *Fact) setAlcoholAndDrugViolations(
	severeViolations common.SevereViolations,
) {
	isDataPresent := false
	count := 0
	for _, violation := range severeViolations {
		if violation.IsControlledSubstancesAndAlcohol != nil && *violation.IsControlledSubstancesAndAlcohol {
			count++
		}
		isDataPresent = true
	}
	if isDataPresent {
		fact.AlcoholAndDrugViolationsCount = null.Float64From(float64(count))
	}
}

func (fact *Fact) setTenPtViolations(
	severeViolations common.SevereViolations,
	effectiveDate time.Time,
	numOfPUs int,
) {
	tenPtViolationsInLastYear := 0
	for _, violation := range severeViolations {
		if violation.Impact >= 10.0 && violation.InspectionDate.After(effectiveDate.AddDate(-1, 0, 0)) {
			tenPtViolationsInLastYear++
		}
	}

	tenPtViolationsPerPU := float64(tenPtViolationsInLastYear) / float64(numOfPUs)
	fact.TenPtInspectionViolationsPerPU = null.Float64From(tenPtViolationsPerPU)
}
