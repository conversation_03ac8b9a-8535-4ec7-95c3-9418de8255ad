// Code generated by "enumer -type=RecommendedActionReason -json -trimprefix=RecommendedActionReason"; DO NOT EDIT.

package appetite_factor

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _RecommendedActionReasonName = "RecentConditionalDOTRatingVinVisibilityLessThanThresholdVinVisibilityLessThanThresholdAfterUWActionHighHazardZonesExposureGTHundredFleetSizeWithNonPremierFiftyToHundredFleetSizeWithNonPremierSafetyScoreMarketCategoryUnavailableLossesBurnRateGreaterThan20KYearsInBusinessLessThanTwoYearsTrsMarketCategoryIsDeclineHazardZoneDistancePercentageIsGreaterThanThresholdHazardZoneDurationPercentageIsGreaterThanThresholdHalfYearlyUtilizationIsGreaterThanThresholdQuarterlyUtilizationIsGreaterThanThresholdDriverTurnoverIsGreaterThanThresholdUnsupportedTSPDriversCountIsLessThanThresholdVehiclesCountIsLessThanThresholdHazardZoneDistancePercentageNJIsGreaterThanThreshold"

var _RecommendedActionReasonIndex = [...]uint16{0, 26, 56, 99, 122, 154, 191, 227, 255, 286, 312, 362, 412, 455, 497, 533, 547, 578, 610, 662}

const _RecommendedActionReasonLowerName = "recentconditionaldotratingvinvisibilitylessthanthresholdvinvisibilitylessthanthresholdafteruwactionhighhazardzonesexposuregthundredfleetsizewithnonpremierfiftytohundredfleetsizewithnonpremiersafetyscoremarketcategoryunavailablelossesburnrategreaterthan20kyearsinbusinesslessthantwoyearstrsmarketcategoryisdeclinehazardzonedistancepercentageisgreaterthanthresholdhazardzonedurationpercentageisgreaterthanthresholdhalfyearlyutilizationisgreaterthanthresholdquarterlyutilizationisgreaterthanthresholddriverturnoverisgreaterthanthresholdunsupportedtspdriverscountislessthanthresholdvehiclescountislessthanthresholdhazardzonedistancepercentagenjisgreaterthanthreshold"

func (i RecommendedActionReason) String() string {
	i -= 1
	if i < 0 || i >= RecommendedActionReason(len(_RecommendedActionReasonIndex)-1) {
		return fmt.Sprintf("RecommendedActionReason(%d)", i+1)
	}
	return _RecommendedActionReasonName[_RecommendedActionReasonIndex[i]:_RecommendedActionReasonIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _RecommendedActionReasonNoOp() {
	var x [1]struct{}
	_ = x[RecommendedActionReasonRecentConditionalDOTRating-(1)]
	_ = x[RecommendedActionReasonVinVisibilityLessThanThreshold-(2)]
	_ = x[RecommendedActionReasonVinVisibilityLessThanThresholdAfterUWAction-(3)]
	_ = x[RecommendedActionReasonHighHazardZonesExposure-(4)]
	_ = x[RecommendedActionReasonGTHundredFleetSizeWithNonPremier-(5)]
	_ = x[RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier-(6)]
	_ = x[RecommendedActionReasonSafetyScoreMarketCategoryUnavailable-(7)]
	_ = x[RecommendedActionReasonLossesBurnRateGreaterThan20K-(8)]
	_ = x[RecommendedActionReasonYearsInBusinessLessThanTwoYears-(9)]
	_ = x[RecommendedActionReasonTrsMarketCategoryIsDecline-(10)]
	_ = x[RecommendedActionReasonHazardZoneDistancePercentageIsGreaterThanThreshold-(11)]
	_ = x[RecommendedActionReasonHazardZoneDurationPercentageIsGreaterThanThreshold-(12)]
	_ = x[RecommendedActionReasonHalfYearlyUtilizationIsGreaterThanThreshold-(13)]
	_ = x[RecommendedActionReasonQuarterlyUtilizationIsGreaterThanThreshold-(14)]
	_ = x[RecommendedActionReasonDriverTurnoverIsGreaterThanThreshold-(15)]
	_ = x[RecommendedActionReasonUnsupportedTSP-(16)]
	_ = x[RecommendedActionReasonDriversCountIsLessThanThreshold-(17)]
	_ = x[RecommendedActionReasonVehiclesCountIsLessThanThreshold-(18)]
	_ = x[RecommendedActionReasonHazardZoneDistancePercentageNJIsGreaterThanThreshold-(19)]
}

var _RecommendedActionReasonValues = []RecommendedActionReason{RecommendedActionReasonRecentConditionalDOTRating, RecommendedActionReasonVinVisibilityLessThanThreshold, RecommendedActionReasonVinVisibilityLessThanThresholdAfterUWAction, RecommendedActionReasonHighHazardZonesExposure, RecommendedActionReasonGTHundredFleetSizeWithNonPremier, RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier, RecommendedActionReasonSafetyScoreMarketCategoryUnavailable, RecommendedActionReasonLossesBurnRateGreaterThan20K, RecommendedActionReasonYearsInBusinessLessThanTwoYears, RecommendedActionReasonTrsMarketCategoryIsDecline, RecommendedActionReasonHazardZoneDistancePercentageIsGreaterThanThreshold, RecommendedActionReasonHazardZoneDurationPercentageIsGreaterThanThreshold, RecommendedActionReasonHalfYearlyUtilizationIsGreaterThanThreshold, RecommendedActionReasonQuarterlyUtilizationIsGreaterThanThreshold, RecommendedActionReasonDriverTurnoverIsGreaterThanThreshold, RecommendedActionReasonUnsupportedTSP, RecommendedActionReasonDriversCountIsLessThanThreshold, RecommendedActionReasonVehiclesCountIsLessThanThreshold, RecommendedActionReasonHazardZoneDistancePercentageNJIsGreaterThanThreshold}

var _RecommendedActionReasonNameToValueMap = map[string]RecommendedActionReason{
	_RecommendedActionReasonName[0:26]:         RecommendedActionReasonRecentConditionalDOTRating,
	_RecommendedActionReasonLowerName[0:26]:    RecommendedActionReasonRecentConditionalDOTRating,
	_RecommendedActionReasonName[26:56]:        RecommendedActionReasonVinVisibilityLessThanThreshold,
	_RecommendedActionReasonLowerName[26:56]:   RecommendedActionReasonVinVisibilityLessThanThreshold,
	_RecommendedActionReasonName[56:99]:        RecommendedActionReasonVinVisibilityLessThanThresholdAfterUWAction,
	_RecommendedActionReasonLowerName[56:99]:   RecommendedActionReasonVinVisibilityLessThanThresholdAfterUWAction,
	_RecommendedActionReasonName[99:122]:       RecommendedActionReasonHighHazardZonesExposure,
	_RecommendedActionReasonLowerName[99:122]:  RecommendedActionReasonHighHazardZonesExposure,
	_RecommendedActionReasonName[122:154]:      RecommendedActionReasonGTHundredFleetSizeWithNonPremier,
	_RecommendedActionReasonLowerName[122:154]: RecommendedActionReasonGTHundredFleetSizeWithNonPremier,
	_RecommendedActionReasonName[154:191]:      RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier,
	_RecommendedActionReasonLowerName[154:191]: RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier,
	_RecommendedActionReasonName[191:227]:      RecommendedActionReasonSafetyScoreMarketCategoryUnavailable,
	_RecommendedActionReasonLowerName[191:227]: RecommendedActionReasonSafetyScoreMarketCategoryUnavailable,
	_RecommendedActionReasonName[227:255]:      RecommendedActionReasonLossesBurnRateGreaterThan20K,
	_RecommendedActionReasonLowerName[227:255]: RecommendedActionReasonLossesBurnRateGreaterThan20K,
	_RecommendedActionReasonName[255:286]:      RecommendedActionReasonYearsInBusinessLessThanTwoYears,
	_RecommendedActionReasonLowerName[255:286]: RecommendedActionReasonYearsInBusinessLessThanTwoYears,
	_RecommendedActionReasonName[286:312]:      RecommendedActionReasonTrsMarketCategoryIsDecline,
	_RecommendedActionReasonLowerName[286:312]: RecommendedActionReasonTrsMarketCategoryIsDecline,
	_RecommendedActionReasonName[312:362]:      RecommendedActionReasonHazardZoneDistancePercentageIsGreaterThanThreshold,
	_RecommendedActionReasonLowerName[312:362]: RecommendedActionReasonHazardZoneDistancePercentageIsGreaterThanThreshold,
	_RecommendedActionReasonName[362:412]:      RecommendedActionReasonHazardZoneDurationPercentageIsGreaterThanThreshold,
	_RecommendedActionReasonLowerName[362:412]: RecommendedActionReasonHazardZoneDurationPercentageIsGreaterThanThreshold,
	_RecommendedActionReasonName[412:455]:      RecommendedActionReasonHalfYearlyUtilizationIsGreaterThanThreshold,
	_RecommendedActionReasonLowerName[412:455]: RecommendedActionReasonHalfYearlyUtilizationIsGreaterThanThreshold,
	_RecommendedActionReasonName[455:497]:      RecommendedActionReasonQuarterlyUtilizationIsGreaterThanThreshold,
	_RecommendedActionReasonLowerName[455:497]: RecommendedActionReasonQuarterlyUtilizationIsGreaterThanThreshold,
	_RecommendedActionReasonName[497:533]:      RecommendedActionReasonDriverTurnoverIsGreaterThanThreshold,
	_RecommendedActionReasonLowerName[497:533]: RecommendedActionReasonDriverTurnoverIsGreaterThanThreshold,
	_RecommendedActionReasonName[533:547]:      RecommendedActionReasonUnsupportedTSP,
	_RecommendedActionReasonLowerName[533:547]: RecommendedActionReasonUnsupportedTSP,
	_RecommendedActionReasonName[547:578]:      RecommendedActionReasonDriversCountIsLessThanThreshold,
	_RecommendedActionReasonLowerName[547:578]: RecommendedActionReasonDriversCountIsLessThanThreshold,
	_RecommendedActionReasonName[578:610]:      RecommendedActionReasonVehiclesCountIsLessThanThreshold,
	_RecommendedActionReasonLowerName[578:610]: RecommendedActionReasonVehiclesCountIsLessThanThreshold,
	_RecommendedActionReasonName[610:662]:      RecommendedActionReasonHazardZoneDistancePercentageNJIsGreaterThanThreshold,
	_RecommendedActionReasonLowerName[610:662]: RecommendedActionReasonHazardZoneDistancePercentageNJIsGreaterThanThreshold,
}

var _RecommendedActionReasonNames = []string{
	_RecommendedActionReasonName[0:26],
	_RecommendedActionReasonName[26:56],
	_RecommendedActionReasonName[56:99],
	_RecommendedActionReasonName[99:122],
	_RecommendedActionReasonName[122:154],
	_RecommendedActionReasonName[154:191],
	_RecommendedActionReasonName[191:227],
	_RecommendedActionReasonName[227:255],
	_RecommendedActionReasonName[255:286],
	_RecommendedActionReasonName[286:312],
	_RecommendedActionReasonName[312:362],
	_RecommendedActionReasonName[362:412],
	_RecommendedActionReasonName[412:455],
	_RecommendedActionReasonName[455:497],
	_RecommendedActionReasonName[497:533],
	_RecommendedActionReasonName[533:547],
	_RecommendedActionReasonName[547:578],
	_RecommendedActionReasonName[578:610],
	_RecommendedActionReasonName[610:662],
}

// RecommendedActionReasonString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func RecommendedActionReasonString(s string) (RecommendedActionReason, error) {
	if val, ok := _RecommendedActionReasonNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _RecommendedActionReasonNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to RecommendedActionReason values", s)
}

// RecommendedActionReasonValues returns all values of the enum
func RecommendedActionReasonValues() []RecommendedActionReason {
	return _RecommendedActionReasonValues
}

// RecommendedActionReasonStrings returns a slice of all String values of the enum
func RecommendedActionReasonStrings() []string {
	strs := make([]string, len(_RecommendedActionReasonNames))
	copy(strs, _RecommendedActionReasonNames)
	return strs
}

// IsARecommendedActionReason returns "true" if the value is listed in the enum definition. "false" otherwise
func (i RecommendedActionReason) IsARecommendedActionReason() bool {
	for _, v := range _RecommendedActionReasonValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for RecommendedActionReason
func (i RecommendedActionReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for RecommendedActionReason
func (i *RecommendedActionReason) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("RecommendedActionReason should be a string, got %s", data)
	}

	var err error
	*i, err = RecommendedActionReasonString(s)
	return err
}
