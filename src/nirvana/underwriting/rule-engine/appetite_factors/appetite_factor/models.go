package appetite_factor

import (
	"reflect"
	"time"

	"nirvanatech.com/nirvana/common-go/slice_utils"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/logic_utils"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

type TelematicsInfo struct {
	IsTspPremier           *bool
	SafetyScore            *SafetyScore
	AdditionalVinInfo      *AdditionalVinInfo
	VinVisibility          *VinVisibility
	VinVisibilityChecklist *VinVisibilityChecklist
}

type BackfillMetadata struct {
	IsBackfill bool
	SequenceId *string
}

// AdditionalVinInfo contains additional information about the VIN list
type AdditionalVinInfo struct {
	OverlapVinCount       *int32
	EquipmentListVinCount int32
}

type VinVisibilityChecklist struct {
	IsCheckListCompleted bool
	Tasks                []VinVisibilityChecklistTask
}

type VinVisibilityChecklistTask struct {
	Id          int32
	Description string
	IsCompleted bool
	Value       interface{}
}

type VinVisibility struct {
	EquipmentListVinCount                   int32
	NonVisibleAgentSubmittedVinList         []string
	NonVisibleAgentSubmittedVinListCount    int32
	VisibleAndOverlappingVinCount           int32
	VisibleAndOverlappingVinCountPercentage float32
	VisibleVinsNotInAgentSubmittedList      []string
	VisibleVinsNotInAgentSubmittedVinCount  int32
}

func (v *VinVisibility) OAPI() *oapi_uw.ApplicationReviewVinVisibility {
	var vinVisibility oapi_uw.ApplicationReviewVinVisibility
	vinVisibility.EquipmentListVinCount = v.EquipmentListVinCount
	vinVisibility.NonVisibleAgentSubmittedVinList = v.NonVisibleAgentSubmittedVinList
	vinVisibility.NonVisibleAgentSubmittedVinListCount = v.NonVisibleAgentSubmittedVinListCount
	vinVisibility.VisibleAndOverlappingVinCount = v.VisibleAndOverlappingVinCount
	vinVisibility.VisibleAndOverlappingVinCountPercentage = v.VisibleAndOverlappingVinCountPercentage
	vinVisibility.VisibleVinsNotInAgentSubmittedList = v.VisibleVinsNotInAgentSubmittedList
	vinVisibility.VisibleVinsNotInAgentSubmittedVinCount = v.VisibleVinsNotInAgentSubmittedVinCount
	return &vinVisibility
}

type SafetyScore struct {
	ScoreType              string
	ScoreVersion           string
	Score                  *float32
	WindowEnd              time.Time
	WindowStart            time.Time
	Timestamp              time.Time
	VinCount               *float32
	WindowType             SafetyScoreWindowType
	MarketCategory         *string
	ExposeScoreTrend       *bool
	ExposeScoreTrendReason []ExposeScoreTrendReason // multiple reasons can be present
	UWRubricVersion        *string                  // is always available from DS, ptr for backwards compatibility
}

func (t *TelematicsInfo) Equals(other *TelematicsInfo) bool {
	if t == nil && other == nil {
		return true
	}
	if t == nil || other == nil {
		return false
	}

	return logic_utils.CompareValuePtr(t.IsTspPremier, other.IsTspPremier) &&
		t.EqualsSafetyScore(other) &&
		t.AdditionalVinInfo.Equals(other.AdditionalVinInfo) &&
		t.VinVisibility.Equals(other.VinVisibility)
}

func (v *VinVisibility) Equals(other *VinVisibility) bool {
	if v == nil && other == nil {
		return true
	}
	if v == nil || other == nil {
		return false
	}

	return v.EquipmentListVinCount == other.EquipmentListVinCount &&
		slice_utils.EqualSorted(v.NonVisibleAgentSubmittedVinList, other.NonVisibleAgentSubmittedVinList) &&
		v.NonVisibleAgentSubmittedVinListCount == other.NonVisibleAgentSubmittedVinListCount &&
		v.VisibleAndOverlappingVinCount == other.VisibleAndOverlappingVinCount &&
		v.VisibleAndOverlappingVinCountPercentage == other.VisibleAndOverlappingVinCountPercentage &&
		slice_utils.EqualSorted(v.VisibleVinsNotInAgentSubmittedList, other.VisibleVinsNotInAgentSubmittedList) &&
		v.VisibleVinsNotInAgentSubmittedVinCount == other.VisibleVinsNotInAgentSubmittedVinCount
}

func (v *VinVisibilityChecklist) Equals(other *VinVisibilityChecklist) bool {
	if v == nil && other == nil {
		return true
	}
	if v == nil || other == nil {
		return false
	}

	if v.IsCheckListCompleted != other.IsCheckListCompleted {
		return false
	}

	if len(v.Tasks) != len(other.Tasks) {
		return false
	}
	// compare each task in a sorted order
	for _, task := range v.Tasks {
		var found bool
		for _, otherTask := range other.Tasks {
			if task.Id == otherTask.Id {
				if !task.Equals(&otherTask) {
					return false
				}
				found = true
				break
			}
		}
		// if a task is not found in the other list, return false
		if !found {
			return false
		}
	}

	return true
}

func (t *VinVisibilityChecklistTask) Equals(other *VinVisibilityChecklistTask) bool {
	if t == nil && other == nil {
		return true
	}
	if t == nil || other == nil {
		return false
	}

	return t.Id == other.Id &&
		t.Description == other.Description &&
		t.IsCompleted == other.IsCompleted &&
		reflect.DeepEqual(t.Value, other.Value)
}

func (a *AdditionalVinInfo) Equals(other *AdditionalVinInfo) bool {
	if a == nil && other == nil {
		return true
	}
	if a == nil || other == nil {
		return false
	}

	return logic_utils.CompareValuePtr(a.OverlapVinCount, other.OverlapVinCount) &&
		a.EquipmentListVinCount == other.EquipmentListVinCount
}

func (t *TelematicsInfo) EqualsSafetyScore(other *TelematicsInfo) bool {
	var firstSafetyScore, secondSafetyScore *SafetyScore
	if t != nil {
		firstSafetyScore = t.SafetyScore
	}
	if other != nil {
		secondSafetyScore = other.SafetyScore
	}
	// if both telematics info ptr are nil, they're equal
	return firstSafetyScore.Equals(secondSafetyScore)
}

func (ss *SafetyScore) Equals(other *SafetyScore) bool {
	if ss == nil && other == nil {
		return true
	}
	if ss == nil || other == nil {
		return false
	}

	return ss.ScoreType == other.ScoreType &&
		ss.ScoreVersion == other.ScoreVersion &&
		logic_utils.CompareValuePtr(ss.Score, other.Score) &&
		ss.WindowEnd.Equal(other.WindowEnd) &&
		ss.WindowStart.Equal(other.WindowStart) &&
		ss.Timestamp.Equal(other.Timestamp) &&
		logic_utils.CompareValuePtr(ss.VinCount, other.VinCount) &&
		ss.WindowType == other.WindowType &&
		logic_utils.CompareValuePtr(ss.MarketCategory, other.MarketCategory) &&
		logic_utils.CompareValuePtr(ss.ExposeScoreTrend, other.ExposeScoreTrend) &&
		slice_utils.EqualSorted(ss.ExposeScoreTrendReason, other.ExposeScoreTrendReason)
}

//go:generate go run github.com/dmarkham/enumer -type=SafetyScoreWindowType -json -trimprefix=SafetyScoreWindowType
type SafetyScoreWindowType int

const (
	SafetyScoreWindowType12M SafetyScoreWindowType = iota + 1
	SafetyScoreWindowType3M
	SafetyScoreWindowType6M
)

//go:generate go run github.com/dmarkham/enumer -type=Category -json -trimprefix=Category
type Category int

const (
	CategoryYearsInBusiness Category = iota + 1
	CategoryMilesPerUnit
	CategoryPowerUnitsRecent
	CategoryPowerUnitsYOY
	CategoryDriverTurnoverPercentage
	CategoryAlcoholAndDrugViolations
	CategoryDOTRating
	CategoryLessThanTwentyThreeDriverPercentage
	CategorySeventyPlusDriverPercentage
	CategoryALLossFrequencyPerUnitPerYear
	CategoryALLossPerUnitPerYear
	CategoryBasicAlerts
	CategoryOutsideAgeBracketDriverPercentage
	CategoryOutOfStateDriverPercentage
	CategoryAverageDriverTenureMonths
	CategoryAverageDriverYOE
	CategoryMVRPointDriverPercentage
	CategoryAverageEquipmentAge
	CategoryTrailerToPURatio
	CategoryThreeYearLossCount
	CategoryMonthsInBusiness
	CategoryTenPtViolationsPerPU
)

func (c Category) Name() string {
	switch c {
	case CategoryYearsInBusiness:
		return "Years in business"
	case CategoryMilesPerUnit:
		return "Miles per unit"
	case CategoryPowerUnitsRecent:
		return "Power Units Recent"
	case CategoryPowerUnitsYOY:
		return "Power Units YOY"
	case CategoryDriverTurnoverPercentage:
		return "Driver turnover %"
	case CategoryAlcoholAndDrugViolations:
		return "Alcohol & Drug Violations"
	case CategoryDOTRating:
		return "DOT Rating"
	case CategoryLessThanTwentyThreeDriverPercentage:
		return "<23 age Driver %"
	case CategorySeventyPlusDriverPercentage:
		return "70+ Driver %"
	case CategoryALLossFrequencyPerUnitPerYear:
		return "AL - Loss Frequency per unit per year"
	case CategoryALLossPerUnitPerYear:
		return "AL - Loss per unit per year in USD"
	case CategoryBasicAlerts:
		return "Basic Alerts"
	case CategoryOutsideAgeBracketDriverPercentage:
		return "Outside Age Bracket Driver %"
	case CategoryOutOfStateDriverPercentage:
		return "Out of State Driver %"
	case CategoryAverageDriverTenureMonths:
		return "Average driver tenure in months"
	case CategoryAverageDriverYOE:
		return "Average driver YOE"
	case CategoryMVRPointDriverPercentage:
		return "% of drivers with at least one MVR point"
	case CategoryAverageEquipmentAge:
		return "Average Equipment Age"
	case CategoryTrailerToPURatio:
		return "Trailer to PU Ratio"
	case CategoryThreeYearLossCount:
		return "Three Year Loss Count"
	case CategoryMonthsInBusiness:
		return "Months in Business"
	case CategoryTenPtViolationsPerPU:
		return "Ten pt violations per PU"
	default:
		return c.String()
	}
}

//go:generate go run github.com/dmarkham/enumer -type=AppetiteFlag -json -trimprefix=Flag
type AppetiteFlag int

const (
	FlagGreen AppetiteFlag = iota + 1
	FlagRed
	FlagYellow
	FlagInsufficientData
)

//go:generate go run github.com/dmarkham/enumer -type=Type -json -trimprefix=Type
type Type int

const (
	TypeMinMax Type = iota + 1
	TypeEquality
)

//go:generate go run github.com/dmarkham/enumer -type=RecommendedAction -json -trimprefix=RecommendedAction
type RecommendedAction int

const (
	RecommendedActionStronglyQuote RecommendedAction = iota + 1
	RecommendedActionQuote
	RecommendedActionNeutral
	RecommendedActionDecline
	RecommendedActionPending
	RecommendedActionNotApplicableForRenewal
	RecommendedActionNotApplicableForNonFleet
)

func (a RecommendedAction) ConvertToOAPI() (oapi_uw.RecommendedAction, error) {
	switch a {
	case RecommendedActionStronglyQuote:
		return oapi_uw.RecommendedActionStronglyQuote, nil
	case RecommendedActionQuote:
		return oapi_uw.RecommendedActionQuote, nil
	case RecommendedActionNeutral:
		return oapi_uw.RecommendedActionNeutral, nil
	case RecommendedActionDecline:
		return oapi_uw.RecommendedActionDecline, nil
	case RecommendedActionPending:
		return oapi_uw.RecommendedActionPending, nil
	case RecommendedActionNotApplicableForRenewal:
		return oapi_uw.RecommendedActionNotApplicableForRenewal, nil
	case RecommendedActionNotApplicableForNonFleet:
		return oapi_uw.RecommendedActionNotApplicableForNonFleet, nil
	default:
		return "", errors.Newf("recommended action %d not handled", a)
	}
}

//go:generate go run github.com/dmarkham/enumer -type=AppetiteScore -json -trimprefix=AppetiteScore
type AppetiteScore int

const (
	AppetiteScoreMarginal AppetiteScore = iota
	AppetiteScoreAcceptable
	AppetiteScorePreferred
	AppetiteScoreHighRisk
)

func (a AppetiteScore) ConvertToOAPI() (oapi_uw.AppetiteScoreEnum, error) {
	switch a {
	case AppetiteScoreMarginal:
		return oapi_uw.AppetiteScoreEnumMarginal, nil
	case AppetiteScoreAcceptable:
		return oapi_uw.AppetiteScoreEnumAcceptable, nil
	case AppetiteScorePreferred:
		return oapi_uw.AppetiteScoreEnumPreferred, nil
	case AppetiteScoreHighRisk:
		return oapi_uw.AppetiteScoreEnumHighRisk, nil
	default:
		return "", errors.Newf("appetite score %d not handled", a)
	}
}

//go:generate go run github.com/dmarkham/enumer -type=RecommendedActionReason -json -trimprefix=RecommendedActionReason
type RecommendedActionReason int

const (
	// RecommendedActionReasonRecentConditionalDOTRating is a case where the DOT rating is conditional and
	// less than 2 years rated from effective date
	RecommendedActionReasonRecentConditionalDOTRating RecommendedActionReason = iota + 1
	RecommendedActionReasonVinVisibilityLessThanThreshold
	// deprecated RecommendedActionReasonVinVisibilityLessThanThresholdAfterUWAction
	RecommendedActionReasonVinVisibilityLessThanThresholdAfterUWAction
	RecommendedActionReasonHighHazardZonesExposure
	RecommendedActionReasonGTHundredFleetSizeWithNonPremier
	RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier
	RecommendedActionReasonSafetyScoreMarketCategoryUnavailable
	RecommendedActionReasonLossesBurnRateGreaterThan20K
	RecommendedActionReasonYearsInBusinessLessThanTwoYears
	RecommendedActionReasonTrsMarketCategoryIsDecline
	RecommendedActionReasonHazardZoneDistancePercentageIsGreaterThanThreshold
	RecommendedActionReasonHazardZoneDurationPercentageIsGreaterThanThreshold
	RecommendedActionReasonHalfYearlyUtilizationIsGreaterThanThreshold
	RecommendedActionReasonQuarterlyUtilizationIsGreaterThanThreshold
	RecommendedActionReasonDriverTurnoverIsGreaterThanThreshold
	RecommendedActionReasonUnsupportedTSP
	RecommendedActionReasonDriversCountIsLessThanThreshold
	RecommendedActionReasonVehiclesCountIsLessThanThreshold
	RecommendedActionReasonHazardZoneDistancePercentageNJIsGreaterThanThreshold
)

func (r RecommendedActionReason) ConvertToOAPI() (oapi_uw.RecommendedActionReason, error) {
	switch r {
	case RecommendedActionReasonRecentConditionalDOTRating:
		return oapi_uw.RecentConditionalDOTRating, nil
	case RecommendedActionReasonVinVisibilityLessThanThreshold:
		return oapi_uw.VINVisibilityIsLessThan60, nil
	case RecommendedActionReasonVinVisibilityLessThanThresholdAfterUWAction:
		return oapi_uw.VINVisibilityIsLessThan60AfterUWAction, nil
	case RecommendedActionReasonHighHazardZonesExposure:
		return oapi_uw.HighHazardZoneExposureDetected, nil
	case RecommendedActionReasonGTHundredFleetSizeWithNonPremier:
		return oapi_uw.N100UnitAccountWithANonPremierTSP, nil
	case RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier:
		return oapi_uw.N50100UnitAccountWithANonPremierTSP, nil
	case RecommendedActionReasonSafetyScoreMarketCategoryUnavailable:
		return oapi_uw.SafetyScoreIsUnavailableFromDataScience, nil
	case RecommendedActionReasonLossesBurnRateGreaterThan20K:
		return oapi_uw.LossesBurnRateIsGreaterThan20K, nil
	case RecommendedActionReasonYearsInBusinessLessThanTwoYears:
		return oapi_uw.YearsInBusinessIsLessThan2Years, nil
	case RecommendedActionReasonTrsMarketCategoryIsDecline:
		return oapi_uw.TRSMarketCategoryIsDecline, nil
	case RecommendedActionReasonHazardZoneDistancePercentageIsGreaterThanThreshold:
		return oapi_uw.HazardZoneDistancePercentageIsGreaterThanThreshold, nil
	case RecommendedActionReasonHazardZoneDurationPercentageIsGreaterThanThreshold:
		return oapi_uw.HazardZoneDurationPercentageIsGreaterThanThreshold, nil
	case RecommendedActionReasonHalfYearlyUtilizationIsGreaterThanThreshold:
		return oapi_uw.HalfYearlyUtilizationIsGreaterThanThreshold, nil
	case RecommendedActionReasonQuarterlyUtilizationIsGreaterThanThreshold:
		return oapi_uw.QuarterlyUtilizationIsGreaterThanThreshold, nil
	case RecommendedActionReasonDriverTurnoverIsGreaterThanThreshold:
		return oapi_uw.DriverTurnoverIsGreaterThanThreshold, nil
	case RecommendedActionReasonUnsupportedTSP:
		return oapi_uw.UnsupportedTSP, nil
	case RecommendedActionReasonDriversCountIsLessThanThreshold:
		return oapi_uw.DriverCountIsLessThanThreshold, nil
	case RecommendedActionReasonVehiclesCountIsLessThanThreshold:
		return oapi_uw.VehicleCountIsLessThanThreshold, nil
	case RecommendedActionReasonHazardZoneDistancePercentageNJIsGreaterThanThreshold:
		return oapi_uw.HazardZoneDistancePercentageForNewJerseyIsGreaterThanThreshold, nil
	default:
		return "", errors.Newf("recommended action reason %d not handled", r)
	}
}

//go:generate go run github.com/dmarkham/enumer -type=ExposeScoreTrendReason -json -trimprefix=ExposeScoreTrendReason
type ExposeScoreTrendReason int

const (
	ExposeScoreTrendReasonShortHaul ExposeScoreTrendReason = iota + 1
)
