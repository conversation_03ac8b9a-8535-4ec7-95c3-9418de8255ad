package loss_burn_rate

import (
	"context"

	"nirvanatech.com/nirvana/common-go/math_utils"

	"github.com/volatiletech/null/v8"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"

	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/types"

	"github.com/cockroachdb/errors"
	uw_app_review "nirvanatech.com/nirvana/underwriting/app_review"
)

type V1 struct {
	manager uw_app_review.ReviewManager
}

func NewV1(manager uw_app_review.ReviewManager) *V1 {
	return &V1{
		manager: manager,
	}
}

func (v *V1) Name() types.GeneratorVersion {
	return types.GeneratorVersion{
		Name:    LossesBurnRateName,
		Version: LossesBurnRateV1,
	}
}

const (
	KeyAL = "AL"
)

type V1Result map[string]*float32

func (v *V1) Generate(ctx context.Context, args any, data *types.CachedData) (any, error) {
	concreteArgs, ok := args.(*types.GeneratorArgs)
	if !ok {
		return nil, errors.Newf("invalid args: %+v; expected GeneratorArgs type", args)
	}
	lossAvg, err := v.manager.Losses.LossAverages.Get(ctx, concreteArgs.AppReviewId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get losses average")
	}

	for _, loss := range lossAvg.Data {
		if loss.AverageType != "Burn Rate" {
			continue
		}
		return V1Result{
			KeyAL: loss.AutoLiability,
		}, nil
	}

	return V1Result{}, nil
}

func (v *V1) Apply(f models.Fact, result any) error {
	fact, ok := f.(*models.InternalInputFact)
	if !ok {
		return errors.Newf("unexpected fact type: %T; expected InternalInputFact", f)
	}

	concreteResult, ok := result.(V1Result)
	if !ok {
		return errors.Newf("unexpected result type: %T; expected V1Result", result)
	}

	if len(concreteResult) == 0 {
		return nil
	}

	al, alOk := concreteResult[KeyAL]
	if !alOk {
		return errors.New("missing required burn rate key: AL")
	}
	fact.AlLossesBurnRate = null.Float32FromPtr(math_utils.RoundFloatPtr(al, 2))

	return nil
}
