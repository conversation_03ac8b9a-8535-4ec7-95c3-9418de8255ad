load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "dot_rating",
    srcs = [
        "types.go",
        "v1.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/dot_rating",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/underwriting/app_review",
        "//nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/types",
        "//nirvana/underwriting/appetite_guidelines/models",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_volatiletech_null_v8//:null",
    ],
)
