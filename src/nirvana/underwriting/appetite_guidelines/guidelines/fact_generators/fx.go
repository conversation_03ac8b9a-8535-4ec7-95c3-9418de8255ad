package fact_generators

import (
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/dot_rating"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/driver_turnover"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/fleet_size"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/hazard_zones"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/loss_burn_rate"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/telematics_risk_score"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/unsupported_tsp"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/utilization"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/yib"
)

var _ = fxregistry.Register(
	fx.Provide(NewRegistry,
		loss_burn_rate.NewV1,
		yib.NewV1,
		dot_rating.NewV1,
		utilization.NewV1,
		telematics_risk_score.NewV1,
		unsupported_tsp.NewV1,
		driver_turnover.NewV1,
		fleet_size.NewV1,
		hazard_zones.NewV1,
		NewComposite,
	),
)

var _ = fxregistry.Register(
	fx.Invoke(RegisterGenerators))

func RegisterGenerators(
	r *Registry,
	lossBurnRateV1 *loss_burn_rate.V1,
	yibV1 *yib.V1,
	dotRatingV1 *dot_rating.V1,
	utilizationV1 *utilization.V1,
	trsV1 *telematics_risk_score.V1,
	unsupportedTSPV1 *unsupported_tsp.V1,
	driverTurnoverV1 *driver_turnover.V1,
	fleetSizeV1 *fleet_size.V1,
	hazardZonesV1 *hazard_zones.V1,
	composite *Composite,
) {
	r.Register(lossBurnRateV1)
	r.Register(yibV1)
	r.Register(dotRatingV1)
	r.Register(utilizationV1)
	r.Register(trsV1)
	r.Register(unsupportedTSPV1)
	r.Register(driverTurnoverV1)
	r.Register(fleetSizeV1)
	r.Register(hazardZonesV1)
	r.Register(composite)
}
