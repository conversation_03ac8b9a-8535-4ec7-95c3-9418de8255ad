package utilization

import (
	"context"
	"sort"
	"time"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/feature_store"

	"nirvanatech.com/nirvana/features"

	"nirvanatech.com/nirvana/feature_store/grpc"
)

func (v *V1) fetchMileageEstimateFeature(
	ctx context.Context,
	tspHandleId string,
) ([]*grpc.FleetRunRateV1_WRDataframeRow, error) {
	mileageEstimateFeature := features.NewProtoFeatureR[*grpc.FleetRunRateV1](
		features.FleetRunRate, features.HandleId)

	err := v.featureStore.FetchLatestFeatureValue(ctx, tspHandleId, nil, mileageEstimateFeature)
	if err != nil {
		if errors.Is(err, feature_store.NoFeatureValueFoundError) {
			log.Info(ctx, "no mileage estimate feature value found", log.TspHandleId(tspHandleId))
			return []*grpc.FleetRunRateV1_WRDataframeRow{}, nil
		}
		return nil, errors.Wrap(err, "failed to fetch mileage estimate feature")
	}

	return mileageEstimateFeature.Value.WeeklyFleetRunRateTable, nil
}

func sortRunRatesDescending(runRates []*grpc.FleetRunRateV1_WRDataframeRow) {
	sort.Slice(runRates, func(i, j int) bool {
		ti, err1 := time.Parse(time.DateOnly, runRates[i].IsoWeekStart)
		tj, err2 := time.Parse(time.DateOnly, runRates[j].IsoWeekStart)

		if err1 != nil && err2 != nil {
			return false
		}
		if err1 != nil {
			return false
		}
		if err2 != nil {
			return true
		}
		return ti.After(tj)
	})
}
