package fact_generators

import (
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/dot_rating"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/driver_turnover"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/fleet_size"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/hazard_zones"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/loss_burn_rate"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/telematics_risk_score"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/types"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/unsupported_tsp"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/utilization"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/yib"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"
)

const (
	GeneratorName           = "composite_generator"
	DefaultGeneratorVersion = 1
)

type RuleBundleKey struct {
	Category models.Category
	Program  models.Program
	Version  models.Version
}

// BundleToGenerators says “Bundle X consists of these sub-generators”
var BundleToGenerators = map[RuleBundleKey][]types.GeneratorVersion{
	{
		Category: models.CategoryInternalGuideline,
		Program:  models.ProgramFleetAdmitted,
		Version:  1,
	}: {
		types.GV(loss_burn_rate.LossesBurnRateName, loss_burn_rate.LossesBurnRateV1),
		types.GV(utilization.UtilizationName, utilization.UtilizationV1),
		types.GV(unsupported_tsp.UnsupportedTSPName, unsupported_tsp.UnsupportedTSPV1),
		types.GV(driver_turnover.DriverTurnoverName, driver_turnover.DriverTurnoverV1),
		types.GV(fleet_size.FleetSizeName, fleet_size.FleetSizeV1),
		types.GV(hazard_zones.HazardZoneName, hazard_zones.HazardZoneV1),
	}, {
		Category: models.CategoryFrontingGuideline,
		Program:  models.ProgramFleetAdmitted,
		Version:  1,
	}: {
		types.GV(yib.YibName, yib.YibV1),
		types.GV(dot_rating.DotRatingName, dot_rating.DotRatingV1),
		types.GV(telematics_risk_score.TelematicsRiskScoreName, telematics_risk_score.TelematicsRiskScoreV1),
		types.GV(hazard_zones.HazardZoneName, hazard_zones.HazardZoneV1),
	},
}
