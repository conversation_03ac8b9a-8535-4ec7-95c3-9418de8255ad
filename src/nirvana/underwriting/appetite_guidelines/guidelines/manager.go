package guidelines

import (
	"context"
	"database/sql"

	"github.com/labstack/gommon/log"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/db"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"
)

type Manager struct {
	ruleService *RuleService
	factService *FactService
	dbWrapper   *db.AppetiteGuidelinesDbWrapper
	clk         clock.Clock
}

func NewManager(
	ruleService *RuleService,
	factService *FactService,
	dbWrapper *db.AppetiteGuidelinesDbWrapper,
	clk clock.Clock,
) *Manager {
	return &Manager{
		ruleService: ruleService,
		factService: factService,
		dbWrapper:   dbWrapper,
		clk:         clk,
	}
}

func (m *Manager) GenerateGuidelines(
	ctx context.Context,
	category models.Category,
	program models.Program,
	appReviewId uuid.UUID,
) error {
	inputFact, err := m.factService.GenerateFact(ctx, category, program, appReviewId.String())
	if err != nil {
		return errors.Wrapf(err, "failed to get fact for application review %s", appReviewId)
	}

	outputFact, err := m.ruleService.LoadAndExecute(ctx, category, program, inputFact, 1)
	if err != nil {
		return errors.Wrapf(err, "failed to execute rules for program %s and category %s", program, category)
	}

	// default to the first version of the rule set
	ruleSet, err := m.ruleService.GetRuleSetByVersion(ctx, category, program, 1)
	if err != nil {
		return errors.Wrapf(err, "failed to get rule set for program %s and category %s", program, category)
	}

	overallDecision, err := outputFact.GenerateOverallDecision()
	if err != nil {
		return errors.Wrapf(err, "failed to generate overall decision for application review %s", appReviewId)
	}

	decision := &models.Decisions{
		Id:                  uuid.New(),
		RuleSetId:           ruleSet.Id,
		Program:             program,
		ApplicationReviewID: appReviewId,
		Decision:            overallDecision,
		CreatedAt:           m.clk.Now(),
		InputContext:        inputFact,
		OutputContext:       outputFact,
	}

	// Check if this decision differs from the latest persisted one. If not, avoid inserting duplicates.
	isDuplicate, err := m.isDuplicateDecision(ctx, decision)
	if err != nil {
		return errors.Wrapf(err, "failed to check for duplicate decision for application review %s", appReviewId)
	}

	if isDuplicate {
		log.Info(ctx, "Decision for application review %s is a duplicate, skipping insertion", appReviewId)
		return nil
	}

	if err := m.dbWrapper.InsertDecision(ctx, decision); err != nil {
		return errors.Wrapf(err, "failed to insert decision for application review %s", appReviewId)
	}

	return nil
}

func (m *Manager) GetGuidelines(
	ctx context.Context,
	category models.Category,
	program models.Program,
	appReviewId uuid.UUID,
) (*models.Decisions, error) {
	ruleSet, err := m.ruleService.GetRuleSetByVersion(ctx, category, program, 1)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get rule set for program %s and category %s", program, category)
	}

	decision, err := m.dbWrapper.GetLatestDecision(ctx, appReviewId.String(), ruleSet.Id.String())
	if err != nil {
		return nil, errors.Wrapf(err,
			"failed to get latest decision for application review %s and rule set %s", appReviewId, ruleSet.Id)
	}

	return decision, nil
}

// isDuplicateDecision determines if `current` decision is identical to `existing`.
func (m *Manager) isDuplicateDecision(ctx context.Context, current *models.Decisions) (bool, error) {
	existing, err := m.dbWrapper.GetLatestDecision(
		ctx, current.ApplicationReviewID.String(), current.RuleSetId.String())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return false, nil // No existing decision means current is not a duplicate.
		}
		return false, err
	}

	return existing.Equal(current), nil
}
