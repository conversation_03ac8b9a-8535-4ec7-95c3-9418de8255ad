package guidelines

import (
	"context"
	"fmt"
	"strconv"
	"sync"

	"github.com/cockroachdb/errors"
	"github.com/hyperjumptech/grule-rule-engine/ast"
	"github.com/hyperjumptech/grule-rule-engine/engine"
	"github.com/hyperjumptech/grule-rule-engine/pkg"
	ruleengine "nirvanatech.com/nirvana/api-server/rule-engine"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/rule_engine"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/db"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"
)

type RuleService struct {
	ruleEngine *ruleengine.RuleEngine
	dbWrapper  *db.AppetiteGuidelinesDbWrapper
	onceMap    sync.Map
}

func NewRuleService(r *ruleengine.RuleEngine, d *db.AppetiteGuidelinesDbWrapper) *RuleService {
	return &RuleService{
		ruleEngine: r,
		dbWrapper:  d,
	}
}

func (r *RuleService) LoadAndExecute(
	ctx context.Context,
	category models.Category,
	program models.Program,
	inputFact models.Fact,
	version int,
) (*models.OutputFact, error) {
	log.Info(ctx, "Loading and executing rule set",
		log.Stringer("program", program), log.Int("version", version))

	ruleSetName := fmt.Sprintf("category_%s_program_%s",
		str_utils.ToSnakeCase(category.String()),
		str_utils.ToSnakeCase(program.String()))
	versionStr := strconv.Itoa(version)

	if err := r.ensureRuleSetLoaded(ctx, category, program, ruleSetName, version); err != nil {
		return nil, err
	}

	dataCtx := ast.NewDataContext()
	if err := dataCtx.Add("input", inputFact); err != nil {
		return nil, errors.Wrapf(err, "failed to add fact to data context")
	}

	if err := dataCtx.Add("constants", models.NewInputFactConstants()); err != nil {
		return nil, errors.Wrapf(err, "failed to add constants to data context")
	}

	outputFact := models.NewOutputFact()
	if err := dataCtx.Add("output", outputFact); err != nil {
		return nil, errors.Wrapf(err, "failed to add output fact to data context")
	}

	workableKnowledgeBase := r.ruleEngine.KnowledgeLibrary.NewKnowledgeBaseInstance(ruleSetName, versionStr)
	if err := engine.NewGruleEngine().ExecuteWithContext(ctx, dataCtx, workableKnowledgeBase); err != nil {
		return nil, errors.Wrapf(err, "failed to execute rule engine")
	}

	outputFact.SortByRuleNameAndVariant()

	return outputFact, nil
}

func (r *RuleService) GetRuleSetByVersion(
	ctx context.Context,
	category models.Category,
	program models.Program,
	version int,
) (*models.RuleSet, error) {
	ruleSet, err := r.dbWrapper.GetRuleSetByVersion(ctx, category.String(), program.String(), version)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get rule set for program %s and version %d", program, version)
	}
	return ruleSet, nil
}

func (r *RuleService) ensureRuleSetLoaded(
	ctx context.Context,
	category models.Category,
	program models.Program,
	ruleSetName string,
	version int,
) error {
	versionStr := strconv.Itoa(version)

	// Check if already loaded (fast path)
	knowledgeBase := r.ruleEngine.KnowledgeLibrary.GetKnowledgeBase(ruleSetName, versionStr)
	if knowledgeBase.ContainsRuleEntry(ruleSetName) {
		return nil
	}

	ruleSetNameWithVersion := fmt.Sprintf("%s_v%d", ruleSetName, version)

	// Get or create sync.Once for this rule set
	actualOnce, _ := r.onceMap.LoadOrStore(ruleSetNameWithVersion, new(sync.Once))

	var loadErr error
	actualOnce.(*sync.Once).Do(func() {
		// Double-check inside Once (in case another goroutine loaded it before .Do runs)
		knowledgeBase = r.ruleEngine.KnowledgeLibrary.GetKnowledgeBase(ruleSetName, versionStr)
		if knowledgeBase.ContainsRuleEntry(ruleSetName) {
			return
		}

		// Lazy load from DB and build
		ruleSet, err := r.GetRuleSetByVersion(ctx, category, program, version)
		if err != nil {
			loadErr = errors.Wrapf(err, "failed to get rule set for program %s and version %d", program, version)
			return
		}

		underlying := pkg.NewBytesResource(ruleSet.Rules)
		resource := pkg.NewJSONResourceFromResource(underlying)

		loadErr = rule_engine.BuildRuleFromResourceWithErrCheck(ctx, r.ruleEngine.RuleBuilder, ruleSetName, versionStr, resource)
		if loadErr != nil {
			loadErr = errors.Wrapf(loadErr, "failed to build rule from resource for program %s and version %d", program, version)
		}
	})

	return loadErr
}
