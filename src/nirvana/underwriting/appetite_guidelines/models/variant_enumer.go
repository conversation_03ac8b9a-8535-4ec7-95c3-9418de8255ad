// Code generated by "enumer -type=Variant -json -trimprefix=Variant"; DO NOT EDIT.

package models

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _VariantName = "InvalidAutoLiabilityHalfYearlyQuarterlyDriverVehicleAverageDistancePercentageNJDistancePercentageDurationPercentage"

var _VariantIndex = [...]uint8{0, 7, 20, 30, 39, 45, 52, 59, 79, 97, 115}

const _VariantLowerName = "invalidautoliabilityhalfyearlyquarterlydrivervehicleaveragedistancepercentagenjdistancepercentagedurationpercentage"

func (i Variant) String() string {
	if i < 0 || i >= Variant(len(_VariantIndex)-1) {
		return fmt.Sprintf("Variant(%d)", i)
	}
	return _VariantName[_VariantIndex[i]:_VariantIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _VariantNoOp() {
	var x [1]struct{}
	_ = x[VariantInvalid-(0)]
	_ = x[VariantAutoLiability-(1)]
	_ = x[VariantHalfYearly-(2)]
	_ = x[VariantQuarterly-(3)]
	_ = x[VariantDriver-(4)]
	_ = x[VariantVehicle-(5)]
	_ = x[VariantAverage-(6)]
	_ = x[VariantDistancePercentageNJ-(7)]
	_ = x[VariantDistancePercentage-(8)]
	_ = x[VariantDurationPercentage-(9)]
}

var _VariantValues = []Variant{VariantInvalid, VariantAutoLiability, VariantHalfYearly, VariantQuarterly, VariantDriver, VariantVehicle, VariantAverage, VariantDistancePercentageNJ, VariantDistancePercentage, VariantDurationPercentage}

var _VariantNameToValueMap = map[string]Variant{
	_VariantName[0:7]:         VariantInvalid,
	_VariantLowerName[0:7]:    VariantInvalid,
	_VariantName[7:20]:        VariantAutoLiability,
	_VariantLowerName[7:20]:   VariantAutoLiability,
	_VariantName[20:30]:       VariantHalfYearly,
	_VariantLowerName[20:30]:  VariantHalfYearly,
	_VariantName[30:39]:       VariantQuarterly,
	_VariantLowerName[30:39]:  VariantQuarterly,
	_VariantName[39:45]:       VariantDriver,
	_VariantLowerName[39:45]:  VariantDriver,
	_VariantName[45:52]:       VariantVehicle,
	_VariantLowerName[45:52]:  VariantVehicle,
	_VariantName[52:59]:       VariantAverage,
	_VariantLowerName[52:59]:  VariantAverage,
	_VariantName[59:79]:       VariantDistancePercentageNJ,
	_VariantLowerName[59:79]:  VariantDistancePercentageNJ,
	_VariantName[79:97]:       VariantDistancePercentage,
	_VariantLowerName[79:97]:  VariantDistancePercentage,
	_VariantName[97:115]:      VariantDurationPercentage,
	_VariantLowerName[97:115]: VariantDurationPercentage,
}

var _VariantNames = []string{
	_VariantName[0:7],
	_VariantName[7:20],
	_VariantName[20:30],
	_VariantName[30:39],
	_VariantName[39:45],
	_VariantName[45:52],
	_VariantName[52:59],
	_VariantName[59:79],
	_VariantName[79:97],
	_VariantName[97:115],
}

// VariantString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func VariantString(s string) (Variant, error) {
	if val, ok := _VariantNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _VariantNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to Variant values", s)
}

// VariantValues returns all values of the enum
func VariantValues() []Variant {
	return _VariantValues
}

// VariantStrings returns a slice of all String values of the enum
func VariantStrings() []string {
	strs := make([]string, len(_VariantNames))
	copy(strs, _VariantNames)
	return strs
}

// IsAVariant returns "true" if the value is listed in the enum definition. "false" otherwise
func (i Variant) IsAVariant() bool {
	for _, v := range _VariantValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for Variant
func (i Variant) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for Variant
func (i *Variant) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("Variant should be a string, got %s", data)
	}

	var err error
	*i, err = VariantString(s)
	return err
}
