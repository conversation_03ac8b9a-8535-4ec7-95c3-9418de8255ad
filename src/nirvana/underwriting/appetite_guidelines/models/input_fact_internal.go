package models

import "github.com/volatiletech/null/v8"

// InternalInputFact represents the input data for the 'internal guideline' appetite guidelines
type InternalInputFact struct {
	BaseFact
	AlLossesBurnRate               null.Float32
	HalfYearlyUtilization          null.Float32
	QuarterlyUtilization           null.Float32
	IsTspSupported                 null.Bool
	DriverTurnover                 null.Float32
	DriverCount                    null.Int32
	VehicleCount                   null.Int32
	HazardZoneDistancePercentageNJ null.Float32 // New Jersey specific
}

func NewInternalInputFact() *InternalInputFact {
	return &InternalInputFact{
		BaseFact: BaseFact{KindField: FactKindInternalGuideline},
	}
}

// TODO: write tests
func (f *InternalInputFact) Equal(other Fact) bool {
	otherFact, ok := other.(*InternalInputFact)
	if !ok {
		return false
	}

	if f == otherFact {
		return true
	}
	if f == nil || otherFact == nil {
		return false
	}

	return f.AlLossesBurnRate == otherFact.AlLossesBurnRate &&
		f.HalfYearlyUtilization == otherFact.HalfYearlyUtilization &&
		f.QuarterlyUtilization == otherFact.QuarterlyUtilization &&
		f.IsTspSupported == otherFact.IsTspSupported &&
		f.DriverTurnover == otherFact.DriverTurnover &&
		f.DriverCount == otherFact.DriverCount &&
		f.VehicleCount == otherFact.VehicleCount &&
		f.HazardZoneDistancePercentageNJ == otherFact.HazardZoneDistancePercentageNJ
}
