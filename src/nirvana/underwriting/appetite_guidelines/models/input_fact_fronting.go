package models

import "github.com/volatiletech/null/v8"

type FrontingInputFact struct {
	BaseFact
	DotRating                    DotRating
	TotalMonths                  null.Int
	TrsMarketCategory            null.String
	HazardZoneDistancePercentage null.Float32
	HazardZoneDurationPercentage null.Float32
}

type DotRating struct {
	Rating               null.String
	IsDateWithinTwoYears null.Bool
}

func NewFrontingInputFact() *FrontingInputFact {
	return &FrontingInputFact{
		BaseFact: BaseFact{KindField: FactKindFrontingGuideline},
	}
}

// TODO: write tests
func (f *FrontingInputFact) Equal(other Fact) bool {
	otherFact, ok := other.(*FrontingInputFact)
	if !ok {
		return false
	}
	if f == otherFact {
		return true
	}
	if f == nil || otherFact == nil {
		return false
	}

	return f.DotRating == otherFact.DotRating &&
		f.TotalMonths == otherFact.TotalMonths &&
		f.TrsMarketCategory == otherFact.TrsMarketCategory &&
		f.HazardZoneDistancePercentage == otherFact.HazardZoneDistancePercentage &&
		f.HazardZoneDurationPercentage == otherFact.HazardZoneDurationPercentage
}
