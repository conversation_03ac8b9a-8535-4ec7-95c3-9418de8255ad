// Code generated by "enumer -type=Rule -json -trimprefix=Rule"; DO NOT EDIT.

package models

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _RuleName = "InvalidLossesBurnRateYearsInBusinessDotRatingUtilizationTelematicsRiskScoreUnsupportedTSPDriverTurnoverFleetSizeHazardZones"

var _RuleIndex = [...]uint8{0, 7, 21, 36, 45, 56, 75, 89, 103, 112, 123}

const _RuleLowerName = "invalidlossesburnrateyearsinbusinessdotratingutilizationtelematicsriskscoreunsupportedtspdriverturnoverfleetsizehazardzones"

func (i Rule) String() string {
	if i < 0 || i >= Rule(len(_RuleIndex)-1) {
		return fmt.Sprintf("Rule(%d)", i)
	}
	return _RuleName[_RuleIndex[i]:_RuleIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _RuleNoOp() {
	var x [1]struct{}
	_ = x[RuleInvalid-(0)]
	_ = x[RuleLossesBurnRate-(1)]
	_ = x[RuleYearsInBusiness-(2)]
	_ = x[RuleDotRating-(3)]
	_ = x[RuleUtilization-(4)]
	_ = x[RuleTelematicsRiskScore-(5)]
	_ = x[RuleUnsupportedTSP-(6)]
	_ = x[RuleDriverTurnover-(7)]
	_ = x[RuleFleetSize-(8)]
	_ = x[RuleHazardZones-(9)]
}

var _RuleValues = []Rule{RuleInvalid, RuleLossesBurnRate, RuleYearsInBusiness, RuleDotRating, RuleUtilization, RuleTelematicsRiskScore, RuleUnsupportedTSP, RuleDriverTurnover, RuleFleetSize, RuleHazardZones}

var _RuleNameToValueMap = map[string]Rule{
	_RuleName[0:7]:          RuleInvalid,
	_RuleLowerName[0:7]:     RuleInvalid,
	_RuleName[7:21]:         RuleLossesBurnRate,
	_RuleLowerName[7:21]:    RuleLossesBurnRate,
	_RuleName[21:36]:        RuleYearsInBusiness,
	_RuleLowerName[21:36]:   RuleYearsInBusiness,
	_RuleName[36:45]:        RuleDotRating,
	_RuleLowerName[36:45]:   RuleDotRating,
	_RuleName[45:56]:        RuleUtilization,
	_RuleLowerName[45:56]:   RuleUtilization,
	_RuleName[56:75]:        RuleTelematicsRiskScore,
	_RuleLowerName[56:75]:   RuleTelematicsRiskScore,
	_RuleName[75:89]:        RuleUnsupportedTSP,
	_RuleLowerName[75:89]:   RuleUnsupportedTSP,
	_RuleName[89:103]:       RuleDriverTurnover,
	_RuleLowerName[89:103]:  RuleDriverTurnover,
	_RuleName[103:112]:      RuleFleetSize,
	_RuleLowerName[103:112]: RuleFleetSize,
	_RuleName[112:123]:      RuleHazardZones,
	_RuleLowerName[112:123]: RuleHazardZones,
}

var _RuleNames = []string{
	_RuleName[0:7],
	_RuleName[7:21],
	_RuleName[21:36],
	_RuleName[36:45],
	_RuleName[45:56],
	_RuleName[56:75],
	_RuleName[75:89],
	_RuleName[89:103],
	_RuleName[103:112],
	_RuleName[112:123],
}

// RuleString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func RuleString(s string) (Rule, error) {
	if val, ok := _RuleNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _RuleNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to Rule values", s)
}

// RuleValues returns all values of the enum
func RuleValues() []Rule {
	return _RuleValues
}

// RuleStrings returns a slice of all String values of the enum
func RuleStrings() []string {
	strs := make([]string, len(_RuleNames))
	copy(strs, _RuleNames)
	return strs
}

// IsARule returns "true" if the value is listed in the enum definition. "false" otherwise
func (i Rule) IsARule() bool {
	for _, v := range _RuleValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for Rule
func (i Rule) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for Rule
func (i *Rule) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("Rule should be a string, got %s", data)
	}

	var err error
	*i, err = RuleString(s)
	return err
}
