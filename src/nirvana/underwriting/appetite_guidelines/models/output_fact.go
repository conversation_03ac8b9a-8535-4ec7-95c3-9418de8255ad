package models

import (
	"sort"

	"github.com/cockroachdb/errors"
)

// OutputFact represents the output data after applying the appetite guidelines rules. Grule rule engine
// uses this to store decisions made based on the input fact.
type OutputFact struct {
	BaseFact
	Decisions []DecisionEntity
}

func NewOutputFact() *OutputFact {
	return &OutputFact{
		BaseFact: BaseFact{KindField: FactKindOutputFact},
	}
}

// TODO: write tests
func (f *OutputFact) Equal(other Fact) bool {
	otherFact, ok := other.(*OutputFact)
	if !ok {
		return false
	}

	if f == otherFact {
		return true
	}
	if f == nil || otherFact == nil {
		return false
	}

	if len(f.Decisions) != len(otherFact.Decisions) {
		return false
	}

	// Track used decisions to avoid duplicates
	used := make([]bool, len(otherFact.Decisions))

	// Order of decisions does not matter
	for _, d1 := range f.Decisions {
		found := false
		for j, d2 := range otherFact.Decisions {
			if !used[j] && d1.Equal(&d2) {
				used[j] = true
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}

func (f *OutputFact) addDecision(rule Rule, decision Decision, variant *Variant) {
	f.Decisions = append(f.Decisions, DecisionEntity{
		RuleName: rule,
		Variant:  variant,
		Decision: decision,
	})
}

func (f *OutputFact) SetLossesBurnRateDecision(d Decision, variant Variant) {
	f.addDecision(RuleLossesBurnRate, d, &variant)
}

func (f *OutputFact) SetYearsInBusinessDecision(d Decision) {
	f.addDecision(RuleYearsInBusiness, d, nil)
}

func (f *OutputFact) SetDotRatingDecision(d Decision) {
	f.addDecision(RuleDotRating, d, nil)
}

func (f *OutputFact) SetUtilizationDecision(d Decision, variant Variant) {
	f.addDecision(RuleUtilization, d, &variant)
}

func (f *OutputFact) SetTelematicsRiskScoreDecision(d Decision) {
	f.addDecision(RuleTelematicsRiskScore, d, nil)
}

func (f *OutputFact) SetUnsupportedTSPDecision(d Decision) {
	f.addDecision(RuleUnsupportedTSP, d, nil)
}

func (f *OutputFact) SetDriverTurnoverDecision(d Decision) {
	f.addDecision(RuleDriverTurnover, d, nil)
}

func (f *OutputFact) SetFleetSizeDecision(d Decision, variant Variant) {
	f.addDecision(RuleFleetSize, d, &variant)
}

func (f *OutputFact) SetHazardZonesDecision(d Decision, variant Variant) {
	f.addDecision(RuleHazardZones, d, &variant)
}

func (f *OutputFact) GenerateOverallDecision() (Decision, error) {
	if len(f.Decisions) == 0 {
		return DecisionInvalid, errors.New("no decisions available to generate overall decision")
	}

	for _, d := range f.Decisions {
		if d.Decision == DecisionDecline {
			return DecisionDecline, nil
		}
		// continue if the decision is not Decline
	}

	for _, d := range f.Decisions {
		if d.Decision == DecisionFurtherReview {
			return DecisionFurtherReview, nil
		}
	}

	return DecisionNone, nil
}

// SortByRuleNameAndVariant sorts the decisions in the OutputFact by RuleName
// and then by Variant in lexicographical order.
func (f *OutputFact) SortByRuleNameAndVariant() {
	sort.Slice(f.Decisions, func(i, j int) bool {
		di, dj := f.Decisions[i], f.Decisions[j]

		ruleNameI := di.RuleName.String()
		ruleNameJ := dj.RuleName.String()

		if ruleNameI != ruleNameJ {
			return ruleNameI < ruleNameJ
		}

		// RuleNames are equal → compare Variant names
		var variantI, variantJ string
		if di.Variant != nil {
			variantI = di.Variant.String()
		}
		if dj.Variant != nil {
			variantJ = dj.Variant.String()
		}

		return variantI < variantJ
	})
}
