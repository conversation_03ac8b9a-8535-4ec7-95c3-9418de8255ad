package models

// InputFactConstants holds the constants used in input facts for the rule engine.
type InputFactConstants struct {
	Variant  VariantConstants
	Decision DecisionConstants
}

type DecisionConstants struct {
	None          Decision
	FurtherReview Decision
	Decline       Decision
	Pending       Decision
}

type VariantConstants struct {
	AutoLiability        Variant
	AutoPhysicalDamage   Variant
	MotorTruckCargo      Variant
	HalfYearly           Variant
	Quarterly            Variant
	Driver               Variant
	Vehicle              Variant
	Average              Variant
	DistancePercentageNJ Variant
	DistancePercentage   Variant
	DurationPercentage   Variant
}

func NewInputFactConstants() *InputFactConstants {
	return &InputFactConstants{
		Decision: DecisionConstants{
			None:          DecisionNone,
			FurtherReview: DecisionFurtherReview,
			Decline:       DecisionDecline,
			Pending:       DecisionPending,
		},
		Variant: VariantConstants{
			AutoLiability:        VariantAutoLiability,
			HalfYearly:           VariantHalfYearly,
			Quarterly:            VariantQuarterly,
			Driver:               VariantDriver,
			Vehicle:              VariantVehicle,
			Average:              VariantAverage,
			DistancePercentageNJ: VariantDistancePercentageNJ,
			DistancePercentage:   VariantDistancePercentage,
			DurationPercentage:   VariantDurationPercentage,
		},
	}
}
