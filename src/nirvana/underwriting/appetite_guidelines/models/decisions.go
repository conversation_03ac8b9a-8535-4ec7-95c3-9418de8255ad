package models

import (
	"time"

	"github.com/google/uuid"
)

type Decisions struct {
	Id                  uuid.UUID
	RuleSetId           uuid.UUID // RuleSetId is the ID of the rule set used to generate this decision
	Program             Program
	ApplicationReviewID uuid.UUID
	Decision            Decision // Decision represents the overall decision made based on the rules applied
	CreatedAt           time.Time
	InputContext        Fact // InputFact contains the input data used to generate the decision
	OutputContext       Fact // OutputFact contains the decisions made based on the input data
}

// DecisionEntity represents a single decision made from a rule.
type DecisionEntity struct {
	RuleName Rule
	Variant  *Variant
	Decision Decision
}

//go:generate go run github.com/dmarkham/enumer -type=Rule -json -trimprefix=Rule
type Rule int

const (
	RuleInvalid Rule = iota
	RuleLossesBurnRate
	RuleYearsInBusiness
	RuleDotRating
	RuleUtilization
	RuleTelematicsRiskScore
	RuleUnsupportedTSP
	RuleDriverTurnover
	RuleFleetSize
	RuleHazardZones
)

//go:generate go run github.com/dmarkham/enumer -type=Variant -json -trimprefix=Variant
type Variant int

const (
	VariantInvalid Variant = iota
	VariantAutoLiability
	VariantHalfYearly
	VariantQuarterly
	VariantDriver
	VariantVehicle
	VariantAverage
	VariantDistancePercentageNJ
	VariantDistancePercentage
	VariantDurationPercentage
)

//go:generate go run github.com/dmarkham/enumer -type=Decision -json -trimprefix=Decision
type Decision int

const (
	DecisionInvalid Decision = iota
	DecisionPending
	DecisionNone
	DecisionDecline
	DecisionFurtherReview
)
