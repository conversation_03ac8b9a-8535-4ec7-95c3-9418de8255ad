package db

import (
	"encoding/json"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"nirvanatech.com/nirvana/db-api/db_models/appetite_guidelines"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"
)

func ruleSetFromDb(dbRuleSet *appetite_guidelines.RuleSet) (*models.RuleSet, error) {
	id, err := uuid.Parse(dbRuleSet.ID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse rule set id %s", dbRuleSet.ID)
	}

	category, err := models.CategoryString(dbRuleSet.Category)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse category %s", dbRuleSet.Category)
	}

	program, err := models.ProgramString(dbRuleSet.Program)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse program %s", dbRuleSet.Program)
	}

	ruleSet := &models.RuleSet{
		Id:        id,
		Category:  category,
		Program:   program,
		Version:   dbRuleSet.Version,
		Rules:     dbRuleSet.Rules,
		CreatedAt: dbRuleSet.CreatedAt,
	}

	if !dbRuleSet.Description.IsZero() {
		ruleSet.Description = &dbRuleSet.Description.String
	}

	return ruleSet, nil
}

func dbToDecision(dbDecision *appetite_guidelines.Decision) (*models.Decisions, error) {
	id, err := uuid.Parse(dbDecision.ID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse decision id %s", dbDecision.ID)
	}

	ruleSetId, err := uuid.Parse(dbDecision.RuleSetID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse rule set id %s", dbDecision.RuleSetID)
	}

	program, err := models.ProgramString(dbDecision.Program)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse program %s", dbDecision.Program)
	}

	appReviewId, err := uuid.Parse(dbDecision.ApplicationReviewID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse application review id %s", dbDecision.ApplicationReviewID)
	}

	decisionType, err := models.DecisionString(dbDecision.Decision)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse decision type %s", dbDecision.Decision)
	}

	var inputFact models.Fact
	if dbDecision.InputContext != nil {
		fact, err := unmarshalFact(dbDecision.InputContext)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshal input context")
		}
		inputFact = fact
	}

	var outputFact models.Fact
	if dbDecision.OutputContext != nil {
		fact, err := unmarshalFact(dbDecision.OutputContext)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshal output context")
		}
		outputFact = fact
	}

	return &models.Decisions{
		Id:                  id,
		RuleSetId:           ruleSetId,
		Program:             program,
		ApplicationReviewID: appReviewId,
		Decision:            decisionType,
		CreatedAt:           dbDecision.CreatedAt,
		InputContext:        inputFact,
		OutputContext:       outputFact,
	}, nil
}

func decisionToDb(input *models.Decisions) (*appetite_guidelines.Decision, error) {
	if input.Id == uuid.Nil {
		return nil, errors.New("decision ID cannot be empty")
	}

	if input.RuleSetId == uuid.Nil {
		return nil, errors.New("rule set ID cannot be empty")
	}

	if input.ApplicationReviewID == uuid.Nil {
		return nil, errors.New("application review ID cannot be empty")
	}

	if input.Decision == models.DecisionInvalid {
		return nil, errors.New("decision cannot be empty")
	}

	if input.CreatedAt.IsZero() {
		return nil, errors.New("created_at timestamp cannot be empty")
	}

	if input.OutputContext == nil {
		return nil, errors.New("output context cannot be nil")
	}

	if input.InputContext == nil {
		return nil, errors.New("input context cannot be nil")
	}

	jsonInputFact, err := json.Marshal(input.InputContext)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshal input context")
	}

	jsonOutputFact, err := json.Marshal(input.OutputContext)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshal output context")
	}

	return &appetite_guidelines.Decision{
		ID:                  input.Id.String(),
		RuleSetID:           input.RuleSetId.String(),
		Program:             input.Program.String(),
		ApplicationReviewID: input.ApplicationReviewID.String(),
		Decision:            input.Decision.String(),
		CreatedAt:           input.CreatedAt,
		InputContext:        jsonInputFact,
		OutputContext:       jsonOutputFact,
	}, nil
}

func unmarshalFact(data []byte) (models.Fact, error) {
	// Extract Kind
	var kindHolder struct {
		Kind string `json:"Kind"`
	}
	if err := json.Unmarshal(data, &kindHolder); err != nil {
		return nil, errors.Wrap(err, "unable to extract fact kind")
	}

	var fact models.Fact

	switch models.FactKind(kindHolder.Kind) {
	case models.FactKindInternalGuideline:
		var f models.InternalInputFact
		if err := json.Unmarshal(data, &f); err != nil {
			return nil, err
		}
		fact = &f

	case models.FactKindFrontingGuideline:
		var f models.FrontingInputFact
		if err := json.Unmarshal(data, &f); err != nil {
			return nil, err
		}
		fact = &f

	case models.FactKindOutputFact:
		var f models.OutputFact
		if err := json.Unmarshal(data, &f); err != nil {
			return nil, err
		}
		fact = &f

	default:
		return nil, errors.Errorf("unsupported fact kind: %s", kindHolder.Kind)
	}

	return fact, nil
}
