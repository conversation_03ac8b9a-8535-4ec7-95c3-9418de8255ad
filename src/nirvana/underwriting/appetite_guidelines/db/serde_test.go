package db

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/sqlboiler/v4/types"
	"nirvanatech.com/nirvana/db-api/db_models/appetite_guidelines"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"
)

func TestRuleSetFromDb(t *testing.T) {
	now := time.Now()
	validID := uuid.New()

	sampleRuleSet := &appetite_guidelines.RuleSet{
		ID:        validID.String(),
		Category:  "InternalGuideline",
		Program:   "FleetAdmitted",
		Version:   1,
		Rules:     types.JSON([]byte(`[{}]`)),
		CreatedAt: now,
	}

	tests := []struct {
		name      string
		input     *appetite_guidelines.RuleSet
		wantErr   bool
		wantMatch bool // if true we assert output matches input
	}{
		{
			name:      "valid rule set converts correctly",
			input:     sampleRuleSet,
			wantErr:   false,
			wantMatch: true,
		},
		{
			name: "invalid uuid returns error",
			input: func() *appetite_guidelines.RuleSet {
				rs := *sampleRuleSet
				rs.ID = "bad-uuid"
				return &rs
			}(),
			wantErr: true,
		},
		{
			name: "invalid category returns error",
			input: func() *appetite_guidelines.RuleSet {
				rs := *sampleRuleSet
				rs.Category = "UnknownCategory"
				return &rs
			}(),
			wantErr: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got, err := ruleSetFromDb(tc.input)
			if tc.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
				return
			}
			require.NoError(t, err)
			require.NotNil(t, got)
			if tc.wantMatch {
				require.Equal(t, validID, got.Id)
				require.Equal(t, models.CategoryInternalGuideline, got.Category)
				require.Equal(t, models.ProgramFleetAdmitted, got.Program)
				require.Equal(t, 1, got.Version)
				require.Equal(t, []byte(`[{}]`), got.Rules)
				require.WithinDuration(t, now, got.CreatedAt, time.Second)
			}
		})
	}
}

func TestDecisionToDb(t *testing.T) {
	now := time.Now()
	ruleSetID := uuid.New()
	appID := uuid.New()

	tests := []struct {
		name    string
		input   *models.Decisions
		wantErr bool
	}{
		{
			name: "valid decision converts to db model",
			input: &models.Decisions{
				Id:                  uuid.New(),
				RuleSetId:           ruleSetID,
				Program:             models.ProgramFleetAdmitted,
				ApplicationReviewID: appID,
				Decision:            models.DecisionDecline,
				CreatedAt:           now,
				InputContext:        models.NewInternalInputFact(),
				OutputContext:       models.NewOutputFact(),
			},
			wantErr: false,
		},
		{
			name: "missing input context error",
			input: &models.Decisions{
				Id:                  uuid.New(),
				RuleSetId:           ruleSetID,
				ApplicationReviewID: appID,
				Decision:            models.DecisionDecline,
				CreatedAt:           now,
			},
			wantErr: true,
		},
		{
			name: "nil id triggers error",
			input: &models.Decisions{
				Id:                  uuid.Nil,
				RuleSetId:           ruleSetID,
				ApplicationReviewID: appID,
				Decision:            models.DecisionDecline,
				CreatedAt:           now,
				InputContext:        models.NewInternalInputFact(),
				OutputContext:       models.NewOutputFact(),
			},
			wantErr: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got, err := decisionToDb(tc.input)
			if tc.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
				return
			}
			require.NoError(t, err)
			require.NotNil(t, got)
			require.Equal(t, tc.input.Id.String(), got.ID)
			require.Equal(t, tc.input.RuleSetId.String(), got.RuleSetID)
			require.Equal(t, tc.input.ApplicationReviewID.String(), got.ApplicationReviewID)
			require.Equal(t, tc.input.Decision.String(), got.Decision)
			require.WithinDuration(t, tc.input.CreatedAt, got.CreatedAt, time.Second)
		})
	}
}

func TestDbToDecision(t *testing.T) {
	now := time.Now()
	ruleSetID := uuid.New()
	appID := uuid.New()

	inputContextJSON, _ := json.Marshal(models.NewInternalInputFact())
	outputContextJSON, _ := json.Marshal(models.NewOutputFact())

	validDecision := &appetite_guidelines.Decision{
		ID:                  uuid.New().String(),
		RuleSetID:           ruleSetID.String(),
		Program:             models.ProgramNonFleetAdmitted.String(),
		ApplicationReviewID: appID.String(),
		Decision:            models.DecisionDecline.String(),
		CreatedAt:           now,
		InputContext:        types.JSON(inputContextJSON),
		OutputContext:       types.JSON(outputContextJSON),
	}

	tests := []struct {
		name    string
		input   *appetite_guidelines.Decision
		wantErr bool
	}{
		{
			name:  "valid db decision converts",
			input: validDecision,
		},
		{
			name: "invalid decision enum returns error",
			input: func() *appetite_guidelines.Decision {
				d := *validDecision
				d.Decision = "UnknownDecision"
				return &d
			}(),
			wantErr: true,
		},
		{
			name: "invalid id uuid returns error",
			input: func() *appetite_guidelines.Decision {
				d := *validDecision
				d.ID = "bad-uuid"
				return &d
			}(),
			wantErr: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got, err := dbToDecision(tc.input)
			if tc.wantErr {
				require.Error(t, err)
				require.Nil(t, got)
				return
			}
			require.NoError(t, err)
			require.NotNil(t, got)
			require.Equal(t, models.DecisionDecline, got.Decision)
			require.Equal(t, ruleSetID, got.RuleSetId)
			require.Equal(t, appID, got.ApplicationReviewID)
			require.WithinDuration(t, now, got.CreatedAt, time.Second)
		})
	}
}
