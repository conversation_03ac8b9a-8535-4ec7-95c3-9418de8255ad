package db

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/infra/fx/testfixtures/appetite_guidelines_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"
)

type env struct {
	fx.In

	Wrapper *AppetiteGuidelinesDbWrapper
	*appetite_guidelines_fixture.Fixture
}

func TestGetRuleSetByVersion(t *testing.T) {
	var e env
	defer testloader.RequireStart(t, &e).RequireStop()

	ctx := context.Background()

	tests := []struct {
		name    string
		version int
		wantErr bool
	}{
		{
			name:    "success returns expected rule set",
			version: 1, // fixture provides V1 of FleetAdmitted/Internal
			wantErr: false,
		},
		{
			name:    "not found returns error",
			version: 999, // non-existent version
			wantErr: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			rs, err := e.Wrapper.GetRuleSetByVersion(
				ctx,
				models.CategoryInternalGuideline.String(),
				models.ProgramFleetAdmitted.String(),
				tc.version,
			)

			if tc.wantErr {
				require.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, rs)
			require.Equal(t, models.CategoryInternalGuideline, rs.Category)
			require.Equal(t, models.ProgramFleetAdmitted, rs.Program)
			require.Equal(t, tc.version, rs.Version)
		})
	}
}

func TestInsertDecision(t *testing.T) {
	var e env
	defer testloader.RequireStart(t, &e).RequireStop()

	ctx := context.Background()

	// Fetch rule-set id from fixture for decision linking.
	rs, err := e.Wrapper.GetRuleSetByVersion(ctx, models.CategoryInternalGuideline.String(), models.ProgramFleetAdmitted.String(), 1)
	require.NoError(t, err)

	now := time.Now()

	cases := []struct {
		name     string
		decision *models.Decisions
		wantErr  bool
	}{
		{
			name: "valid decision inserted successfully",
			decision: &models.Decisions{
				Id:                  uuid.New(),
				RuleSetId:           rs.Id,
				ApplicationReviewID: uuid.New(),
				Decision:            models.DecisionDecline,
				CreatedAt:           now,
				InputContext:        models.NewInternalInputFact(),
				OutputContext:       models.NewOutputFact(),
			},
			wantErr: false,
		},
		{
			name: "missing triggered snapshot returns error",
			decision: &models.Decisions{
				Id:                  uuid.New(),
				RuleSetId:           rs.Id,
				ApplicationReviewID: uuid.New(),
				Decision:            models.DecisionDecline,
				CreatedAt:           now,
			},
			wantErr: true,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			err := e.Wrapper.InsertDecision(ctx, tc.decision)
			if tc.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestGetLatestDecision(t *testing.T) {
	var e env
	defer testloader.RequireStart(t, &e).RequireStop()

	ctx := context.Background()

	rs, err := e.Wrapper.GetRuleSetByVersion(ctx, models.CategoryInternalGuideline.String(), models.ProgramFleetAdmitted.String(), 1)
	require.NoError(t, err)

	now := time.Now()

	cases := []struct {
		name          string
		seedFunc      func(appID uuid.UUID)
		expectErr     bool
		expectedDecID uuid.UUID
	}{
		{
			name:      "no decisions returns error",
			seedFunc:  func(appID uuid.UUID) {},
			expectErr: true,
		},
		{
			name: "returns latest decision",
			seedFunc: func(appID uuid.UUID) {
				older := &models.Decisions{
					Id:                  uuid.New(),
					RuleSetId:           rs.Id,
					Program:             models.ProgramFleetAdmitted,
					ApplicationReviewID: appID,
					Decision:            models.DecisionFurtherReview,
					CreatedAt:           now.Add(-time.Minute),
					InputContext:        models.NewInternalInputFact(),
					OutputContext:       models.NewOutputFact(),
				}
				newer := &models.Decisions{
					Id:                  uuid.New(),
					RuleSetId:           rs.Id,
					Program:             models.ProgramFleetAdmitted,
					ApplicationReviewID: appID,
					Decision:            models.DecisionDecline,
					CreatedAt:           now,
					InputContext:        models.NewInternalInputFact(),
					OutputContext:       models.NewOutputFact(),
				}
				require.NoError(t, e.Wrapper.InsertDecision(ctx, older))
				require.NoError(t, e.Wrapper.InsertDecision(ctx, newer))
				// store latest decision id for assertion
			},
			expectErr: false,
			// will set expectedDecID below after seeding
		},
	}

	for i := range cases {
		t.Run(cases[i].name, func(t *testing.T) {
			appID := uuid.New()

			// run seeding
			cases[i].seedFunc(appID)

			// fetch latest
			got, err := e.Wrapper.GetLatestDecision(ctx, appID.String(), rs.Id.String())

			if cases[i].expectErr {
				require.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, got)
			// if not expecting error, we must have seeded decisions
			require.Equal(t, models.DecisionDecline, got.Decision)
		})
	}
}
