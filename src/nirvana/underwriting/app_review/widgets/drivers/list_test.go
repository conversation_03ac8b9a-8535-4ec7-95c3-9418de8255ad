package drivers

import (
	"context"
	"errors"
	"strings"
	"testing"
	"time"

	"nirvanatech.com/nirvana/underwriting/test_utils"

	"nirvanatech.com/nirvana/common-go/us_states"

	"nirvanatech.com/nirvana/common-go/slice_utils"

	"nirvanatech.com/nirvana/infra/fx/testloader"

	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/rating/data_fetching/mvr_fetching"

	"google.golang.org/protobuf/types/known/timestamppb"

	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"

	"github.com/stretchr/testify/require"

	"go.uber.org/fx"

	openapi_types "github.com/oapi-codegen/runtime/types"
	"github.com/stretchr/testify/assert"
	uw_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

func TestProcessDriverExperienceMismatchWithYoeFromAgentInput(t *testing.T) {
	t.Parallel()
	fixedTime := time.Now()

	tests := []struct {
		name            string
		driverList      []oapi_uw.ApplicationReviewDriversListItem
		inputDriverList *application.DriversInfo
		submittedAt     time.Time
		validateFunc    func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error)
	}{
		{
			name:            "nil input driver list",
			driverList:      []oapi_uw.ApplicationReviewDriversListItem{{DlNumber: "DL123", UsState: "CA"}},
			inputDriverList: nil,
			submittedAt:     fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 1)
				assert.Equal(t, "DL123", result[0].DlNumber)
				assert.Equal(t, "CA", result[0].UsState)
				// Ensure nothing was changed
				assert.Nil(t, result[0].ExperienceStartDate)
				assert.Nil(t, result[0].YearsOfExperienceAgentInput)
				assert.Nil(t, result[0].YearsOfExperienceMVR)
				assert.Nil(t, result[0].IsExperienceMismatch)
			},
		},
		{
			name:       "empty driver list",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL123", YearsOfExperience: pointer_utils.Float32(5.0)},
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				assert.Empty(t, result)
			},
		},
		{
			name: "no matching drivers",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{
				{DlNumber: "DL123", UsState: "CA"},
			},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL456", YearsOfExperience: pointer_utils.Float32(5.0)},
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 1)
				assert.Equal(t, "DL123", result[0].DlNumber)
				// Ensure nothing was changed
				assert.Nil(t, result[0].ExperienceStartDate)
				assert.Nil(t, result[0].YearsOfExperienceAgentInput)
				assert.Nil(t, result[0].YearsOfExperienceMVR)
				assert.Nil(t, result[0].IsExperienceMismatch)
			},
		},
		{
			name: "matching driver without YearsOfExperience",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{
				{DlNumber: "DL123", UsState: "CA"},
			},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL123", YearsOfExperience: nil},
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 1)
				assert.Equal(t, "DL123", result[0].DlNumber)
				// Ensure nothing was changed
				assert.Nil(t, result[0].ExperienceStartDate)
				assert.Nil(t, result[0].YearsOfExperienceAgentInput)
				assert.Nil(t, result[0].YearsOfExperienceMVR)
				assert.Nil(t, result[0].IsExperienceMismatch)
			},
		},
		{
			name: "matching driver with YearsOfExperience but no MvrDetails",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{
				{DlNumber: "DL123", UsState: "CA"},
			},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL123", YearsOfExperience: pointer_utils.Float32(5.0)},
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 1)
				assert.Equal(t, "DL123", result[0].DlNumber)

				// Check if ExperienceStartDate was set
				require.NotNil(t, result[0].ExperienceStartDate)

				// Check if YearsOfExperienceAgentInput was formatted correctly
				require.NotNil(t, result[0].YearsOfExperienceAgentInput)
				assert.Contains(t, *result[0].YearsOfExperienceAgentInput, "5 years")

				// These fields should not be set
				assert.Nil(t, result[0].YearsOfExperienceMVR)
				assert.Nil(t, result[0].IsExperienceMismatch)
			},
		},
		{
			name: "matching driver with YearsOfExperience and MvrDetails with no significant difference",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{
				{
					DlNumber: "DL123",
					UsState:  "CA",
					MvrDetails: &oapi_uw.ApplicationReviewDriverMvrDetails{
						DateIssued: &openapi_types.Date{Time: fixedTime.AddDate(-5, 0, 0)},
					},
				},
			},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL123", YearsOfExperience: pointer_utils.Float32(5.0)},
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 1)
				assert.Equal(t, "DL123", result[0].DlNumber)

				// Check if ExperienceStartDate was set
				require.NotNil(t, result[0].ExperienceStartDate)

				// Check if YearsOfExperienceAgentInput was formatted correctly
				require.NotNil(t, result[0].YearsOfExperienceAgentInput)
				assert.Contains(t, *result[0].YearsOfExperienceAgentInput, "5 years")

				// Check if YearsOfExperienceMVR was set
				require.NotNil(t, result[0].YearsOfExperienceMVR)

				// We know the MVR date is 5 years ago
				assert.Contains(t, *result[0].YearsOfExperienceMVR, "5 years")

				// With no significant difference, IsExperienceMismatch should be nil or false
				if result[0].IsExperienceMismatch != nil {
					assert.False(t, *result[0].IsExperienceMismatch)
				}
			},
		},
		{
			name: "matching driver with YearsOfExperience and MvrDetails with significant difference",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{
				{
					DlNumber: "DL123",
					UsState:  "CA",
					MvrDetails: &oapi_uw.ApplicationReviewDriverMvrDetails{
						DateIssued: &openapi_types.Date{Time: fixedTime.AddDate(-2, 0, 0)},
					},
				},
			},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL123", YearsOfExperience: pointer_utils.Float32(5.0)},
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 1)
				assert.Equal(t, "DL123", result[0].DlNumber)

				// Check if ExperienceStartDate was set
				require.NotNil(t, result[0].ExperienceStartDate)

				// Check if YearsOfExperienceAgentInput was formatted correctly
				require.NotNil(t, result[0].YearsOfExperienceAgentInput)
				assert.Contains(t, *result[0].YearsOfExperienceAgentInput, "5 years")

				// Check if YearsOfExperienceMVR was set
				require.NotNil(t, result[0].YearsOfExperienceMVR)

				// We know the MVR date is 2 years ago
				assert.Contains(t, *result[0].YearsOfExperienceMVR, "2 years")

				// With a significant difference (5 years vs 2 years),
				// IsExperienceMismatch should be set to true
				require.NotNil(t, result[0].IsExperienceMismatch)
				assert.True(t, *result[0].IsExperienceMismatch)
			},
		},
		{
			name: "fractional years of experience - 5.5 years",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{
				{DlNumber: "DL123", UsState: "CA"},
			},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL123", YearsOfExperience: pointer_utils.Float32(5.5)},
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 1)
				assert.Equal(t, "DL123", result[0].DlNumber)

				require.NotNil(t, result[0].ExperienceStartDate)
				require.NotNil(t, result[0].YearsOfExperienceAgentInput)
				assert.Contains(t, *result[0].YearsOfExperienceAgentInput, "5 years 6 months")
			},
		},
		{
			name: "fractional years of experience - 2.25 years",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{
				{DlNumber: "DL123", UsState: "CA"},
			},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL123", YearsOfExperience: pointer_utils.Float32(2.25)},
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 1)
				assert.Equal(t, "DL123", result[0].DlNumber)

				require.NotNil(t, result[0].ExperienceStartDate)
				require.NotNil(t, result[0].YearsOfExperienceAgentInput)
				assert.Contains(t, *result[0].YearsOfExperienceAgentInput, "2 years 3 months")
			},
		},
		{
			name: "multiple drivers with mixed scenarios",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{
				{DlNumber: "DL123", UsState: "CA"},
				{
					DlNumber: "DL456",
					UsState:  "NY",
					MvrDetails: &oapi_uw.ApplicationReviewDriverMvrDetails{
						DateIssued: &openapi_types.Date{Time: fixedTime.AddDate(-3, 0, 0)},
					},
				},
				{DlNumber: "DL789", UsState: "TX"},
			},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL123", YearsOfExperience: pointer_utils.Float32(5.0)},
					{DriverLicenseNumber: "DL456", YearsOfExperience: pointer_utils.Float32(1.5)},
					{DriverLicenseNumber: "DL789", YearsOfExperience: nil},
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 3)

				// First driver: YearsOfExperience but no MvrDetails
				assert.Equal(t, "DL123", result[0].DlNumber)
				require.NotNil(t, result[0].ExperienceStartDate)
				require.NotNil(t, result[0].YearsOfExperienceAgentInput)
				assert.Contains(t, *result[0].YearsOfExperienceAgentInput, "5 years")
				assert.Nil(t, result[0].YearsOfExperienceMVR)

				// Second driver: YearsOfExperience and MvrDetails
				assert.Equal(t, "DL456", result[1].DlNumber)
				require.NotNil(t, result[1].ExperienceStartDate)
				require.NotNil(t, result[1].YearsOfExperienceAgentInput)
				assert.Contains(t, *result[1].YearsOfExperienceAgentInput, "1 years 6 months")
				require.NotNil(t, result[1].YearsOfExperienceMVR)
				assert.Contains(t, *result[1].YearsOfExperienceMVR, "3 years")
				require.NotNil(t, result[1].IsExperienceMismatch)
				assert.True(t, *result[1].IsExperienceMismatch)

				// Third driver: No YearsOfExperience
				assert.Equal(t, "DL789", result[2].DlNumber)
				assert.Nil(t, result[2].ExperienceStartDate)
				assert.Nil(t, result[2].YearsOfExperienceAgentInput)
				assert.Nil(t, result[2].YearsOfExperienceMVR)
			},
		},
		{
			name: "edge case - zero years of experience",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{
				{DlNumber: "DL123", UsState: "CA"},
			},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL123", YearsOfExperience: pointer_utils.Float32(0.0)},
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 1)
				assert.Equal(t, "DL123", result[0].DlNumber)

				require.NotNil(t, result[0].ExperienceStartDate)
				require.NotNil(t, result[0].YearsOfExperienceAgentInput)
				assert.Contains(t, *result[0].YearsOfExperienceAgentInput, "0 years")
			},
		},
		{
			name: "edge case - very small years of experience",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{
				{DlNumber: "DL123", UsState: "CA"},
			},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL123", YearsOfExperience: pointer_utils.Float32(0.08)}, // About 1 month
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 1)
				assert.Equal(t, "DL123", result[0].DlNumber)

				require.NotNil(t, result[0].ExperienceStartDate)
				require.NotNil(t, result[0].YearsOfExperienceAgentInput)
				assert.Contains(t, *result[0].YearsOfExperienceAgentInput, "0 years")
				// Due to rounding, it could be either 0 or 1 month
				assert.True(t,
					strings.Contains(*result[0].YearsOfExperienceAgentInput, "0 months") ||
						strings.Contains(*result[0].YearsOfExperienceAgentInput, "1 months"))
			},
		},
		{
			name: "fractional years of experience - 2.2 years with MVR",
			driverList: []oapi_uw.ApplicationReviewDriversListItem{
				{
					DlNumber: "DL123",
					UsState:  "CA",
					MvrDetails: &oapi_uw.ApplicationReviewDriverMvrDetails{
						DateIssued: &openapi_types.Date{Time: fixedTime.AddDate(-1, 0, 0)}, // One year ago from now
					},
				},
			},
			inputDriverList: &application.DriversInfo{
				Drivers: []application.DriverListRecord{
					{DriverLicenseNumber: "DL123", YearsOfExperience: pointer_utils.Float32(2.2)},
				},
			},
			submittedAt: fixedTime,
			validateFunc: func(t *testing.T, result []oapi_uw.ApplicationReviewDriversListItem, err error) {
				require.NoError(t, err)
				require.Len(t, result, 1)
				assert.Equal(t, "DL123", result[0].DlNumber)

				require.NotNil(t, result[0].ExperienceStartDate)
				require.NotNil(t, result[0].YearsOfExperienceAgentInput)
				assert.Contains(t, *result[0].YearsOfExperienceAgentInput, "2 years 2 months")

				require.NotNil(t, result[0].YearsOfExperienceMVR)
				assert.Contains(t, *result[0].YearsOfExperienceMVR, "1 years")

				require.NotNil(t, result[0].IsExperienceMismatch)
				assert.True(t, *result[0].IsExperienceMismatch)
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result, err := processDriverExperienceMismatchWithYoeFromAgentInput(
				tc.driverList,
				tc.inputDriverList,
				tc.submittedAt,
			)

			tc.validateFunc(t, result, err)
		})
	}
}

func TestMVRRefetchWhenDriversAreBulkUpdated(t *testing.T) {
	// Temporarily skipping: flaky test to be fixed soon.
	t.Skip("skipping flaky test")
	ctx := context.Background()
	var env struct {
		fx.In
		AppReviewFixture *application_review_fixture.ApplicationReviewsFixture
		DriverList       List
		AppReviewWrapper uw_wrapper.ApplicationReviewWrapper
	}

	testApp, mockFetcherClient := setupMocksForMVRAndLoadEnv(t, &env)
	defer testApp.RequireStop()

	appReview := env.AppReviewFixture.ApplicationReview

	tests := []struct {
		name     string
		initData func()
		// defines overrides that needs to be applied to the driver
		updater       func(driver *oapi_uw.ApplicationReviewDriversListFormItem)
		shouldRefetch bool
	}{
		{
			name: "should refetch if first name is updated",
			initData: func() {
				err := enableMVRPull(ctx, appReview.Id, env.AppReviewWrapper)
				require.NoError(t, err)
			},
			updater: func(driver *oapi_uw.ApplicationReviewDriversListFormItem) {
				driver.FirstName = pointer_utils.String(test_utils.RandomString(10))
			},
			shouldRefetch: true,
		},
		{
			name: "should refetch if last name is updated",
			initData: func() {
				err := enableMVRPull(ctx, appReview.Id, env.AppReviewWrapper)
				require.NoError(t, err)
			},
			updater: func(driver *oapi_uw.ApplicationReviewDriversListFormItem) {
				driver.LastName = pointer_utils.String(test_utils.RandomString(10))
			},
			shouldRefetch: true,
		},
		{
			name: "should refetch if DOB is updated",
			initData: func() {
				err := enableMVRPull(ctx, appReview.Id, env.AppReviewWrapper)
				require.NoError(t, err)
			},
			updater: func(driver *oapi_uw.ApplicationReviewDriversListFormItem) {
				var dateToExclude *time.Time
				if driver.DateOfBirth != nil {
					dateToExclude = &driver.DateOfBirth.Time
				}
				driver.DateOfBirth = &openapi_types.Date{
					Time: test_utils.RandomValidDateOfBirth(dateToExclude),
				}
			},
			shouldRefetch: true,
		},
		{
			name: "should not refetch if experience start date is updated",
			initData: func() {
				err := enableMVRPull(ctx, appReview.Id, env.AppReviewWrapper)
				require.NoError(t, err)
			},
			updater: func(driver *oapi_uw.ApplicationReviewDriversListFormItem) {
				var dateToExclude *time.Time
				if driver.ExperienceStartDate != nil {
					dateToExclude = &driver.ExperienceStartDate.Time
				}
				driver.ExperienceStartDate = &openapi_types.Date{
					Time: test_utils.RandomValidDate(dateToExclude),
				}
			},
			shouldRefetch: false,
		},
		{
			name: "should not refetch if date hired is updated",
			initData: func() {
				err := enableMVRPull(ctx, appReview.Id, env.AppReviewWrapper)
				require.NoError(t, err)
			},
			updater: func(driver *oapi_uw.ApplicationReviewDriversListFormItem) {
				driver.DateHired = openapi_types.Date{
					Time: test_utils.RandomValidDate(&driver.DateHired.Time),
				}
			},
			shouldRefetch: false,
		},
		{
			name: "should not refetch if MVR pull is disabled",
			initData: func() {
				err := disableMVRPull(ctx, appReview.Id, env.AppReviewWrapper)
				require.NoError(t, err)
			},
			updater: func(driver *oapi_uw.ApplicationReviewDriversListFormItem) {
				// even though first name is updated, but re-fetch should not happen because MVR pull is disabled
				driver.FirstName = pointer_utils.String(test_utils.RandomString(10))
			},
			shouldRefetch: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			test.initData()
			// reset overrides so that subtests do not impact each other
			err := resetDriverOverrides(ctx, appReview.Id, env.AppReviewWrapper)
			require.NoError(t, err)

			driver1 := appReview.Submission.DriversInfo.Drivers[0]
			driver2 := appReview.Submission.DriversInfo.Drivers[1]
			driver3 := appReview.Submission.DriversInfo.Drivers[2]

			driver1UpdateRequest := prepareDriveBulkUpdateRequestItemFromDriver(driver1)
			test.updater(&driver1UpdateRequest) // update driver1 fields

			// no updates for driver 2 and 3
			driver2UpdateRequest := prepareDriveBulkUpdateRequestItemFromDriver(driver2)
			driver3UpdateRequest := prepareDriveBulkUpdateRequestItemFromDriver(driver3)

			if test.shouldRefetch {
				// assert that MVR report is fetched
				matcher := MVRRequestMatcher{expected: &data_fetching.MVRReportRequestV1{
					DlNumber:  driver1.DriverLicenseNumber,
					UsState:   driver1.UsState,
					FirstName: *driver1UpdateRequest.FirstName,
					LastName:  *driver1UpdateRequest.LastName,
					Dob:       timestamppb.New(driver1UpdateRequest.DateOfBirth.Time),
				}}

				// verify that MVR is fetched
				mockFetcherClient.EXPECT().
					GetMVRReportV1(gomock.Any(), matcher).
					Return(pointer_utils.ToPointer(getTestMVRReport(driver1.DriverLicenseNumber)), nil).
					Times(1)
			}

			driversUpdateRequest := []oapi_uw.ApplicationReviewDriversListFormItem{
				driver1UpdateRequest,
				driver2UpdateRequest,
				driver3UpdateRequest,
			}
			err = env.DriverList.Set(ctx, appReview.Id, oapi_uw.ApplicationReviewDriversListForm{
				Data: &oapi_uw.ApplicationReviewDriversListFormData{
					Drivers: driversUpdateRequest,
				},
			})
			require.NoError(t, err)
		})
	}
}

func TestMVRFetchWhenDriverIsCreated(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		AppReviewFixture *application_review_fixture.ApplicationReviewsFixture
		DriverList       List
		AppReviewWrapper uw_wrapper.ApplicationReviewWrapper
	}

	testApp, mockFetcherClient := setupMocksForMVRAndLoadEnv(t, &env)
	defer testApp.RequireStop()

	appReview := env.AppReviewFixture.ApplicationReview

	tests := []struct {
		name          string
		initData      func()
		newDriver     uw_wrapper.DriverListRecord
		shouldRefetch bool
	}{
		{
			name: "should fetch if MVR pull is enabled",
			initData: func() {
				err := enableMVRPull(ctx, appReview.Id, env.AppReviewWrapper)
				require.NoError(t, err)
			},
			newDriver: uw_wrapper.DriverListRecord{
				DriverListRecord: getRandomDriver(),
				IsCreatedByUW:    null.BoolFrom(true),
			},
			shouldRefetch: true,
		},
		{
			name: "should not fetch if MVR pull is disabled",
			initData: func() {
				err := disableMVRPull(ctx, appReview.Id, env.AppReviewWrapper)
				require.NoError(t, err)
			},
			newDriver: uw_wrapper.DriverListRecord{
				DriverListRecord: getRandomDriver(),
				IsCreatedByUW:    null.BoolFrom(true),
			},
			shouldRefetch: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			test.initData()
			newDriver := test.newDriver
			if test.shouldRefetch {
				// assert that MVR report is fetched
				matcher := MVRRequestMatcher{expected: &data_fetching.MVRReportRequestV1{
					DlNumber:  newDriver.DriverLicenseNumber,
					UsState:   newDriver.UsState,
					FirstName: newDriver.FirstName.String,
					LastName:  newDriver.LastName.String,
					Dob:       timestamppb.New(newDriver.DateOfBirth.Time),
				}}
				mockFetcherClient.EXPECT().
					GetMVRReportV1(gomock.Any(), matcher).
					Return(pointer_utils.ToPointer(getTestMVRReport(newDriver.DriverLicenseNumber)), nil).
					Times(1)
			}

			err := env.DriverList.CreateDriver(ctx, appReview.Id, newDriver)
			require.NoError(t, err)
		})
	}
}

func TestProblemsWhenMVRIsRefetched(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		AppReviewFixture *application_review_fixture.ApplicationReviewsFixture
		DriverList       List
		AppReviewWrapper uw_wrapper.ApplicationReviewWrapper
		AppWrapper       application.DataWrapper
	}

	testApp, mockFetcherServer := setupMocksForMVRAndLoadEnv(t, &env)
	defer testApp.RequireStop()

	// init data
	appReview := env.AppReviewFixture.ApplicationReview
	err := enableMVRPull(ctx, appReview.Id, env.AppReviewWrapper)
	require.NoError(t, err)
	driver := appReview.Submission.DriversInfo.Drivers[0]
	// remove problems from app
	err = env.AppWrapper.UpdateApp(ctx, appReview.ApplicationID, func(a application.Application) (application.Application, error) {
		a.Problems = nil
		return a, nil
	})

	// mock MVR fetcher to return problem for driver
	mockFetcherServer.EXPECT().
		GetMVRReportV1(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("incorrect driver details")).
		Times(1)
	// try fetching MVR, this should update problem in the app
	err = env.DriverList.tryBulkFetchingMVRReportAndUpdateProblems(ctx, appReview.Id, []application.DriverListRecord{driver})
	require.NoError(t, err)
	// verify problem is added in application
	updatedApp, err := env.AppWrapper.GetAppById(ctx, appReview.ApplicationID)
	require.NoError(t, err)
	problem := updatedApp.Problems.Get(mvr_fetching.MVRProblemTag, mvr_fetching.DriverId(driver.UsState, driver.DriverLicenseNumber))
	require.NotNil(t, problem)
	mvrProblem, ok := problem.(*mvr_fetching.MVRProblem)
	require.True(t, ok)
	require.Regexp(t, "incorrect driver details", mvrProblem.Error)

	// mock MVR fetcher to return problem for driver again (this verifies that latest problem is updated in DB if fetching fails again)
	mockFetcherServer.EXPECT().
		GetMVRReportV1(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("some intermittent issue")).
		Times(1)
	// try fetching MVR, this should update problem in the app
	err = env.DriverList.tryBulkFetchingMVRReportAndUpdateProblems(ctx, appReview.Id, []application.DriverListRecord{driver})
	require.NoError(t, err)
	// verify problem is added in application
	updatedApp, err = env.AppWrapper.GetAppById(ctx, appReview.ApplicationID)
	require.NoError(t, err)
	problem = updatedApp.Problems.Get(mvr_fetching.MVRProblemTag, mvr_fetching.DriverId(driver.UsState, driver.DriverLicenseNumber))
	require.NotNil(t, problem)
	mvrProblem, ok = problem.(*mvr_fetching.MVRProblem)
	require.True(t, ok)
	require.Regexp(t, "some intermittent issue", mvrProblem.Error)

	// now mock MVR fetcher to successfully return MVR report
	mockFetcherServer.EXPECT().
		GetMVRReportV1(gomock.Any(), gomock.Any()).
		Return(pointer_utils.ToPointer(getTestMVRReport(driver.DriverLicenseNumber)), nil).
		Times(1)
	// try fetching MVR, this should remove problem from the app
	err = env.DriverList.tryBulkFetchingMVRReportAndUpdateProblems(ctx, appReview.Id, []application.DriverListRecord{driver})
	require.NoError(t, err)
	// verify problem is removed from application
	updatedApp, err = env.AppWrapper.GetAppById(ctx, appReview.ApplicationID)
	require.NoError(t, err)
	problem = updatedApp.Problems.Get(mvr_fetching.MVRProblemTag, mvr_fetching.DriverId(driver.UsState, driver.DriverLicenseNumber))
	require.Nil(t, problem)
}

func TestShouldBeAbleToDeleteDriverAddedLaterInSubmission(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		AppReviewFixture *application_review_fixture.ApplicationReviewsFixture
		DriverList       List
		AppReviewWrapper uw_wrapper.ApplicationReviewWrapper
		AppWrapper       application.DataWrapper
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	appReview := env.AppReviewFixture.ApplicationReview

	// drivers
	driver1 := getRandomDriver()
	driver2 := getRandomDriver()
	driver3 := getRandomDriver()
	// update drivers in submission
	err := env.AppWrapper.UpdateSub(ctx, appReview.Submission.ID, func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.DriversInfo.Drivers = []application.DriverListRecord{driver1, driver2, driver3}
		return sub, nil
	})
	require.NoError(t, err)

	// update overrides in app review, this means that underwriter made some changes
	err = env.AppReviewWrapper.UpdateAppReview(ctx, appReview.Id, func(r uw_wrapper.ApplicationReview) (uw_wrapper.ApplicationReview, error) {
		r.Overrides.DriverList = &[]*uw_wrapper.DriverListRecord{
			{DriverListRecord: driver1},
			{DriverListRecord: driver2},
			{DriverListRecord: driver3},
		}
		return r, nil
	})
	require.NoError(t, err)

	// now add a new driver in submission (this can happen if agent resubmits the application with a new driver)
	driver4 := getRandomDriver()
	// update drivers in submission
	err = env.AppWrapper.UpdateSub(ctx, appReview.Submission.ID, func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.DriversInfo.Drivers = []application.DriverListRecord{driver1, driver2, driver3, driver4}
		return sub, nil
	})
	require.NoError(t, err)

	// now try deleting the newly added driver
	err = env.DriverList.UpdateDriver(ctx, UpdateDriverRequest{
		ApplicationReviewId: appReview.Id,
		DlNumber:            driver4.DriverLicenseNumber,
		USStateCode:         driver4.UsState,

		IsDeletedByUW: pointer_utils.Bool(true),
	})
	require.NoError(t, err)

	// get driver list, driver-4 should be returned and it should be marked as deleted
	resp, err := env.DriverList.Get(ctx, appReview.Id)
	require.NoError(t, err)
	require.Len(t, resp.Drivers, 4)

	filteredDrivers := slice_utils.Filter(resp.Drivers, func(d oapi_uw.ApplicationReviewDriversListItem) bool {
		return d.DlNumber == driver4.DriverLicenseNumber
	})
	require.Equal(t, 1, len(filteredDrivers))
	require.True(t, filteredDrivers[0].IsDeletedByUW)
}

func prepareDriveBulkUpdateRequestItemFromDriver(driver application.DriverListRecord) oapi_uw.ApplicationReviewDriversListFormItem {
	requestItem := oapi_uw.ApplicationReviewDriversListFormItem{
		DlNumber: driver.DriverLicenseNumber,
		UsState:  driver.UsState,
		DateHired: openapi_types.Date{
			Time: driver.DateHired,
		},
		FirstName: driver.FirstName.Ptr(),
		LastName:  driver.LastName.Ptr(),
	}
	if driver.DateOfBirth.Valid {
		requestItem.DateOfBirth = &openapi_types.Date{
			Time: driver.DateOfBirth.Time,
		}
	}
	return requestItem
}

func TestGetSummary(t *testing.T) {
	now := time.Date(2025, 7, 1, 0, 0, 0, 0, time.UTC)

	makeDate := func(t time.Time) openapi_types.Date {
		return openapi_types.Date{Time: t}
	}

	drivers := []oapi_uw.ApplicationReviewDriversListItem{
		{
			DateHired:       makeDate(now.AddDate(-2, 0, 0)), // 2 years ago
			IsDeletedByUW:   false,
			IsMexicanDriver: true,
			UsState:         "MX",
		},
		{
			DateHired:         makeDate(now.AddDate(-3, 0, 0)),
			DateHiredOverride: &openapi_types.Date{Time: now.AddDate(-1, -6, 0)}, // 1.5 years ago (override)
			IsDeletedByUW:     false,
			IsMexicanDriver:   false,
			UsState:           "TX",
		},
		{
			DateHired:       makeDate(now.AddDate(-6, 0, 0)),
			IsDeletedByUW:   true, // should be filtered out
			IsMexicanDriver: true,
			UsState:         "CA",
		},
	}

	summary := getSummary(drivers, us_states.TX, now)

	require.Equal(t, int32(2), summary.NumberOfDrivers)

	// Tenure: [2y, 1.5y] in seconds
	firstDate := now.AddDate(-2, 0, 0)
	secondDate := now.AddDate(-1, -6, 0)

	expectedAvgTenure := float32((now.Sub(firstDate).Seconds() + now.Sub(secondDate).Seconds()) / 2)
	require.InDelta(t, expectedAvgTenure, summary.AverageTenure, 1.0)

	// Turnover: only the 1.5 year driver is within the 1 year window (so 0)
	require.Equal(t, float32(0), summary.TenureTurnover)

	// 1 of 2 is Mexican
	require.NotNil(t, summary.PercentageOfB1Drivers)
	require.InDelta(t, 50.0, *summary.PercentageOfB1Drivers, 0.01)
}
