package safety

import (
	"context"

	"github.com/cockroachdb/errors"
	"go.uber.org/fx"

	uw_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	"nirvanatech.com/nirvana/underwriting"
)

type DotRating struct {
	fx.In

	Deps underwriting.ReviewManagerDeps
}

func (d *DotRating) Get(ctx context.Context, reviewId string) (*oapi_uw.ApplicationReviewSafetyDotRating, error) {
	reviewObj, err := d.Deps.ApplicationReviewWrapper.GetReview(ctx, reviewId)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get review %s", reviewId)
	}

	rating, ratingDate, err := d.Deps.UWSafetyFetcher.DOTRating(ctx, reviewObj.Submission.CompanyInfo.DOTNumber, true)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get dot rating for %d", reviewObj.Submission.CompanyInfo.DOTNumber)
	}
	r := oapi_uw.ApplicationReviewSafetyDotRatingSafetyRating(*rating)
	res := oapi_uw.ApplicationReviewSafetyDotRating{
		Meta: &oapi_uw.ApplicationReviewWidgetMeta{
			Merit:  &reviewObj.ReviewInfo.Safety.DotRating.Merit,
			Credit: &reviewObj.ReviewInfo.Safety.DotRating.Credit,
		},
		EffectiveDate: ratingDate,
		SafetyRating:  &r,
	}
	if reviewObj.CoverageReview.AutoLiability != nil {
		res.Meta.AutoLiability = &oapi_uw.ApplicationReviewWidgetCoverageMeta{
			Credit: &reviewObj.CoverageReview.AutoLiability.ReviewInfo.Safety.DotRating.Credit,
		}
	}
	if reviewObj.CoverageReview.AutoPhysicalDamage != nil {
		res.Meta.AutoPhysicalDamage = &oapi_uw.ApplicationReviewWidgetCoverageMeta{
			Credit: &reviewObj.CoverageReview.AutoPhysicalDamage.ReviewInfo.Safety.DotRating.Credit,
		}
	}
	if reviewObj.CoverageReview.GeneralLiability != nil {
		res.Meta.GeneralLiability = &oapi_uw.ApplicationReviewWidgetCoverageMeta{
			Credit: &reviewObj.CoverageReview.GeneralLiability.ReviewInfo.Safety.DotRating.Credit,
		}
	}
	if reviewObj.CoverageReview.MotorTruckCargo != nil {
		res.Meta.MotorTruckCargo = &oapi_uw.ApplicationReviewWidgetCoverageMeta{
			Credit: &reviewObj.CoverageReview.MotorTruckCargo.ReviewInfo.Safety.DotRating.Credit,
		}
	}

	return &res, nil
}

func (d *DotRating) Set(
	reviewId oapi_uw.ApplicationReviewID,
	form oapi_uw.ApplicationReviewSafetyDotRatingForm,
) error {
	return d.updateMetadata(string(reviewId), form.Meta)
}

func (d *DotRating) updateMetadata(reviewID string, meta *oapi_uw.ApplicationReviewWidgetMeta) error {
	if meta != nil {
		err := d.Deps.ApplicationReviewWrapper.UpdateAppReview(context.TODO(),
			reviewID,
			func(input uw_wrapper.ApplicationReview) (uw_wrapper.ApplicationReview, error) {
				if meta.Merit != nil {
					input.ReviewInfo.Safety.DotRating.Merit = *meta.Merit
				}
				if meta.Credit != nil {
					input.ReviewInfo.Safety.DotRating.Credit = *meta.Credit
					input.CoverageReview.AutoLiability.ReviewInfo.Safety.DotRating.Credit = *meta.Credit
				}
				if meta.AutoLiability != nil && meta.AutoLiability.Credit != nil && input.CoverageReview.AutoLiability != nil {
					input.CoverageReview.AutoLiability.ReviewInfo.Safety.DotRating.Credit = *meta.AutoLiability.Credit
				}
				if meta.AutoPhysicalDamage != nil && meta.AutoPhysicalDamage.Credit != nil && input.CoverageReview.AutoPhysicalDamage != nil {
					input.CoverageReview.AutoPhysicalDamage.ReviewInfo.Safety.DotRating.Credit = *meta.AutoPhysicalDamage.Credit
				}
				if meta.GeneralLiability != nil && meta.GeneralLiability.Credit != nil && input.CoverageReview.GeneralLiability != nil {
					input.CoverageReview.GeneralLiability.ReviewInfo.Safety.DotRating.Credit = *meta.GeneralLiability.Credit
				}
				if meta.MotorTruckCargo != nil && meta.MotorTruckCargo.Credit != nil && input.CoverageReview.MotorTruckCargo != nil {
					input.CoverageReview.MotorTruckCargo.ReviewInfo.Safety.DotRating.Credit = *meta.MotorTruckCargo.Credit
				}
				input.ReviewInfo = uw_wrapper.UpdateAggregateMeritAndCredit(input.ReviewInfo)
				input.CoverageReview = uw_wrapper.UpdateAggregateMeritAndCreditPerCoverage(input.CoverageReview,
					input.PricingExperimentsInfo)
				return input, nil
			},
		)
		if err != nil {
			return errors.Wrapf(err, "unable to update review info for review %s", reviewID)
		}
	}
	return nil
}
