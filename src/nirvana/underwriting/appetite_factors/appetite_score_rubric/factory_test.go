package appetite_score_rubric

import (
	"context"
	"testing"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw/appetite_factors"

	"github.com/stretchr/testify/require"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"

	"github.com/stretchr/testify/assert"
	"go.uber.org/fx"
	rubric "nirvanatech.com/nirvana/common-go/application-util/appetite_factors/recommended_action_rubric"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/appetite_factor"
)

func TestResolveRecommendedActionLogic(t *testing.T) {
	var env struct {
		fx.In

		ResolverFactory LogicResolverFactory
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	tests := []struct {
		name    string
		version appetite_factors.Version
		kind    AppetiteScoreLogicKind
		wantErr bool
		errMsg  string
	}{
		{
			name:    "test successful logic retrieval for v1.0.0",
			version: appetite_factors.Version{Major: 1, Minor: 0, Patch: 0},
			wantErr: false,
		},
		{
			name:    "test successful logic retrieval for v2.0.0",
			version: appetite_factors.Version{Major: 2, Minor: 0, Patch: 0},
			wantErr: false,
		},
		{
			name:    "test successful logic retrieval for v3.0.0",
			version: appetite_factors.Version{Major: 3, Minor: 0, Patch: 0},
			wantErr: false,
		},
		{
			name:    "test successful logic retrieval for v4.0.0",
			version: appetite_factors.Version{Major: 4, Minor: 0, Patch: 0},
			wantErr: false,
		},
		{
			name:    "test successful logic retrieval for v5.0.0",
			version: appetite_factors.Version{Major: 5, Minor: 0, Patch: 0},
			wantErr: false,
		},
		{
			name:    "test successful logic retrieval for v6.0.0",
			version: appetite_factors.Version{Major: 6, Minor: 0, Patch: 0},
			wantErr: false,
		},
		{
			name:    "test successful logic retrieval for v7.0.0",
			version: appetite_factors.Version{Major: 7, Minor: 0, Patch: 0},
			wantErr: false,
		},
		{
			name:    "test successful logic retrieval for v8.0.0",
			version: appetite_factors.Version{Major: 8, Minor: 0, Patch: 0},
			wantErr: false,
		},
		{
			name:    "test successful logic retrieval for v9.0.0",
			version: appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			wantErr: false,
		},
		{
			name:    "test unsupported version",
			version: appetite_factors.Version{Major: 10, Minor: 0, Patch: 0},
			wantErr: true,
			errMsg:  "implementations for version 10.0.0 not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resolver, err := env.ResolverFactory.GetLogicResolver(enums.ProgramTypeFleet)
			require.NoError(t, err)
			calcFunc, err := resolver.ResolveRecommendedActionLogic(tt.version)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.errMsg, err.Error())
			} else {
				assert.NoError(t, err)
				action, _, _, tErr := calcFunc.CalculateRecommendedAction(
					context.TODO(),
					rubric.CalculateRecommendationActionRequest{
						AppetiteScore:  appetite_factor.AppetiteScoreHighRisk,
						MarketCategory: pointer_utils.ToPointer("Extended"),
					})
				assert.NoError(t, tErr)
				assert.Equal(t, appetite_factor.RecommendedActionDecline, action)
			}
		})
	}
}

func TestGetLatestVersion(t *testing.T) {
	tests := []struct {
		name     string
		registry map[appetite_factors.Version]map[AppetiteScoreLogicKind]interface{}
		want     appetite_factors.Version
	}{
		{
			name: "test get latest version",
			registry: map[appetite_factors.Version]map[AppetiteScoreLogicKind]interface{}{
				{Major: 1, Minor: 0, Patch: 0}: {AppetiteScoreLogicKindRecommendedAction: nil},
				{Major: 2, Minor: 0, Patch: 0}: {AppetiteScoreLogicKindRecommendedAction: nil},
				{Major: 3, Minor: 1, Patch: 0}: {AppetiteScoreLogicKindRecommendedAction: nil},
				{Major: 4, Minor: 1, Patch: 0}: {AppetiteScoreLogicKindRecommendedAction: nil},
			},
			want: appetite_factors.Version{Major: 4, Minor: 1, Patch: 0},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resolver := &LogicResolverImpl{
				logicRegistry: tt.registry,
			}
			got := resolver.GetLatestVersion()
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestGetActualLatestVersion(t *testing.T) {
	var env struct {
		fx.In

		ResolverFactory LogicResolverFactory
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	resolver, err := env.ResolverFactory.GetLogicResolver(enums.ProgramTypeFleet)
	assert.NoError(t, err)
	latestVersion := resolver.GetLatestVersion()
	assert.Equal(t, appetite_factors.Version{Major: 9, Minor: 0, Patch: 0}, latestVersion)
}

func TestResolveAppetiteFactorsAndScoreLogic(t *testing.T) {
	var env struct {
		fx.In

		ResolverFactory LogicResolverFactory
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	tests := []struct {
		name             string
		version          appetite_factors.Version
		expectError      bool
		expectedImplType interface{}
	}{
		{
			name:             "version 9.0.0 uses DS Calculator",
			version:          appetite_factors.Version{Major: 9, Minor: 0, Patch: 0},
			expectError:      false,
			expectedImplType: &AppetiteFactorsDataScienceCalculatorV1{},
		},
		{
			name:             "version 8.0.0 uses DS Calculator",
			version:          appetite_factors.Version{Major: 8, Minor: 0, Patch: 0},
			expectError:      false,
			expectedImplType: &AppetiteFactorsDataScienceCalculatorV1{},
		},
		{
			name:             "version 7.0.0 uses DS Calculator",
			version:          appetite_factors.Version{Major: 7, Minor: 0, Patch: 0},
			expectError:      false,
			expectedImplType: &AppetiteFactorsDataScienceCalculatorV1{},
		},
		{
			name:             "version 6.0.0 uses Rule Engine Calculator",
			version:          appetite_factors.Version{Major: 6, Minor: 0, Patch: 0},
			expectError:      false,
			expectedImplType: &AppetiteFactorsRuleEngineCalculator{},
		},
		{
			name:             "unsupported version returns error",
			version:          appetite_factors.Version{Major: 10, Minor: 0, Patch: 0},
			expectError:      true,
			expectedImplType: nil,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			resolver, err := env.ResolverFactory.GetLogicResolver(enums.ProgramTypeFleet)
			require.NoError(t, err)

			calculator, err := resolver.ResolveAppetiteFactorsAndScoreLogic(tc.version)

			if tc.expectError {
				assert.Error(t, err)
				assert.Nil(t, calculator)
			} else {
				require.NoError(t, err)
				require.NotNil(t, calculator)
				assert.IsType(t, tc.expectedImplType, calculator)
			}
		})
	}
}

func TestLogicResolver_GetPenultimateMajorVersion(t *testing.T) {
	registry := map[appetite_factors.Version]map[AppetiteScoreLogicKind]interface{}{
		{Major: 5, Minor: 0, Patch: 0}: {},
		{Major: 6, Minor: 0, Patch: 0}: {},
		{Major: 7, Minor: 0, Patch: 0}: {},
		{Major: 8, Minor: 0, Patch: 0}: {},
		{Major: 9, Minor: 0, Patch: 0}: {},
	}

	resolver := &LogicResolverImpl{
		logicRegistry: registry,
	}

	penultimateVersion := resolver.GetPenultimateMajorVersion()
	expectedPenultimateVersion := appetite_factors.Version{Major: 8, Minor: 0, Patch: 0}

	assert.Equal(t, expectedPenultimateVersion, penultimateVersion)
}
