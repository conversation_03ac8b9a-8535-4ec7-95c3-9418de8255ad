package appetite_score_rubric

import (
	"github.com/cockroachdb/errors"

	"go.uber.org/fx"

	rubric "nirvanatech.com/nirvana/common-go/application-util/appetite_factors/recommended_action_rubric"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw/appetite_factors"
)

// LogicResolver defines an interface for resolving logic functions based on version and kind
type LogicResolver interface {
	ResolveRecommendedActionLogic(version appetite_factors.Version) (rubric.RecommendedActionCalculator, error)
	ResolveAppetiteScoreLogic(version appetite_factors.Version) (AppetiteScoreCalculator, error)
	ResolveAppetiteFactorsAndScoreLogic(version appetite_factors.Version) (AppetiteFactorsAndScoreCalculator, error)
	GetLatestVersion() appetite_factors.Version
	GetPenultimateMajorVersion() appetite_factors.Version
}

// LogicResolverImpl implements LogicResolver interface
type LogicResolverImpl struct {
	logicRegistry map[appetite_factors.Version]map[AppetiteScoreLogicKind]interface{}
	latestVersion appetite_factors.Version
}

type LogicResolverParams struct {
	fx.In

	DSCalculatorV1 *AppetiteFactorsDataScienceCalculatorV1
	DSCalculatorV2 *AppetiteFactorsDataScienceCalculatorV2
}

// NewLogicResolver creates a new instance of LogicResolver
//  1. We envision one overall version for the entire appetite score use-case (sem versioning here - major.minor.patch)
//  2. Appetite score use-case has multiple, mutually exclusive logical steps (called logic kind here),
//     each step taking it's input from the previous step's output
//  3. Each logical step is versioned independently, using major versioning only (e.g. V1, V2)
//
// In case of Appetite score use-case version semantics, major will be primarily used for any iteration as it signifies
// backward incompatible changes. Minor and patch versions are not sporadically as they're meant for
// backward compatible changes and bug fixes respectively.
//
// Illustration (L = Logical step, V = Version):
// Appetite Score V1.0.0 - {L1 v1, L2 v1, L3 v1, L4 v1}
// Appetite Score V2.0.0 - {L1 v1, L2 v1, L3 v1, L4 v2}
// Appetite Score V3.0.0 - {L1 v2, L2 v1, L3 v1, L4 v2}
func NewLogicResolver(params LogicResolverParams) LogicResolver {
	// Initialize logic registry with predefined versions and logic functions
	registry := make(map[appetite_factors.Version]map[AppetiteScoreLogicKind]interface{})
	// Appetite Score V1.0.0
	//nolint:exhaustive
	registry[appetite_factors.Version{Major: 1, Minor: 0, Patch: 0}] = map[AppetiteScoreLogicKind]interface{}{
		AppetiteScoreLogicKindRecommendedAction:       &rubric.RecommendedActionCalculatorV1{},
		AppetiteScoreLogicKindAppetiteFactorsAndScore: &AppetiteFactorsRuleEngineCalculator{},
	}
	// Appetite Score V2.0.0
	//nolint:exhaustive
	registry[appetite_factors.Version{Major: 2, Minor: 0, Patch: 0}] = map[AppetiteScoreLogicKind]interface{}{
		AppetiteScoreLogicKindRecommendedAction:       &rubric.RecommendedActionCalculatorV2{},
		AppetiteScoreLogicKindAppetiteFactorsAndScore: &AppetiteFactorsRuleEngineCalculator{},
	}
	// Appetite Score V3.0.0
	//nolint:exhaustive
	registry[appetite_factors.Version{Major: 3, Minor: 0, Patch: 0}] = map[AppetiteScoreLogicKind]interface{}{
		AppetiteScoreLogicKindRecommendedAction:       &rubric.RecommendedActionCalculatorV3{},
		AppetiteScoreLogicKindAppetiteFactorsAndScore: &AppetiteFactorsRuleEngineCalculator{},
	}

	// Appetite Score V4.0.0
	//nolint:exhaustive
	registry[appetite_factors.Version{Major: 4, Minor: 0, Patch: 0}] = map[AppetiteScoreLogicKind]interface{}{
		AppetiteScoreLogicKindRecommendedAction:       &rubric.RecommendedActionCalculatorV4{},
		AppetiteScoreLogicKindAppetiteFactorsAndScore: &AppetiteFactorsRuleEngineCalculator{},
	}

	// Appetite Score V5.0.0
	//nolint:exhaustive
	registry[appetite_factors.Version{Major: 5, Minor: 0, Patch: 0}] = map[AppetiteScoreLogicKind]interface{}{
		AppetiteScoreLogicKindRecommendedAction:       &rubric.RecommendedActionCalculatorV5{},
		AppetiteScoreLogicKindAppetiteFactorsAndScore: &AppetiteFactorsRuleEngineCalculator{},
	}

	// Appetite Score V6.0.0
	//nolint:exhaustive
	registry[appetite_factors.Version{Major: 6, Minor: 0, Patch: 0}] = map[AppetiteScoreLogicKind]interface{}{
		AppetiteScoreLogicKindRecommendedAction:       &rubric.RecommendedActionCalculatorV6{},
		AppetiteScoreLogicKindAppetiteFactorsAndScore: &AppetiteFactorsRuleEngineCalculator{},
	}

	// Appetite Score V7.0.0
	//nolint:exhaustive
	registry[appetite_factors.Version{Major: 7, Minor: 0, Patch: 0}] = map[AppetiteScoreLogicKind]interface{}{
		AppetiteScoreLogicKindRecommendedAction:       &rubric.RecommendedActionCalculatorV6{},
		AppetiteScoreLogicKindAppetiteFactorsAndScore: params.DSCalculatorV1,
	}

	//// Appetite Score V8.0.0
	//nolint:exhaustive
	registry[appetite_factors.Version{Major: 8, Minor: 0, Patch: 0}] = map[AppetiteScoreLogicKind]interface{}{
		AppetiteScoreLogicKindRecommendedAction:       &rubric.RecommendedActionCalculatorV7{},
		AppetiteScoreLogicKindAppetiteFactorsAndScore: params.DSCalculatorV1,
	}

	//// Appetite Score V9.0.0
	//nolint:exhaustive
	registry[appetite_factors.Version{Major: 9, Minor: 0, Patch: 0}] = map[AppetiteScoreLogicKind]interface{}{
		AppetiteScoreLogicKindRecommendedAction:       &rubric.RecommendedActionCalculatorV8{},
		AppetiteScoreLogicKindAppetiteFactorsAndScore: params.DSCalculatorV1,
	}

	//// Appetite Score V8.0.0
	//// Version 8 uses the new data science calculator (DSCalculatorV2) for the appetite factors and score calculation
	//// This version uses 4 major features that are part of the input:
	//// 1. DOT Rating
	//// 2. ALLossFrequency
	//// 3. DriverTurnoverPercentage
	//// 4. Basic Alerts
	//// The model does not allow for any of these features to be null. If any of them are null or inconclusive, we must not use this version.
	//// nolint:exhaustive
	//registry[appetite_factors.Version{Major: 8, Minor: 0, Patch: 0}] = map[AppetiteScoreLogicKind]interface{}{
	//	AppetiteScoreLogicKindRecommendedAction:       &rubric.RecommendedActionCalculatorV6{},
	//	AppetiteScoreLogicKindAppetiteFactorsAndScore: params.DSCalculatorV2,
	//}

	return &LogicResolverImpl{
		logicRegistry: registry,
	}
}

func NewNonFleetLogicResolver() LogicResolver {
	// Initialize logic registry with predefined versions and logic functions
	registry := make(map[appetite_factors.Version]map[AppetiteScoreLogicKind]interface{})
	// Appetite Score V1.0.0
	//nolint:exhaustive
	registry[appetite_factors.Version{Major: 1, Minor: 0, Patch: 0}] = map[AppetiteScoreLogicKind]interface{}{
		AppetiteScoreLogicKindAppetiteScore: &NonFleetAppetiteScoreCalculatorV1{},
	}

	return &LogicResolverImpl{
		logicRegistry: registry,
	}
}

// ResolveRecommendedActionLogic resolves and returns the recommended action logic function for a given version
func (lr *LogicResolverImpl) ResolveRecommendedActionLogic(version appetite_factors.Version) (rubric.RecommendedActionCalculator, error) {
	if logicMap, ok := lr.logicRegistry[version]; ok {
		if impl, ok := logicMap[AppetiteScoreLogicKindRecommendedAction]; ok {
			if calculator, ok := impl.(rubric.RecommendedActionCalculator); ok {
				return calculator, nil
			}
			return nil, errors.New("implementation does not conform to RecommendedActionCalculator interface")
		}
		return nil, errors.Newf("implementation for kind %d not found", AppetiteScoreLogicKindRecommendedAction)
	}
	return nil, errors.Newf("implementations for version %v not found", version)
}

func (lr *LogicResolverImpl) ResolveAppetiteScoreLogic(version appetite_factors.Version) (AppetiteScoreCalculator, error) {
	if logicMap, ok := lr.logicRegistry[version]; ok {
		if impl, ok := logicMap[AppetiteScoreLogicKindAppetiteScore]; ok {
			if calculator, ok := impl.(AppetiteScoreCalculator); ok {
				return calculator, nil
			}
			return nil, errors.New("implementation does not conform to AppetiteScoreCalculator interface")
		}
		return nil, errors.Newf("implementation for kind %d not found", AppetiteScoreLogicKindAppetiteScore)
	}
	return nil, errors.Newf("implementations for version %v not found", version)
}

func (lr *LogicResolverImpl) ResolveAppetiteFactorsAndScoreLogic(version appetite_factors.Version) (
	AppetiteFactorsAndScoreCalculator, error,
) {
	if logicMap, ok := lr.logicRegistry[version]; ok {
		if impl, ok := logicMap[AppetiteScoreLogicKindAppetiteFactorsAndScore]; ok {
			if calculator, ok := impl.(AppetiteFactorsAndScoreCalculator); ok {
				return calculator, nil
			}
			return nil, errors.New("implementation does not conform to AppetiteFactorsAndScoreCalculator interface")
		}
		return nil, errors.Newf("implementation for kind %d not found", AppetiteScoreLogicKindAppetiteFactorsAndScore)
	}
	return nil, errors.Newf("implementations for version %v not found", version)
}

// GetLatestVersion iterates over the keys in the logicRegistry map and returns the highest version
func (lr *LogicResolverImpl) GetLatestVersion() appetite_factors.Version {
	zeroValueVersion := appetite_factors.Version{}
	if lr.latestVersion != zeroValueVersion {
		return lr.latestVersion
	}

	var latestVersion appetite_factors.Version
	for version := range lr.logicRegistry {
		if version.Major > latestVersion.Major {
			latestVersion = version
		} else if version.Major == latestVersion.Major {
			if version.Minor > latestVersion.Minor {
				latestVersion = version
			} else if version.Minor == latestVersion.Minor {
				if version.Patch > latestVersion.Patch {
					latestVersion = version
				}
			}
		}
	}
	lr.latestVersion = latestVersion
	return latestVersion
}

// GetPenultimateMajorVersion returns the penultimate major version (however minor & patch are 0)
func (lr *LogicResolverImpl) GetPenultimateMajorVersion() appetite_factors.Version {
	latestVersion := lr.GetLatestVersion()
	if latestVersion.Major <= 1 {
		// No penultimate version for V1
		return latestVersion
	}
	penultimateVersion := appetite_factors.Version{Major: latestVersion.Major - 1, Minor: 0, Patch: 0}
	return penultimateVersion
}

var _ LogicResolver = &LogicResolverImpl{}
