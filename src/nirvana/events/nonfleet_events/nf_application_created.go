package nonfleet_events

import (
	"context"
	"time"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/operations"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"gopkg.in/segmentio/analytics-go.v3"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/nonfleet/state-machine/enums"
)

type NFApplicationCreated struct {
	baseEvent
	Dot                          *int64
	CompanyName                  *string
	USState                      *string
	CreatedByID                  *uuid.UUID
	CreatedByName                *string
	CreatedByEmail               *string
	AgencyName                   *string
	EffectiveDate                *time.Time
	UnderwriterID                *uuid.UUID
	UnderwriterName              *string
	IncumbentCarrier             *string
	ProducerId                   *uuid.UUID
	ProducerName                 *string
	ProducerEmail                *string
	ActiveOrPendingInsurance     *data_fetching.InsuranceRecordV1
	MarketerId                   *uuid.UUID
	MarketerName                 *string
	MarketerEmail                *string
	FilingType                   *string
	RenewalOriginalApplicationId *uuid.UUID
}

func (N NFApplicationCreated) Upload(
	ctx context.Context, deps events.EventDeps,
) error {
	return N.baseEvent.Upload(ctx, deps, N.Name(), N.GetProperties())
}

func (N NFApplicationCreated) Name() string {
	return enums.NFApplicationCreated.String()
}

func (N NFApplicationCreated) UserVisibleName() string {
	return N.Name()
}

func (N NFApplicationCreated) GetProperties() analytics.Properties {
	baseProperties := N.baseEvent.Properties()
	baseProperties.Set("Dot", N.Dot).
		Set("CompanyName", N.CompanyName).
		Set("USState", N.USState).
		Set("CreatedByID", N.CreatedByID).
		Set("CreatedByName", N.CreatedByName).
		Set("CreatedByEmail", N.CreatedByEmail).
		Set("AgencyName", N.AgencyName).
		Set("EffectiveDate", N.EffectiveDate).
		Set("UnderwriterID", N.UnderwriterID).
		Set("UnderwriterName", N.UnderwriterName).
		Set("IncumbentCarrier", N.IncumbentCarrier).
		Set("ProducerId", N.ProducerId).
		Set("ProducerName", N.ProducerName).
		Set("ProducerEmail", N.ProducerEmail).
		Set("MarketerId", N.MarketerId).
		Set("MarketerName", N.MarketerName).
		Set("MarketerEmail", N.MarketerEmail).
		Set("FilingType", N.FilingType).
		Set("RenewalOriginalApplicationId", N.RenewalOriginalApplicationId)

	if N.ActiveOrPendingInsurance != nil {
		if N.ActiveOrPendingInsurance.EffectiveDate.IsValid() {
			baseProperties.
				Set("ActiveOrPendingInsuranceEffectiveDate",
					N.ActiveOrPendingInsurance.EffectiveDate.AsTime())
		}
		if N.ActiveOrPendingInsurance.CancelEffectiveDate.IsValid() {
			baseProperties.
				Set("ActiveOrPendingInsuranceCancellationDate",
					N.ActiveOrPendingInsurance.CancelEffectiveDate.AsTime())
		}
		baseProperties.
			Set("ActiveOrPendingInsuranceInsuranceCarrier",
				N.ActiveOrPendingInsurance.InsuranceCompanyName)
	}

	return baseProperties
}

func NewNFApplicationCreated[T application.AppInfo](
	ctx context.Context,
	app *application.Application[T],
	authWrapper auth.DataWrapper,
	agency agency.DataWrapper,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) (events.Event, error) {
	baseEvent, err := createBaseEventFromApplication[T](ctx, app, authWrapper)
	if err != nil {
		return nil, errors.Wrapf(err, "Failed to create the baseEvent")
	}
	companyName := app.Info.GetCompanyName()
	retval := &NFApplicationCreated{
		baseEvent:     *baseEvent,
		Dot:           pointer_utils.Int64(app.Info.GetDot()),
		CompanyName:   &companyName,
		CreatedByID:   &app.CreatedBy,
		EffectiveDate: &app.EffectiveDate,
		UnderwriterID: &app.UnderwriterID,
		ProducerId:    &app.ProducerID,
		MarketerId:    &app.MarketerId,
	}
	if app.Info.GetState() != us_states.InvalidStateCode {
		retval.USState = pointer_utils.String(app.Info.GetState().ToCode())
	}

	if app.CreatedBy != uuid.Nil {
		user, err := authWrapper.FetchAuthzUser(ctx, *retval.CreatedByID)
		if err != nil {
			return nil, errors.Wrapf(
				err, "Failed to fetch authz user with id %s.", app.CreatedBy.String(),
			)
		}
		retval.CreatedByName = pointer_utils.String(user.FullName())
		retval.CreatedByEmail = pointer_utils.String(user.Email)
	}

	if app.AgencyID != uuid.Nil {
		currentAgency, err := agency.FetchAgency(ctx, app.AgencyID)
		if err != nil {
			return nil, errors.Wrapf(
				err, "Failed to fetch agency for id %s.", app.AgencyID.String(),
			)
		}
		retval.AgencyName = pointer_utils.String(currentAgency.Name)
	}

	if app.UnderwriterID != uuid.Nil {
		underwriter, err := authWrapper.FetchAuthzUser(ctx, app.UnderwriterID)
		if err != nil {
			return nil, errors.Wrapf(
				err, "Failed to fetch underwriter with id %s.", app.UnderwriterID.String(),
			)
		}
		retval.UnderwriterName = pointer_utils.String(underwriter.FullName())
	}

	if app.ProducerID != uuid.Nil {
		producer, err := authWrapper.FetchAuthzUser(ctx, app.ProducerID)
		if err != nil {
			return nil, errors.Wrapf(
				err, "Failed to fetch producer with id %s.", app.ProducerID.String(),
			)
		}
		retval.ProducerName = pointer_utils.String(producer.FullName())
		retval.ProducerEmail = pointer_utils.String(producer.Email)
	}

	if app.MarketerId != uuid.Nil {
		marketer, err := authWrapper.FetchAuthzUser(ctx, app.MarketerId)
		if err != nil {
			return nil, errors.Wrapf(
				err, "Failed to fetch marketer with id %s.", app.MarketerId.String(),
			)
		}
		retval.MarketerName = pointer_utils.String(marketer.FullName())
		retval.MarketerEmail = pointer_utils.String(marketer.Email)
	}

	fetcherClient, closer, err := fetcherClientFactory()
	if err != nil {
		return nil, errors.Wrap(err, "failed to create data fetcher from factory")
	}
	defer func() { _ = closer() }()

	latestRecord, err := operations.GetLatestInsuranceRecord(ctx, fetcherClient, app.Info.GetDot())
	if err != nil {
		// We don't want to fail the event if we can't get the latest record, as
		// it is possible that the record doesn't exist yet on LNI website
		log.Error(ctx, "failed to get latest insurance record", log.Err(err))
	}

	incumbentCarrier := ""
	if latestRecord != nil {
		incumbentCarrier = latestRecord.InsuranceCompanyName
	}
	retval.IncumbentCarrier = pointer_utils.String(incumbentCarrier)

	retval.ActiveOrPendingInsurance = latestRecord

	if app.FilingType != nil {
		retval.FilingType = pointer_utils.String(app.FilingType.String())
	}

	// If the application is a renewal, we need to set the original application id
	if app.IsRenewal() {
		originalAppId, err := uuid.Parse(app.RenewalMetadata.OriginalApplicationId)
		if err != nil {
			return nil, errors.Wrapf(err, "Failed to parse original application id %s", app.RenewalMetadata.OriginalApplicationId)
		}
		retval.RenewalOriginalApplicationId = pointer_utils.ToPointer(originalAppId)
	}

	return retval, nil
}

var _ events.Event = (*NFApplicationCreated)(nil)
