package carfax_workflow

import (
	"bytes"
	"context"
	"fmt"
	"sync/atomic"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/benb<PERSON>hnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/errgroup"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/s3_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	carfax_db "nirvanatech.com/nirvana/db-api/db_wrappers/telematics/carfax"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/common"
	carfax_integration "nirvanatech.com/nirvana/telematics/integrations/carfax"
	"nirvanatech.com/nirvana/telematics/terrors"
)

const (
	pullerTaskId                = "PullerTask"
	expireThreshold             = time_utils.Day * 60 // threshold for subscription expiration
	nextPullThresholdInitial    = time_utils.Day * 19 // threshold after the first pull
	nextPullThresholdSubsequent = time_utils.Day * 20 // threshold after subsequent pulls
	// we have two different thresholds for the first pull and subsequent pulls as we want to pull data on
	// 1st, 20th, 40th and 60th days. The difference between the first 2 pulls is 19 days, while for the rest, it is 20.
	// Hence, we have two different thresholds to achieve this. After 60 days, we will have to renew the subscription
	// but the next pull will not be allowed before 80th day, i.e. 20 days after the 60th day.

	pullerRoutines               = 10 // number of goroutines to pull & upload VINs in parallel
	consecutiveFailuresThreshold = 5  // consecutiveFailuresThreshold represents the number of consecutive API call
	// failures after which the subscription for the failed VIN will be discarded.
	apiErrorThreshold = 0.25 // fraction of total carfax API errors that our puller task can allow above
	// which the task (& the job) will fail
)

type (
	apiCallHandlerFn = func(ctx context.Context, vin string) ([]byte, error)
	callbackFn       = func(
		ctx context.Context, sub *carfax_db.Subscription, carfaxApiResp []byte, carfaxApiErr error,
	) error
)

// pullerTask  pulls the vin info using carfax api, stores them in s3 and updates the subscription in the database.
type pullerTask struct {
	apiClient *carfax_integration.Client
	dbWrapper *carfax_db.SubscriptionsWrapper
	s3Client  s3_utils.Client
	clk       clock.Clock
	common.Retryable[job_utils.EmptyMessageT]
	job_utils.NoopUndoTask[job_utils.EmptyMessageT]
}

func (task *pullerTask) ID() string {
	return pullerTaskId
}

func (task *pullerTask) Run(ctx jtypes.Context, _ job_utils.EmptyMessageT) error {
	// TODO: Add `coalesce( next_pull_at <= curr_time, true)` to the query for further optimization
	subs, err := task.dbWrapper.GetAllSubscriptions(ctx)
	if err != nil {
		return errors.Wrap(err, "failed to get all subscriptions")
	}
	puller := ctx.GetJobRunId().String()
	attempt, err := ctx.GetRetryAttempt()
	if err == nil {
		puller = fmt.Sprintf("%s::%d", puller, attempt)
	}
	subsToUpsert := filterAndPopulateSubs(ctx, subs, task.clk.Now(), puller)
	return pullVinsAndUpdate(
		ctx,
		getVinApiHandler(task.apiClient),
		subsToUpsert,
		persistInS3AndUpdateDBCallback(task.s3Client, task.dbWrapper),
	)
}

// pullVinsAndUpdate takes the subscriptions that need to be pulled, and pulls the vin info from the apiCallHandlerFn.
// It then executes the callbackFn for the given subscription and the respective response. This is done using
// goroutines and channels. The number of goroutines is open to experimentation.
func pullVinsAndUpdate(
	ctx context.Context,
	apiCaller apiCallHandlerFn,
	subs []*carfax_db.Subscription,
	callback callbackFn,
) error {
	if len(subs) == 0 {
		return nil
	}
	subsChan := make(chan *carfax_db.Subscription, pullerRoutines)

	errCount := atomic.Uint32{}
	// vin puller function
	puller := func(ctx context.Context) error {
		for {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case sub, ok := <-subsChan:
				if !ok { // channel closed and empty
					return nil
				}
				vin := sub.Vin
				// TODO: Do not hard fail the job if errors occur only for a small subset of vins. We should log
				// warning messages and continue with the rest of the vins.
				resp, err := apiCaller(ctx, vin)
				if err != nil {
					httpErr := &terrors.ResponseError{}
					fields := []log.Field{log.String("vin", vin)}
					if errors.As(err, &httpErr) {
						fields = append(fields, log.Int("statusCode", httpErr.StatusCode))
					}
					fields = append(fields, log.Err(err))
					log.Error(ctx, "Carfax API call failed", fields...)
					if !sub.ConsecutiveFailures.Valid {
						errCount.Add(1) // only count new failures
					}
				}
				err = callback(ctx, sub, resp, err)
				if err != nil {
					return errors.Wrapf(err, "failed to execute callback for vin %s", vin)
				}
			}
		}
	}

	g, groupCtx := errgroup.WithContext(ctx)
	// start puller routines
	for i := 0; i < pullerRoutines; i++ {
		g.Go(func() error {
			return puller(groupCtx)
		})
	}
	// send all subs to puller routines
	g.Go(func() error {
		defer close(subsChan)
		for _, sub := range subs {
			select {
			case <-groupCtx.Done():
				return groupCtx.Err()
			case subsChan <- sub:
			}
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		return errors.Wrap(err, "failed to pull vins and update subscriptions")
	}
	// check if the error count is above or equal to the threshold
	totalErrs := int(errCount.Load())
	if float64(totalErrs)/float64(len(subs)) >= apiErrorThreshold {
		return errors.Newf(
			"API error threshold %f exceeded, got %d errors for %d subscriptions",
			apiErrorThreshold, totalErrs, len(subs),
		)
	}
	return nil
}

// uploadToS3 uploads the response to S3 in two locations:
// 1. tempDir: for temporary storage for further processing by the combiner task
// 2. vinDir: for permanent storage for the vin
// We could have used batch uploads, but since there are only two uploads, we are using the normal upload method.
func uploadToS3(ctx context.Context, s3Client s3_utils.Client, vin string, resp []byte) error {
	// upload to tmp dir
	_, err := s3Client.UploadWithContext(ctx, &s3manager.UploadInput{
		Body:   bytes.NewReader(resp),
		Bucket: aws.String(stageBucket),
		Key:    aws.String(fmt.Sprintf("%s%s%s.json", prefix, tempDir, vin)),
	})
	if err != nil {
		return errors.Wrapf(err, "failed to upload vin %s to %s", vin, tempDir)
	}
	// upload to vin dir
	_, err = s3Client.UploadWithContext(ctx, &s3manager.UploadInput{
		Body:   bytes.NewReader(resp),
		Bucket: aws.String(telematicsBucket),
		Key:    aws.String(fmt.Sprintf("%s%s%s.json", prefix, vinDir, vin)),
	})
	if err != nil {
		return errors.Wrapf(err, "failed to upload vin %s to %s", vin, vinDir)
	}
	return nil
}

// getVinApiHandler returns an apiCallHandlerFn that takes a vin and returns the vin info from the carfax api.
func getVinApiHandler(client *carfax_integration.Client) apiCallHandlerFn {
	return func(ctx context.Context, vin string) ([]byte, error) {
		resp, err := client.GetVinInfo(ctx, vin)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get vin info for %s", vin)
		}
		return resp, nil
	}
}

// persistInS3AndUpdateDBCallback returns a callbackFn that takes a subscription and the carfax api response, and
// uploads the response to S3 and updates the subscription in the database. S3 Uploads and Db Updates are already
// tried and tested, hence we do not need complicated tests for this function. We only write a simple test to verify
// that consecutive failures are updated in the db when the api call fails, and reset when the api call succeeds.
func persistInS3AndUpdateDBCallback(s3Client s3_utils.Client, dbWrapper *carfax_db.SubscriptionsWrapper) callbackFn {
	return func(ctx context.Context, sub *carfax_db.Subscription, carfaxApiResp []byte, carfaxApiErr error) error {
		if carfaxApiErr != nil {
			sub.ConsecutiveFailures = null.IntFrom(sub.ConsecutiveFailures.Int + 1)
			err := dbWrapper.UpdateSubscriptionWithFailureCount(ctx, sub)
			if err != nil {
				return errors.Wrapf(err, "failed to update subscription %s for failure count %d",
					sub, sub.ConsecutiveFailures.Int)
			}
			return nil
		}
		err := uploadToS3(ctx, s3Client, sub.Vin, carfaxApiResp)
		if err != nil {
			return errors.Wrapf(err, "failed to upload vin %s to S3", sub.Vin)
		}
		sub.ConsecutiveFailures = null.Int{} // reset the consecutive failures
		err = dbWrapper.UpdateSubscriptionWithChanges(ctx, sub)
		if err != nil {
			return errors.Wrapf(err, "failed to update subscription %s", sub)
		}
		return nil
	}
}

// filterAndPopulateSubs filters the subscriptions that need to be pulled, populates the necessary fields for
// upserting them in the db on each pull, and returns the final subscriptions. The logic is as follows:
//  1. If the subscription is not pulled ever, set the expiration time as current day + expireThreshold, and the
//     next pull time as current day + nextPullThresholdInitial.
//  2. Else, check if the subscription is expired. If it is expired, check if it is renewed or not.
//     a. If it is not renewed, do not pull.
//     b. If it is renewed, but current day is before the next pull time, do not pull.
//     c. If it is renewed, and current day is equal or after the next pull time, set the expiration time as
//     current day + expireThreshold, and the next pull time as current day + nextPullThresholdInitial.
//  3. If the subscription is not expired, check if the current day is greater than or equal to the next pull time.
//     If it is, set the next pull time as next pull time + nextPullThresholdSubsequent.
//  4. Finally, return the subscriptions that need to be pulled.
func filterAndPopulateSubs(ctx context.Context, subs []*carfax_db.Subscription, curr time.Time, puller string,
) []*carfax_db.Subscription {
	// round-down time to start of the day
	day := time_utils.StartOfDayFor(curr)

	// populator function to set the subscription fields with necessary values.
	// Note how populator always sets the subscription to not renewed. This is because our subscription mechanism
	// allows renewals ONLY if a subscription is expired. And on renewing a subscription, we set the
	// subscription to not renewed till further expiration. Also, in all other cases, the subscription is set to
	// not renewed. Hence, we always set the subscription to not renewed.
	populator := func(sub *carfax_db.Subscription, nextPull time.Time) *carfax_db.Subscription {
		sub.Renewed = false
		sub.LastPulled = null.TimeFrom(day)
		sub.LastPulledBy = null.StringFrom(puller)
		sub.NextPullAt = null.TimeFrom(nextPull)
		return sub
	}

	var subsToUpsert []*carfax_db.Subscription
	for _, sub := range subs {
		fields := []log.Field{
			log.String("vin", sub.Vin),
			log.Time("currTime", curr),
			log.Time("nextPullAt", sub.NextPullAt.Time), // deliberately logging old values for debugging
			log.Time("expiresAt", sub.ExpiresAt.Time),   // deliberately logging old values for debugging
			log.Int("consecutive_failures", sub.ConsecutiveFailures.Int),
		}
		if sub.ConsecutiveFailures.Valid && sub.ConsecutiveFailures.Int >= consecutiveFailuresThreshold {
			log.Info(ctx, "Discarding subscription because of consecutive failures", fields...)
			continue
		}
		if !sub.LastPulled.Valid {
			sub := populator(sub, day.Add(nextPullThresholdInitial))
			sub.ExpiresAt = null.TimeFrom(day.Add(expireThreshold)) // set expiration time
			subsToUpsert = append(subsToUpsert, sub)

			// NOTE: THIS LOG LINE IS FOR AUDITING PURPOSES.
			// DO NOT EDIT, REMOVE OR REFACTOR WITHOUT NOTICE.
			log.Info(ctx, "Pulling vin for the first time",
				log.String("vin", sub.Vin), // no need to log other fields
			)
			continue
		}
		if !day.Before(sub.ExpiresAt.Time) {
			// subscription has expired
			if !sub.Renewed || day.Before(sub.NextPullAt.Time) {
				// subscription is not renewed or the current day is before the next pull time
				log.Info(ctx, "Discarding expired subscription",
					append(fields, log.Bool("renewed", sub.Renewed))...,
				)
				continue
			}
			// subscription is set for renewal, and the current day is not before the next pull time

			// NOTE: THIS LOG LINE IS FOR AUDITING PURPOSES.
			// DO NOT EDIT, REMOVE OR REFACTOR WITHOUT NOTICE.
			log.Info(ctx, "Pulling expired but renewed subscription", fields...)

			sub := populator(sub, day.Add(nextPullThresholdInitial))
			sub.ExpiresAt = null.TimeFrom(day.Add(expireThreshold)) // update expiration time
			subsToUpsert = append(subsToUpsert, sub)
			continue
		}
		// subscription is not expired
		if day.Before(sub.NextPullAt.Time) {
			// current day is before to the next pull time
			log.Info(ctx, "Discarding subscription because current day is before next pull time", fields...)
			continue
		}
		// current day is equal to or after the next pull time, and it is not the first pull for the current
		// subscription session since creation or renewal. We DO NOT update Expiration time. Also, next pull time
		// will not be updated based on the current day, but based on the last stored next pull time.
		log.Info(ctx, "Pulling existing unexpired subscription", fields...)
		sub := populator(sub, sub.NextPullAt.Time.Add(nextPullThresholdSubsequent))
		subsToUpsert = append(subsToUpsert, sub)
	}
	return subsToUpsert
}

func (task *pullerTask) Retry(ctx jtypes.Context, msg job_utils.EmptyMessageT) error {
	return task.Run(ctx, msg)
}
