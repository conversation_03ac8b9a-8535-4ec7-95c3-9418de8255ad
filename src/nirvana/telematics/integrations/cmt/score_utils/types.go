package score_utils

import (
	"strings"
	"time"
)

// VehicleScore is the struct that represents a vehicle score. It is used to unmarshal the
// CSV response from CMT's vehicle scoring results stored in S3.
type VehicleScore struct {
	Vin             string  `csv:"vehicle_id"`    // unique vehicle identifier (VIN, to be specific)
	RollingWindow   string  `csv:"interval"`      // retrospective period for scoring (e.g.'91 days').
	ScoreDate       CSVDate `csv:"score_date"`    // date of the score
	TripDistanceKm  float64 `csv:"trip_distance"` // total distance driven in km
	TripDurationSec float64 `csv:"trip_duration"` // total time driven in seconds
	TripCount       int     `csv:"trip_count"`    // number of trips

	Score int `csv:"vehicle_score"`
	// CMT Enhanced Vehicle Score (actuarially-valided risk score). Defined in the range of 1
	// (high risk) to 100 (low risk).

	ScoreEligibility CSVStringSlice `csv:"score_eligibility"`
	// A string containing a comma-separated list of reasons why the enhanced commercial score might
	// not be fully eligible or credible based on potential issues with the input data. The strings will also
	// contain a description of the remedy applied by CMT to make the scores as
	// actuarially valid and useful as possible.

	ScoreAttrExposure int `csv:"score_attr_exposure"`
	// The amount of score points that the amount of
	// exposure contributes to the vehicle score being less
	// than 100.

	ScoreAttrBraking int `csv:"score_attr_braking"`
	// The amount of score points that the amount of hard
	// braking contributes to the vehicle score being less
	// than 100.

	ScoreAttrContextualSpeeding int `csv:"score_attr_contextual_speeding"`
	// The amount of score points that the amount of
	// contextual speeding contributes to the vehicle score
	// being less than 100.

	ScoreAttrNightFatigue int `csv:"score_attr_night_fatigue"`
	// The amount of score points that the amount of
	// nighttime driving and long trips contribute to the
	// vehicle score being less than 100.

	ScoreAttrRoadRisk int `csv:"score_attr_road_risk"`
	// The amount of score points that the amount of road
	// risk contributes to the vehicle score being less than
	// 100.
}

// FleetScore is the struct that represents a fleet score. It is used to unmarshal the CSV response from CMT's
// fleet scoring results stored in S3.
type FleetScore struct {
	HandleId      string  `csv:"fleet_id"`   // unique fleet identifier (handle_id, to be specific)
	RollingWindow string  `csv:"interval"`   // retrospective period for scoring (e.g.'91 days').
	ScoreDate     CSVDate `csv:"score_date"` // date of the score
	Score         int     `csv:"fleet_score"`
	// Aggregated fleet score of the CMT Enhanced Vehicle
	// Score (actuarially-valided risk score). Defined in the
	// range of 1 (high risk) to 100 (low risk).

	TotalTripDistanceKm  float64 `csv:"total_trip_distance"` // Total distance driven in km for all vehicles
	TotalTripDurationSec float64 `csv:"total_trip_duration"` // Total time driven in seconds for all vehicles
	TotalTripCount       int     `csv:"total_trip_count"`    // Total number of trips for all vehicles
}

type CSVDate time.Time

func (d *CSVDate) UnmarshalCSV(data []byte) error {
	s := string(data)
	if s == "" {
		return nil
	}
	t, err := time.Parse("2006-01-02", s)
	if err != nil {
		return err
	}
	*d = CSVDate(t)
	return nil
}

func (d *CSVDate) MarshalCSV() ([]byte, error) {
	t := time.Time(*d)
	return []byte(t.Format("2006-01-02")), nil
}

type CSVStringSlice []string

func (s *CSVStringSlice) UnmarshalCSV(data []byte) error {
	str := string(data)
	if str == "" {
		*s = []string{}
		return nil
	}
	items := strings.Split(str, ",")
	for i, v := range items {
		items[i] = strings.TrimSpace(v)
	}
	*s = items
	return nil
}

func (s *CSVStringSlice) MarshalCSV() ([]byte, error) {
	return []byte(strings.Join(*s, ",")), nil
}
