package impl

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/policy_common/forms_generator/forms"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/durationpb"

	"nirvanatech.com/nirvana/common-go/math_utils"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/insurance-bundle/model/charges"

	"nirvanatech.com/nirvana/business-auto/enums"
	ba_jobs "nirvanatech.com/nirvana/business-auto/jobs"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/type_utils"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	program_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	insuranceBundleModel "nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/pdfgen"
	policyutils "nirvanatech.com/nirvana/policy/business_auto"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
	"nirvanatech.com/nirvana/quoting/ancillary_coverages"
)

const (
	GenerateQuotePDFTaskID    = "GenerateBusinessAutoQuotePDFTask"
	pdfTemplate               = "quoteBizAuto"
	QuotePDFExpirationDefault = time.Minute * 15
	defaultAssessmentAmount   = 145.0
	defaultDotNumber          = "-"
	defaultFeinNumber         = "-"
)

var covTolimitMetaDataMap = map[app_enums.Coverage]string{
	app_enums.CoverageAutoLiability:      "CSL",
	app_enums.CoverageAutoPhysicalDamage: "See Specific Unit",
}

func NewGenerateQuotePDFJob(deps *Deps) (*jtypes.Job[*ba_jobs.GenerateQuotePDFArgs], error) {
	return jtypes.NewJob(
		ba_jobs.GenerateQuotePDF,
		[]jtypes.TaskCreator[*ba_jobs.GenerateQuotePDFArgs]{
			func() jtypes.Task[*ba_jobs.GenerateQuotePDFArgs] {
				return &GenerateQuotePDFTask{deps: deps}
			},
		},
		ba_jobs.GenerateQuotePDFUnmarshalFn,
	)
}

type GenerateQuotePDFTask struct {
	job_utils.NonRetryableTask[*ba_jobs.GenerateQuotePDFArgs]
	job_utils.NoopUndoTask[*ba_jobs.GenerateQuotePDFArgs]
	deps *Deps
}

func (t *GenerateQuotePDFTask) ID() string {
	return GenerateQuotePDFTaskID
}

func (t *GenerateQuotePDFTask) Run(
	jCtx jtypes.Context,
	msg *ba_jobs.GenerateQuotePDFArgs,
) error {
	err := validateGenerateQuotePDFArgs(msg)
	if err != nil {
		log.Error(jCtx, "failed to validate GenerateQuotePDFArgs", log.Err(err))
		return errors.Wrap(err, "failed to validate GenerateQuotePDFArgs for business auto")
	}

	log.Info(jCtx, "GenerateQuotePDF job started",
		log.Stringer("applicationID", msg.ApplicationID),
		log.Stringer("signaturePacketID", msg.SignaturePacketID),
		log.String("applicationType", msg.ApplicationType.String()))

	jCtx = jCtx.WithUpdatedBaseCtx(func(ctx context.Context) context.Context {
		return log.ContextWithFields(ctx, log.Stringer("applicationID", msg.ApplicationID))
	})

	// Get signature packet compilation if provided
	var sigPacketComp *compilation.FormsCompilation
	if msg.SignaturePacketID != uuid.Nil {
		sigPacketComp, err = t.deps.FormsWrapper.GetFormCompilationById(jCtx, msg.SignaturePacketID)
		if err != nil {
			log.Error(jCtx, "failed to get signature packet compilation", log.Err(err))
			return errors.Wrapf(err, "failed to get signature packet compilation for ID %s", msg.SignaturePacketID)
		}
	}

	// Create quote PDF inputs (with actual forms and customer data)
	quotePDFInputs, err := t.createBusinessAutoQuotePDFInputs(jCtx, msg.ApplicationID, sigPacketComp)
	if err != nil {
		log.Error(jCtx, "failed to create quote PDF inputs", log.Err(err))
		return errors.Wrap(err, "failed to create quote PDF inputs")
	}

	// Marshal inputs to JSON
	data, err := json.Marshal(quotePDFInputs)
	if err != nil {
		log.Error(jCtx, "failed to marshal quote PDF inputs for business auto", log.Err(err))
		return errors.Wrap(err, "failed to marshal quote PDF inputs for business auto")
	}

	// Generate filename
	fileName := generateFileNameForQuotePDF(
		t.deps.Clock,
		quotePDFInputs.CustomerInfo.Name,
		quotePDFInputs.CustomerInfo.FeinNumber,
	)

	// Call PDF generation service
	resp, err := t.deps.PDFGenClient.Generate(jCtx, &pdfgen.GeneratePDFRequest{
		UseReactPdf: true,
		Template:    pdfTemplate,
		Data:        data,
		LinkExpiry:  durationpb.New(QuotePDFExpirationDefault),
		FileName:    fileName,
	})
	if err != nil {
		log.Error(jCtx, "failed to generate business auto quote PDF", log.Err(err))
		return errors.Wrap(err, "failed to generate business auto quote PDF")
	}

	// Parse handle ID from response
	handleID, err := uuid.Parse(resp.HandleUuid)
	if err != nil {
		log.Error(jCtx, "failed to parse handle UUID from PDF generation response", log.Err(err))
		return errors.Wrap(err, "failed to parse handle UUID from PDF generation response")
	}

	log.Info(jCtx, "Successfully generated business auto quote PDF",
		log.String("fileName", fileName),
		log.Stringer("handleID", handleID),
		log.String("downloadUrl", resp.DownloadLink))

	// Update business auto application with quote PDF handle ID
	err = t.deps.BusinessAutoAppWrapper.UpdateApp(jCtx, msg.ApplicationID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		if app.DocumentsInfo == nil {
			app.DocumentsInfo = &model.DocumentsInfo{}
		}
		app.DocumentsInfo.QuotePDFHandleID = &handleID
		return app, nil
	})
	if err != nil {
		log.Error(jCtx, "failed to update business auto application with quote PDF handle ID", log.Err(err))
		return errors.Wrap(err, "failed to update business auto application with quote PDF handle ID")
	}

	log.Info(jCtx, "GenerateQuotePDF job completed successfully")
	return nil
}

// Main method - orchestrates the PDF input creation
func (t *GenerateQuotePDFTask) createBusinessAutoQuotePDFInputs(
	jCtx jtypes.Context,
	applicationID uuid.UUID,
	sigPacketComp *compilation.FormsCompilation,
) (*businessAutoQuotePDFInputs, error) {
	// Fetch application
	app, err := t.deps.BusinessAutoAppWrapper.GetByID(jCtx, applicationID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get business auto application")
	}

	// Validate application
	if err := t.validateApplication(app); err != nil {
		return nil, errors.Wrapf(err, "invalid application %s", app.ID)
	}

	// Build PDF inputs
	builder := &pdfInputBuilder{
		task: t,
		app:  app,
	}

	pdfInputs, err := builder.build(jCtx, sigPacketComp)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to build PDF inputs for application %s", app.ID)
	}

	return pdfInputs, nil
}

// pdfInputBuilder encapsulates the PDF input building logic
type pdfInputBuilder struct {
	task *GenerateQuotePDFTask
	app  *model.BusinessAutoApp
}

func (b *pdfInputBuilder) build(jCtx jtypes.Context, sigPacketComp *compilation.FormsCompilation) (*businessAutoQuotePDFInputs, error) {
	// Generate policy number
	policyNumber, err := b.generatePolicyNumber()
	if err != nil {
		return nil, err
	}

	// Build customer info
	customerInfo, err := b.buildCustomerInfo(jCtx, policyNumber)
	if err != nil {
		return nil, err
	}

	// Get pricing context and charges
	chargeList, err := b.getPricingCharges(policyNumber, jCtx)
	if err != nil {
		return nil, err
	}

	// Calculate charge distributions
	baseCharges, surcharges, chargesBySubCov, chargesByVehicles, err := insuranceBundleModel.CalculateBaseChargeDistributionAtAppSubCovLevel(
		chargeList.GetCharges(),
		nil,
	)
	if err != nil {
		return nil, err
	}

	// Build coverages
	coverages, err := b.buildCoverages(chargesBySubCov, b.app.CompanyInfo.USState)
	if err != nil {
		return nil, err
	}

	// Build vehicles
	vehicles, totalStatedValue := b.buildVehicles(chargesByVehicles)

	// Build pricing info
	pricingInfo := b.buildPricingInfo(baseCharges, totalStatedValue, surcharges)

	// Extract forms
	formsList, err := b.extractForms(jCtx, sigPacketComp)
	if err != nil {
		return nil, err
	}

	return &businessAutoQuotePDFInputs{
		QuoteNumber:  policyNumber,
		CustomerInfo: customerInfo,
		Coverages:    coverages,
		Vehicles:     vehicles,
		PricingInfo:  pricingInfo,
		Forms:        formsList,
	}, nil
}

// Validation
func (t *GenerateQuotePDFTask) validateApplication(app *model.BusinessAutoApp) error {
	if app == nil {
		return errors.New("application is nil")
	}
	if app.CoveragesInfo == nil {
		return errors.New("coverages info is nil")
	}
	return nil
}

// Policy number generation
func (b *pdfInputBuilder) generatePolicyNumber() (string, error) {
	policyNumberImpl, err := policyutils.GeneratePolicyNumber(
		app_enums.CoverageAutoLiability,
		b.app.EffectiveDurationStart,
		string(b.app.ShortID),
	)
	if err != nil {
		return "", errors.Wrap(err, "failed to generate policy number")
	}
	return policyNumberImpl.String(), nil
}

// Customer info building
func (b *pdfInputBuilder) buildCustomerInfo(jCtx jtypes.Context, policyNumber string) (*customerInfo, error) {
	agencyName := b.fetchAgencyName(jCtx)
	producerName := b.fetchProducerName(jCtx)

	return &customerInfo{
		Name: b.app.CompanyInfo.Name,
		PolicyPeriod: &policyPeriod{
			StartDate: b.app.EffectiveDurationStart,
			EndDate:   b.app.EffectiveDurationEnd,
		},
		DotNumber:    formatDotNumber(b.app.CompanyInfo.DOTNumber),
		FeinNumber:   type_utils.GetValueOrDefault(b.app.CompanyInfo.FEIN, defaultFeinNumber),
		Agency:       agencyName,
		Producer:     producerName,
		PolicyNumber: policyNumber,
	}, nil
}

func (b *pdfInputBuilder) fetchAgencyName(jCtx jtypes.Context) string {
	agencyInfo, err := b.task.deps.AgencyWrapper.FetchAgency(jCtx, b.app.AgencyID)
	if err != nil {
		log.Warn(jCtx, "failed to fetch agency info",
			log.String("agencyID", b.app.AgencyID.String()),
			log.Err(err))
		return ""
	}
	return agencyInfo.Name
}

func (b *pdfInputBuilder) fetchProducerName(jCtx jtypes.Context) string {
	producerInfo, err := b.task.deps.AuthWrapper.FetchUserInfo(jCtx, b.app.ProducerID)
	if err != nil {
		log.Warn(jCtx, "failed to fetch producer info",
			log.String("producerID", b.app.ProducerID.String()),
			log.Err(err))
		return ""
	}
	return fmt.Sprintf("%s %s", producerInfo.FirstName, producerInfo.LastName)
}

// Pricing context and charges
func (b *pdfInputBuilder) getPricingCharges(policyNumber string, jCtx jtypes.Context) (*charges.ChargeList, error) {
	if b.app.SelectedQuotingPricingContextID == nil {
		log.Error(jCtx, "no pricing context ID found for application",
			log.String("appID", b.app.ID.String()))
		return &charges.ChargeList{}, errors.Newf("failed to get pricing context for application %s", b.app.ID.String())
	}

	pricingContext, err := b.task.deps.PricingWrapper.GetQuotingPricingContextById(
		jCtx,
		b.app.SelectedQuotingPricingContextID.String(),
	)
	if err != nil {
		return nil,
			errors.Wrapf(err, "failed to get pricing context for SelectedQuotingPricingContextID: %s",
				b.app.SelectedQuotingPricingContextID.String())
	}

	if pricingContext.Charges != nil && pricingContext.Charges.PolicyCharges != nil {
		return pricingContext.Charges.PolicyCharges[policyNumber], nil
	}

	return &charges.ChargeList{}, errors.Newf("got null charges of pricing context for SelectedQuotingPricingContextID: %s",
		b.app.SelectedQuotingPricingContextID.String())
}

// Coverage building
func (b *pdfInputBuilder) buildCoverages(chargesBySubCov map[app_enums.Coverage]*float64, usState us_states.USState) ([]*coverage, error) {
	limitForAllSubCoverages := b.app.CoveragesInfo.GetLimitForAllSubCoverages()
	coverages := make([]*coverage, 0, len(chargesBySubCov))
	// symbolCounter := 1

	for subCov, premium := range chargesBySubCov {
		limit, err := b.getCoverageLimit(subCov, limitForAllSubCoverages)
		if err != nil {
			return nil, err
		}

		// TODO remove once symbol modelling is done
		var symbol string
		symbolsAndDefinitions := ancillary_coverages.AncillaryCoverages[usState][program_enums.ProgramTypeBusinessAuto][subCov].SymbolsAndDefinitions
		if symbolsAndDefinitions == nil {
			// Confirmed that as AL won't be in ancillary coverages sheet for symbols,
			// but as we are adding it in quotePDF, assign its symbol as 7 for all states
			symbol = "7"
		} else {
			symbol = (*symbolsAndDefinitions)[0].Symbol
		}

		coverages = append(coverages, &coverage{
			Symbol:      symbol,
			DisplayName: str_utils.PrettyEnumString(subCov.String(), "Coverage"),
			Limit:       &limit,
			Premium:     premium,
			LimitMeta:   pointer_utils.ToPointer(covTolimitMetaDataMap[subCov]),
		})
		// symbolCounter++
	}

	return coverages, nil
}

func (b *pdfInputBuilder) getCoverageLimit(
	subCov app_enums.Coverage,
	limitForAllSubCoverages map[app_enums.Coverage]float64,
) (float64, error) {
	if limit, ok := limitForAllSubCoverages[subCov]; ok {
		return limit, nil
	}

	// Special handling for auto liability (combined BI/PD)
	if subCov == app_enums.CoverageAutoLiability {
		return b.getCombinedLiabilityLimit(limitForAllSubCoverages)
	}

	return float64(0), errors.Errorf("missing limit for sub coverage %s", subCov)
}

func (b *pdfInputBuilder) getCombinedLiabilityLimit(
	limitForAllSubCoverages map[app_enums.Coverage]float64,
) (float64, error) {
	biLimit, hasBi := limitForAllSubCoverages[app_enums.CoverageBodilyInjury]
	_, hasPd := limitForAllSubCoverages[app_enums.CoveragePropertyDamage]

	if hasBi && hasPd {
		return biLimit, nil
	}

	return float64(0), errors.New("missing BI/PD limits for auto liability coverage")
}

// Vehicle building
func (b *pdfInputBuilder) buildVehicles(
	chargesByVehicles map[string]map[app_enums.Coverage]*float64,
) ([]*vehicle, float64) {
	if b.app.VehiclesInfo == nil {
		return []*vehicle{}, 0
	}

	var vehicles []*vehicle
	var totalStatedValue float64

	for _, v := range *b.app.VehiclesInfo {
		createVehicle := b.createVehicle(v, chargesByVehicles[v.VIN])
		vehicles = append(vehicles, createVehicle)

		totalStatedValue = *math_utils.RoundedTotal(
			pointer_utils.ToPointer(totalStatedValue),
			v.StatedValue,
		)
	}

	return vehicles, totalStatedValue
}

func (b *pdfInputBuilder) createVehicle(
	v model.VehicleInfo,
	vehicleCharges map[app_enums.Coverage]*float64,
) *vehicle {
	vehicle := &vehicle{
		Year:   int(v.Year),
		Make:   v.Make,
		Model:  v.Model,
		Vin:    v.VIN,
		Radius: convertRadiusClassificationToDisplayText(v.RadiusClassification),
	}

	if vehicleCharges != nil {
		// Map charges to vehicle premiums
		if premium, found := vehicleCharges[app_enums.CoverageAutoLiability]; found {
			vehicle.LiabilityPremium = premium
		}

		if premium, found := vehicleCharges[app_enums.CoverageAutoPhysicalDamage]; found {
			vehicle.CollComp = premium
		}
		if premium, found := vehicleCharges[app_enums.CoverageUMUIMPhysicalDamage]; found {
			vehicle.PhysDamagePremium = premium
		}
		if premium, found := vehicleCharges[app_enums.CoverageMedicalPayments]; found {
			vehicle.MedPayPipPremium = premium
		}
		if _, found := vehicleCharges[app_enums.CoverageUninsuredMotoristPropertyDamage]; found {
			vehicle.UmPremium = pointer_utils.ToPointer("Incl.")
		}
		if _, found := vehicleCharges[app_enums.CoverageUninsuredMotoristBodilyInjury]; found {
			vehicle.UmPremium = pointer_utils.ToPointer("Incl.")
		}
		if _, found := vehicleCharges[app_enums.CoverageUnderinsuredMotoristPropertyDamage]; found {
			vehicle.UimPremium = pointer_utils.ToPointer("Incl.")
		}
		if _, found := vehicleCharges[app_enums.CoverageUnderinsuredMotoristBodilyInjury]; found {
			vehicle.UimPremium = pointer_utils.ToPointer("Incl.")
		}
		vehicle.UnitSubTotal = math_utils.RoundedTotal(
			vehicle.LiabilityPremium,
			vehicle.CollComp,
			vehicle.PhysDamagePremium,
			vehicle.MedPayPipPremium,
		)
	}
	vehicle.Deductible = v.APDDeductible

	return vehicle
}

// Pricing info building
func (b *pdfInputBuilder) buildPricingInfo(baseCharges *float64, totalStatedValue float64, surcharges *float64,
) *premiumDetails {
	total := math_utils.RoundedTotal(baseCharges, surcharges)

	return &premiumDetails{
		TotalInsValue:   totalStatedValue,
		Assessments:     *surcharges,
		TotalPremium:    *total,
		PremiumSubTotal: *baseCharges,
	}
}

// Helper functions
func formatDotNumber(dotNumber *int) string {
	if dotNumber == nil || *dotNumber == 0 {
		return defaultDotNumber
	}
	return fmt.Sprintf("%d", *dotNumber)
}

func (b *pdfInputBuilder) extractForms(jCtx jtypes.Context, sigPacketComp *compilation.FormsCompilation) ([]*form, error) {
	if sigPacketComp == nil {
		return nil, errors.New("signature packet nil")
	}
	metadata := (*sigPacketComp).Metadata()
	if metadata.CoverageIdsMap == nil {
		return nil, errors.New("signature packet missing coverage ids map")
	}
	coverageIdMaps := *metadata.CoverageIdsMap
	formSet := forms.NewFormSet()
	var formsResult []*form
	// Iterate over the coverage map and gather their ids
	// append all the forms
	var policyIds []uuid.UUID
	for coverage, id := range coverageIdMaps {
		if coverage != app_enums.CoverageAutoPhysicalDamage {
			policyIds = append(policyIds, *id)
		}
	}
	// If all the policy compilation ids are present then
	// extract all forms attached on the policies
	for _, id := range policyIds {
		formComp, err := b.task.deps.FormsWrapper.GetFormCompilationById(jCtx, id)
		if err != nil {
			return nil, errors.Wrapf(err, "Couldn't fetch form compilation %s",
				id)
		}
		if formComp == nil {
			return nil, errors.Newf("form compilation nil for %s", id)
		}
		formSet.Add((*formComp).FlattenAllForms().ToSlice()...)
	}
	formsSlice := formSet.ToSlice()
	sort.Sort(forms.ByOrderCategory(formsSlice))

	for _, formItem := range formsSlice {
		formsResult = append(formsResult, &form{
			Code: formItem.Code,
			Name: formItem.Name,
		})
	}

	return formsResult, nil
}

func generateFileNameForQuotePDF(clk clock.Clock, insuredName, feinNumber string) string {
	timestamp := clk.Now().Format("2006-01-02")
	return insuredName + " - Business Auto Quote " + feinNumber + " - " + timestamp + ".pdf"
}

func validateGenerateQuotePDFArgs(args *ba_jobs.GenerateQuotePDFArgs) error {
	if args == nil {
		return errors.New("nil args")
	}
	if args.ApplicationID == uuid.Nil {
		return errors.New("nil applicationID")
	}
	return nil
}

func convertRadiusClassificationToDisplayText(radius enums.RadiusClassification) string {
	switch radius {
	case enums.RadiusClassification0To100:
		return "Up to 100 Miles"
	case enums.RadiusClassification101To300:
		return "101-300 Miles"
	case enums.RadiusClassificationGreaterThan301:
		return "Over 300 Miles"
	default:
		return "" // default fallback
	}
}

var _ jtypes.Task[*ba_jobs.GenerateQuotePDFArgs] = (*GenerateQuotePDFTask)(nil)
