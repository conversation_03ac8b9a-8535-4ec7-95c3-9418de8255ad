package impl

import "time"

// businessAutoQuotePDFInputs represents the input structure for business auto quote PDF
type businessAutoQuotePDFInputs struct {
	QuoteNumber  string          `json:"quoteNumber"`
	CustomerInfo *customerInfo   `json:"customerInfo"`
	Coverages    []*coverage     `json:"coverages"`
	Vehicles     []*vehicle      `json:"vehicles"`
	PricingInfo  *premiumDetails `json:"premiumDetails"`
	Forms        []*form         `json:"forms"`
}

type customerInfo struct {
	Name         string        `json:"name"`
	PolicyPeriod *policyPeriod `json:"policyPeriod"`
	DotNumber    string        `json:"dotNumber"`
	FeinNumber   string        `json:"feinNumber"`
	Agency       string        `json:"agency"`
	Producer     string        `json:"producer"`
	PolicyNumber string        `json:"policyNumber"`
}

type policyPeriod struct {
	StartDate time.Time `json:"startDate"`
	EndDate   time.Time `json:"endDate"`
}

type coverage struct {
	Symbol      string   `json:"symbol"`
	DisplayName string   `json:"displayName"`
	Limit       *float64 `json:"limit"`
	Premium     *float64 `json:"premium"`
	LimitMeta   *string  `json:"limitMeta"`
}

type vehicle struct {
	Year              int      `json:"year"`
	Make              string   `json:"make"`
	Model             string   `json:"model"`
	Vin               string   `json:"vin"`
	LiabilityPremium  *float64 `json:"liabilityPremium"`
	UmPremium         *string  `json:"umPremium"`
	UimPremium        *string  `json:"uimPremium"`
	MedPayPipPremium  *float64 `json:"medPay/PipPremium"`
	PhysDamagePremium *float64 `json:"physDamagePremium"`
	UnitSubTotal      *float64 `json:"unitSubTotal"`
	CollComp          *float64 `json:"coll/comp"`
	Deductible        *int64   `json:"deductible"`
	Radius            string   `json:"radius"`
}

type premiumDetails struct {
	TotalInsValue   float64 `json:"totalInsValue"`
	Assessments     float64 `json:"assessments"`
	TotalPremium    float64 `json:"totalPremium"`
	PremiumSubTotal float64 `json:"premiumSubTotal"`
	TexasAntiTheft  float64 `json:"texasAntiTheft"`
}

type form struct {
	Code string `json:"code"`
	Name string `json:"name"`
}
