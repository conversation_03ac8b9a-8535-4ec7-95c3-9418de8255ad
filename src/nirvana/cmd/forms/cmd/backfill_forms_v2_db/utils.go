package backfill_forms_v2_db

import (
	"cmp"
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"

	"nirvanatech.com/nirvana/common-go/log"

	"github.com/cockroachdb/errors"
	"github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/api-server/handlers/forms"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_forms "nirvanatech.com/nirvana/openapi-specs/components/forms"
	"nirvanatech.com/nirvana/policy_common/constants"
	fg_model "nirvanatech.com/nirvana/policy_common/forms_generator/cmd/model"
)

func getApplicabilityRule(
	ctx context.Context,
	appType constants.ApplicationType,
	form fg_model.ScheduledFormsMatrixRow,
	cov app_enums.Coverage,
) (*oapi_forms.FormApplicabilityRule, error) {
	includedStates := getUSStatesFromForm(form.StatesToInclude)
	excludedStates := getUSStatesFromForm(form.StatesToExclude)

	var applicableCoveragePackages []oapi_forms.CoveragePackagePair
	applicableCoveragePackage, err := getApplicableCoveragePackage(cov, form)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to get applicable coverage package for coverage %s and form %s", cov.String(), form.FormCode,
		)
	}

	if applicableCoveragePackage.PackageType != "None" {
		applicableCoveragePackages = append(applicableCoveragePackages, applicableCoveragePackage)
	}
	if cov == app_enums.CoverageAutoLiability {
		applicableCoveragePackage, err = getApplicableCoveragePackage(app_enums.CoverageAutoPhysicalDamage, form)
		if err != nil {
			return nil, errors.Wrapf(
				err, "failed to get applicable coverage package for auto physical damage and form %s", form.FormCode,
			)
		}
		if applicableCoveragePackage.PackageType != "None" {
			applicableCoveragePackages = append(applicableCoveragePackages, applicableCoveragePackage)
		}
	}

	if len(applicableCoveragePackages) == 0 || len(*includedStates) == 0 {
		log.Warn(ctx, "No applicable coverage packages or included states for form ",
			log.Any("fullFormCode", form.FormCode))
		// nolint:nilnil
		return nil, nil
	}
	slices.SortFunc(applicableCoveragePackages,
		func(a, b oapi_forms.CoveragePackagePair) int {
			return cmp.Compare(a.CoverageType, b.CoverageType)
		})

	var programTypes []oapi_common.ProgramType
	programTypes = append(programTypes, getProgramTypeFromApplicationType(appType))

	scheduleType, err := getFormScheduleEnum(form.FormScheduleType)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to get form schedule enum for type %s in form %s", form.FormScheduleType, form.FormCode,
		)
	}
	var formCompilationTypes []oapi_forms.FormType
	formCompilationType := getCompilationTypeFromScheduleType(scheduleType)
	formCompilationTypes = append(formCompilationTypes, formCompilationType)
	if scheduleType == oapi_forms.ScheduleTypeManualForms {
		formCompilationTypes = append(formCompilationTypes, oapi_forms.FormTypeSignaturePacket)
	}
	slices.Sort(formCompilationTypes)

	effectiveDates, err := getDateFromForm(form.StatewiseEffectiveDates, getEffectiveDate, isEffectiveDateNil)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get effective dates for form %s", form.FormCode)
	}

	expirationDates, err := getDateFromForm(form.StatewiseEffectiveDates, getExpirationDate, isExpirationDateNil)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get expiration dates for form %s", form.FormCode)
	}

	additionalRule, scheduleTypes := getAdditionalRule(form.ApplicableAncillaryCoverages), []oapi_forms.ScheduleType{scheduleType}

	var oapiAdditionalRule *oapi_forms.FormApplicabilityAdditionalRule
	if additionalRule != nil {
		oapiAdditionalRule = &oapi_forms.FormApplicabilityAdditionalRule{
			When: additionalRule.when,
		}

		formCompilationTypes = convertToOapiCompilationTypes(form.ApplicableCompilationTypes)

		if !slice_utils.Contains(scheduleTypes, oapi_forms.ScheduleTypeCore) {
			scheduleTypes = append(scheduleTypes, oapi_forms.ScheduleTypeCore)
		}
	}

	return &oapi_forms.FormApplicabilityRule{
		CoreRule: oapi_forms.FormApplicabilityCoreRule{
			ApplicableCoveragePackages:  &applicableCoveragePackages,
			ApplicableInsuranceCarriers: getInsuranceCarriersFromForm(form),
			EffectiveDate:               effectiveDates,
			ExpirationDate:              expirationDates,
			ExcludedStates:              excludedStates,
			IncludedStates:              includedStates,
			FormCompilationTypes:        &formCompilationTypes,
			ProgramTypes:                &programTypes,
		},
		AdditionalRule: oapiAdditionalRule,
		ScheduleType:   &scheduleTypes,
	}, nil
}

func isStateInList(state oapi_common.USState, states *[]oapi_common.USState) bool {
	for _, s := range *states {
		if s == state {
			return true
		}
	}
	return false
}

func getDateFromForm(
	statewiseEffectiveDates fg_model.StateToStatewiseEffectiveDates,
	getDateFunc func(date fg_model.StatewiseEffectiveDates) (time.Time, error),
	isDateNilFunc func(date fg_model.StatewiseEffectiveDates) bool,
) (*oapi_forms.HierarchicalStateDateRule, error) {
	var dates oapi_forms.HierarchicalStateDateRule
	var stateDatePair []oapi_forms.StateDatePair

	for state, date := range statewiseEffectiveDates {
		if isDateNilFunc(date) {
			continue
		}
		parsedDate, err := getDateFunc(date)
		if err != nil {
			return nil, errors.Wrapf(
				err, "unable to parse date for state %s with start date %s and end date %s",
				state, date.StartDate, date.EndDate,
			)
		}

		stateDatePair = append(stateDatePair, oapi_forms.StateDatePair{
			State: oapi_common.USState(state),
			Date:  types.Date{Time: parsedDate},
		})
	}

	slices.SortFunc(stateDatePair, func(a, b oapi_forms.StateDatePair) int {
		return cmp.Compare(a.State, b.State)
	})

	dates.StateSpecific = &stateDatePair
	return &dates, nil
}

func getEffectiveDate(date fg_model.StatewiseEffectiveDates) (time.Time, error) {
	parsedTime, err := time.Parse(time_utils.USLayout, date.StartDate)
	if err != nil {
		return time.Time{}, errors.Wrapf(err, "failed to parse effective date %s", date.StartDate)
	}
	return parsedTime, nil
}

func getExpirationDate(date fg_model.StatewiseEffectiveDates) (time.Time, error) {
	parsedTime, err := time.Parse(time_utils.USLayout, date.EndDate)
	if err != nil {
		return time.Time{}, errors.Wrapf(err, "failed to parse expiration date %s", date.EndDate)
	}
	return parsedTime, nil
}

func isEffectiveDateNil(date fg_model.StatewiseEffectiveDates) bool {
	return date.StartDate == ""
}

func isExpirationDateNil(date fg_model.StatewiseEffectiveDates) bool {
	return date.EndDate == ""
}

func getUSStatesFromForm(statesToInclude []us_states.USState) *[]oapi_common.USState {
	var includedStates []oapi_common.USState
	for _, state := range statesToInclude {
		includedStates = append(includedStates, oapi_common.USState(state.ToCode()))
	}
	slices.Sort(includedStates)
	return &includedStates
}

func getFormScheduleEnum(scheduleType string) (oapi_forms.ScheduleType, error) {
	switch scheduleType {
	case fg_model.CoreForm:
		return oapi_forms.ScheduleTypeCore, nil
	case fg_model.ManualForm:
		return oapi_forms.ScheduleTypeManualForms, nil
	case fg_model.StateForm:
		return oapi_forms.ScheduleTypeStateDependent, nil
	case fg_model.CoverageForm:
		return oapi_forms.ScheduleTypePackageDependent, nil
	case fg_model.SignedForm:
		return oapi_forms.ScheduleTypeSignedQuoteForms, nil
	default:
		return oapi_forms.ScheduleTypeCore, errors.Newf("unable to find schedule type %s", scheduleType)
	}
}

func getCompilationTypeFromScheduleType(scheduleType oapi_forms.ScheduleType) oapi_forms.FormType {
	//nolint:exhaustive
	switch scheduleType {
	case oapi_forms.ScheduleTypeSignedQuoteForms:
		return oapi_forms.FormTypeSignaturePacket
	default:
		return oapi_forms.FormTypePolicy
	}
}

func getApplicableCoveragePackage(cov app_enums.Coverage, form fg_model.ScheduledFormsMatrixRow) (oapi_forms.CoveragePackagePair, error) {
	//nolint:exhaustive
	switch cov {
	case app_enums.CoverageAutoLiability:
		return oapi_forms.CoveragePackagePair{
			CoverageType: oapi_common.CoverageAutoLiability,
			PackageType:  oapi_forms.PackageType(form.ALPackages.String()),
		}, nil
	case app_enums.CoverageAutoPhysicalDamage:
		return oapi_forms.CoveragePackagePair{
			CoverageType: oapi_common.CoverageAutoPhysicalDamage,
			PackageType:  oapi_forms.PackageType(form.APDPackages.String()),
		}, nil
	case app_enums.CoverageGeneralLiability:
		return oapi_forms.CoveragePackagePair{
			CoverageType: oapi_common.CoverageGeneralLiability,
			PackageType:  oapi_forms.PackageType(form.GLPackages.String()),
		}, nil
	case app_enums.CoverageMotorTruckCargo:
		return oapi_forms.CoveragePackagePair{
			CoverageType: oapi_common.CoverageMotorTruckCargo,
			PackageType:  oapi_forms.PackageType(form.MTCPackages.String()),
		}, nil
	default:
		return oapi_forms.CoveragePackagePair{}, errors.Newf("unable to find coverage type %s", cov.String())
	}
}

func getSupportedApplicationTypes() []constants.ApplicationType {
	applicationTypes := constants.ApplicationTypeValues()
	// delete constants.ApplicationTypeNonFleet from applicationTypes
	for i, appType := range applicationTypes {
		if appType == constants.ApplicationTypeNonFleet {
			applicationTypes = append(applicationTypes[:i], applicationTypes[i+1:]...)
			break
		}
	}
	return applicationTypes
}

func getProgramTypeFromApplicationType(appType constants.ApplicationType) oapi_common.ProgramType {
	//nolint:exhaustive
	switch appType {
	case constants.ApplicationTypeFleet:
		return oapi_common.ProgramTypeFleet
	case constants.ApplicationTypeNonFleetAdmitted:
		return oapi_common.ProgramTypeNonFleetAdmitted
	case constants.ApplicationTypeBusinessAuto:
		return oapi_common.ProgramTypeBusinessAuto
	default:
		return oapi_common.ProgramTypeInvalid
	}
}

func getInsuranceCarriersFromForm(form fg_model.ScheduledFormsMatrixRow) *[]string {
	ic := form.EffectiveInsuranceCarrier.InsuranceCarrier
	var icSlice []string
	for carrier, ok := range ic {
		if ok {
			icSlice = append(icSlice, carrier.String())
		}
	}
	slices.Sort(icSlice)
	return &icSlice
}

func getRules(covs []app_enums.Coverage) (rules []map[string]interface{}) {
	for _, cov := range covs {
		rules = append(rules,
			map[string]interface{}{
				"and": []interface{}{
					map[string]interface{}{
						"eq": []interface{}{
							fmt.Sprintf("common.MapContains(F.AllCoverages, \"%s\")", cov.String()),
							true,
						},
					},
					map[string]interface{}{
						"eq": []interface{}{
							fmt.Sprintf("F.AllCoverages.%s", cov.String()),
							true,
						},
					},
				},
			},
		)
	}

	return
}

func getAdditionalRule(ancillaryCovs []app_enums.Coverage) *conditionalRuleInput {
	rules := getRules(ancillaryCovs)
	if len(rules) == 0 {
		return nil
	}

	retval := &conditionalRuleInput{
		when: map[string]interface{}{},
	}

	if len(rules) == 1 {
		retval.when = rules[0]
		return retval
	}

	var orRules []interface{}

	for _, rule := range rules {
		orRules = append(orRules, rule)
	}
	retval.when = map[string]interface{}{
		"or": orRules,
	}

	return retval
}

var formsWithGenFns = getAllFormsWithGeneratorFns()

func setStatus(form forms.PostFormScheduleRequest) forms.PostFormScheduleRequest {
	inactive := oapi_forms.Inactive

	if len(form.FormScheduleRequest.ApplicabilityRules) == 0 {
		form.FormScheduleRequest.Status = &inactive
	}

	_, gnFnExists := formsWithGenFns[form.FormScheduleRequest.FullFormCode]
	// check if fill template exists in FormCodeToFieldConfigs and BusinessAutoFormCodeToFieldConfigs
	fillTemplateExists := false
	if _, exists := FormCodeToFieldConfigs[form.FormScheduleRequest.FullFormCode]; exists {
		fillTemplateExists = true
	}
	if _, exists := BusinessAutoFormCodeToFieldConfigs[form.FormScheduleRequest.FullFormCode]; exists {
		fillTemplateExists = true
	}

	if !gnFnExists && !fillTemplateExists {
		form.FormScheduleRequest.Status = &inactive
	}
	return form
}

func generateRuleHash(
	rule oapi_forms.FormApplicabilityRule,
) string {
	return fmt.Sprintf("%v%v%v%v%v%v%v%v",
		rule.CoreRule.ApplicableInsuranceCarriers,
		rule.CoreRule.IncludedStates,
		rule.CoreRule.ExcludedStates,
		rule.CoreRule.EffectiveDate,
		rule.CoreRule.ExpirationDate,
		rule.CoreRule.FormCompilationTypes,
		rule.CoreRule.ProgramTypes,
		rule.ScheduleType,
	)
}

func deepCopy[T any](ctx context.Context, rule T) (T, error) {
	var copiedRule T
	serialised, err := json.Marshal(rule)
	if err != nil {
		log.Error(ctx, "Failed to marshal rule", log.Err(err), log.Any("rule", rule))
		return copiedRule, errors.Wrap(err, "failed to marshal rule")
	}
	err = json.Unmarshal(serialised, &copiedRule)
	if err != nil {
		log.Error(ctx, "Failed to unmarshal rule", log.Err(err), log.Any("rule", rule))
		return copiedRule, errors.Wrap(err, "failed to unmarshal rule during deep copy")
	}
	return copiedRule, nil
}

func mergeApplicabilityRule(ctx context.Context, rule1, rule2 oapi_forms.FormApplicabilityRule) (oapi_forms.FormApplicabilityRule, error) {
	mergedScheduleType := *rule1.ScheduleType

	for _, scheduleType := range *rule2.ScheduleType {
		if !containsScheduleType(mergedScheduleType, scheduleType) {
			mergedScheduleType = append(mergedScheduleType, scheduleType)
		}
	}

	var mergedApplicableCoveragePackages []oapi_forms.CoveragePackagePair
	if rule1.CoreRule.ApplicableCoveragePackages != nil {
		var err error
		mergedApplicableCoveragePackages, err = deepCopy(ctx, *rule1.CoreRule.ApplicableCoveragePackages)
		if err != nil {
			return oapi_forms.FormApplicabilityRule{}, errors.Wrap(
				err, "failed to deep copy rule1 applicable coverage packages",
			)
		}
	}
	if rule2.CoreRule.ApplicableCoveragePackages != nil {
		for _, coveragePackage := range *rule2.CoreRule.ApplicableCoveragePackages {
			if !containsCoveragePackage(mergedApplicableCoveragePackages, coveragePackage) {
				coveragePackageCopy, err := deepCopy(ctx, coveragePackage)
				if err != nil {
					log.Error(
						ctx, "Failed to deep copy coverage package",
						log.Err(err), log.Any("coveragePackage", coveragePackage),
					)
					return oapi_forms.FormApplicabilityRule{}, errors.Wrapf(err, "failed to deep copy coverage package")
				}
				mergedApplicableCoveragePackages = append(mergedApplicableCoveragePackages, coveragePackageCopy)
			}
		}
	}
	mergedRule := oapi_forms.FormApplicabilityRule{
		AdditionalRule: rule1.AdditionalRule,
		CoreRule: oapi_forms.FormApplicabilityCoreRule{
			ApplicableCoveragePackages:  &mergedApplicableCoveragePackages,
			ApplicableInsuranceCarriers: rule1.CoreRule.ApplicableInsuranceCarriers,
			EffectiveDate:               rule1.CoreRule.EffectiveDate,
			ExcludedStates:              rule1.CoreRule.ExcludedStates,
			ExpirationDate:              rule1.CoreRule.ExpirationDate,
			FormCompilationTypes:        rule1.CoreRule.FormCompilationTypes,
			IncludedStates:              rule1.CoreRule.IncludedStates,
			ProgramTypes:                rule1.CoreRule.ProgramTypes,
		},
		ScheduleType: &mergedScheduleType,
	}

	return mergedRule, nil
}

func containsCoveragePackage(coveragePackages []oapi_forms.CoveragePackagePair, coveragePackage oapi_forms.CoveragePackagePair) bool {
	for _, c := range coveragePackages {
		if c.CoverageType == coveragePackage.CoverageType && c.PackageType == coveragePackage.PackageType {
			return true
		}
	}
	return false
}

func containsScheduleType(scheduleTypes []oapi_forms.ScheduleType, scheduleType oapi_forms.ScheduleType) bool {
	for _, s := range scheduleTypes {
		if s == scheduleType {
			return true
		}
	}
	return false
}

func convertToOapiCompilationTypes(compilationTypes []compilation.CompilationType) (retval []oapi_forms.FormType) {
	for _, compType := range compilationTypes {
		switch compType {
		case compilation.CompilationTypeSignaturePacket:
			retval = append(retval, oapi_forms.FormTypeSignaturePacket)
		case compilation.CompilationTypePolicy:
			retval = append(retval, oapi_forms.FormTypePolicy)
		}
	}

	return retval
}
