package backfill_forms_v2_db

import (
	"context"
	"reflect"
	"testing"
)

// Test helper functions
func simpleRule(field string, value interface{}) map[string]interface{} {
	return map[string]interface{}{
		"eq": []interface{}{field, value},
	}
}

func gtRule(field string, value interface{}) map[string]interface{} {
	return map[string]interface{}{
		"gt": []interface{}{field, map[string]interface{}{"const": value}},
	}
}

func ltRule(field string, value interface{}) map[string]interface{} {
	return map[string]interface{}{
		"lt": []interface{}{field, map[string]interface{}{"const": value}},
	}
}

func andRule(conditions ...interface{}) map[string]interface{} {
	return map[string]interface{}{
		"and": conditions,
	}
}

func orRule(conditions ...interface{}) map[string]interface{} {
	return map[string]interface{}{
		"or": conditions,
	}
}

// Test data
var (
	fieldA = simpleRule("F.FieldA", true)
	fieldB = simpleRule("F.FieldB", false)
	fieldC = gtRule("F.FieldC", 10.0)
	fieldD = ltRule("F.FieldD", 5.0)
)

func TestExtractConditionsForOperator(t *testing.T) {
	tests := []struct {
		name     string
		rule     any
		operator LogicalOperator
		expected []any
	}{
		{
			name:     "simple condition",
			rule:     fieldA,
			operator: LogicalOperatorAnd,
			expected: []any{fieldA},
		},
		{
			name:     "extract AND conditions",
			rule:     andRule(fieldA, fieldB),
			operator: LogicalOperatorAnd,
			expected: []any{fieldA, fieldB},
		},
		{
			name:     "extract OR conditions",
			rule:     orRule(fieldA, fieldB),
			operator: LogicalOperatorOr,
			expected: []any{fieldA, fieldB},
		},
		{
			name:     "wrong operator - treat as single condition",
			rule:     andRule(fieldA, fieldB),
			operator: LogicalOperatorOr,
			expected: []any{andRule(fieldA, fieldB)},
		},
		{
			name:     "non-map rule",
			rule:     "string rule",
			operator: LogicalOperatorAnd,
			expected: []any{"string rule"},
		},
		{
			name:     "invalid structure",
			rule:     map[string]interface{}{"and": "not a slice"},
			operator: LogicalOperatorAnd,
			expected: []any{map[string]interface{}{"and": "not a slice"}},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractConditionsForOperator(tt.rule, tt.operator)
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("extractConditionsForOperator() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCombineRules_AND(t *testing.T) {
	tests := []struct {
		name         string
		existingRule any
		newRule      any
		expected     any
	}{
		{
			name:         "two simple rules",
			existingRule: fieldA,
			newRule:      fieldB,
			expected:     andRule(fieldA, fieldB),
		},
		{
			name:         "identical rules - deduplication",
			existingRule: fieldA,
			newRule:      fieldA,
			expected:     fieldA, // Single condition optimization
		},
		{
			name:         "existing AND + simple rule",
			existingRule: andRule(fieldA, fieldB),
			newRule:      fieldC,
			expected:     andRule(fieldA, fieldB, fieldC),
		},
		{
			name:         "simple + existing AND",
			existingRule: fieldA,
			newRule:      andRule(fieldB, fieldC),
			expected:     andRule(fieldA, fieldB, fieldC),
		},
		{
			name:         "two AND rules with overlap",
			existingRule: andRule(fieldA, fieldB),
			newRule:      andRule(fieldA, fieldC), // fieldA overlaps
			expected:     andRule(fieldA, fieldB, fieldC),
		},
		{
			name:         "identical AND rules",
			existingRule: andRule(fieldA, fieldB),
			newRule:      andRule(fieldA, fieldB),
			expected:     andRule(fieldA, fieldB),
		},
		{
			name:         "OR rule treated as single condition",
			existingRule: orRule(fieldA, fieldB),
			newRule:      fieldC,
			expected:     andRule(orRule(fieldA, fieldB), fieldC),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			op := LogicalOperatorAnd
			result, err := combineRules(context.Background(), tt.existingRule, tt.newRule, &op)
			if err != nil {
				t.Errorf("combineRules() error = %v", err)
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("combineRules() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCombineRules_OR(t *testing.T) {
	tests := []struct {
		name         string
		existingRule any
		newRule      any
		expected     any
	}{
		{
			name:         "two simple rules",
			existingRule: fieldA,
			newRule:      fieldB,
			expected:     orRule(fieldA, fieldB),
		},
		{
			name:         "identical rules - deduplication",
			existingRule: fieldA,
			newRule:      fieldA,
			expected:     fieldA, // Single condition optimization
		},
		{
			name:         "existing OR + simple rule",
			existingRule: orRule(fieldA, fieldB),
			newRule:      fieldC,
			expected:     orRule(fieldA, fieldB, fieldC),
		},
		{
			name:         "simple + existing OR",
			existingRule: fieldA,
			newRule:      orRule(fieldB, fieldC),
			expected:     orRule(fieldA, fieldB, fieldC),
		},
		{
			name:         "two OR rules with overlap",
			existingRule: orRule(fieldA, fieldB),
			newRule:      orRule(fieldA, fieldC), // fieldA overlaps
			expected:     orRule(fieldA, fieldB, fieldC),
		},
		{
			name:         "identical OR rules",
			existingRule: orRule(fieldA, fieldB),
			newRule:      orRule(fieldA, fieldB),
			expected:     orRule(fieldA, fieldB),
		},
		{
			name:         "AND rule treated as single condition",
			existingRule: andRule(fieldA, fieldB),
			newRule:      fieldC,
			expected:     orRule(andRule(fieldA, fieldB), fieldC),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			op := LogicalOperatorOr
			result, err := combineRules(context.Background(), tt.existingRule, tt.newRule, &op)
			if err != nil {
				t.Errorf("combineRules() error = %v", err)
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("combineRules() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCombineRules_EdgeCases(t *testing.T) {
	tests := []struct {
		name         string
		existingRule any
		newRule      any
		operator     LogicalOperator
		expectError  bool
	}{
		{
			name:         "nil rules with AND",
			existingRule: nil,
			newRule:      nil,
			operator:     LogicalOperatorAnd,
			expectError:  false,
		},
		{
			name:         "empty map rules",
			existingRule: map[string]interface{}{},
			newRule:      fieldA,
			operator:     LogicalOperatorAnd,
			expectError:  false,
		},
		{
			name:         "string rules",
			existingRule: "rule1",
			newRule:      "rule2",
			operator:     LogicalOperatorOr,
			expectError:  false,
		},
		{
			name:         "mixed types",
			existingRule: fieldA,
			newRule:      "string rule",
			operator:     LogicalOperatorAnd,
			expectError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				if r := recover(); r != nil {
					t.Errorf("combineRules() panicked: %v", r)
				}
			}()

			result, err := combineRules(context.Background(), tt.existingRule, tt.newRule, &tt.operator)
			if (err != nil) != tt.expectError {
				t.Errorf("combineRules() error = %v, expectError %v", err, tt.expectError)
			}
			if !tt.expectError && result == nil && (tt.existingRule != nil || tt.newRule != nil) {
				t.Errorf("combineRules() returned nil result without error")
			}
		})
	}
}

func TestCombineRules_ErrorHandling(t *testing.T) {
	tests := []struct {
		name         string
		existingRule any
		newRule      any
		operator     *LogicalOperator
		expectError  bool
		expectedMsg  string
	}{
		{
			name:         "nil operator",
			existingRule: fieldA,
			newRule:      fieldB,
			operator:     nil,
			expectError:  true,
			expectedMsg:  "logical operator is required for rule combination",
		},
		{
			name:         "valid operator",
			existingRule: fieldA,
			newRule:      fieldB,
			operator:     func() *LogicalOperator { op := LogicalOperatorAnd; return &op }(),
			expectError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := combineRules(context.Background(), tt.existingRule, tt.newRule, tt.operator)

			if tt.expectError {
				if err == nil {
					t.Errorf("combineRules() expected error but got none")
				} else if err.Error() != tt.expectedMsg {
					t.Errorf("combineRules() error = %v, want %v", err.Error(), tt.expectedMsg)
				}
				if result != nil {
					t.Errorf("combineRules() should return nil result on error")
				}
			} else {
				if err != nil {
					t.Errorf("combineRules() unexpected error = %v", err)
				}
				if result == nil {
					t.Errorf("combineRules() should return non-nil result on success")
				}
			}
		})
	}
}

func TestCombineRules_SingleConditionOptimization(t *testing.T) {
	tests := []struct {
		name         string
		existingRule any
		newRule      any
		operator     LogicalOperator
		expected     any
	}{
		{
			name:         "AND: identical simple rules",
			existingRule: fieldA,
			newRule:      fieldA,
			operator:     LogicalOperatorAnd,
			expected:     fieldA,
		},
		{
			name:         "OR: identical simple rules",
			existingRule: fieldA,
			newRule:      fieldA,
			operator:     LogicalOperatorOr,
			expected:     fieldA,
		},
		{
			name:         "AND: identical complex rules",
			existingRule: andRule(fieldA),
			newRule:      andRule(fieldA),
			operator:     LogicalOperatorAnd,
			expected:     fieldA, // Extracted from wrapper
		},
		{
			name:         "OR: identical complex rules",
			existingRule: orRule(fieldA),
			newRule:      orRule(fieldA),
			operator:     LogicalOperatorOr,
			expected:     fieldA, // Extracted from wrapper
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := combineRules(context.Background(), tt.existingRule, tt.newRule, &tt.operator)
			if err != nil {
				t.Errorf("combineRules() error = %v", err)
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("combineRules() = %v, want %v", result, tt.expected)
			}

			// Verify single conditions are NOT wrapped
			if resultMap, ok := result.(map[string]interface{}); ok {
				if conditions, hasOp := resultMap[string(tt.operator)]; hasOp {
					if slice, ok := conditions.([]interface{}); ok && len(slice) == 1 {
						t.Errorf("Single condition should not be wrapped in %s: %v", tt.operator, result)
					}
				}
			}
		})
	}
}

func TestCommonAdditionalRules_DataIntegrity(t *testing.T) {
	if len(commonAdditionalRules) == 0 {
		t.Error("commonAdditionalRules should not be empty")
	}

	// Test specific rules exist
	expectedRules := []string{
		"NIS IM 020 05 25",
		"NIS IM DS 001 05 25",
		"IL 09 52 01 15",
		"CA 22 64 10 13",
		"CSA 12 23",
	}

	for _, rule := range expectedRules {
		if _, exists := commonAdditionalRules[rule]; !exists {
			t.Errorf("Expected rule %s not found in commonAdditionalRules", rule)
		}
	}

	// Test that rules have valid structure
	for formCode, rule := range commonAdditionalRules {
		if rule.when == nil && rule.scheduleTypeToAdd == nil &&
			rule.applicableCoveragePackage == nil && rule.formTemplateType == nil &&
			rule.statesIncluded == nil && rule.programType == nil {
			t.Errorf("Rule for %s has no valid fields set", formCode)
		}
	}
}

func TestPredefinedRules(t *testing.T) {
	expectedMTCRule := map[string]interface{}{
		"eq": []interface{}{
			"F.IsMTCRatingModelV2",
			true,
		},
	}

	if !reflect.DeepEqual(IsMTCRatingModelV2, expectedMTCRule) {
		t.Errorf("IsMTCRatingModelV2 rule structure is incorrect")
	}

	expectedNotMTCRule := map[string]interface{}{
		"eq": []interface{}{
			"F.IsMTCRatingModelV2",
			false,
		},
	}

	if !reflect.DeepEqual(IsNotMTCRatingModelV2, expectedNotMTCRule) {
		t.Errorf("IsNotMTCRatingModelV2 rule structure is incorrect")
	}

	// Test camera subsidy conditions have proper structure
	if cameraSubsidyConditionKeepTruckin["and"] == nil {
		t.Error("cameraSubsidyConditionKeepTruckin should have 'and' field")
	}

	if cameraSubsidyConditionNotKeepTruckin["and"] == nil {
		t.Error("cameraSubsidyConditionNotKeepTruckin should have 'and' field")
	}
}

func TestCombineRules_ComplexCombinations(t *testing.T) {
	tests := []struct {
		name         string
		existingRule any
		newRule      any
		operator     LogicalOperator
		expected     any
		description  string
	}{
		// AND + AND combinations
		{
			name:         "AND rule + AND rule (no overlap)",
			existingRule: andRule(fieldA, fieldB),
			newRule:      andRule(fieldC, fieldD),
			operator:     LogicalOperatorAnd,
			expected:     andRule(fieldA, fieldB, fieldC, fieldD),
			description:  "Should flatten both AND rules into single AND",
		},
		{
			name:         "AND rule + AND rule (with overlap)",
			existingRule: andRule(fieldA, fieldB),
			newRule:      andRule(fieldB, fieldC), // fieldB overlaps
			operator:     LogicalOperatorAnd,
			expected:     andRule(fieldA, fieldB, fieldC),
			description:  "Should deduplicate overlapping conditions",
		},
		{
			name:         "multi-condition AND + multi-condition AND",
			existingRule: andRule(fieldA, fieldB, fieldC),
			newRule:      andRule(fieldC, fieldD, gtRule("F.FieldE", 20.0)),
			operator:     LogicalOperatorAnd,
			expected:     andRule(fieldA, fieldB, fieldC, fieldD, gtRule("F.FieldE", 20.0)),
			description:  "Should handle multiple conditions with partial overlap",
		},

		// OR + OR combinations
		{
			name:         "OR rule + OR rule (no overlap)",
			existingRule: orRule(fieldA, fieldB),
			newRule:      orRule(fieldC, fieldD),
			operator:     LogicalOperatorOr,
			expected:     orRule(fieldA, fieldB, fieldC, fieldD),
			description:  "Should flatten both OR rules into single OR",
		},
		{
			name:         "OR rule + OR rule (with overlap)",
			existingRule: orRule(fieldA, fieldB),
			newRule:      orRule(fieldB, fieldC), // fieldB overlaps
			operator:     LogicalOperatorOr,
			expected:     orRule(fieldA, fieldB, fieldC),
			description:  "Should deduplicate overlapping conditions in OR",
		},

		// Mixed operator combinations with AND
		{
			name:         "AND rule + OR rule (combined with AND)",
			existingRule: andRule(fieldA, fieldB),
			newRule:      orRule(fieldC, fieldD),
			operator:     LogicalOperatorAnd,
			expected:     andRule(fieldA, fieldB, orRule(fieldC, fieldD)),
			description:  "Should treat OR rule as single condition in AND context",
		},
		{
			name:         "OR rule + AND rule (combined with AND)",
			existingRule: orRule(fieldA, fieldB),
			newRule:      andRule(fieldC, fieldD),
			operator:     LogicalOperatorAnd,
			expected:     andRule(orRule(fieldA, fieldB), fieldC, fieldD),
			description:  "Should treat OR rule as single condition, flatten AND rule",
		},

		// Mixed operator combinations with OR
		{
			name:         "AND rule + OR rule (combined with OR)",
			existingRule: andRule(fieldA, fieldB),
			newRule:      orRule(fieldC, fieldD),
			operator:     LogicalOperatorOr,
			expected:     orRule(andRule(fieldA, fieldB), fieldC, fieldD),
			description:  "Should treat AND rule as single condition, flatten OR rule",
		},
		{
			name:         "OR rule + AND rule (combined with OR)",
			existingRule: orRule(fieldA, fieldB),
			newRule:      andRule(fieldC, fieldD),
			operator:     LogicalOperatorOr,
			expected:     orRule(fieldA, fieldB, andRule(fieldC, fieldD)),
			description:  "Should treat AND rule as single condition in OR context",
		},

		// Complex nested structures
		{
			name:         "nested AND-OR + simple rule",
			existingRule: andRule(fieldA, orRule(fieldB, fieldC)),
			newRule:      fieldD,
			operator:     LogicalOperatorAnd,
			expected:     andRule(fieldA, orRule(fieldB, fieldC), fieldD),
			description:  "Should handle nested structures correctly",
		},
		{
			name:         "nested OR-AND + simple rule",
			existingRule: orRule(fieldA, andRule(fieldB, fieldC)),
			newRule:      fieldD,
			operator:     LogicalOperatorOr,
			expected:     orRule(fieldA, andRule(fieldB, fieldC), fieldD),
			description:  "Should handle nested OR-AND structures",
		},
		{
			name:         "simple + nested structure",
			existingRule: fieldA,
			newRule:      andRule(orRule(fieldB, fieldC), fieldD),
			operator:     LogicalOperatorAnd,
			expected:     andRule(fieldA, orRule(fieldB, fieldC), fieldD),
			description:  "Should add complex nested rule to simple rule",
		},

		// Edge cases with identical nested structures
		{
			name:         "identical nested AND-OR structures",
			existingRule: andRule(fieldA, orRule(fieldB, fieldC)),
			newRule:      andRule(fieldA, orRule(fieldB, fieldC)),
			operator:     LogicalOperatorAnd,
			expected:     andRule(fieldA, orRule(fieldB, fieldC)),
			description:  "Should deduplicate identical nested structures",
		},
		{
			name:         "partially overlapping nested structures",
			existingRule: andRule(fieldA, orRule(fieldB, fieldC)),
			newRule:      andRule(fieldA, orRule(fieldC, fieldD)),
			operator:     LogicalOperatorAnd,
			expected:     andRule(fieldA, orRule(fieldB, fieldC), orRule(fieldC, fieldD)),
			description:  "Should handle partial overlap in nested structures",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := combineRules(context.Background(), tt.existingRule, tt.newRule, &tt.operator)
			if err != nil {
				t.Errorf("combineRules() error = %v", err)
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("combineRules() = %v, want %v", result, tt.expected)
			}
			t.Logf("✓ %s", tt.description)
		})
	}
}

func TestCombineRules_RealWorldScenarios(t *testing.T) {
	// Test scenarios that might actually occur in the application
	mtcV2Rule := simpleRule("F.IsMTCRatingModelV2", true)
	cameraCountRule := gtRule("F.Camera.Count", 0.0)
	subsidyRule := gtRule("F.Camera.SubsidyAmount", 0.0)
	tspRule := simpleRule("F.TSPProvider", "TSPKeepTruckin")
	renewalRule := simpleRule("F.IsRenewal", true)
	powerUnitsRule := ltRule("F.NumberOfPowerUnits", 5.0)

	tests := []struct {
		name         string
		existingRule any
		newRule      any
		operator     LogicalOperator
		expected     any
		description  string
	}{
		{
			name:         "camera subsidy conditions",
			existingRule: andRule(cameraCountRule, subsidyRule),
			newRule:      tspRule,
			operator:     LogicalOperatorAnd,
			expected:     andRule(cameraCountRule, subsidyRule, tspRule),
			description:  "Adding TSP provider condition to camera subsidy rules",
		},
		{
			name:         "MTC rating model alternatives",
			existingRule: mtcV2Rule,
			newRule:      orRule(renewalRule, powerUnitsRule),
			operator:     LogicalOperatorOr,
			expected:     orRule(mtcV2Rule, renewalRule, powerUnitsRule),
			description:  "Multiple conditions that could trigger MTC behavior",
		},
		{
			name:         "complex business rule",
			existingRule: andRule(mtcV2Rule, orRule(cameraCountRule, subsidyRule)),
			newRule:      andRule(renewalRule, powerUnitsRule),
			operator:     LogicalOperatorAnd,
			expected:     andRule(mtcV2Rule, orRule(cameraCountRule, subsidyRule), renewalRule, powerUnitsRule),
			description:  "Complex nested rule with additional AND conditions",
		},
		{
			name:         "fallback conditions",
			existingRule: orRule(andRule(mtcV2Rule, cameraCountRule), renewalRule),
			newRule:      andRule(powerUnitsRule, tspRule),
			operator:     LogicalOperatorOr,
			expected:     orRule(andRule(mtcV2Rule, cameraCountRule), renewalRule, andRule(powerUnitsRule, tspRule)),
			description:  "Adding fallback condition to existing fallback logic",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := combineRules(context.Background(), tt.existingRule, tt.newRule, &tt.operator)
			if err != nil {
				t.Errorf("combineRules() error = %v", err)
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("combineRules() = %v, want %v", result, tt.expected)
			}
			t.Logf("✓ %s", tt.description)
		})
	}
}
