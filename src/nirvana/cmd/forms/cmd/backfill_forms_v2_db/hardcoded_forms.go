package backfill_forms_v2_db

import (
	"context"

	"nirvanatech.com/nirvana/api-server/handlers/forms"

	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"

	"github.com/spf13/cobra"

	oapi_forms "nirvanatech.com/nirvana/openapi-specs/components/forms"

	"nirvanatech.com/nirvana/api-server/interceptors/forms/deps"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
)

const hardcodedFormsV2DB = "hardcodedFormsV2Db"

var HardcodedFormsV2DBCmd = cobrafx.NewCLI(
	&cobra.Command{
		Use:   hardcodedFormsV2DB,
		Short: "Hardcoded forms v2 db",
	},
	runHardcodedFormsBackfill,
)

func runHardcodedFormsBackfill(cmd *cobra.Command, args []string, e env) error {
	ctx := cmd.Context()
	upsertReactForms(ctx, e.Deps)
	log.Info(ctx, "Starting CLI job")
	return nil
}

func upsertReactForms(ctx context.Context, deps deps.Deps) {
	appFS := oapi_forms.CreateFormScheduleRequest{
		FullFormCode:     "App PDF",
		FormCode:         "App PDF",
		FormName:         "Application PDF",
		OrderCategory:    oapi_forms.M,
		FormTemplateType: oapi_forms.ComposedPDF,
		ApplicabilityRules: []oapi_forms.FormApplicabilityRule{{
			CoreRule: oapi_forms.FormApplicabilityCoreRule{
				ApplicableCoveragePackages: &[]oapi_forms.CoveragePackagePair{{
					CoverageType: oapi_common.CoverageAutoLiability,
					PackageType:  oapi_forms.AllPackages,
				}},
				FormCompilationTypes: &[]oapi_forms.FormType{oapi_forms.FormTypeSignaturePacket},
				ProgramTypes: &[]oapi_common.ProgramType{
					oapi_common.ProgramTypeFleet,
					oapi_common.ProgramTypeNonFleetAdmitted,
				},
			},
			ScheduleType: &[]oapi_forms.ScheduleType{oapi_forms.ScheduleTypeCore},
		}},
		Metadata: &oapi_forms.FormScheduleMetadata{
			"IsDynamic": true,
		},
	}

	quoteFS := oapi_forms.CreateFormScheduleRequest{
		FullFormCode:     "Quote PDF",
		FormCode:         "Quote PDF",
		FormName:         "Quote PDF",
		OrderCategory:    oapi_forms.A,
		FormTemplateType: oapi_forms.ComposedPDF,
		ApplicabilityRules: []oapi_forms.FormApplicabilityRule{{
			CoreRule: oapi_forms.FormApplicabilityCoreRule{
				ApplicableCoveragePackages: &[]oapi_forms.CoveragePackagePair{{
					CoverageType: oapi_common.CoverageAutoLiability,
					PackageType:  oapi_forms.AllPackages,
				}},
				FormCompilationTypes: &[]oapi_forms.FormType{oapi_forms.FormTypeSignaturePacket},
				ProgramTypes: &[]oapi_common.ProgramType{
					oapi_common.ProgramTypeFleet,
					oapi_common.ProgramTypeNonFleetAdmitted,
					oapi_common.ProgramTypeBusinessAuto,
				},
			},
			ScheduleType: &[]oapi_forms.ScheduleType{oapi_forms.ScheduleTypeCore},
		}},
		Metadata: &oapi_forms.FormScheduleMetadata{
			"IsDynamic": true,
		},
	}

	affidavitFS := oapi_forms.CreateFormScheduleRequest{
		FullFormCode:     "Affidavit PDF",
		FormCode:         "Affidavit PDF",
		FormName:         "Affidavit PDF",
		OrderCategory:    oapi_forms.N,
		FormTemplateType: oapi_forms.PDF,
		ApplicabilityRules: []oapi_forms.FormApplicabilityRule{{
			CoreRule: oapi_forms.FormApplicabilityCoreRule{
				ApplicableCoveragePackages: &[]oapi_forms.CoveragePackagePair{{
					CoverageType: oapi_common.CoverageAutoLiability,
					PackageType:  oapi_forms.AllPackages,
				}},
				FormCompilationTypes: &[]oapi_forms.FormType{oapi_forms.FormTypeSignaturePacket},
				ProgramTypes:         &[]oapi_common.ProgramType{oapi_common.ProgramTypeFleet},
			},
			ScheduleType: &[]oapi_forms.ScheduleType{oapi_forms.ScheduleTypeCore},
		}},
		Metadata: &oapi_forms.FormScheduleMetadata{
			"IsDynamic": true,
		},
	}

	allForms := []oapi_forms.CreateFormScheduleRequest{appFS, quoteFS, affidavitFS}

	for _, form := range allForms {
		request := forms.PostFormScheduleRequest{
			FormScheduleRequest: form,
		}
		_, err := createFormScheduleInDB(ctx, request, deps)
		if err != nil {
			log.Fatal(ctx, "unable to create form schedule",
				log.Err(err),
				log.String("fullFormCode", form.FullFormCode),
				log.String("formCode", form.FormCode))
		}
	}
}
