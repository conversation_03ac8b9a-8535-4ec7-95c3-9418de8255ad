package backfill_forms_v2_db

import (
	"context"
	"fmt"

	"nirvanatech.com/nirvana/db-api/db_wrappers/forms/formschedule"

	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"

	"nirvanatech.com/nirvana/common-go/map_utils"
	policyEnums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/policy_common/forms_generator/cmd"

	"github.com/cockroachdb/errors"
	"github.com/spf13/cobra"
	"go.uber.org/fx"
	"golang.org/x/exp/maps"

	"nirvanatech.com/nirvana/api-server/handlers/forms"
	"nirvanatech.com/nirvana/api-server/handlers/forms/transformer"
	"nirvanatech.com/nirvana/api-server/handlers/forms/validator"
	"nirvanatech.com/nirvana/api-server/interceptors/forms/deps"
	"nirvanatech.com/nirvana/common-go/coverage_utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms/enums"
	"nirvanatech.com/nirvana/forms/model"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_forms "nirvanatech.com/nirvana/openapi-specs/components/forms"
	"nirvanatech.com/nirvana/policy_common/constants"
	"nirvanatech.com/nirvana/policy_common/forms_generator/cmd/form_fetcher"
	fg_model "nirvanatech.com/nirvana/policy_common/forms_generator/cmd/model"
)

type env struct {
	fx.In
	deps.Deps
}

const backfillFormsV2DB = "backfillFormsV2Db"

var BackfillFormsV2DBCmd = cobrafx.NewCLI(
	&cobra.Command{
		Use:   backfillFormsV2DB,
		Short: "Backfill forms v2 db",
	},
	run,
)

func run(cmd *cobra.Command, args []string, e env) error {
	ctx := cmd.Context()

	log.Info(ctx, "Starting CLI job")

	err := runBackfillFormsV2DB(ctx, e)
	if err != nil {
		log.Fatal(ctx, "unable to backfill forms v2 db", log.Err(err))
	}
	log.Info(ctx, "Finish CLI job successfully")
	return nil
}

func runBackfillFormsV2DB(
	ctx context.Context,
	e env,
) error {
	allForms := make(map[string]forms.PostFormScheduleRequest)
	for _, appType := range getSupportedApplicationTypes() {
		coverages := coverage_utils.GetCoverages(appType)

		for _, cov := range coverages {
			allFormsFromSheets, err := form_fetcher.GetAllForms(cov, appType)
			if err != nil {
				log.Error(ctx, "failed to fetch forms",
					log.Err(err),
					log.String("coverage", cov.String()),
					log.String("appType", appType.String()))
				return errors.Wrapf(
					err, "failed to fetch forms for coverage %s and app type %s", cov.String(), appType.String(),
				)
			}

			for _, form := range allFormsFromSheets {
				// skipping this form because it is an edge case and will handle this manually later.
				if form.FormCode == "FMCSA Form MCS-90" {
					continue
				}
				if _, ok := allForms[form.FormCode]; !ok {
					formScheduleRequest, err := createFormScheduleRequest(ctx, appType, form, cov)
					if err != nil {
						log.Error(ctx, "failed to create form schedule request",
							log.Err(err),
							log.String("formCode", form.FormCode),
							log.String("appType", appType.String()),
							log.String("coverage", cov.String()))
						return errors.Wrapf(
							err, "failed to create form schedule request for form %s with app type %s and coverage %s",
							form.FormCode, appType.String(), cov.String(),
						)
					}
					allForms[form.FormCode] = formScheduleRequest
				} else {
					// update existing formScheduleRequest
					formScheduleRequest := allForms[form.FormCode]
					allForms[form.FormCode] = updateFormScheduleRequest(ctx, appType, form, cov, formScheduleRequest)
				}

			}
		}
	}

	for _, form := range allForms {
		// Set Active or Inactive
		form = setStatus(form)

		// Merge applicability rules
		if len(form.FormScheduleRequest.ApplicabilityRules) > 1 {
			var err error
			form, err = mergeApplicabilityRules(ctx, form)
			if err != nil {
				log.Fatal(ctx, "unable to merge applicability rules",
					log.Err(err),
					log.String("fullFormCode", form.FormScheduleRequest.FullFormCode),
					log.Int("rulesCount", len(form.FormScheduleRequest.ApplicabilityRules)))
			}
		}

		form = markStatesListAsAllIfAllStatesAreIncluded(ctx, form)

		_, err := createFormScheduleInDB(ctx, form, e.Deps)
		if err != nil {
			log.Fatal(ctx, "unable to create form schedule",
				log.Err(err),
				log.String("fullFormCode", form.FormScheduleRequest.FullFormCode))
			return errors.Wrapf(err, "failed to create form schedule for %s", form.FormScheduleRequest.FullFormCode)
		}
	}

	return nil
}

func markStatesListAsAllIfAllStatesAreIncluded(ctx context.Context, form forms.PostFormScheduleRequest) forms.PostFormScheduleRequest {
	for i, rule := range form.FormScheduleRequest.ApplicabilityRules {
		var includedStates []string
		var err error
		includedStates, err = transformer.GetStateArray(rule.CoreRule.IncludedStates)
		if err != nil {
			log.Fatal(ctx, "unable to get state array",
				log.Err(err),
				log.Any("includedStates", rule.CoreRule.IncludedStates))
		}
		if len(includedStates) == 0 {
			log.Fatal(ctx, "SHOULD NOT HAPPEN: states list is empty")
		}
		activeStates := getActiveStates(ctx, (*rule.CoreRule.ProgramTypes)[0])
		statesIncludesAll := slice_utils.EqualSorted(includedStates, activeStates)
		if statesIncludesAll {
			form.FormScheduleRequest.ApplicabilityRules[i].CoreRule.IncludedStates = &[]oapi_forms.USState{}
		}
		dedupIncludedAndExcludedStates(form.FormScheduleRequest.ApplicabilityRules[i].CoreRule.IncludedStates,
			form.FormScheduleRequest.ApplicabilityRules[i].CoreRule.ExcludedStates)
	}
	return form
}

func getActiveStates(ctx context.Context, programType oapi_common.ProgramType) []string {
	pt, err := policyEnums.ProgramTypeString(string(programType))
	if err != nil {
		log.Fatal(ctx, "unable to get program type",
			log.Err(err),
			log.String("programType", string(programType)))
	}
	if pt == policyEnums.ProgramTypeInvalid {
		log.Fatal(ctx, "invalid program type",
			log.String("programType", string(programType)))
	}
	states := map_utils.Keys(cmd.GetSupportedStates(pt))
	stateStr := make([]string, len(states))
	for i, state := range states {
		stateStr[i] = state.ToCode()
	}
	stateStr = slice_utils.Dedup(stateStr)
	return stateStr
}

func mergeApplicabilityRules(ctx context.Context, form forms.PostFormScheduleRequest) (forms.PostFormScheduleRequest, error) {
	// nolint: typecheck
	applicabilityRules, err := deepCopy(ctx, form.FormScheduleRequest.ApplicabilityRules)
	if err != nil {
		return forms.PostFormScheduleRequest{}, errors.Wrapf(
			err, "failed to deep copy applicability rules for form %s", form.FormScheduleRequest.FullFormCode,
		)
	}

	allRulesMap := make(map[string]oapi_forms.FormApplicabilityRule)
	for _, rule := range applicabilityRules {
		key := generateRuleHash(rule)
		if existing, ok := allRulesMap[key]; ok {
			mergedRule, err := mergeApplicabilityRule(ctx, existing, rule)
			if err != nil {
				return forms.PostFormScheduleRequest{}, errors.Wrapf(
					err, "failed to merge applicability rule for form %s with key %s",
					form.FormScheduleRequest.FullFormCode, key,
				)
			}
			allRulesMap[generateRuleHash(rule)] = mergedRule
		} else {
			allRulesMap[generateRuleHash(rule)] = rule
		}
	}

	mergedRules := maps.Values(allRulesMap)
	form.FormScheduleRequest.ApplicabilityRules = mergedRules
	return form, err
}

func createFormScheduleInDB(ctx context.Context, req forms.PostFormScheduleRequest, deps deps.Deps) (*model.FormSchedule, error) {
	err := validator.ValidateFormScheduleRequest(&req.FormScheduleRequest)
	if err != nil {
		return nil, errors.Wrap(err, "invalid form schedule request")
	}

	formSchedule, err := transformer.GetFormScheduleFromCreateRequest(ctx, &req.FormScheduleRequest)
	if err != nil {
		return nil, errors.Wrap(err, "failed to transform form schedule request")
	}

	if overrideMethod, exists := getFormApplicabilityRulesOverride()[formSchedule.FullFormCode]; exists {
		formSchedule = overrideMethod(ctx, formSchedule)
	}

	if overrideMethod, exists := getFormTemplateTypeOverride()[formSchedule.FullFormCode]; exists {
		formSchedule = overrideMethod(ctx, formSchedule)
	}

	err = deps.FormScheduleManager.CreateFormSchedule(ctx, formSchedule)
	if err != nil {
		if errors.Is(err, formschedule.ErrFormScheduleAlreadyExists) {
			log.Info(ctx, fmt.Sprintf("form schedule already exists, updating form schedule %s", formSchedule.FullFormCode))
			err = updateFormScheduleInDB(ctx, deps, *formSchedule)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to update form schedule %s", formSchedule.FullFormCode)
			}
		} else {
			return nil, errors.Wrapf(err, "failed to create form schedule %s", formSchedule.FullFormCode)
		}
	}
	return formSchedule, nil
}

func updateFormScheduleInDB(ctx context.Context, deps deps.Deps, fs model.FormSchedule) error {
	updateFSReq := convertCreateFSReqToUpdateFSReq(fs)
	err := deps.FormScheduleManager.UpdateFormSchedule(ctx, &updateFSReq)
	if err != nil {
		return errors.Wrapf(err, "failed to update form schedule %s", fs.FullFormCode)
	}
	return nil
}

func convertCreateFSReqToUpdateFSReq(fs model.FormSchedule) model.UpdateFormScheduleRequest {
	return model.UpdateFormScheduleRequest{
		ApplicabilityRules: fs.ApplicabilityRules,
		Description:        fs.Description,
		FormName:           &fs.FormName,
		FullFormCode:       fs.FullFormCode,
		Metadata:           fs.Metadata,
		OrderCategory:      &fs.OrderCategory,
		Status:             &fs.Status,
		FillTemplate:       fs.FillTemplate,
	}
}

func createFormScheduleRequest(
	ctx context.Context,
	appType constants.ApplicationType,
	form fg_model.ScheduledFormsMatrixRow,
	cov app_enums.Coverage,
) (forms.PostFormScheduleRequest, error) {
	var formScheduleRequest oapi_forms.CreateFormScheduleRequest
	formScheduleRequest.FormName = form.FormName
	formScheduleRequest.FullFormCode = form.FormCode
	formScheduleRequest.FormCode = form.QuoteDisplay
	formScheduleRequest.OrderCategory = oapi_forms.OrderCategory(form.OrderCategory.String())
	formScheduleRequest.FormTemplateType = oapi_forms.FormTemplateType(enums.FormTemplateTypePDF.String())
	metadata := map[string]interface{}{
		"IsDynamic": form.IsDynamic,
	}
	formScheduleRequest.Metadata = &metadata

	applicabilityRule, err := getApplicabilityRule(ctx, appType, form, cov)
	if err != nil {
		log.Fatal(ctx, "unable to get applicability rules",
			log.Err(err),
			log.String("formCode", form.FormCode),
			log.String("appType", appType.String()),
			log.String("coverage", cov.String()))
		return forms.PostFormScheduleRequest{}, errors.Wrapf(
			err, "failed to get applicability rule for form %s with app type %s and coverage %s",
			form.FormCode, appType.String(), cov.String(),
		)
	}
	if applicabilityRule != nil {
		formScheduleRequest.ApplicabilityRules = []oapi_forms.FormApplicabilityRule{*applicabilityRule}
	}

	// Handle fill template creation
	if template, hasTemplate := createFillTemplateForForm(appType, form); hasTemplate {
		formScheduleRequest.FillTemplate = &oapi_forms.FillTemplate{
			Templates: &[]oapi_forms.Template{*template},
		}
	} else {
		log.Warn(ctx, "no field config found for form code",
			log.String("formCode", form.FormCode),
			log.String("appType", appType.String()))
	}
	return forms.PostFormScheduleRequest{
		FormScheduleRequest: formScheduleRequest,
	}, nil
}

func populateOAPIConfigFromFieldConfig(
	fieldConfig map[string]model.Field,
) *oapi_forms.Configs {
	var oapiConfig oapi_forms.Configs
	for formFieldName, field := range fieldConfig {
		configField := oapi_forms.ConfigField{
			Source: oapi_forms.ConfigFieldSource{
				Path: field.Source.Key,
			},
			Validation: &oapi_forms.ConfigFieldValidation{
				Required: field.Validation.Required,
			},
		}
		if oapiConfig == nil {
			oapiConfig = make(oapi_forms.Configs, 0)
		}
		oapiConfig = append(
			oapiConfig,
			oapi_forms.Config{
				Field:            configField,
				FormFieldVarName: formFieldName,
			},
		)
	}
	return &oapiConfig
}

// createFillTemplateForForm creates a fill template for the given form if field config exists
func createFillTemplateForForm(
	appType constants.ApplicationType,
	form fg_model.ScheduledFormsMatrixRow,
) (*oapi_forms.Template, bool) {
	var fieldConfig map[string]model.Field
	var hasFieldConfig bool

	if appType != constants.ApplicationTypeBusinessAuto {
		fieldConfig, hasFieldConfig = FormCodeToFieldConfigs[form.FormCode]
	} else {
		fieldConfig, hasFieldConfig = BusinessAutoFormCodeToFieldConfigs[form.FormCode]
	}

	if !hasFieldConfig {
		return nil, false
	}

	oapiFieldConfig := populateOAPIConfigFromFieldConfig(fieldConfig)
	programType := getProgramTypeFromApplicationType(appType)

	template := &oapi_forms.Template{
		Applicability: &oapi_forms.Applicability{
			ProgramType: []oapi_common.ProgramType{programType},
		},
		Config: oapiFieldConfig,
	}

	return template, true
}

// templatesHaveSameConfig compares two templates to see if they have identical configurations
func templatesHaveSameConfig(t1, t2 oapi_forms.Template) bool {
	// Compare configs - both should be non-nil for a valid comparison
	if t1.Config == nil || t2.Config == nil {
		return t1.Config == t2.Config
	}

	config1 := *t1.Config
	config2 := *t2.Config

	if len(config1) != len(config2) {
		return false
	}

	// Create maps for easier comparison
	configMap1 := make(map[string]oapi_forms.Config)
	configMap2 := make(map[string]oapi_forms.Config)

	for _, cfg := range config1 {
		configMap1[cfg.FormFieldVarName] = cfg
	}

	for _, cfg := range config2 {
		configMap2[cfg.FormFieldVarName] = cfg
	}

	// Compare each config
	for fieldName, cfg1 := range configMap1 {
		cfg2, exists := configMap2[fieldName]
		if !exists {
			return false
		}

		// Compare source paths
		if cfg1.Field.Source.Path != cfg2.Field.Source.Path {
			return false
		}

		// Compare validation requirements
		if (cfg1.Field.Validation == nil) != (cfg2.Field.Validation == nil) {
			return false
		}

		if cfg1.Field.Validation != nil && cfg2.Field.Validation != nil {
			if cfg1.Field.Validation.Required != cfg2.Field.Validation.Required {
				return false
			}
		}
	}

	return true
}

// addProgramTypeToTemplate adds a program type to a template's applicability if not already present
func addProgramTypeToTemplate(template *oapi_forms.Template, programType oapi_common.ProgramType) {
	if template.Applicability == nil {
		template.Applicability = &oapi_forms.Applicability{
			ProgramType: []oapi_common.ProgramType{programType},
		}
		return
	}

	// Check if program type already exists
	for _, existingType := range template.Applicability.ProgramType {
		if existingType == programType {
			return // Already exists, no need to add
		}
	}

	// Add the new program type
	template.Applicability.ProgramType = append(template.Applicability.ProgramType, programType)
}

func updateFormScheduleRequest(
	ctx context.Context,
	appType constants.ApplicationType,
	form fg_model.ScheduledFormsMatrixRow,
	cov app_enums.Coverage,
	formScheduleRequest forms.PostFormScheduleRequest,
) forms.PostFormScheduleRequest {
	applicabilityRule, err := getApplicabilityRule(ctx, appType, form, cov)
	if err != nil {
		log.Fatal(ctx, "unable to update applicability rules",
			log.Err(err),
			log.String("formCode", form.FormCode),
			log.String("appType", appType.String()),
			log.String("coverage", cov.String()))
	}
	if applicabilityRule != nil {
		formScheduleRequest.FormScheduleRequest.ApplicabilityRules = append(
			formScheduleRequest.FormScheduleRequest.ApplicabilityRules,
			*applicabilityRule,
		)
	}

	// Handle fill template backfilling
	if template, hasTemplate := createFillTemplateForForm(appType, form); hasTemplate {
		programType := (*template.Applicability).ProgramType[0]

		if formScheduleRequest.FormScheduleRequest.FillTemplate == nil {
			// Create new fill template if none exists
			formScheduleRequest.FormScheduleRequest.FillTemplate = &oapi_forms.FillTemplate{
				Templates: &[]oapi_forms.Template{*template},
			}
		} else {
			// Check existing templates to see if any have the same configuration
			existingTemplates := *formScheduleRequest.FormScheduleRequest.FillTemplate.Templates
			templateMerged := false

			for i := range existingTemplates {
				if templatesHaveSameConfig(existingTemplates[i], *template) {
					// Same configuration found, add program type to existing template
					log.Info(ctx, "merging templates with same configuration",
						log.String("formCode", form.FormCode),
						log.String("programType", string(programType)))
					addProgramTypeToTemplate(&existingTemplates[i], programType)
					templateMerged = true
					break
				}
			}

			if !templateMerged {
				// No matching configuration found, add new template
				existingTemplates = append(existingTemplates, *template)
			}

			formScheduleRequest.FormScheduleRequest.FillTemplate.Templates = &existingTemplates
		}
	} else {
		log.Warn(ctx, "no field config found for form code during update",
			log.String("formCode", form.FormCode),
			log.String("appType", appType.String()))
	}

	return formScheduleRequest
}

// If states are included in both includedStates and excludedStates, remove state from includedStates
func dedupIncludedAndExcludedStates(includedStates, excludedStates *[]oapi_common.USState) {
	if includedStates == nil || excludedStates == nil {
		return
	}
	var dedupedIncludedStates []oapi_common.USState
	for _, includedState := range *includedStates {
		if !isStateInList(includedState, excludedStates) {
			dedupedIncludedStates = append(dedupedIncludedStates, includedState)
		}
	}
	*includedStates = dedupedIncludedStates
}

func updateNISIMDS0020525(ctx context.Context, fs *model.FormSchedule) *model.FormSchedule {
	fs.FormTemplateType = enums.FormTemplateTypeComposedPDF
	return fs
}

func updateNISILDS0020124(ctx context.Context, fs *model.FormSchedule) *model.FormSchedule {
	fs.FormTemplateType = enums.FormTemplateTypeComposedPDF
	return fs
}

func updateCADS211120(ctx context.Context, fs *model.FormSchedule) *model.FormSchedule {
	rules := fs.ApplicabilityRules
	for i, rule := range rules {
		if slice_utils.Contains(rule.CoreRule.ProgramTypes, policyEnums.ProgramTypeFleet) {
			rules[i].CoreRule.ApplicableInsuranceCarriers = []constants.InsuranceCarrier{
				constants.InsuranceCarrierFalseLake,
			}
			rules[i].CoreRule.ExcludedStates = []string{
				us_states.AL.ToCode(),
				us_states.MI.ToCode(),
				us_states.CA.ToCode(),
			}
			rules[i].CoreRule.FormCompilationTypes = []compilation.CompilationType{compilation.CompilationTypePolicy}
			rules[i].ScheduleType = []compilation.ScheduleType{compilation.ScheduleTypeCore}

			mstRule, err := deepCopy(ctx, rules[i])
			if err != nil {
				log.Fatal(ctx, "unable to copy applicability rule for CADS211120",
					log.Err(err),
					log.Int("ruleIndex", i))
			}
			mstRule.CoreRule.ApplicableInsuranceCarriers = []constants.InsuranceCarrier{
				constants.InsuranceCarrierMSTransverse,
			}
			mstRule.CoreRule.ExcludedStates = []string{
				us_states.MI.ToCode(),
				us_states.PA.ToCode(),
				us_states.CA.ToCode(),
			}
			rules = append(rules, mstRule)
		}
	}
	fs.ApplicabilityRules = rules
	return fs
}

type formApplicabilityRulesOverrideFn func(ctx context.Context, formSchedule *model.FormSchedule) *model.FormSchedule

func getFormApplicabilityRulesOverride() map[string]formApplicabilityRulesOverrideFn {
	return map[string]formApplicabilityRulesOverrideFn{
		"CA DS 21 11 20": updateCADS211120,
	}
}

func getFormTemplateTypeOverride() map[string]formApplicabilityRulesOverrideFn {
	return map[string]formApplicabilityRulesOverrideFn{
		"NIS IM DS 002 05 25": updateNISIMDS0020525,
		"NIS IL DS 002 01 24": updateNISILDS0020124,
	}
}
