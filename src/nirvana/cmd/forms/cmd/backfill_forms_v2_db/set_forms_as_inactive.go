package backfill_forms_v2_db

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/spf13/cobra"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms/enums"
	"nirvanatech.com/nirvana/forms/model"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
	fl_core "nirvanatech.com/nirvana/policy_common/forms_generator/fill/fleet/core_forms"
	fl_manual "nirvanatech.com/nirvana/policy_common/forms_generator/fill/fleet/manual_forms"
	fl_package "nirvanatech.com/nirvana/policy_common/forms_generator/fill/fleet/package_forms"
	fl_signed "nirvanatech.com/nirvana/policy_common/forms_generator/fill/fleet/signed_quote_forms"
	fl_state "nirvanatech.com/nirvana/policy_common/forms_generator/fill/fleet/state_forms"
	nfa_core "nirvanatech.com/nirvana/policy_common/forms_generator/fill/non_fleet_admitted/core_forms"
	nfa_manual "nirvanatech.com/nirvana/policy_common/forms_generator/fill/non_fleet_admitted/manual_forms"
	nfa_package "nirvanatech.com/nirvana/policy_common/forms_generator/fill/non_fleet_admitted/package_forms"
	nfa_signed "nirvanatech.com/nirvana/policy_common/forms_generator/fill/non_fleet_admitted/signed_quote_forms"
)

const setFormsAsInactive = "setFormsAsInactive"

var SetFormsAsInactiveCmd = cobrafx.NewCLI(
	&cobra.Command{
		Use:   setFormsAsInactive,
		Short: "Set forms without generator function as inactive",
	},
	runner,
)

func runner(cmd *cobra.Command, args []string, e env) error {
	ctx := cmd.Context()

	log.Info(ctx, "Starting CLI job")

	err := runSetFormsAsInactive(ctx, e)
	if err != nil {
		log.Error(ctx, "Failed to run CLI job", log.Err(err))
		return errors.Wrap(err, "failed to run set forms as inactive CLI job")
	}
	log.Info(ctx, "Finish CLI job successfully")
	return nil
}

func getAllFormsWithGeneratorFns() map[string]bool {
	allForms := map[string]bool{}
	for _, forms := range fl_core.FillCoreFormsGeneratorFuncs {
		for form := range forms {
			allForms[form] = true
		}
	}
	for _, forms := range fl_state.FillStateGeneratorFuncs {
		for form := range forms {
			allForms[form] = true
		}
	}
	for _, forms := range fl_signed.FillSignedQuoteFormsGeneratorFuncs {
		for form := range forms {
			allForms[form] = true
		}
	}
	for _, forms := range fl_package.FillPackageGeneratorFuncs {
		for form := range forms {
			allForms[form] = true
		}
	}
	for _, forms := range fl_manual.FillManualFormsGeneratorFuncs {
		for form := range forms {
			allForms[form] = true
		}
	}
	for _, forms := range nfa_core.FillCoreFormsGeneratorFuncs {
		for form := range forms {
			allForms[form] = true
		}
	}
	for _, forms := range nfa_package.FillPackageGeneratorFuncs {
		for form := range forms {
			allForms[form] = true
		}
	}
	for _, forms := range nfa_signed.FillSignedQuoteFormsGeneratorFuncs {
		for form := range forms {
			allForms[form] = true
		}
	}
	for _, forms := range nfa_manual.FillManualFormsGeneratorFuncs {
		for form := range forms {
			allForms[form] = true
		}
	}
	return allForms
}

func runSetFormsAsInactive(ctx context.Context, e env) error {
	statusActive := enums.FormScheduleStateActive
	stateInactive := enums.FormScheduleStateInactive
	getReq := model.GetFormScheduleRequest{
		Status: &statusActive,
	}
	formSchedules, err := e.Deps.FormScheduleManager.GetFormSchedules(ctx, &getReq)
	if err != nil {
		return errors.Wrapf(err, "failed to get form schedules with status %s", statusActive.String())
	}
	formsWithGnFns := getAllFormsWithGeneratorFns()

	for _, formSchedule := range formSchedules {
		_, exists := formsWithGnFns[formSchedule.FullFormCode]
		if !exists {
			log.Info(ctx, fmt.Sprintf("Setting form %s as inactive", formSchedule.FullFormCode))
			err := e.Deps.FormScheduleManager.UpdateFormSchedule(
				ctx,
				&model.UpdateFormScheduleRequest{
					Status:       &stateInactive,
					FullFormCode: formSchedule.FullFormCode,
				},
			)
			if err != nil {
				log.Error(ctx, "Failed to update form schedule",
					log.Err(err),
					log.String("fullFormCode", formSchedule.FullFormCode),
					log.String("newStatus", stateInactive.String()))
			}
		}
	}
	return nil
}
