package backfill_forms_v2_db

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/spf13/cobra"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/forms/model"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
)

const backfillFillTemplate = "backfillFillTemplate"

var BackfillFillTemplateCmd = cobrafx.NewCLI(
	&cobra.Command{
		Use:   backfillFillTemplate,
		Short: "Backfill fill template",
	},
	runBackfillFillTemplate,
)

// WIP
// Do not use on production
func runBackfillFillTemplate(cmd *cobra.Command, args []string, e env) error {
	ctx := cmd.Context()
	log.Info(ctx, "Starting CLI job")

	for formCode, ok := range NFFormCodesUsingPolicyNumberAndCompanyConfig {
		if !ok {
			continue
		}
		err := updateFormSchedule(
			ctx,
			e,
			formCode,
			[]enums.ProgramType{enums.ProgramTypeNonFleetAdmitted},
			FormCodeToFieldConfigs[formCode],
		)
		if err != nil {
			log.Error(ctx, "Failed to update form schedule",
				log.Err(err),
				log.String("formCode", formCode))
			return errors.Wrapf(err, "failed to update form schedule for %s", formCode)
		}
		log.Info(ctx, "Updated form schedule", log.String("formCode", formCode))
	}

	return nil
}

func updateFormSchedule(
	ctx context.Context,
	e env,
	formCode string,
	progTypes []enums.ProgramType,
	config map[string]model.Field,
) error {
	err := e.FormScheduleManager.UpdateFormSchedule(
		ctx,
		&model.UpdateFormScheduleRequest{
			FullFormCode: formCode,
			FillTemplate: &model.FillTemplate{
				Templates: []model.Template{
					{
						Applicability: model.TemplateApplicability{
							ProgramType: progTypes,
						},
						Config: config,
					},
				},
			},
		},
	)
	if err != nil {
		log.Error(ctx, "Failed to update form schedule",
			log.Err(err),
			log.String("formCode", formCode),
			log.Any("programTypes", progTypes))
		return errors.Wrapf(err, "failed to update form schedule %s with program types %+v", formCode, progTypes)
	}
	return nil
}

var NFFormCodesUsingPolicyNumberAndCompanyConfig = map[string]bool{
	"CAP 70 09 00 NF 01 22": true,
	"CAP 04 02 00 NF 11 23": true,
	"CAP 70 90 00 NF 11 23": true,
	"CAP 71 02 00 NF 01 22": true,
	"CAP 70 84 00 NF 11 23": true,
	"CAP 70 85 00 NF 11 23": true,
}
