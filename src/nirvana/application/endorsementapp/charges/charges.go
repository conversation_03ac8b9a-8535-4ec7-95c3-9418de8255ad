package charges

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/application/endorsementapp"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/type_utils"
	appEnums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	"nirvanatech.com/nirvana/insurance-core/coverage"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func GetEndorsementWrittenPremium(
	ctx context.Context,
	insuranceBundleManagerClient service.InsuranceBundleManagerClient,
	pricingWrapper quoting.PricingWrapper,
	insuranceBundleInternalId uuid.UUID,
	pricingContextIds []uuid.UUID,
) (*endorsementapp.EndorsementWrittenPremium, error) {
	// 1. get insurance bundle
	insuranceBundle, err := insuranceBundleManagerClient.GetInsuranceBundle(ctx, &service.GetInsuranceBundleRequest{
		PrimaryFilter: &service.GetInsuranceBundleRequest_PrimaryFilter{
			Identifier: &service.GetInsuranceBundleRequest_PrimaryFilter_InternalId{
				InternalId: insuranceBundleInternalId.String(),
			},
		},
	})
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get insurance bundle with internalID: %s",
			insuranceBundleInternalId.String())
	}

	// 2. get quoting pricing context by pricing request ids
	quotingPricingContextIds := slice_utils.Map(pricingContextIds, func(prId uuid.UUID) string { return prId.String() })
	quotingPricingContexts, err := pricingWrapper.GetQuotingPricingContextByIds(ctx, quotingPricingContextIds)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get quoting pricing contexts")
	}

	// 3. calculate charge distribution before endorsement
	bundle := insuranceBundle.InsuranceBundle

	// useChargeAdjustments is set as false, as during endorsement price calculation, we don't want to consider charge
	// adjustments from the legacy system.
	preEndorsementChargeDistribution, err := bundle.GetChargesWithDistribution(nil, false)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get insurance bundle charges")
	}

	// 4. calculate charge distribution after endorsement
	postEndorsementChargeDistribution, err := getChargeDistributionFromQPC(quotingPricingContexts, nil)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get endorsement charges")
	}

	// 5. subtract pre-endorsement charges from post-endorsement charges to get endorsement fees
	endorsementFees := type_utils.GetValueOrDefault(postEndorsementChargeDistribution.TotalCharge, decimal.Zero).
		Sub(type_utils.GetValueOrDefault(preEndorsementChargeDistribution.TotalCharge, decimal.Zero))

	feeBreakdownByCoverage, err := getFeeBreakdownByCoverage(
		preEndorsementChargeDistribution.ChargesBySubCoverage,
		postEndorsementChargeDistribution.ChargesBySubCoverage)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get fee breakdown by coverage")
	}

	surchargeFees := postEndorsementChargeDistribution.Surcharges.Sub(preEndorsementChargeDistribution.Surcharges)
	flatChargeFees := postEndorsementChargeDistribution.FeeCharges.Sub(preEndorsementChargeDistribution.FeeCharges)
	return &endorsementapp.EndorsementWrittenPremium{
		EndorsementFees:        pointer_utils.ToPointer(endorsementFees),
		FeeBreakdownByCoverage: feeBreakdownByCoverage.Endorsement,
		Before: endorsementapp.PriceSnapshot{
			TotalPremium:      preEndorsementChargeDistribution.TotalCharge,
			StateSurcharge:    &preEndorsementChargeDistribution.Surcharges,
			FlatCharge:        &preEndorsementChargeDistribution.FeeCharges,
			PremiumByCoverage: feeBreakdownByCoverage.PreEndorsement,
		},
		After: endorsementapp.PriceSnapshot{
			TotalPremium:      postEndorsementChargeDistribution.TotalCharge,
			StateSurcharge:    &postEndorsementChargeDistribution.Surcharges,
			FlatCharge:        &postEndorsementChargeDistribution.FeeCharges,
			PremiumByCoverage: feeBreakdownByCoverage.PostEndorsement,
		},
		EndorsementStateSurchargeFees: &surchargeFees,
		EndorsementFlatChargeFees:     &flatChargeFees,
	}, nil
}

// getChargeDistributionFromQPC returns the charge distribution from the quoting pricing contexts which includes
// total charge, charges by sub-coverage and charges associated to "fee charges".
func getChargeDistributionFromQPC(
	quotingPricingContexts []quoting.PricingContext,
	rateBasisValue *ptypes.RateBasisValue,
) (*model.ChargeDistribution, error) {
	endorsementCharges := decimal.Zero
	chargesBySubCov := make(map[ptypes.SubCoverageType]decimal.Decimal)
	surcharges, feeCharges := decimal.Zero, decimal.Zero
	for _, qpc := range quotingPricingContexts {
		var charges []*ptypes.Charge
		if qpc.Charges != nil {
			for _, chargeList := range qpc.Charges.GetPolicyCharges() {
				charges = append(charges, chargeList.Charges...)
			}
		}
		subCovChargesForQPC, chargesBySubCovForQPC, err := model.CalculateBaseChargeDistributionAtSubCovLevel(
			charges, rateBasisValue)
		if err != nil {
			return nil, err
		}

		endorsementCharges = endorsementCharges.Add(*subCovChargesForQPC)
		for cov := range chargesBySubCovForQPC {
			chargesBySubCov[cov] = chargesBySubCov[cov].Add(chargesBySubCovForQPC[cov])
		}

		pciChargesForQPC, err := model.CalculateFeeChargeAndSurchargeDistribution(
			charges,
			rateBasisValue)
		if err != nil {
			return nil, err
		}

		endorsementCharges = endorsementCharges.Add(pciChargesForQPC.TotalCharge)
		surcharges = surcharges.Add(pciChargesForQPC.Surcharge)
		feeCharges = feeCharges.Add(pciChargesForQPC.FeeCharge)
	}

	return &model.ChargeDistribution{
		TotalCharge:          pointer_utils.ToPointer(endorsementCharges),
		ChargesBySubCoverage: chargesBySubCov,
		Surcharges:           surcharges,
		FeeCharges:           feeCharges,
	}, nil
}

// getFeeBreakdownByCoverage calculates the fee breakdown by traversing pre- and post-endorsement charges.
// It adds negative charges for pre-endorsement sub-coverages and positive charges for post-endorsement
// sub-coverages to ensure all sub-coverages are included, even if missing in one of the charge sets.
func getFeeBreakdownByCoverage(
	chargesBySubCovPreEndorsement, chargesBySubCovPostEndorsement map[ptypes.SubCoverageType]decimal.Decimal,
) (*model.FeeBreakdown, error) {
	preFeeBreakdown := make(map[appEnums.Coverage]decimal.Decimal)
	postFeeBreakdown := make(map[appEnums.Coverage]decimal.Decimal)
	totalFeeBreakdown := make(map[appEnums.Coverage]decimal.Decimal)

	// Helper function to process charges
	processCharge := func(
		subCov ptypes.SubCoverageType,
		charge decimal.Decimal,
		breakdown map[appEnums.Coverage]decimal.Decimal,
		isPre bool,
	) error {
		subCovEnum, err := coverage.GetAppCoverageFromPricingSubCoverage(subCov)
		if err != nil {
			return errors.Wrapf(err, "failed to get app coverage from pricing sub coverage")
		}
		primaryCov, err := appEnums.GetPrimaryCoverageFromCoverage(*subCovEnum)
		if err != nil {
			return errors.Wrapf(err, "failed to get primary coverage from coverage")
		}

		// Update specific breakdown
		breakdown[*primaryCov] = breakdown[*primaryCov].Add(charge)

		// Update total breakdown
		if isPre {
			// Subtract pre-endorsement charges
			totalFeeBreakdown[*primaryCov] = totalFeeBreakdown[*primaryCov].Sub(charge)
		} else {
			// Add post-endorsement charges
			totalFeeBreakdown[*primaryCov] = totalFeeBreakdown[*primaryCov].Add(charge)
		}
		return nil
	}

	// Process pre-endorsement charges
	for subCov, charge := range chargesBySubCovPreEndorsement {
		err := processCharge(subCov, charge, preFeeBreakdown, true)
		if err != nil {
			return nil, err
		}
	}

	// Process post-endorsement charges
	for subCov, charge := range chargesBySubCovPostEndorsement {
		err := processCharge(subCov, charge, postFeeBreakdown, false)
		if err != nil {
			return nil, err
		}
	}

	return &model.FeeBreakdown{
		PreEndorsement:  preFeeBreakdown,
		PostEndorsement: postFeeBreakdown,
		Endorsement:     totalFeeBreakdown,
	}, nil
}
