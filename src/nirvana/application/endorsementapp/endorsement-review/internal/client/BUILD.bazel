load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "client",
    srcs = [
        "client.go",
        "fx.go",
        "mvr.go",
    ],
    importpath = "nirvanatech.com/nirvana/application/endorsementapp/endorsement-review/internal/client",
    visibility = ["//nirvana/application/endorsementapp/endorsement-review:__subpackages__"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/handlers/common/endorsement",
        "//nirvana/api-server/handlers/common/ib",
        "//nirvana/application/endorsementapp",
        "//nirvana/application/endorsementapp/endorsement-review/internal",
        "//nirvana/application/endorsementapp/endorsement-review/internal/state-machine",
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/model",
        "//nirvana/nonfleet/rating",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/openapi-specs/components/endorsementapp/intake",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_looplab_fsm//:fsm",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "client_test",
    srcs = ["client_test.go"],
    deps = [
        ":client",
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/application/endorsementapp/endorsement-request",
        "//nirvana/application/endorsementapp/endorsement-review/internal",
        "//nirvana/common-go/test_utils/builders/endorsementapp/endorsement-request",
        "//nirvana/common-go/test_utils/builders/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums",
        "//nirvana/endorsement/legacy-write-gateway",
        "//nirvana/infra/fx/testloader",
        "//nirvana/insurance-core/proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)
