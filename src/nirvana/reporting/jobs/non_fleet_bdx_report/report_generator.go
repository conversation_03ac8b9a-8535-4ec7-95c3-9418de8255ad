package non_fleet_bdx_report

import (
	"context"
	"strconv"
	"strings"
	"time"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/handlers/common/application"
	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/type_utils"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	endorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"
	nfapp "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	admittedenums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/infra/authz"
	infraconstants "nirvanatech.com/nirvana/infra/constants"
	ibmodel "nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	"nirvanatech.com/nirvana/nonfleet/model"
	"nirvanatech.com/nirvana/policy/constants"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

type bundleProcessor func(context.Context, string, bundleVersionData, *NFBDXReportArgs) ([][]string, error)

// generateBDXReportRows generates the data rows for the report
func (n *nfBDXReport) generateBDXReportRows(
	ctx context.Context,
	msg *NFBDXReportArgs,
) ([][]string, error) {
	// Get endorsement data
	endorsementRows, err := n.generateEndorsementReportRows(ctx, msg)
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate endorsement report rows")
	}
	log.Info(ctx, "Generated Endorsement Report Rows", log.Int("count", len(endorsementRows)))

	// Get base bundle data
	baseBundleRows, err := n.generateNewBusinessReportRows(ctx, msg)
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate new business report rows")
	}
	log.Info(ctx, "Generated Base Bundle Report Rows", log.Int("count", len(baseBundleRows)))

	// Combine results
	return append(endorsementRows, baseBundleRows...), nil
}

// generateEndorsementReportRows generates data rows for endorsements
func (n *nfBDXReport) generateEndorsementReportRows(
	ctx context.Context,
	msg *NFBDXReportArgs,
) ([][]string, error) {
	// Get endorsement data
	bundleEndorsementRanges, boundEndorsements, err := n.fetchBoundEndorsementsAndRanges(ctx, msg)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch bound endorsements and ranges")
	}
	log.Info(ctx, "Fetched Bound Endorsements and Ranges", log.Int("count", len(boundEndorsements)))

	// Get bundle data for endorsements
	endorsementData, err := n.fetchBundleDataForEndorsements(ctx, boundEndorsements, bundleEndorsementRanges)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch bundle data for endorsements")
	}
	log.Info(ctx, "Fetched Bundle Data for Endorsements", log.Int("count", len(endorsementData)))

	// Process endorsement bundles
	return n.processAndFormatBundleRows(ctx, endorsementData, msg, n.createFormattedBundleRowsProcessor(false))
}

// generateNewBusinessReportRows generates data rows for new business
func (n *nfBDXReport) generateNewBusinessReportRows(
	ctx context.Context,
	msg *NFBDXReportArgs,
) ([][]string, error) {
	// Get all non-fleet admitted bundles
	request := &service.ListCondensedInsuranceBundlesRequest{
		ProgramType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
		Version:     pointer_utils.ToPointer(int32(0)),
	}
	condensedBundles, err := n.deps.IBClient.ListCondensedInsuranceBundles(ctx, request)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get list of insurance bundles")
	}
	log.Info(ctx, "Fetched Condensed Bundles", log.Int("count", len(condensedBundles.GetCondensedInsuranceBundles())))

	// Filter bundles within date range
	condensedBundlesToProcess := make([]*ibmodel.CondensedInsuranceBundle, 0)
	for _, bundle := range condensedBundles.GetCondensedInsuranceBundles() {
		startTime := bundle.GetCreatedAt().AsTime()
		if !startTime.Before(msg.StartDate) && startTime.Before(msg.EndDate) {
			condensedBundlesToProcess = append(condensedBundlesToProcess, bundle)
		}
	}
	log.Info(ctx, "Filtered Condensed Bundles", log.Int("count", len(condensedBundlesToProcess)))

	// Get bundle data for base bundles
	baseBundleData, err := n.fetchBundleDataForNewBusiness(ctx, condensedBundlesToProcess)
	if err != nil {
		return nil, err
	}

	// Process base bundles
	return n.processAndFormatBundleRows(ctx, baseBundleData, msg, n.createFormattedBundleRowsProcessor(true))
}

// fetchBoundEndorsementsAndRanges fetches the bound endorsement requests and their ranges
func (n *nfBDXReport) fetchBoundEndorsementsAndRanges(
	ctx context.Context,
	msg *NFBDXReportArgs,
) (map[string]struct{ min, max int }, []*endorsementrequest.Request, error) {
	boundEndorsementRequests, err := n.deps.EndorsementRequestManager.GetAll(
		ctx,
		endorsementrequest.RequestStateIn(enums.EndorsementRequestStateBound.String()),
		endorsementrequest.ProgramTypeIs(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted.String()),
		endorsementrequest.BoundWithinRange(msg.StartDate, msg.EndDate),
		endorsementrequest.IncludeActiveChanges(),
		endorsementrequest.SkipTestAgencies(),
	)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to get bound endorsements")
	}

	log.Info(ctx, "Fetched Bound Endorsement Requests", log.Int("count", len(boundEndorsementRequests)))

	// Track endorsement number ranges per bundle
	bundleEndorsementRanges := make(map[string]struct {
		min int
		max int
	})

	// Find min/max endorsement numbers for each bundle
	for _, endReq := range boundEndorsementRequests {
		endorsementNum, err := strconv.Atoi(endReq.ProvisionalEndorsementNumber)
		if err != nil {
			return nil, nil, errors.Wrap(err, "failed to convert endorsement number to int")
		}

		bundleRange, exists := bundleEndorsementRanges[endReq.BundleExternalID]
		if !exists {
			bundleEndorsementRanges[endReq.BundleExternalID] = struct {
				min int
				max int
			}{
				min: endorsementNum,
				max: endorsementNum,
			}
			continue
		}

		if endorsementNum < bundleRange.min {
			bundleRange.min = endorsementNum
			bundleEndorsementRanges[endReq.BundleExternalID] = bundleRange
		}
		if endorsementNum > bundleRange.max {
			bundleRange.max = endorsementNum
			bundleEndorsementRanges[endReq.BundleExternalID] = bundleRange
		}
	}

	return bundleEndorsementRanges, boundEndorsementRequests, nil
}

// fetchBundleDataForEndorsements fetches the bundle data for the given endorsements
func (n *nfBDXReport) fetchBundleDataForEndorsements(
	ctx context.Context,
	boundEndorsementRequests []*endorsementrequest.Request,
	bundleEndorsementRanges map[string]struct{ min, max int },
) (map[string]bundleVersionData, error) {
	// Pre-group endorsements by BundleExternalID
	endorsementsByBundle := make(map[string][]*endorsementrequest.Request)
	for _, endReq := range boundEndorsementRequests {
		endorsementsByBundle[endReq.BundleExternalID] = append(
			endorsementsByBundle[endReq.BundleExternalID],
			endReq,
		)
	}

	uniqueBundleIDs := slice_utils.Dedup(
		slice_utils.Map(boundEndorsementRequests, func(e *endorsementrequest.Request) string {
			return e.BundleExternalID
		}),
	)

	bundleData := make(map[string]bundleVersionData)

	for _, bundleID := range uniqueBundleIDs {
		versionRange := bundleEndorsementRanges[bundleID]
		start := int32(versionRange.min) - 1
		end := int32(versionRange.max)
		bundles := make([]*ibmodel.InsuranceBundle, 0)
		var err error
		var nextCursor *string

		for {
			response, err := n.deps.IBClient.ListInsuranceBundles(ctx, &service.ListInsuranceBundleRequest{
				ProgramType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
				ExternalId:  pointer_utils.ToPointer(bundleID),
				VersionRange: &service.VersionRange{
					Start: pointer_utils.ToPointer(start),
					End:   pointer_utils.ToPointer(end),
				},
				Pagination: &ibmodel.Pagination{
					Cursor: nextCursor,
				},
			})
			if err != nil {
				log.Error(
					ctx,
					"Failed to list insurance bundles for bundle ID for endorsement requests in BDX report",
					log.String("bundleID", bundleID),
					log.Int32("versionMin", start),
					log.Int32("versionMax", end),
					log.Err(err),
				)
				break
				// return nil, errors.Wrapf(err, "failed to list insurance bundles for bundle ID %s", bundleID)
			}

			bundles = append(bundles, response.InsuranceBundles...)
			nextCursor = response.NextCursor

			if nextCursor == nil {
				break
			}
		}

		// Initialize the maps for this bundleID if not already done
		if _, found := bundleData[bundleID]; !found {
			bundleData[bundleID] = bundleVersionData{
				InsuranceBundles:    make(map[int64]*ibmodel.InsuranceBundle),
				EndorsementRequests: make(map[string]*endorsementrequest.Request),
			}
		}
		bData := bundleData[bundleID]

		for _, bundle := range bundles {
			if bundle.ExternalId == bundleID {
				bData.InsuranceBundles[bundle.Version] = bundle
			}
		}

		// Use pre-grouped endorsement requests
		if bundleEndorsementRequests, exists := endorsementsByBundle[bundleID]; exists {
			for _, endReq := range bundleEndorsementRequests {
				bData.EndorsementRequests[endReq.ID.String()] = endReq
			}
		}

		insuranceBundleRequest := &service.GetInsuranceBundleRequest{
			PrimaryFilter: &service.GetInsuranceBundleRequest_PrimaryFilter{
				Identifier: &service.GetInsuranceBundleRequest_PrimaryFilter_ExternalId{ExternalId: bundleID},
			},
			SecondaryFilter: &service.GetInsuranceBundleRequest_SecondaryFilter{
				ProgramType: pointer_utils.ToPointer(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted),
			},
		}

		resp, err := n.deps.IBClient.GetInsuranceBundle(ctx, insuranceBundleRequest)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get insurance bundle for bundle ID %s", bundleID)
		}

		bData.LatestBundle = resp.InsuranceBundle
		bundleData[bundleID] = bData

		log.Info(
			ctx,
			"Fetched Bundles",
			log.Int("count", len(bundleData)),
			log.String("bundleID", bundleID),
		)
	}

	return bundleData, nil
}

// fetchBundleDataForNewBusiness fetches the bundle data for the given base bundles
func (n *nfBDXReport) fetchBundleDataForNewBusiness(
	ctx context.Context,
	baseBundlesToProcess []*ibmodel.CondensedInsuranceBundle,
) (map[string]bundleVersionData, error) {
	//nolint:staticcheck
	testAgencies := infraconstants.TestAgencies()
	bundleData := make(map[string]bundleVersionData)
	// Add insurance bundles for the month of the report
	for _, bundle := range baseBundlesToProcess {
		b, err := n.deps.IBClient.GetInsuranceBundle(ctx, &service.GetInsuranceBundleRequest{
			PrimaryFilter: &service.GetInsuranceBundleRequest_PrimaryFilter{
				Identifier: &service.GetInsuranceBundleRequest_PrimaryFilter_InternalId{
					InternalId: bundle.InternalID,
				},
			},
		})
		if err != nil {
			return nil, errors.Wrapf(err, "failed to list insurance bundles for bundle ID %s", bundle.ExternalID)
		}

		agencyID, err := uuid.Parse(b.GetInsuranceBundle().GetDefaultSeller().GetAgencyID())
		if err != nil {
			return nil, errors.Wrapf(err, "failed to parse agency ID %s", b.GetInsuranceBundle().GetDefaultSeller().GetAgencyID())
		}

		if _, found := testAgencies[agencyID]; found {
			continue
		}

		bundleData[bundle.ExternalID] = bundleVersionData{
			InsuranceBundles:    make(map[int64]*ibmodel.InsuranceBundle),
			EndorsementRequests: make(map[string]*endorsementrequest.Request),
		}

		bdData := bundleData[bundle.ExternalID]
		bdData.LatestBundle = b.GetInsuranceBundle()
		bdData.InsuranceBundles[b.GetInsuranceBundle().Version] = b.GetInsuranceBundle()
		bundleData[bundle.ExternalID] = bdData
	}

	log.Info(ctx, "Fetched Base Bundle Data", log.Int("count", len(bundleData)))

	return bundleData, nil
}

// processAndFormatBundleRows processes and formats the bundle rows
func (n *nfBDXReport) processAndFormatBundleRows(
	ctx context.Context,
	bundleData map[string]bundleVersionData,
	msg *NFBDXReportArgs,
	processor bundleProcessor,
) ([][]string, error) {
	var retval [][]string

	for bundleID, bd := range bundleData {
		rows, err := processor(ctx, bundleID, bd, msg)
		if err != nil {
			return nil, err
		}
		retval = append(retval, rows...)
	}

	return retval, nil
}

// createFormattedBundleRowsProcessor creates a bundle processor that formats the bundle rows
func (n *nfBDXReport) createFormattedBundleRowsProcessor(isBaseBundle bool) bundleProcessor {
	return func(ctx context.Context, bundleID string, bd bundleVersionData, msg *NFBDXReportArgs) ([][]string, error) {
		if bd.LatestBundle.GetMetadata() == nil {
			return nil, errors.Newf("bundle %s has no metadata", bundleID)
		}

		// Get underwriter information
		underwriterID, err := application.GetUnderwriterID(
			ctx,
			n.deps.AppWrapper,
			n.deps.AdmittedAppWrapper,
			uuid.MustParse(bd.LatestBundle.GetMetadata().RootApplicationId),
			bd.LatestBundle.GetProgramType())
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get underwriter ID for bundle %s", bundleID)
		}

		uwInfo, err := n.deps.AuthWrapper.FetchUserInfo(ctx, underwriterID)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get underwriter info for bundle %s", bundleID)
		}

		// Get policy information
		policyProcessor, err := endorsement.GetProcessor[endorsement.PolicyProcessor](bd.LatestBundle.ProgramType)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get policy processor")
		}

		policy, err := policyProcessor.ExtractAutoLiabilityPolicy(bd.LatestBundle.GetLastSegment())
		if err != nil {
			return nil, errors.Wrapf(err, "failed to extract auto liability policy")
		}

		coverageProcessor, err := endorsement.GetProcessor[endorsement.CoverageProcessor](bd.LatestBundle.ProgramType)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get coverage processor")
		}

		appReview, err := n.deps.NFApplicationReviewWrapper.GetAppReviewByAppID(ctx, uuid.MustParse(bd.LatestBundle.GetMetadata().RootApplicationId))
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get application review for app review %s",
				bd.LatestBundle.GetMetadata().RootApplicationId)
		}

		// Get common data
		programData := policy.ProgramData.GetNonFleetData()
		mailingAddress := programData.CompanyInfo.MailingAddress
		terminalAddress := programData.CompanyInfo.TerminalAddress

		var puCount, tiv int
		for _, veh := range programData.Vehicles {
			if slice_utils.Contains([]model.VehicleType{
				model.VehicleType_VEHICLE_TYPE_TRUCK,
				model.VehicleType_VEHICLE_TYPE_TRACTOR,
			}, veh.VehicleType) {
				puCount++
			}

			tiv += int(type_utils.GetValueOrDefault(veh.StatedValue, 0))
		}

		primaryCommodity, err := admittedenums.GetNFAdmittedCommodityEnumFromNFCommodityCategoryProto(programData.CommodityDetails.PrimaryCommodity)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get commodity enum")
		}
		classCode, err := admitted_app.GetBizClass(*primaryCommodity)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get classcode for bundle %s", bundleID)
		}

		rootAppID, err := uuid.Parse(bd.LatestBundle.GetMetadata().RootApplicationId)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to parse application ID for bundle %s", bundleID)
		}

		appObj, err := n.deps.NFApplicationWrapper.GetAppById(ctx, rootAppID)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get application by ID for bundle %s", bundleID)
		}

		var rows [][]string
		if isBaseBundle {
			latestBundleChargeDistribution, err := bd.LatestBundle.GetChargesWithDistribution(nil, false)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to get charges for bundle %s", bd.LatestBundle.InternalId)
			}

			var indicationOption nfapp.IndicationOption
			if bd.LatestBundle.GetLastSegment().GetPrimaryInsured().GetAddress().GetState() == "TX" {
				bindableSubmissionId, err := uuid.Parse(bd.LatestBundle.GetMetadata().RootBindableSubmissionId)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to parse bindable submission ID for bundle %s", bundleID)
				}

				submission, err := n.deps.AdmittedAppWrapper.GetSubmissionById(ctx, bindableSubmissionId)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to get submission by ID for bundle %s", bundleID)
				}
				indicationOption, err = n.deps.AdmittedAppWrapper.GetIndOptionById(ctx, submission.SelectedIndicationID)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to get indication option by ID for bundle %s", bundleID)
				}
			}

			rows, err = formatPolicyDataIntoReportRows(
				ctx,
				n.deps.NFApplicationWrapper,
				bd.LatestBundle,
				coverageProcessor,
				classCode,
				nil,
				latestBundleChargeDistribution,
				rows,
				bd.LatestBundle,
				terminalAddress,
				puCount,
				tiv,
				uwInfo,
				mailingAddress,
				programData,
				pointer_utils.ToPointer(bd.LatestBundle.DefaultEffectiveDuration.Start.AsTime()),
				bd.LatestBundle,
				"0",
				msg.StartDate,
				msg.EndDate,
				"",
				pointer_utils.ToPointer(bd.LatestBundle.GetDefaultEffectiveDuration().Start.AsTime()),
				appReview,
				false,
				indicationOption,
				appObj,
			)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to create bundle rows from policies for bundle %s", bundleID)
			}
		} else {
			for _, req := range bd.EndorsementRequests {
				provisionalEndorsementNumber, err := strconv.Atoi(req.ProvisionalEndorsementNumber)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to convert endorsement number to int")
				}
				bundleCreatedByEndReq, ok := bd.InsuranceBundles[int64(provisionalEndorsementNumber)]
				if !ok {
					return nil, errors.Wrapf(err, "failed to find bundle for endorsement number %d", provisionalEndorsementNumber)
				}
				bundleBasedOnEndReq, ok := bd.InsuranceBundles[int64(provisionalEndorsementNumber)-1]
				if !ok {
					return nil, errors.Wrapf(err, "failed to find previous bundle for endorsement number %d", provisionalEndorsementNumber)
				}

				postDistribution, err := bundleCreatedByEndReq.GetChargesWithDistribution(nil, false)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to get charges for bundle %s", bundleCreatedByEndReq.InternalId)
				}

				preDistribution, err := bundleBasedOnEndReq.GetChargesWithDistribution(nil, false)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to get charges for bundle %s", bundleBasedOnEndReq.InternalId)
				}

				rows, err = formatPolicyDataIntoReportRows(
					ctx,
					n.deps.NFApplicationWrapper,
					bundleCreatedByEndReq,
					coverageProcessor,
					classCode,
					preDistribution,
					postDistribution,
					rows,
					bundleBasedOnEndReq,
					terminalAddress,
					puCount,
					tiv,
					uwInfo,
					mailingAddress,
					programData,
					req.DefaultEffectiveDate,
					bd.LatestBundle,
					strconv.Itoa(provisionalEndorsementNumber),
					msg.StartDate,
					msg.EndDate,
					req.ID.String(),
					req.BoundAt,
					appReview,
					true,
					nil,
					appObj,
				)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to create bundle rows from policies for bundle %s", bundleID)
				}
			}
		}

		return rows, nil
	}
}

// formatPolicyDataIntoReportRows formats the policy data into report rows
func formatPolicyDataIntoReportRows(
	ctx context.Context,
	nfAppWrapper nfapp.Wrapper[*admitted_app.AdmittedApp],
	bundle *ibmodel.InsuranceBundle,
	coverageProcessor endorsement.CoverageProcessor,
	classCode string,
	preDistribution *ibmodel.ChargeDistribution,
	postDistribution *ibmodel.ChargeDistribution,
	rows [][]string,
	lastSegmentOfBundleAfterEndorsement *ibmodel.InsuranceBundle,
	terminalAddress *proto.Address,
	puCount int,
	tiv int,
	uwInfo *authz.UserInfo,
	mailingAddress *proto.Address,
	programData *model.NFAdmittedProgramDataV1,
	defaultEffectiveDate *time.Time,
	latestBundle *ibmodel.InsuranceBundle,
	provisionalEndorsementNumber string,
	reportStartDate time.Time,
	reportEndDate time.Time,
	endorsementRequestID string,
	boundAt *time.Time,
	appReview application_review.ApplicationReview,
	isEndorsement bool,
	indicationOption nfapp.IndicationOption,
	appObj *nfapp.Application[*admitted_app.AdmittedApp],
) ([][]string, error) {
	hasPIPWorkLossCoverageCharge := false

	if postDistribution != nil {
		for sc := range postDistribution.ChargesBySubCoverage {
			if sc == ptypes.SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService {
				hasPIPWorkLossCoverageCharge = true
				break
			}
		}
	}

	if preDistribution != nil {
		for sc := range preDistribution.ChargesBySubCoverage {
			if sc == ptypes.SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService {
				hasPIPWorkLossCoverageCharge = true
				break
			}
		}
	}

	for _, p := range bundle.GetLastSegment().GetPolicies() {
		var scDetailsList []*subCoverageDetails
		for _, c := range p.Coverages {
			for _, sc := range c.SubCoverages {
				scDetails, err := processCoverage(
					ctx,
					nfAppWrapper,
					coverageProcessor,
					lastSegmentOfBundleAfterEndorsement.GetLastSegment().CoverageCriteria,
					sc.Id,
					type_utils.GetValueOrDefault(terminalAddress.State, ""),
					strconv.Itoa(puCount),
					strconv.Itoa(tiv),
					preDistribution, postDistribution,
					isEndorsement,
					appReview,
					indicationOption,
					appObj,
					p.PolicyNumber,
				)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to process coverage %s", sc.Id)
				}

				scDetails.Id = sc.Id
				scDetails.DisplayName = sc.DisplayName
				scDetailsList = append(scDetailsList, scDetails)
			}
		}

		if hasPIPWorkLossCoverageCharge && strings.HasPrefix(p.PolicyNumber, constants.NFAdmittedALPrefix) {
			scDetails, err := processCoverage(
				ctx,
				nfAppWrapper,
				coverageProcessor,
				lastSegmentOfBundleAfterEndorsement.GetLastSegment().CoverageCriteria,
				appenums.CoveragePIPWorkLossAndRPLService.String(),
				type_utils.GetValueOrDefault(terminalAddress.State, ""),
				strconv.Itoa(puCount),
				strconv.Itoa(tiv),
				preDistribution, postDistribution,
				isEndorsement,
				appReview,
				indicationOption,
				appObj,
				p.PolicyNumber,
			)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to process coverage %s", appenums.CoveragePIPWorkLossAndRPLService)
			}

			scDetails.DisplayName = "PIP Work Loss and RPL Service"
			scDetailsList = append(scDetailsList, scDetails)
		}

		if strings.HasPrefix(p.PolicyNumber, constants.NFAdmittedALPrefix) && p.GetClauses() != nil {
			hasBlanketAdditionalInsured, hasBlanketWaiverOfSubrogation := p.Clauses.GetBlanketClauseStatus()
			if hasBlanketAdditionalInsured {
				scDetails, err := processCoverage(
					ctx,
					nfAppWrapper,
					coverageProcessor,
					lastSegmentOfBundleAfterEndorsement.GetLastSegment().CoverageCriteria,
					appenums.CoverageBlanketAdditional.String(),
					type_utils.GetValueOrDefault(terminalAddress.State, ""),
					strconv.Itoa(puCount),
					strconv.Itoa(tiv),
					preDistribution, postDistribution,
					isEndorsement,
					appReview,
					indicationOption,
					appObj,
					p.PolicyNumber,
				)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to process coverage %s", appenums.CoverageBlanketAdditional)
				}

				scDetails.DisplayName = "Blanket Additional Insured"
				scDetailsList = append(scDetailsList, scDetails)
			}
			if hasBlanketWaiverOfSubrogation {
				scDetails, err := processCoverage(
					ctx,
					nfAppWrapper,
					coverageProcessor,
					lastSegmentOfBundleAfterEndorsement.GetLastSegment().CoverageCriteria,
					appenums.CoverageBlanketWaiverOfSubrogation.String(),
					type_utils.GetValueOrDefault(terminalAddress.State, ""),
					strconv.Itoa(puCount),
					strconv.Itoa(tiv),
					preDistribution, postDistribution,
					isEndorsement,
					appReview,
					indicationOption,
					appObj,
					p.PolicyNumber,
				)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to process coverage %s", appenums.CoverageBlanketWaiverOfSubrogation)
				}

				scDetails.DisplayName = "Blanket Waiver of Subrogration"
				scDetailsList = append(scDetailsList, scDetails)
			}
		}

		for _, scDetails := range scDetailsList {
			tax1Type := ""
			tax2Type := ""
			if scDetails.SLTax != "" {
				tax1Type = "Surplus Lines Tax"
			}
			if scDetails.StampingFee != "" {
				tax2Type = "Stamping Fee"
			}

			mgaCommission := mgaCommissionPercentBeforeDec2024
			if time_utils.DateFromTime(*defaultEffectiveDate).After(time_utils.NewDate(2024, 12, 1)) {
				mgaCommission = mgaCommissionPercentAfterDec2024
			}

			transactionType := transactionTypeEndorsement
			if !isEndorsement {
				transactionType = transactionTypeNewBusiness
			}

			endorsementEffectiveDate := defaultEffectiveDate.Format("01/02/2006")
			if !isEndorsement {
				endorsementEffectiveDate = ""
			}

			endorsementExpiryDate := latestBundle.DefaultEffectiveDuration.End.AsTime().Format("01/02/2006")
			if !isEndorsement {
				endorsementExpiryDate = ""
			}

			endorsementNumber := provisionalEndorsementNumber
			if !isEndorsement {
				endorsementNumber = ""
			}

			carrier := carrierSPAIC
			if programData.CompanyInfo.UsState == "TX" {
				carrier = carrierSPSIC
			}

			policyEffectiveDate := latestBundle.DefaultEffectiveDuration.Start.AsTime().Format("01/02/2006")

			policyExpirationDate := latestBundle.DefaultEffectiveDuration.End.AsTime().Format("01/02/2006")

			underwritingYear := "1"

			if latestBundle.DefaultEffectiveDuration.Start.AsTime().After(underwritingYear2StartDate) {
				underwritingYear = "2"
			}

			renewalIndicator := renewalIndicatorForNewBusiness
			if appObj.IsRenewal() {
				renewalIndicator = renewalIndicatorForRenewals
			}

			rows = append(rows, []string{
				p.PolicyNumber, // Policy Locator
				latestBundle.GetLastSegment().PrimaryInsured.Name.BusinessName, // Insured
				transactionType,                 // Transaction Type
				scDetails.DisplayName,           // Coverage
				programData.CompanyInfo.UsState, // Account State
				carrier,                         // Carrier
				getLOBName(p.PolicyNumber),      // LOB
				mgaName,                         // MGA Name
				type_utils.GetValueOrDefault(mailingAddress.Street, ""), // Account Mailing Address Line 1
				"", // Account Mailing Address Line 2
				type_utils.GetValueOrDefault(mailingAddress.City, ""),    // Account Mailing City
				type_utils.GetValueOrDefault(mailingAddress.State, ""),   // Account Mailing State
				type_utils.GetValueOrDefault(mailingAddress.ZipCode, ""), // Account Mailing Zip Code
				country,                                 // Account Mailing Country
				getAnnualStatementLOBCode(scDetails.Id), // Annual Statement LOB Code
				endorsementEffectiveDate,                // Endorsement Effective Date
				endorsementExpiryDate,                   // Endorsement Expiration Date
				endorsementNumber,                       // Endorsement Number
				policyEffectiveDate,                     // Policy Effective Date
				policyExpirationDate,                    // Policy Expiration Date
				"",                                      // Policy Cancel Date
				p.PolicyNumber,                          // Policy Number
				scDetails.PreviousPolicyNumber,          // Previous Policy Number
				renewalIndicator,                        // Renewal Indicator
				reportStartDate.Format("01/02/2006"),    // Reporting Period Start Date
				reportEndDate.AddDate(0, 0, -1).Format("01/02/2006"), // Reporting Period End Date
				programData.CompanyInfo.UsState,                      // State of Filing
				uwInfo.Email,                                         // Underwriter
				strconv.Itoa(latestBundle.DefaultEffectiveDuration.Start.AsTime().Year()), // Year of Account
				defaultEffectiveDate.Format("01/02/2006"),                                 // Coverage Effective Date
				latestBundle.DefaultEffectiveDuration.End.AsTime().Format("01/02/2006"),   // Coverage Expiration Date
				type_utils.GetValueOrDefault(terminalAddress.Street, ""),                  // Risk Location Address Line 1
				"", // Risk Location Address Line 2
				type_utils.GetValueOrDefault(terminalAddress.City, ""), // Risk Location City
				country, // Risk Location Country
				type_utils.GetValueOrDefault(terminalAddress.State, ""),   // Risk Location State
				type_utils.GetValueOrDefault(terminalAddress.ZipCode, ""), // Risk Location Zip Code
				programData.Operations.MaxRadiusOfOperation.String(),      // Segmentation
				usd,                                      // Transaction Currency
				exchangeRate,                             // Transaction Currency Exchange Rate
				boundAt.Format("01/02/2006"),             // Transaction Effective Date
				provisionalEndorsementNumber,             // Transaction Number
				classCode,                                // Class Code
				"",                                       // Class Code Description
				scDetails.Exposure,                       // Exposure
				scDetails.ExposureBasis,                  // Exposure Basis
				scDetails.Deductible,                     // Deductible
				scDetails.DeductibleType,                 // Deductible Type
				scDetails.CombinedDeductible,             // Combined Deductible
				scDetails.BIPDCombinedSingleLimit,        // BIPD Combined Single Limit
				scDetails.EachOccurenceCoverageLimit,     // Each Occurrence Coverage Limit
				scDetails.CombinedUnitTractorTrailer,     // Combined Unit Tractor/Trailer
				scDetails.GeneralAggregateLimit,          // General Aggregate Limit
				scDetails.PIPLimit,                       // PIP Limit
				scDetails.PIPAttendantLimit,              // PIP Attendant Care Limit
				scDetails.PIPWorkLossAndReplacementLimit, // PIP Work Loss Limit
				scDetails.PIPWorkLossAndReplacementLimit, // PIP Replacement Services Limit
				scDetails.PPILimit,                       // PPI Limit
				scDetails.RentalReimbursementLimit,       // Rental Reimbursement Limit
				scDetails.MedpayLimit,                    // Med Pay Limit
				scDetails.UMLimit,                        // UM Limit
				scDetails.UIMLimit,                       // UIM Limit
				scDetails.UMUIMLimit,                     // UMUIM Limit
				scDetails.UMBILimit,                      // UMBI Limit
				scDetails.UMPDLimit,                      // UMPD Limit
				scDetails.UIMBILimit,                     // UIMBI Limit
				scDetails.UIMPDLimit,                     // UIMPD Limit
				scDetails.NOTLimit,                       // NOT Limit
				scDetails.TrailerInterchangeLimit,        // Trailer Interchange Limit
				scDetails.HiredAutoLimit,                 // Hired Auto Limit
				mgaCommission,                            // MGA Commission Percent
				scDetails.GrossWrittenPremium,            // Gross Written Premium
				scDetails.Surcharge,                      // Surcharge
				scDetails.SurchargeType,                  // Surcharge Type
				tax1Type,                                 // Tax1_Type
				scDetails.GrossWrittenPremium,            // Tax_1_Taxable_Premium_Amount
				scDetails.SLTax,                          // Tax_1_Amount
				"Producer or Broker",                     // Tax1_Payable_By
				tax2Type,                                 // Tax2_Type
				scDetails.GrossWrittenPremium,            // Tax_2_Taxable_Premium_Amount
				scDetails.StampingFee,                    // Tax_2_Amount
				"Producer or Broker",                     // Tax2_Payable_By
				scDetails.ScheduleMod,                    // Schedule Mod
				scDetails.FeeCharge,                      // Flat Charges
				underwritingYear,                         // Underwriting Year
				"",                                       // Internal
				latestBundle.GetMetadata().RootBindableSubmissionId, // Submission ID
				endorsementRequestID, // Endorsement Request ID
			})
		}
	}

	return rows, nil
}
