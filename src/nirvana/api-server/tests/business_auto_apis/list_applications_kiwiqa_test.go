package business_auto_apis

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/api-server/handlers/business_auto"
	"nirvanatech.com/nirvana/api-server/interceptors/business_auto/deps"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/constants"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/biz_auto_app_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type listApplicationsKiwiQATestSuite struct {
	suite.Suite
	deps  deps.Deps
	ctx   context.Context
	fxapp *fxtest.App

	bizAutoAppFixture              *biz_auto_app_fixture.BizAutoAppFixture
	kiwiApp                        model.BusinessAutoApp
	normalApp                      model.BusinessAutoApp
	clearanceTestAgencyApplication model.BusinessAutoApp
}

func TestListApplicationsKiwiQASuite(t *testing.T) {
	suite.Run(t, new(listApplicationsKiwiQATestSuite))
}

func (s *listApplicationsKiwiQATestSuite) SetupTest() {
	var env struct {
		fx.In
		Deps           deps.Deps
		BizAutoFixture *biz_auto_app_fixture.BizAutoAppFixture
	}

	s.fxapp = testloader.RequireStart(s.T(), &env)
	s.deps = env.Deps
	s.bizAutoAppFixture = env.BizAutoFixture
	s.ctx = context.Background()
	s.prepareData()
}

func (s *listApplicationsKiwiQATestSuite) TearDownTest() {
	s.fxapp.RequireStop()
}

func (s *listApplicationsKiwiQATestSuite) prepareData() {
	// Ensure test agencies exist in DB with correct is_test_agency flag
	now := time.Now()

	// Insert Kiwi QA agency (is_test_agency = true)
	kiwiAgency := agency.NewAgency(constants.KiwiQAAgencyID, "Kiwi QA Agency", now, nil, true, nil)
	if err := s.deps.AgencyWrapper.InsertAgency(s.ctx, kiwiAgency); err != nil {
		s.T().Logf("Warning: Failed to insert Kiwi QA agency (may already exist): %v", err)
	}

	// Insert Clearance Testing agency (is_test_agency = true)
	clearanceAgency := agency.NewAgency(constants.ClearanceTestingAgencyID3, "Clearance Test Agency", now, nil, true, nil)
	if err := s.deps.AgencyWrapper.InsertAgency(s.ctx, clearanceAgency); err != nil {
		s.T().Logf("Warning: Failed to insert Clearance agency (may already exist): %v", err)
	}

	// Insert a normal (non-test) agency for the normal app
	normalAgencyID := uuid.New()
	normalAgency := agency.NewAgency(normalAgencyID, "Normal Production Agency", now, nil, false, nil)
	if err := s.deps.AgencyWrapper.InsertAgency(s.ctx, normalAgency); err != nil {
		s.FailNow("failed to insert normal agency", err)
	}

	// Create a Kiwi QA app using the KiwiQA agency (this will be visible to KiwiQA users)
	s.kiwiApp = *s.bizAutoAppFixture.MinimalApp
	s.kiwiApp.ID = uuid.New()
	s.kiwiApp.ShortID = "KIWI123"
	s.kiwiApp.AgencyID = constants.KiwiQAAgencyID
	s.kiwiApp.CompanyInfo.Name = "Kiwi Test Company"

	if err := s.deps.AppWrapper.Insert(s.ctx, &s.kiwiApp); err != nil {
		s.FailNow("failed to insert kiwi qa app", err)
	}

	// Create a normal app using the normal agency we just created
	s.normalApp = *s.bizAutoAppFixture.MinimalApp
	s.normalApp.ID = uuid.New()
	s.normalApp.ShortID = "NORM456"
	s.normalApp.AgencyID = normalAgencyID // Use the non-test agency
	s.normalApp.CompanyInfo.Name = "Normal Company"

	if err := s.deps.AppWrapper.Insert(s.ctx, &s.normalApp); err != nil {
		s.FailNow("failed to insert normal app", err)
	}

	// Create a clearance test agency application (other than kiwiQA)
	s.clearanceTestAgencyApplication = *s.bizAutoAppFixture.MinimalApp
	s.clearanceTestAgencyApplication.ID = uuid.New()
	s.clearanceTestAgencyApplication.ShortID = "CLEAR789"
	s.clearanceTestAgencyApplication.AgencyID = constants.ClearanceTestingAgencyID3
	s.clearanceTestAgencyApplication.CompanyInfo.Name = "Clearance Testing Company"

	if err := s.deps.AppWrapper.Insert(s.ctx, &s.clearanceTestAgencyApplication); err != nil {
		s.FailNow("failed to insert clearance test agency application", err)
	}
}

func (s *listApplicationsKiwiQATestSuite) TestListApplications_QAUser() {
	// Set up QA user context
	qaUser := authz.User{
		Roles: []authz.Role{
			{
				Group:    authz.QAUnderwriterRole,
				AgencyID: &constants.KiwiQAAgencyID,
			},
		},
	}
	ctx := authz.WithUser(s.ctx, qaUser)

	// Call handler
	response, err := business_auto.HandleListApplications(ctx, s.deps)
	s.NoError(err)
	s.Len(response, 2, "QA user should see both KiwiQA and clearance testing agency applications")

	// Check response length before accessing elements
	if len(response) >= 2 {
		// Verify applications are from both KiwiQA and clearance testing agencies
		appIDs := make([]string, len(response))
		for i, app := range response {
			appIDs[i] = app.Id.String()
		}
		s.Contains(appIDs, s.kiwiApp.ID.String(), "Should contain KiwiQA app")
		s.Contains(appIDs, s.clearanceTestAgencyApplication.ID.String(), "Should contain Clearance test app")
	}
}

func (s *listApplicationsKiwiQATestSuite) TestListApplications_RegularUser() {
	// Set up regular user context
	regularUser := authz.User{
		Roles: []authz.Role{
			{
				Group: authz.AgencyProducerRole,
				// set agency id as nirvana demo id, non kiwi qa agency
				AgencyID: &constants.NirvanaDemoAgencyID,
			},
		},
	}
	ctx := authz.WithUser(s.ctx, regularUser)

	// Call handler
	response, err := business_auto.HandleListApplications(ctx, s.deps)
	s.NoError(err)
	// Regular users should only see non-Kiwi QA applications
	s.Len(response, 1)
	s.Equal(s.normalApp.ID.String(), response[0].Id.String(), "Regular user should only see normal app")
}
