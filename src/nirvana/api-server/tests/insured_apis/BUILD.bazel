load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "insured_apis_test",
    srcs = ["provisional_loss_runs_reports_test.go"],
    deps = [
        "//nirvana/claims/reporting/loss_runs/db",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/test_utils/builders/policy",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/file_upload",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/infra/fx/testfixtures/agency_fixture",
        "//nirvana/infra/fx/testfixtures/api_server_fixture",
        "//nirvana/infra/fx/testfixtures/fixture_utils",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/openapi-specs/api_server_insured",
        "//nirvana/openapi-specs/components/insured",
        "//nirvana/policy_common/constants",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
