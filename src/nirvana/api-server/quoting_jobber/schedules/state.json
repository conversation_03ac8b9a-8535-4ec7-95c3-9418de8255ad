{"cluster_id": "7c954af2-8c21-4c33-b982-ca667889cc33", "store_id": "7c954af2-8c21-4c33-b982-ca667889cc33", "schedule": {"CreatorUUIDs": {"09065e6e-360a-526f-aa35-05f9f94b2aad": {"ScheduleName": "StuckJobDetector", "Params": {"RegistryKey": "StuckJobDetector", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "09065e6e-360a-526f-aa35-05f9f94b2aad", "config": {"type": 0, "interval": 1800000000000}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "09065e6e-360a-526f-aa35-05f9f94b2aad"}}, "16a6fd01-033f-598b-be1c-93dbb260bfc2": {"ScheduleName": "UpdateApplicationPreTelematicsStatus", "Params": {"RegistryKey": "UpdateApplicationPreTelematicsStatus", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "16a6fd01-033f-598b-be1c-93dbb260bfc2", "config": {"type": 1, "cronSpec": "0 0 * * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "16a6fd01-033f-598b-be1c-93dbb260bfc2"}}, "1cfe851a-e79e-529d-9b00-314c195551d5": {"ScheduleName": "FleetRiskMetricsPipeline", "Params": {"RegistryKey": "RiskMetricsPipeline", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "1cfe851a-e79e-529d-9b00-314c195551d5", "config": {"type": 1, "cronSpec": "0 12 15 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "1cfe851a-e79e-529d-9b00-314c195551d5"}}, "42aed3ec-bb70-58f0-9477-8b82ba01c52f": {"ScheduleName": "ClaimFeedbackSummaries", "Params": {"RegistryKey": "ClaimFeedbackSummaries", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "42aed3ec-bb70-58f0-9477-8b82ba01c52f", "config": {"type": 1, "cronSpec": "0 30 13 * * 1"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "42aed3ec-bb70-58f0-9477-8b82ba01c52f"}}, "65e3d1e9-bb18-52b8-b13c-1afe3877179d": {"ScheduleName": "NonFleetPolicyEndorsementReporting", "Params": {"RegistryKey": "NonFleetPolicyEndorsementReporting", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "65e3d1e9-bb18-52b8-b13c-1afe3877179d", "config": {"type": 1, "cronSpec": "0 0 10 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "65e3d1e9-bb18-52b8-b13c-1afe3877179d"}}, "6d94f9f1-cc69-5531-a7c3-06cfb3c2c05c": {"ScheduleName": "TransitionPoliciesToActiveState", "Params": {"RegistryKey": "TransitionPoliciesToActiveStatus", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "6d94f9f1-cc69-5531-a7c3-06cfb3c2c05c", "config": {"type": 1, "cronSpec": "0 0 5 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "6d94f9f1-cc69-5531-a7c3-06cfb3c2c05c"}}, "749f6a87-eccb-5f19-a949-5e0ec969a494": {"ScheduleName": "TransitionAppsToClosedState", "Params": {"RegistryKey": "TransitionAppsToClosedState", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "749f6a87-eccb-5f19-a949-5e0ec969a494", "config": {"type": 1, "cronSpec": "0 0 5 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "749f6a87-eccb-5f19-a949-5e0ec969a494"}}, "76a4698d-190e-5393-b204-3e4614377d36": {"ScheduleName": "TransitionPoliciesToExpiredState", "Params": {"RegistryKey": "TransitionPoliciesToExpiredStatus", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "76a4698d-190e-5393-b204-3e4614377d36", "config": {"type": 1, "cronSpec": "0 0 5 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "76a4698d-190e-5393-b204-3e4614377d36"}}, "780c661f-12b8-56fe-89f6-36c18010429b": {"ScheduleName": "RefreshFleetUnderwritingUsecases", "Params": {"RegistryKey": "RefreshFleetUnderwritingUsecases", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "780c661f-12b8-56fe-89f6-36c18010429b", "config": {"type": 1, "cronSpec": "0 0 10 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "780c661f-12b8-56fe-89f6-36c18010429b"}}, "9666efe6-7cfb-5723-9191-ad6066dc7ac1": {"ScheduleName": "ActivateAndExpireInsuranceBundles", "Params": {"RegistryKey": "ActivateAndExpireInsuranceBundles", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "9666efe6-7cfb-5723-9191-ad6066dc7ac1", "config": {"type": 1, "cronSpec": "0 0 5 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "9666efe6-7cfb-5723-9191-ad6066dc7ac1"}}, "a6a273b8-c5e6-5233-90ff-794989978ee7": {"ScheduleName": "UpdateAppetiteFactorsNF", "Params": {"RegistryKey": "UpdateAppetiteFactorsNF", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "a6a273b8-c5e6-5233-90ff-794989978ee7", "config": {"type": 1, "cronSpec": "0 30 6 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "a6a273b8-c5e6-5233-90ff-794989978ee7"}}, "a9683a6c-14bd-55d3-b2b6-c5b6201dd462": {"ScheduleName": "NARSSync", "Params": {"RegistryKey": "NARSSync", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "a9683a6c-14bd-55d3-b2b6-c5b6201dd462", "config": {"type": 1, "cronSpec": "0 0 10 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "a9683a6c-14bd-55d3-b2b6-c5b6201dd462"}}, "bb9a2f85-6ea0-5d38-a70b-eb89d025b619": {"ScheduleName": "UpdateSFDCWinningCarrier", "Params": {"RegistryKey": "UpdateSfdcWinningCarrier", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "bb9a2f85-6ea0-5d38-a70b-eb89d025b619", "config": {"type": 1, "cronSpec": "0 0 10 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "bb9a2f85-6ea0-5d38-a70b-eb89d025b619"}}, "c1d79649-1061-5533-b8b4-177cb3a7a77a": {"ScheduleName": "ExecuteEndorsements", "Params": {"RegistryKey": "ExecuteEndorsements", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "c1d79649-1061-5533-b8b4-177cb3a7a77a", "config": {"type": 1, "cronSpec": "0 0 10 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "c1d79649-1061-5533-b8b4-177cb3a7a77a"}}, "e460dc25-58a0-5532-8a0f-2f1ca872b297": {"ScheduleName": "ClaimProactiveStatusSummaries", "Params": {"RegistryKey": "ClaimProactiveStatusSummaries", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "e460dc25-58a0-5532-8a0f-2f1ca872b297", "config": {"type": 1, "cronSpec": "0 0 16 * * 1"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "e460dc25-58a0-5532-8a0f-2f1ca872b297"}}, "f9821d0d-c69c-55c8-96b0-0c30e8ce9b56": {"ScheduleName": "RemoveClearanceProtection", "Params": {"RegistryKey": "RemoveClearanceProtection", "Message": null, "Metadata": {"owner": null, "registryKey": "", "runType": "Scheduled", "requestedStartTime": null, "message": {"data": null, "version": 0}}, "JobIdSuffix": "", "Schedule": {"id": "f9821d0d-c69c-55c8-96b0-0c30e8ce9b56", "config": {"type": 1, "cronSpec": "0 30 4 * * *"}, "runsRemaining": null, "prevRunId": -1, "nextRunId": -1}, "CreatorUuid": "f9821d0d-c69c-55c8-96b0-0c30e8ce9b56"}}}, "CanceledUUIDs": {"0a61edd6-ec6c-556c-a3f9-d8c6de7c5428": true, "1325eae3-eba9-5bd5-97ce-9495a274647e": true, "17dd4a70-85a8-50a4-ba6f-79dd30783560": true, "2edf154c-006b-53b9-bdd0-d24637b01a15": true, "396871a9-5629-58f4-961f-605b25079968": true, "4c4163e4-2d7d-55c0-8898-22d250fd6b57": true, "4cf02dcf-ee89-5b54-bcc0-90095bbea539": true, "52784988-f533-54fb-a996-1872c70c2010": true, "87c09b7a-a979-50b6-a7e8-e40ae096887c": true, "8f880c1f-167b-50f9-a722-f272a3f66d73": true, "9bdeab92-254e-5935-99af-5997a77b565d": true, "a79e841f-6470-5d38-9d83-ceac1b26d20e": true, "a946a337-38ba-5219-a297-52411f73df02": true, "bdee615e-2fcc-5446-a86b-3e5b5589b529": true, "dfd20bc0-f1f3-506f-a827-f64950c41d49": true, "e3ba88e6-109b-568e-9c1c-2c90bf3647d1": true, "f1f3d5a9-f9ee-517d-a99b-4999c70206c9": true}, "JobParamsMessages": {"09065e6e-360a-526f-aa35-05f9f94b2aad": {"data": "bnVsbA==", "version": 0}, "16a6fd01-033f-598b-be1c-93dbb260bfc2": {"data": "bnVsbA==", "version": 0}, "1cfe851a-e79e-529d-9b00-314c195551d5": {"data": "eyJNb250aCI6IjAwMDEtMDEtMDFUMDA6MDA6MDBaIiwiUHJvZ3JhbVR5cGUiOiJQcm9ncmFtVHlwZUZsZWV0In0=", "version": 0}, "42aed3ec-bb70-58f0-9477-8b82ba01c52f": {"data": "bnVsbA==", "version": 0}, "65e3d1e9-bb18-52b8-b13c-1afe3877179d": {"data": "bnVsbA==", "version": 0}, "6d94f9f1-cc69-5531-a7c3-06cfb3c2c05c": {"data": "bnVsbA==", "version": 0}, "749f6a87-eccb-5f19-a949-5e0ec969a494": {"data": "bnVsbA==", "version": 0}, "76a4698d-190e-5393-b204-3e4614377d36": {"data": "bnVsbA==", "version": 0}, "780c661f-12b8-56fe-89f6-36c18010429b": {"data": "bnVsbA==", "version": 0}, "9666efe6-7cfb-5723-9191-ad6066dc7ac1": {"data": "bnVsbA==", "version": 0}, "a6a273b8-c5e6-5233-90ff-794989978ee7": {"data": "bnVsbA==", "version": 0}, "a9683a6c-14bd-55d3-b2b6-c5b6201dd462": {"data": "bnVsbA==", "version": 0}, "bb9a2f85-6ea0-5d38-a70b-eb89d025b619": {"data": "bnVsbA==", "version": 0}, "c1d79649-1061-5533-b8b4-177cb3a7a77a": {"data": "bnVsbA==", "version": 0}, "e460dc25-58a0-5532-8a0f-2f1ca872b297": {"data": "eyJVc2VySWQiOiIyMzc4YzA5ZC1kMjY4LTQ5MmEtYjc4NC04ZjU1MWE3NGYzMTkiLCJDbGFpbUlkcyI6bnVsbH0=", "version": 0}, "f9821d0d-c69c-55c8-96b0-0c30e8ce9b56": {"data": "e30=", "version": 0}}}}