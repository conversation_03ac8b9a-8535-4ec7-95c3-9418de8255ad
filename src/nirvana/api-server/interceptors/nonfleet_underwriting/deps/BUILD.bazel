load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "deps",
    srcs = ["deps.go"],
    importpath = "nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/nonfleet/rule_runs",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/mvr",
        "//nirvana/infra/authz/checker",
        "//nirvana/nonfleet/state-machine",
        "//nirvana/nonfleet/underwriting_panels/base_panel",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/nonfleet/underwriting_panels/equipments",
        "//nirvana/nonfleet/underwriting_panels/losses",
        "//nirvana/nonfleet/underwriting_panels/operations",
        "//nirvana/nonfleet/underwriting_panels/packages",
        "//nirvana/nonfleet/underwriting_panels/safety",
        "//nirvana/nonfleet/underwriting_panels/summary",
        "//nirvana/servers/quote_scraper",
        "//nirvana/underwriting/app_review/actions/permission",
        "@com_github_benbjohnson_clock//:clock",
        "@in_gopkg_segmentio_analytics_go_v3//:analytics-go_v3",
        "@org_uber_go_fx//:fx",
    ],
)
