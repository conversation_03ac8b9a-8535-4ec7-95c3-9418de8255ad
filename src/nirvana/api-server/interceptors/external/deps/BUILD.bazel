load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "deps",
    srcs = ["deps.go"],
    importpath = "nirvanatech.com/nirvana/api-server/interceptors/external/deps",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/application/endorsementapp/endorsement-request",
        "//nirvana/application/endorsementapp/endorsement-review",
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/pibit_ai",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/external/pibit",
        "//nirvana/db-api/db_wrappers/forms",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/endorsement/endorsement-review",
        "//nirvana/events",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/infra/authz/checker",
        "//nirvana/insurance-bundle/service",
        "//nirvana/parsed_loss_runs/files",
        "//nirvana/safety/common",
        "//nirvana/telematics/integrations/samsara_lib",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@org_uber_go_fx//:fx",
    ],
)
