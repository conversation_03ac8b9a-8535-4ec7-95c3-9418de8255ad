package deps

import (
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/api-server/quoting_jobber"

	"nirvanatech.com/nirvana/insurance-bundle/service"

	ib_endorsement_request "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	ib_endorsement_review "nirvanatech.com/nirvana/application/endorsementapp/endorsement-review"
	"nirvanatech.com/nirvana/common-go/crypto_utils"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/common-go/pibit_ai"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	endorsementreview "nirvanatech.com/nirvana/endorsement/endorsement-review"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/infra/authz/checker"
	"nirvanatech.com/nirvana/parsed_loss_runs/files"
	"nirvanatech.com/nirvana/safety/common"
	"nirvanatech.com/nirvana/telematics/integrations/samsara_lib"
)

type Deps struct {
	fx.In
	CryptoClient                    *crypto_utils.Client
	PolicyWrapper                   policy.DataWrapper
	ApplicationWrapper              application.DataWrapper
	FileUploadManager               file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
	AuthzChecker                    *checker.Checker
	DataStores                      common.DataStores
	ParsedLossRunWrapper            pibit.DataWrapper
	EventsHandler                   events.EventsHandler
	CoverageMapFileWrapper          *files.CoverageMapFileWrapper
	FeatureFlagClient               feature_flag_lib.Client
	AdmittedAppWrapper              nf_app.Wrapper[*admitted_app.AdmittedApp]
	FetcherClientFactory            data_fetching.FetcherClientFactory
	FormsWrapper                    forms.FormWrapper
	AuthWrapper                     auth.DataWrapper
	AgencyWrapper                   agency.DataWrapper
	EndorsementReviewClient         endorsementreview.Client
	SamsaraAPIClient                samsara_lib.APIIntegration
	Statter                         statsd.Statter
	ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
	WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
	InsuranceBundleManagerClient    service.InsuranceBundleManagerClient
	EndorsementRequestManager       ib_endorsement_request.Manager
	EndorsementReviewManager        ib_endorsement_review.Manager
	PibitAiClient                   *pibit_ai.PibitAiClient
	Clk                             clock.Clock
	JobberClient                    quoting_jobber.Client
}
