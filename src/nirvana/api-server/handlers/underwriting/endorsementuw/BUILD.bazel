load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "endorsementuw",
    srcs = [
        "bind_endorsement.go",
        "errors.go",
        "get_endorsement_review.go",
        "get_endorsement_reviews.go",
        "helpers.go",
        "patch_endorsement_review.go",
        "post_endorsement_review_price_refresh.go",
        "post_pull_mvr.go",
        "replace_policy_change_form.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/underwriting/endorsementuw",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/handlers/application",
        "//nirvana/api-server/handlers/common/application",
        "//nirvana/api-server/handlers/common/endorsement",
        "//nirvana/api-server/handlers/common/ib",
        "//nirvana/api-server/handlers/underwriting/endorsementuw/converters",
        "//nirvana/api-server/helpers",
        "//nirvana/api-server/interceptors/underwriting/deps",
        "//nirvana/application/endorsementapp",
        "//nirvana/application/endorsementapp/endorsement-request",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/infra/authz",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/nonfleet/rating",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/endorsementapp/intake",
        "//nirvana/openapi-specs/components/endorsementuw",
        "//nirvana/openapi-specs/components/nirvana",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
    ],
)
