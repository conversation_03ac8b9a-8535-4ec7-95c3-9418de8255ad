load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "pibit",
    srcs = [
        "oapi_request_mapper.go",
        "post_parsed_loss_run.go",
        "post_parsed_loss_run_DEV.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/external/pibit",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/interceptors/external/deps",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pibit_ai",
        "//nirvana/openapi-specs/components/external",
        "//nirvana/parsed_loss_runs",
        "//nirvana/quoting/jobs",
        "//nirvana/underwriting/jobs",
        "@com_github_cockroachdb_errors//:errors",
    ],
)
