package pibit

import (
	"context"
	jobs "nirvanatech.com/nirvana/quoting/jobs"
	underwritingjobs "nirvanatech.com/nirvana/underwriting/jobs"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/interceptors/external/deps"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pibit_ai"
	oapi_external "nirvanatech.com/nirvana/openapi-specs/components/external"
	"nirvanatech.com/nirvana/parsed_loss_runs"
)

var emptyStruct = struct{}{}

func HandleCreateParsedLossRun(
	ctx context.Context, deps deps.Deps, req CreateParsedLossRunRequest,
) CreateParsedLossRunResponse {
	ctx = log.ContextWithFields(ctx, log.String("documentId", req.DocumentID),
		log.String("pibitRequestId", req.Form.RequestId))

	log.Info(ctx, "HandleCreateParsedLossRun: Received a request")
	log.Info(ctx, "HandleCreateParsedLossRun: Parsed loss run", log.Any("loss record", req))

	lossRunsRequest, err := mapOAPIFormToCreateParsedLossesRequest(req.Form)
	if err != nil {
		log.Error(ctx, "HandleCreateParsedLossRun: Error mapping OAPI form to parsed loss runs request", log.Err(err))
		return CreateParsedLossRunResponse{
			Error: common.NewNirvanaBadRequestErrorWithReason(err, "Error mapping OAPI form to parsed loss runs request"),
		}
	}
	err = parsed_loss_runs.CreateParsedLossRuns(ctx, deps, *lossRunsRequest, req.DocumentID, true)
	if err != nil {
		log.Error(ctx, "HandleCreateParsedLossRun: Error creating parsed loss run", log.Err(err))
		if errors.Is(err, parsed_loss_runs.ValidationError) {
			msg := "validation error: " + err.Error()
			return CreateParsedLossRunResponse{
				Error: common.NewNirvanaBadRequestErrorWithReason(err, msg),
			}
		}
		if errors.Is(err, parsed_loss_runs.NotFoundError) {
			return CreateParsedLossRunResponse{
				Error: common.NewNirvanaNotFoundErrorf(err, "document", req.DocumentID),
			}
		}
		if errors.Is(err, pibit_ai.ErrFailedPibitDependency) {
			msg := "failed dependency: " + err.Error()
			return CreateParsedLossRunResponse{
				Error: common.NewNirvanaErrorWithCodeAndReason(424, err, msg),
			}
		}
		if errors.Is(err, parsed_loss_runs.ForbiddenError) {
			return CreateParsedLossRunResponse{
				Error: common.NewNirvanaForbiddenErrorf(err, "document", req.DocumentID),
			}
		}
		return CreateParsedLossRunResponse{
			Error: common.NewNirvanaInternalServerWithReason(err, "Internal server error occurred"),
		}
	}

	triggerLossRunAggregation(ctx, req, deps)

	return CreateParsedLossRunResponse{}
}

func triggerLossRunAggregation(ctx context.Context, req CreateParsedLossRunRequest, deps deps.Deps) {
	aggregateLossRunDeps := &underwritingjobs.AggregateLossRunsJobDeps{
		ParsedLossRunsWrapper: deps.ParsedLossRunWrapper,
		AppWrapper:            deps.ApplicationWrapper,
		JobberClient:          deps.JobberClient,
	}
	_, err := underwritingjobs.TriggerLossRunAggregation(ctx, req.Form.AdditionalDetails.ApplicationId, aggregateLossRunDeps)
	if err != nil {
		if errors.Is(err, underwritingjobs.ErrShouldTriggerFalse) {
			log.Info(ctx, "HandleCreateParsedLossRun: Skipping LossRunAggregation job", log.Err(err))
			return
		}
		log.Info(ctx, "HandleCreateParsedLossRun: Error triggering LossRunAggregation job", log.Err(err))
		underwritingjobs.EmitFailedMetricForJob(
			ctx, deps.Statter, jobs.AggregateLossRuns,
		)
	}
}

type CreateParsedLossRunRequest struct {
	DocumentID oapi_external.DocumentID
	Form       oapi_external.CreateLossRunDocumentRecordForm
}

type CreateParsedLossRunResponse struct {
	Error error
}
