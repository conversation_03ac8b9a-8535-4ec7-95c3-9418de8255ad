package nonfleet_underwriting

import (
	context "context"
	"fmt"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/summary"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	"nirvanatech.com/nirvana/common-go/log"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	nonfleet_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

func HandleGetApplicationReviewSummaryAuthz(
	ctx context.Context,
	deps deps.Deps,
	request GetApplicationReviewSummaryRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.ReadAction, request.AppReviewID)
}

func HandleGetApplicationReviewSummary(
	ctx context.Context,
	deps deps.Deps,
	req GetApplicationReviewSummaryRequest,
) GetApplicationReviewSummaryResponse {
	log.Info(ctx, "HandleGetApplicationReviewSummary", log.Any("req", req))

	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, req.AppReviewID)
	if err != nil {
		return GetApplicationReviewSummaryResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get app review for id %v, %v", req.AppReviewID, err),
			},
		}
	}

	var summary *nonfleet_uw.ApplicationReviewSummary
	var gErr error
	programType := appReview.GetProgram()

	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		summary, gErr = getSummary(ctx, deps.AdmittedBasePanel, deps.AdmittedSummaryPanel, appReview)
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeInvalid:
		return GetApplicationReviewSummaryResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get summary, invalid program type %v",
				programType)},
		}
	default:
		return GetApplicationReviewSummaryResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get summary, unidentified type %v",
				programType)},
		}
	}

	if gErr != nil {
		return GetApplicationReviewSummaryResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get summary %v", gErr)},
		}
	}

	return GetApplicationReviewSummaryResponse{
		Success: summary,
	}
}

func getSummary[T nf_app.AppInfo](
	ctx context.Context,
	basePanel *base_panel.BasePanel[T],
	summaryPanel *summary.SummaryPanel[T],
	reviewObj application_review.ApplicationReview,
) (*nonfleet_uw.ApplicationReviewSummary, error) {
	input, err := basePanel.GetPanelInput(ctx, reviewObj.GetID().String())
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get panel input")
	}

	summary, err := summaryPanel.Summary(ctx, input, reviewObj.GetProgram())
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get summary")
	}
	return summary, nil
}

type GetApplicationReviewSummaryRequest struct {
	AppReviewID string
}

type GetApplicationReviewSummaryResponse struct {
	Success     *nonfleet_uw.ApplicationReviewSummary
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *GetApplicationReviewSummaryResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *GetApplicationReviewSummaryResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &GetApplicationReviewSummaryResponse{}
