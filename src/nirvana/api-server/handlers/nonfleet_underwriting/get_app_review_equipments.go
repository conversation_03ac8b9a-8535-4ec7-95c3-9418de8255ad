package nonfleet_underwriting

import (
	"context"
	"fmt"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/equipments"

	nf_uw_interceptors "nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"

	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/common-go/log"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

func HandleGetApplicationReviewEquipmentsAuthz(
	ctx context.Context,
	deps nf_uw_interceptors.Deps,
	request GetApplicationReviewEquipmentsRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.ReadAction, request.AppReviewID)
}

func HandleGetApplicationReviewEquipments(
	ctx context.Context,
	deps nf_uw_interceptors.Deps,
	request GetApplicationReviewEquipmentsRequest,
) GetApplicationReviewEquipmentsResponse {
	log.Info(ctx, "HandleGetApplicationReviewEquipments", log.Any("req", request))

	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, request.AppReviewID)
	if err != nil {
		return GetApplicationReviewEquipmentsResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get app review for id %v, %v", request.AppReviewID, err),
			},
		}
	}

	var equipments *nonfleet_underwriting.ApplicationReviewEquipment
	var gErr error
	programType := appReview.GetProgram()

	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		equipments, gErr = getEquipments(ctx, deps.AdmittedBasePanel, deps.AdmittedEquipmentPanel, request)
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeInvalid:
		return GetApplicationReviewEquipmentsResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get equipments, invalid program type %v", programType)},
		}
	}
	if gErr != nil {
		return GetApplicationReviewEquipmentsResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get equipments %v", gErr)},
		}
	}

	return GetApplicationReviewEquipmentsResponse{
		Success: equipments,
	}
}

func getEquipments[T nf_app.AppInfo](
	ctx context.Context,
	basePanel *base_panel.BasePanel[T],
	equipmentsPanel *equipments.EquipmentsPanel[T],
	req GetApplicationReviewEquipmentsRequest,
) (*oapi_uw.ApplicationReviewEquipment, error) {
	input, err := basePanel.GetPanelInput(ctx, req.AppReviewID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get panel input")
	}
	equipmentInfo := oapi_uw.ApplicationReviewEquipment{
		Summary:    equipmentsPanel.EquipmentSummary(input),
		Units:      equipmentsPanel.EquipmentDetails(input, true),
		IsReviewed: equipmentsPanel.IsReviewed(input),
	}
	return &equipmentInfo, nil
}

type GetApplicationReviewEquipmentsRequest struct {
	AppReviewID string
}

type GetApplicationReviewEquipmentsResponse struct {
	Success     *oapi_uw.ApplicationReviewEquipment
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *GetApplicationReviewEquipmentsResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *GetApplicationReviewEquipmentsResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &GetApplicationReviewEquipmentsResponse{}
