package nonfleet_underwriting

import (
	"context"
	"fmt"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	"nirvanatech.com/nirvana/common-go/log"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

func HandleSetApplicationReviewMVRPullAuthz(
	ctx context.Context,
	deps deps.Deps,
	request SetApplicationReviewMVRPullRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.WriteAction, request.AppReviewID)
}

func HandleSetApplicationReviewMVRPull(
	ctx context.Context,
	deps deps.Deps,
	request SetApplicationReviewMVRPullRequest,
) SetApplicationReviewMVRPullResponse {
	log.Info(ctx, "HandleGetApplicationReviewOperations", log.Any("req", request))

	allowed := utils.IsUserUpdateAllowed(authz.UserFromContext(ctx))
	if !allowed {
		return SetApplicationReviewMVRPullResponse{
			Error: helpers.ErrorMessagePtr(errors.New("not allowed"), "user is not authorised for this endpoint"),
		}
	}
	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, request.AppReviewID)
	if err != nil {
		return SetApplicationReviewMVRPullResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get app review for id %v, %v", request.AppReviewID, err),
			},
		}
	}
	var gErr error
	programType := appReview.GetProgram()
	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		gErr = pullMVR(ctx, deps.AdmittedBasePanel, deps.AdmittedDriversPanel, appReview.GetID().String(), deps.FetcherClientFactory)
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeInvalid:
		return SetApplicationReviewMVRPullResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to set mvr pull, invalid program type %v", programType)},
		}
	}
	if gErr != nil {
		return SetApplicationReviewMVRPullResponse{Error: &oapi_common.ErrorMessage{Message: gErr.Error()}}
	}
	return SetApplicationReviewMVRPullResponse{Success: &oapi_common.MVRFlag{PullMvr: true}}
}

func pullMVR[T nf_app.AppInfo](
	ctx context.Context,
	basePanel *base_panel.BasePanel[T],
	driversPanel *driver.DriversPanel[T],
	appReviewId string,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) error {
	input, err := basePanel.GetPanelInput(ctx, appReviewId)
	if err != nil {
		return errors.Wrap(err, "Failed to get panel input")
	}
	return driversPanel.SetPullMVRAndUpdateOverrides(ctx, input, fetcherClientFactory)
}

type SetApplicationReviewMVRPullRequest struct {
	AppReviewID string
}

type SetApplicationReviewMVRPullResponse struct {
	Success     *oapi_common.MVRFlag
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *SetApplicationReviewMVRPullResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *SetApplicationReviewMVRPullResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &SetApplicationReviewMVRPullResponse{}
