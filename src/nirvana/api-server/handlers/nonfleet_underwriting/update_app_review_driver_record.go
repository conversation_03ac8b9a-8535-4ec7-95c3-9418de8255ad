package nonfleet_underwriting

import (
	"context"
	"fmt"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"

	"nirvanatech.com/nirvana/underwriting/jobs"

	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/common-go/log"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

func HandleUpdateApplicationReviewDriverRecordAuthz(
	ctx context.Context,
	deps deps.Deps,
	request UpdateApplicationReviewDriverRecordRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.WriteAction, request.AppReviewID)
}

func HandleUpdateApplicationReviewDriverRecord(
	ctx context.Context,
	deps deps.Deps,
	request UpdateApplicationReviewDriverRecordRequest,
) UpdateApplicationReviewDriverRecordResponse {
	log.Info(ctx, "HandleUpdateApplicationReviewDriverRecord", log.Any("req", request))

	allowed := utils.IsUserUpdateAllowed(authz.UserFromContext(ctx))
	if !allowed {
		return UpdateApplicationReviewDriverRecordResponse{
			Error: helpers.ErrorMessagePtr(errors.New("not allowed"), "user is not authorised for this endpoint"),
		}
	}
	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, request.AppReviewID)
	if err != nil {
		return UpdateApplicationReviewDriverRecordResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get app review for id %v, %v", request.AppReviewID, err),
			},
		}
	}
	if request.Form == nil {
		return UpdateApplicationReviewDriverRecordResponse{Error: &oapi_common.ErrorMessage{Message: "Form is required"}}
	}
	var gErr error
	programType := appReview.GetProgram()
	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		gErr = updateDrivers(ctx, deps.AdmittedBasePanel, deps.AdmittedDriversPanel, request, deps.FetcherClientFactory)
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeInvalid:
		return UpdateApplicationReviewDriverRecordResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get drivers, invalid program type %v", programType)},
		}
	}

	if gErr != nil {
		return UpdateApplicationReviewDriverRecordResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get drivers %v", gErr)},
		}
	}

	jobRunId, err := jobs.TriggerGenerateAppetiteFactorsNFJob(
		ctx,
		deps.Jobber,
		appReview.GetID().String(),
		jobs.TriggerSourceHandlerDriverRecordUpdate,
	)
	if err != nil {
		return UpdateApplicationReviewDriverRecordResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("failed to trigger appetite factors job for app review id %s", appReview.GetID().String())},
		}
	}
	log.Info(
		ctx,
		"successfully triggered appetite factors job for app review",
		log.String("appReviewId", appReview.GetID().String()),
		log.String("jobId", string(jobRunId.JobId)),
		log.Int("runId", int(jobRunId.RunId)),
	)

	return UpdateApplicationReviewDriverRecordResponse{Success: &emptyStruct}
}

func updateDrivers[T nf_app.AppInfo](
	ctx context.Context,
	basePanel *base_panel.BasePanel[T],
	driversPanel *driver.DriversPanel[T],
	req UpdateApplicationReviewDriverRecordRequest,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) error {
	input, err := basePanel.GetPanelInput(ctx, req.AppReviewID)
	if err != nil {
		return errors.Wrap(err, "Failed to get panel input")
	}
	if err := driversPanel.UpdateDriver(ctx, input, req.DLNumber, req.Form, fetcherClientFactory); err != nil {
		return errors.Wrap(err, "Failed to update driver")
	}
	return nil
}

type UpdateApplicationReviewDriverRecordRequest struct {
	AppReviewID, DLNumber string
	Form                  *oapi_uw.UpdateApplicationReviewDriverRecordForm
}

type UpdateApplicationReviewDriverRecordResponse struct {
	Success     *struct{}
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *UpdateApplicationReviewDriverRecordResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *UpdateApplicationReviewDriverRecordResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &UpdateApplicationReviewDriverRecordResponse{}
