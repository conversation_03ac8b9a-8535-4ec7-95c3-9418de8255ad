package nonfleet_underwriting

import (
	"context"
	"fmt"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/operations"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/str_utils"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	admitted_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/events/nonfleet_events"
	"nirvanatech.com/nirvana/infra/authz"
	statemachine "nirvanatech.com/nirvana/nonfleet/state-machine"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

const (
	creditScorePrefix = "CreditScore"
	usDotScorePrefix  = "USDotScore"
)

func HandleUpdateApplicationReviewOperationsAuthz(
	ctx context.Context,
	deps deps.Deps,
	request UpdateApplicationReviewOperationsRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.WriteAction, request.AppReviewID)
}

func HandleUpdateApplicationReviewOperations(
	ctx context.Context,
	deps deps.Deps,
	request UpdateApplicationReviewOperationsRequest,
) UpdateApplicationReviewOperationsResponse {
	log.Info(ctx, "HandleUpdateApplicationReviewOperations", log.Any("req", request))

	allowed := utils.IsUserUpdateAllowed(authz.UserFromContext(ctx))
	if !allowed {
		return UpdateApplicationReviewOperationsResponse{
			Error: helpers.ErrorMessagePtr(errors.New("not allowed"), "user is not authorised for this endpoint"),
		}
	}
	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, request.AppReviewID)
	if err != nil {
		return UpdateApplicationReviewOperationsResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get app review for id %v, %v", request.AppReviewID, err),
			},
		}
	}
	var gErr error
	programType := appReview.GetProgram()
	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		gErr = updateOperationDetails(ctx,
			deps.AdmittedOperationsPanel,
			deps.AdmittedAppWrapper,
			deps.NFAppStateMachineWrapper,
			request,
			appReview.GetApplicationID())
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeNonFleetCanopiusNRB, policy_enums.ProgramTypeInvalid:
		return UpdateApplicationReviewOperationsResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to set operation review, invalid program type %v", programType)},
		}
	}
	if gErr != nil {
		return UpdateApplicationReviewOperationsResponse{Error: &oapi_common.ErrorMessage{Message: gErr.Error()}}
	}
	return UpdateApplicationReviewOperationsResponse{Success: &emptyStruct}
}

func updateOperationDetails[T nf_app.AppInfo](
	ctx context.Context,
	operationsPanel *operations.OperationsPanel[T],
	applicationWrapper nf_app.Wrapper[T],
	NFAppStateMachineWrapper *statemachine.NFAppStateMachineImpl,
	req UpdateApplicationReviewOperationsRequest,
	appID uuid.UUID,
) error {
	if req.Form == nil {
		return errors.New("operations update form can't be nil")
	}

	err := operationsPanel.SetIsReviewed(ctx, req.AppReviewID, req.Form.IsReviewed)
	if err != nil {
		return errors.Wrap(err, "couldn't set reviewed for the operations panel")
	}

	if req.Form.BizOwnerCreditScore != nil {
		bizOwnerCreditScore, err := admitted_enums.CreditScoreString(
			str_utils.PrettyEnumString(
				string(*req.Form.BizOwnerCreditScore), creditScorePrefix,
			),
		)
		if err != nil {
			return errors.Wrap(err, "couldn't parse business owner credit score")
		}
		err = operationsPanel.UpdateBizOwnerCreditScore(
			ctx, req.AppReviewID, bizOwnerCreditScore,
		)
		if err != nil {
			return errors.Wrap(err, "couldn't set business owner credit score")
		}
	}

	if req.Form.UsDotScore != nil {
		usDotScore, err := enums.USDotScoreString(
			str_utils.PrettyEnumString(
				string(*req.Form.UsDotScore), usDotScorePrefix,
			),
		)
		if err != nil {
			return errors.Wrap(err, "couldn't parse usDotScore score")
		}
		err = operationsPanel.UpdateUSDotScore(ctx, req.AppReviewID, usDotScore)
		if err != nil {
			return errors.Wrap(err, "couldn't set usDotScore")
		}
	}

	if req.Form.EffectiveDate != nil {
		err = operationsPanel.UpdateEffectiveDate(ctx, req.AppReviewID, req.Form.EffectiveDate.Time)
		if err != nil {
			return errors.Wrap(err, "couldn't set eff date")
		}
		appObj, err := applicationWrapper.GetAppById(ctx, appID)
		if err != nil {
			return errors.Wrapf(err, "unable to get application for id %s", appID.String())
		}
		effectiveDateUpdateEvent, err := nonfleet_events.NewNFUWEffectiveDateUpdate(
			ctx, NFAppStateMachineWrapper.Deps.AuthWrapper, appObj, req.Form.EffectiveDate.Time,
		)
		if err != nil {
			log.Error(ctx, "Error while creating effective date update event", log.Err(err))
		}
		err = statemachine.ValidateAndUpload(
			ctx, &effectiveDateUpdateEvent, events.EventDeps{
				SegmentClient: NFAppStateMachineWrapper.Deps.SegmentClient,
			},
		)
		if err != nil {
			log.Error(ctx, "Error while uploading effective date update event", log.Err(err))
		}
	}

	return nil
}

type UpdateApplicationReviewOperationsRequest struct {
	AppReviewID string
	Form        *oapi_uw.UpdateApplicationReviewOperationForm
}

type UpdateApplicationReviewOperationsResponse struct {
	Success     *struct{}
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *UpdateApplicationReviewOperationsResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *UpdateApplicationReviewOperationsResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &UpdateApplicationReviewOperationsResponse{}
