package nonfleet_underwriting

import (
	"context"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"

	quoting_jobs "nirvanatech.com/nirvana/nonfleet/quoting-jobs"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	"nirvanatech.com/nirvana/common-go/log"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/nonfleet/rating"
	rating_jobs "nirvanatech.com/nirvana/nonfleet/rating-jobs"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

func HandlePostUWQuoteSubmitAuthz(
	ctx context.Context,
	deps deps.Deps,
	request PostUWQuoteSubmitRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.WriteAction, request.AppReviewID)
}

func HandlePostUWQuoteSubmit(
	ctx context.Context,
	deps deps.Deps,
	req PostUWQuoteSubmitRequest,
) PostUWQuoteSubmitResponse {
	log.Info(ctx, "HandlePostUWQuoteSubmit", log.Any("req", req))

	allowed := utils.IsUserUpdateAllowed(authz.UserFromContext(ctx))
	if !allowed {
		return PostUWQuoteSubmitResponse{
			Error: helpers.ErrorMessagePtr(errors.New("not allowed"), "user is not authorised for this endpoint"),
		}
	}

	appReviewObj, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, req.AppReviewID)
	if err != nil {
		return PostUWQuoteSubmitResponse{
			Error: helpers.ErrorMessagePtr(err, "couldn't fetch app review"),
		}
	}
	programType := appReviewObj.GetProgram()

	var submitError error
	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		submitError = SubmitAdmittedAppReviewForQuote(ctx, deps, req, appReviewObj)
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeInvalid:
		{
		}
	default:
		log.Error(
			ctx, "HandlePostUWQuoteSubmit:ProgramType invalid.",
			log.Err(err),
			log.Any("appReviewID", req.AppReviewID),
			log.String("programType", programType.String()),
		)
		errMessage := helpers.WrapErrorMessage(err, "ProgramType invalid.")
		return PostUWQuoteSubmitResponse{Error: &errMessage}
	}
	if submitError != nil {
		return PostUWQuoteSubmitResponse{Error: helpers.ErrorMessagePtr(err, "unable to submit app review for quote")}
	}

	return PostUWQuoteSubmitResponse{Success: &struct{}{}}
}

type PostUWQuoteSubmitRequest struct {
	AppReviewID string
	Bindable    bool
}

type PostUWQuoteSubmitResponse struct {
	Success     *struct{}
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *PostUWQuoteSubmitResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *PostUWQuoteSubmitResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

func SubmitAdmittedAppReviewForQuote(
	ctx context.Context,
	deps deps.Deps,
	req PostUWQuoteSubmitRequest,
	appReview application_review.ApplicationReview,
) error {
	err := driver.RepullMVRAndPersistInOverrides(
		ctx,
		deps.AdmittedAppWrapper,
		deps.NFApplicationReviewWrapper,
		deps.FFClient,
		appReview,
		deps.FetcherClientFactory,
	)
	if err != nil {
		return errors.Wrap(err, "unable to re-pull MVR and persist in overrides")
	}

	subId := uuid.New()
	err = utils.CreateNewSubmissionForUW(ctx, deps, req.AppReviewID, subId, req.Bindable)
	if err != nil {
		return errors.Wrapf(err, "unable to create new submission for UW")
	}

	runType := rating.RunTypeUWSubmission
	if req.Bindable {
		runType = rating.RunTypeUWBindableSubmission
	}
	jobRunId, err := deps.Jobber.AddJobRun(ctx,
		jtypes.NewAddJobRunParams(
			rating_jobs.GenerateNFIndicationWorkflow,
			&rating_jobs.NFIndicationWorkflowMessage{
				SubmissionID: subId,
				ProgramType:  policy_enums.ProgramTypeNonFleetAdmitted,
				IndicationReqs: []rating_jobs.IndicationType{
					{
						ID:          uuid.New(),
						PackageType: app_enums.IndicationOptionTagBasic,
					},
					{
						ID:          uuid.New(),
						PackageType: app_enums.IndicationOptionTagStandard,
					},
					{
						ID:          uuid.New(),
						PackageType: app_enums.IndicationOptionTagComplete,
					},
				},
				IsMVRPulled: appReview.IsMVRPulled(),
				RunType:     runType,
			},
			jtypes.NewMetadata(jtypes.Immediate),
		))
	if err != nil {
		return errors.Wrapf(err, "unable to add job run")
	}

	if _, err := quoting_jobs.TriggerGenerateNonFleetAuthoritiesJob(ctx, deps.Jobber, req.AppReviewID); err != nil {
		log.Error(ctx, "failed to trigger TriggerGenerateNonFleetAuthoritiesJob job", log.Err(err))
		return errors.Wrapf(err, "failed to trigger TriggerGenerateNonFleetAuthoritiesJob job for app review %s", req.AppReviewID)
	}

	err = deps.AdmittedAppWrapper.UpdateSubmission(ctx, subId,
		func(sub application.Submission[*admitted_app.AdmittedApp]) (*application.Submission[*admitted_app.AdmittedApp], error) {
			sub.JobRunInfo = &jobRunId
			return &sub, nil
		})
	if err != nil {
		return errors.Wrapf(err, "unable to update submission with jobInfo")
	}
	return nil
}

var _ common.HandlerResponse = &PostUWQuoteSubmitResponse{}
