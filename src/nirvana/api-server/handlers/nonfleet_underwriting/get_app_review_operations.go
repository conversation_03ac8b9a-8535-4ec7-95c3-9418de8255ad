package nonfleet_underwriting

import (
	"context"
	"fmt"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/operations"

	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/common-go/log"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	nonfleet_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

func HandleGetApplicationReviewOperationsAuthz(
	ctx context.Context,
	deps deps.Deps,
	request GetApplicationReviewOperationsRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.ReadAction, request.AppReviewID)
}

func HandleGetApplicationReviewOperations(
	ctx context.Context,
	deps deps.Deps,
	request GetApplicationReviewOperationsRequest,
) GetApplicationReviewOperationsResponse {
	log.Info(ctx, "HandleGetApplicationReviewOperations", log.Any("req", request))

	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, request.AppReviewID)
	if err != nil {
		return GetApplicationReviewOperationsResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get app review for id %v, %v", request.AppReviewID, err),
			},
		}
	}

	var operations *nonfleet_uw.ApplicationReviewOperation
	var gErr error
	programType := appReview.GetProgram()

	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		operations, gErr = getOperations(ctx, deps.AdmittedBasePanel, deps.AdmittedOperationsPanel, request)
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeInvalid:
		return GetApplicationReviewOperationsResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get operations, invalid program type %v",
				programType)},
		}
	default:
		return GetApplicationReviewOperationsResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get operations, unidentified type %v",
				programType)},
		}
	}
	if gErr != nil {
		return GetApplicationReviewOperationsResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get operations %v", gErr)},
		}
	}

	return GetApplicationReviewOperationsResponse{
		Success: operations,
	}
}

func getOperations[T nf_app.AppInfo](
	ctx context.Context,
	basePanel *base_panel.BasePanel[T],
	operationsPanel *operations.OperationsPanel[T],
	req GetApplicationReviewOperationsRequest,
) (*oapi_uw.ApplicationReviewOperation, error) {
	input, err := basePanel.GetPanelInput(ctx, req.AppReviewID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get panel input")
	}
	operations, err := operationsPanel.Operations(ctx, input)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get operations panel")
	}
	return operations, nil
}

type GetApplicationReviewOperationsRequest struct {
	AppReviewID string
}

type GetApplicationReviewOperationsResponse struct {
	Success     *oapi_uw.ApplicationReviewOperation
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *GetApplicationReviewOperationsResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *GetApplicationReviewOperationsResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &GetApplicationReviewOperationsResponse{}
