package nonfleet_underwriting

import (
	context "context"
	"fmt"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/packages"

	nf_uw_interceptors "nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"

	"github.com/cockroachdb/errors"

	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/common-go/log"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
	nonfleet_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

func HandleGetApplicationReviewPackagesAuthz(
	ctx context.Context,
	deps nf_uw_interceptors.Deps,
	request GetApplicationReviewPackagesRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.ReadAction, request.AppReviewID)
}

func HandleGetApplicationReviewPackages(
	ctx context.Context,
	deps nf_uw_interceptors.Deps,
	req GetApplicationReviewPackagesRequest,
) GetApplicationReviewPackagesResponse {
	log.Info(ctx, "HandleGetApplicationReviewPackages", log.Any("req", req))
	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, req.AppReviewID)
	if err != nil {
		return GetApplicationReviewPackagesResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get app review for id %v, %v", req.AppReviewID, err),
			},
		}
	}

	var packages *nonfleet_underwriting.ApplicationReviewPackages
	var gErr error
	programType := appReview.GetProgram()

	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		packages, gErr = getPackages(ctx, deps.AdmittedBasePanel, deps.AdmittedPackagesPanel, req)
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeInvalid:
		return GetApplicationReviewPackagesResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get packages, invalid program type %v", programType)},
		}
	}

	if gErr != nil {
		return GetApplicationReviewPackagesResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to get packages %v", gErr)},
		}
	}

	return GetApplicationReviewPackagesResponse{
		Success: packages,
	}
}

func getPackages[T nf_app.AppInfo](
	ctx context.Context,
	basePanel *base_panel.BasePanel[T],
	packagesPanel *packages.PackagesPanel[T],
	req GetApplicationReviewPackagesRequest,
) (*nonfleet_underwriting.ApplicationReviewPackages, error) {
	input, err := basePanel.GetPanelInput(ctx, req.AppReviewID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get panel input")
	}

	packages, err := packagesPanel.GetPackages(ctx, input)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get packages")
	}
	return packages, nil
}

type GetApplicationReviewPackagesRequest struct {
	AppReviewID string
}

type GetApplicationReviewPackagesResponse struct {
	Success     *nonfleet_uw.ApplicationReviewPackages
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *GetApplicationReviewPackagesResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *GetApplicationReviewPackagesResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &GetApplicationReviewPackagesResponse{}
