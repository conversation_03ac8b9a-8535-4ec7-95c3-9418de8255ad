package nonfleet_underwriting

import (
	"context"
	"fmt"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/packages"

	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/common-go/log"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

func HandleUpdateApplicationReviewPackagesAuthz(
	ctx context.Context,
	deps deps.Deps,
	request UpdateApplicationReviewPackagesRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.WriteAction, request.AppReviewID)
}

func HandleUpdateApplicationReviewPackages(
	ctx context.Context,
	deps deps.Deps,
	request UpdateApplicationReviewPackagesRequest,
) UpdateApplicationReviewPackagesResponse {
	log.Info(ctx, "HandleUpdateApplicationReviewPackages", log.Any("req", request))

	allowed := utils.IsUserUpdateAllowed(authz.UserFromContext(ctx))
	if !allowed {
		return UpdateApplicationReviewPackagesResponse{
			Error: helpers.ErrorMessagePtr(errors.New("not allowed"), "user is not authorised for this endpoint"),
		}
	}

	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, request.AppReviewID)
	if err != nil {
		return UpdateApplicationReviewPackagesResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get app review for id %v, %v", request.AppReviewID, err),
			},
		}
	}

	programType := appReview.GetProgram()
	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		err = updatePackages(ctx, deps.AdmittedPackagesPanel, request)
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeInvalid:
		return UpdateApplicationReviewPackagesResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to update packages panel, "+
				"invalid program type %v", programType)},
		}
	}
	if err != nil {
		return UpdateApplicationReviewPackagesResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to update packages panel %v", err)},
		}
	}

	return UpdateApplicationReviewPackagesResponse{Success: &emptyStruct}
}

func updatePackages[T nf_app.AppInfo](
	ctx context.Context,
	packagesPanel *packages.PackagesPanel[T],
	req UpdateApplicationReviewPackagesRequest,
) error {
	if req.Form == nil {
		return errors.New("package update form can't be nil")
	}

	if req.Form.IsReviewed != nil {
		err := packagesPanel.SetIsReviewed(ctx, req.AppReviewID, *req.Form.IsReviewed)
		if err != nil {
			return errors.Wrap(err, "couldn't set reviewed for the packages tab")
		}
	}

	if err := packagesPanel.UpdatePackages(
		ctx,
		req.AppReviewID,
		req.Form.AlPercent,
		req.Form.ApdPercent,
		req.Form.MtcPercent,
		req.Form.GlPercent,
		req.Form.AncillaryCoverages,
		req.Form.PaymentPlan,
	); err != nil {
		return errors.Wrap(err, "couldn't update packages")
	}

	return nil
}

type UpdateApplicationReviewPackagesRequest struct {
	AppReviewID string
	Form        *oapi_uw.UpdateApplicationReviewPackagesForm
}

type UpdateApplicationReviewPackagesResponse struct {
	Success     *struct{}
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *UpdateApplicationReviewPackagesResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *UpdateApplicationReviewPackagesResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &UpdateApplicationReviewPackagesResponse{}
