package nonfleet_underwriting

import (
	"context"
	"fmt"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/safety"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	"nirvanatech.com/nirvana/common-go/log"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

func HandleUpdateApplicationReviewSafetyAuthz(
	ctx context.Context,
	deps deps.Deps,
	request UpdateApplicationReviewSafetyRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.WriteAction, request.AppReviewID)
}

func HandleUpdateApplicationReviewSafety(
	ctx context.Context,
	deps deps.Deps,
	request UpdateApplicationReviewSafetyRequest,
) UpdateApplicationReviewSafetyResponse {
	log.Info(ctx, "HandleUpdateApplicationReviewSafety", log.Any("req", request))

	allowed := utils.IsUserUpdateAllowed(authz.UserFromContext(ctx))
	if !allowed {
		return UpdateApplicationReviewSafetyResponse{
			Error: helpers.ErrorMessagePtr(errors.New("not allowed"), "user is not authorised for this endpoint"),
		}
	}

	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, request.AppReviewID)
	if err != nil {
		return UpdateApplicationReviewSafetyResponse{
			Error: &oapi_common.ErrorMessage{
				Message: fmt.Sprintf("Failed to get app review for id %v, %v", request.AppReviewID, err),
			},
		}
	}

	programType := appReview.GetProgram()
	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		err = updateSafetyDetails(ctx, deps.AdmittedSafetyPanel, request)
	case policy_enums.ProgramTypeFleet, policy_enums.ProgramTypeInvalid:
		return UpdateApplicationReviewSafetyResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to update safety panel, "+
				"invalid program type %v", programType)},
		}
	}
	if err != nil {
		return UpdateApplicationReviewSafetyResponse{
			Error: &oapi_common.ErrorMessage{Message: fmt.Sprintf("Failed to update safety panel %v", err)},
		}
	}

	return UpdateApplicationReviewSafetyResponse{Success: &emptyStruct}
}

func updateSafetyDetails[T nf_app.AppInfo](
	ctx context.Context,
	safetyPanel *safety.SafetyPanel[T],
	req UpdateApplicationReviewSafetyRequest,
) error {
	if req.Form == nil {
		return errors.New("safety update form can't be nil")
	}

	err := safetyPanel.SetIsReviewed(ctx, req.AppReviewID, req.Form.IsReviewed)
	if err != nil {
		return errors.Wrap(err, "couldn't set reviewed for the safety tab")
	}

	if req.Form.SafetyCredits != nil {
		err = safetyPanel.SetSafetyCredit(ctx, req.AppReviewID, *req.Form.SafetyCredits)
		if err != nil {
			return errors.Wrap(err, "couldn't set safety credits")
		}
	}

	return nil
}

type UpdateApplicationReviewSafetyRequest struct {
	AppReviewID string
	Form        *oapi_uw.UpdateApplicationReviewSafetyForm
}

type UpdateApplicationReviewSafetyResponse struct {
	Success     *struct{}
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *UpdateApplicationReviewSafetyResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *UpdateApplicationReviewSafetyResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &UpdateApplicationReviewSafetyResponse{}
