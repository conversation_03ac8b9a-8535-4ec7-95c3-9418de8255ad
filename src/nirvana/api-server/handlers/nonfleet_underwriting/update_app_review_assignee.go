package nonfleet_underwriting

import (
	"context"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/summary"

	"github.com/cockroachdb/errors"
	"github.com/labstack/echo/v4"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	"nirvanatech.com/nirvana/common-go/log"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/events/nonfleet_events"
	"nirvanatech.com/nirvana/infra/authz"
	statemachine "nirvanatech.com/nirvana/nonfleet/state-machine"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

func HandleUpdateAppReviewAssigneeAuthz(
	ctx context.Context,
	deps deps.Deps,
	request UpdateAppReviewAssigneeRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(
		ctx,
		deps,
		authz.UserFromContext(ctx),
		authz.WriteAction,
		request.ApplicationReviewId,
	)
}

func HandleUpdateAppReviewAssignee(
	echoCtx echo.Context,
	deps deps.Deps,
	request UpdateAppReviewAssigneeRequest,
) error {
	ctx := echoCtx.Request().Context()
	log.Info(ctx, "HandleUpdateAppReviewAssignee", log.Any("req", request))

	allowed := utils.IsUserUpdateAllowed(authz.UserFromContext(ctx))
	if !allowed {
		return common.NewHttpErrorf(http.StatusForbidden, "user is not authorised for this endpoint")
	}
	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, request.ApplicationReviewId)
	if err != nil {
		return common.WrapAsHttpErrorf(
			err,
			http.StatusBadRequest,
			"failed to get app review for id %v",
			request.ApplicationReviewId,
		)
	}

	if appReview.GetProgram() != enums.ProgramTypeNonFleetAdmitted {
		return common.NewHttpErrorf(http.StatusBadRequest, "invalid program type")
	}

	uw, err := updateAssignee(ctx, request, deps.AdmittedBasePanel, deps.AdmittedSummaryPanel)
	if err != nil {
		return common.NewHttpErrorf(http.StatusBadRequest, "failed to update assignee %v", err)
	}

	appObj, err := deps.AdmittedAppWrapper.GetAppById(ctx, appReview.GetApplicationID())
	if err != nil {
		return common.WrapAsHttpErrorf(
			err,
			http.StatusBadRequest,
			"failed to get app for id %v",
			appReview.GetApplicationID(),
		)
	}

	uwReassignedEvent, err := nonfleet_events.NewNFUWReassigned(
		ctx, deps.AuthWrapper, appObj, uw, false, // BypassUWAssignmentNotification is set to false
	)
	if err != nil {
		log.Error(ctx, "failed to create nf uw reassigned event", log.Err(err))
	}
	err = statemachine.ValidateAndUpload(
		ctx, &uwReassignedEvent, events.EventDeps{
			SegmentClient: deps.SegmentClient,
		},
	)
	if err != nil {
		log.Error(ctx, "failed to upload nf uw reassigned event", log.Err(err))
	}

	return echoCtx.JSON(http.StatusOK, nil)
}

func updateAssignee[T nf_app.AppInfo](
	ctx context.Context,
	req UpdateAppReviewAssigneeRequest,
	basePanel *base_panel.BasePanel[T],
	summaryPanel *summary.SummaryPanel[T],
) (*authz.User, error) {
	input, err := basePanel.GetPanelInput(ctx, req.ApplicationReviewId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get panel input for appReviewID: %s", req.ApplicationReviewId)
	}
	uw, err := summaryPanel.UpdateAssignee(ctx, input, req.Form.UnderwriterID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get update assignee for appReviewID: %s", req.ApplicationReviewId)
	}

	return uw, nil
}

type UpdateAppReviewAssigneeRequest struct {
	ApplicationReviewId string
	Form                oapi_uw.ApplicationReviewAssigneesForm
}
