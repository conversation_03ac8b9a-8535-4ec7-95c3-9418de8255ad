package nonfleet_underwriting

import (
	"context"
	"net/http"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/base_panel"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"

	"github.com/cockroachdb/errors"
	"github.com/labstack/echo/v4"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting/utils"
	"nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	"nirvanatech.com/nirvana/common-go/log"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

func HandleGetApplicationReviewDriversAuthz(
	ctx context.Context,
	deps deps.Deps,
	request GetApplicationReviewDriversRequest,
) common.HandlerAuthzResponse {
	return utils.HasPermissionOverAppReview(ctx, deps, authz.UserFromContext(ctx), authz.ReadAction, request.AppReviewID)
}

func HandleGetApplicationReviewDrivers(
	echCtx echo.Context,
	deps deps.Deps,
	request GetApplicationReviewDriversRequest,
) error {
	ctx := echCtx.Request().Context()
	log.Info(ctx, "HandleGetApplicationReviewDrivers", log.Any("req", request))
	appReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByID(ctx, request.AppReviewID)
	if err != nil {
		log.Error(ctx, "failed to get app review",
			log.String("appReviewId", request.AppReviewID),
			log.Err(err))
		return common.NewNirvanaNotFoundErrorf(err, common.EntityApplicationReview, request.AppReviewID)
	}

	var drivers *oapi_uw.ApplicationReviewDriver
	var gErr error
	programType := appReview.GetProgram()

	switch programType {
	case policy_enums.ProgramTypeNonFleetAdmitted:
		drivers, gErr = GetDrivers(ctx, deps.AdmittedBasePanel, deps.AdmittedDriversPanel, request, deps.FetcherClientFactory)
	default:
		errMessage := "Failed to get drivers, invalid program type"
		log.Error(ctx, errMessage, log.String("programType", programType.String()))
		return common.NewHttpErrorf(http.StatusInternalServerError, errMessage)
	}

	if gErr != nil {
		log.Error(ctx, "Failed to get drivers", log.Err(gErr))
		return common.NewNirvanaInternalServerWithReason(gErr, "Failed to get drivers")
	}

	return echCtx.JSON(http.StatusOK, drivers)
}

func GetDrivers[T nf_app.AppInfo](
	ctx context.Context,
	basePanel *base_panel.BasePanel[T],
	driversPanel *driver.DriversPanel[T],
	req GetApplicationReviewDriversRequest,
	fetcherClientFactory data_fetching.FetcherClientFactory,
) (*oapi_uw.ApplicationReviewDriver, error) {
	input, err := basePanel.GetPanelInput(ctx, req.AppReviewID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get panel input")
	}

	records, err := driversPanel.Drivers(ctx, input, fetcherClientFactory, false)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get drivers")
	}

	drivers := oapi_uw.ApplicationReviewDriver{
		Records:     records,
		IsReviewed:  driversPanel.IsReviewed(input),
		IsMVRPulled: driversPanel.IsMVRPulled(input),
	}
	return &drivers, nil
}

type GetApplicationReviewDriversRequest struct {
	AppReviewID string
}

type GetApplicationReviewDriversResponse struct {
	Success     *oapi_uw.ApplicationReviewDriver
	Error       *oapi_common.ErrorMessage
	ServerError *oapi_common.ErrorMessage
}

func (g *GetApplicationReviewDriversResponse) StatusCode() int {
	switch {
	case g.Success != nil:
		return http.StatusOK
	case g.Error != nil:
		return http.StatusUnprocessableEntity
	case g.ServerError != nil:
		return http.StatusUnprocessableEntity
	default:
		return http.StatusInternalServerError
	}
}

func (g *GetApplicationReviewDriversResponse) Body() interface{} {
	switch {
	case g.Success != nil:
		return *g.Success
	case g.Error != nil:
		return *g.Error
	case g.ServerError != nil:
		return *g.ServerError
	default:
		return nil
	}
}

var _ common.HandlerResponse = &GetApplicationReviewDriversResponse{}
