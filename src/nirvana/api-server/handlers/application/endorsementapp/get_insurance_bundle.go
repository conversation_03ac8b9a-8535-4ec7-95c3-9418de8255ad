package endorsementapp

import (
	"context"
	"slices"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/google/uuid"
	oapitypes "github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/application/endorsementapp/converters"
	"nirvanatech.com/nirvana/api-server/handlers/common/application"
	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement/impls/nonfleet"
	commonib "nirvanatech.com/nirvana/api-server/handlers/common/ib"
	"nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/infra/authz"
	ibmodel "nirvanatech.com/nirvana/insurance-bundle/model"
	commonoapi "nirvanatech.com/nirvana/openapi-specs/components/common"
	endorsementappoapi "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
	iboapi "nirvanatech.com/nirvana/openapi-specs/components/insurance-bundle"
	"nirvanatech.com/nirvana/openapi-specs/components/nirvana"
)

type processors struct {
	driver   endorsement.DriverChangeProcessor
	vehicle  endorsement.VehicleChangeProcessor
	policy   endorsement.PolicyProcessor
	coverage endorsement.CoverageProcessor
}

type bundleData struct {
	companyName         string
	drivers             []iboapi.Driver
	vehicles            []iboapi.Vehicle
	coverages           []commonoapi.Coverage
	coverageGroups      []commonoapi.CoverageGroup
	packageType         nirvana.PackageName
	totalPremium        float64
	autoLiabilityPolicy *ibmodel.Policy
	companyContact      companyContact
}

type userInfo struct {
	producer    nirvana.ProducerInfo
	underwriter nirvana.UserInfo
}

func HandleGetInsuranceBundle(
	ctx context.Context,
	deps deps.Deps,
	bundleID string,
) (*iboapi.GetInsuranceBundleResponse, error) {
	// TODO(CIP-641): Get the program type from the client.
	programType := insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted
	ib, err := getLatestInsuranceBundle(ctx, deps, bundleID, programType)
	if err != nil {
		return nil, err
	}

	if vErr := validateUserAccess(ctx, ib); vErr != nil {
		return nil, vErr
	}

	bundleProcessors, err := initializeProcessors(ib.ProgramType)
	if err != nil {
		return nil, err
	}

	bundleDetails, err := extractBundleData(ctx, deps, bundleProcessors, ib)
	if err != nil {
		return nil, err
	}

	userDetails, err := getUserInformation(ctx, deps, ib)
	if err != nil {
		return nil, err
	}

	additionalInsureds, err := getAdditionalInsureds(bundleProcessors, ib)
	if err != nil {
		return nil, err
	}

	return &iboapi.GetInsuranceBundleResponse{
		AdditionalInsureds: additionalInsureds,
		CompanyName:        bundleDetails.companyName,
		CoverageGroups:     bundleDetails.coverageGroups,
		Coverages:          bundleDetails.coverages,
		Drivers:            bundleDetails.drivers,
		EffectiveDate: oapitypes.Date{
			Time: ib.DefaultEffectiveDuration.Start.AsTime(),
		},
		EffectiveDateTo: oapitypes.Date{
			Time: ib.DefaultEffectiveDuration.End.AsTime(),
		},
		Status:          converters.InsuranceBundleStateToPolicyStatus(ib.State),
		MailingAddress:  bundleDetails.companyContact.MailingAddress,
		Owner:           bundleDetails.companyContact.BusinessOwner,
		PackageType:     bundleDetails.packageType,
		PolicyNumber:    bundleDetails.autoLiabilityPolicy.PolicyNumber,
		Producer:        userDetails.producer,
		TerminalAddress: bundleDetails.companyContact.TerminalAddress,
		TotalPremium:    bundleDetails.totalPremium,
		Underwriter:     userDetails.underwriter,
		Vehicles:        bundleDetails.vehicles,
	}, nil
}

func validateUserAccess(ctx context.Context, ib *ibmodel.InsuranceBundle) error {
	isUserAuthorised := checkAgencyAccess(authz.UserFromContext(ctx), ib.DefaultSeller.GetAgencyID())
	if !isUserAuthorised {
		return common.NewNirvanaUnauthorizedErrorf(common.ErrUserUnauthorized, "", "")
	}
	return nil
}

func initializeProcessors(programType insurancecoreproto.ProgramType) (*processors, error) {
	driverProcessor, err := endorsement.GetProcessor[endorsement.DriverChangeProcessor](programType)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, endorsement.ErrGetDriverProcessor.Error())
	}

	vehicleProcessor, err := endorsement.GetProcessor[endorsement.VehicleChangeProcessor](programType)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, endorsement.ErrGetVehicleProcessor.Error())
	}

	policyProcessor, err := endorsement.GetProcessor[endorsement.PolicyProcessor](programType)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, endorsement.ErrGetPolicyProcessor.Error())
	}

	coverageProcessor, err := endorsement.GetProcessor[endorsement.CoverageProcessor](programType)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, endorsement.ErrGetCoverageProcessor.Error())
	}

	return &processors{
		driver:   driverProcessor,
		vehicle:  vehicleProcessor,
		policy:   policyProcessor,
		coverage: coverageProcessor,
	}, nil
}

func extractBundleData(
	ctx context.Context,
	deps deps.Deps,
	p *processors,
	ib *ibmodel.InsuranceBundle,
) (*bundleData, error) {
	ibLastSegment := ib.GetLastSegment()
	if ibLastSegment == nil {
		return nil, common.NewNirvanaInternalServerWithReason(nil, commonib.ErrGetLastSegment.Error())
	}

	drivers, vehicles := extractDriversAndVehicles(ctx, p, ibLastSegment)

	packageType, totalPremium, err := extractPackageInfo(ctx, deps, ib)
	if err != nil {
		return nil, err
	}

	coverages, coverageGroups := extractCoverageInfo(ctx, p.coverage, ibLastSegment)

	autoPolicy, err := p.policy.ExtractAutoLiabilityPolicy(ibLastSegment)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, ErrExtractAutoPolicy.Error())
	}

	return &bundleData{
		companyName:         ibLastSegment.PrimaryInsured.Name.BusinessName,
		drivers:             drivers,
		vehicles:            vehicles,
		coverages:           coverages,
		coverageGroups:      coverageGroups,
		packageType:         *packageType,
		totalPremium:        *totalPremium,
		autoLiabilityPolicy: autoPolicy,
		companyContact:      getCompanyContact(autoPolicy, ib.GetProgramType()),
	}, nil
}

func getUserInformation(ctx context.Context, deps deps.Deps, ib *ibmodel.InsuranceBundle) (*userInfo, error) {
	originalApplicationID, err := parseApplicationID(ib.GetMetadata().RootApplicationId)
	if err != nil {
		return nil, err
	}

	producer, err := getProducerInfo(ctx, deps, originalApplicationID, ib.GetProgramType())
	if err != nil {
		return nil, err
	}

	underwriter, err := getUnderwriterInfo(ctx, deps, originalApplicationID, ib.GetProgramType())
	if err != nil {
		return nil, err
	}

	return &userInfo{
		producer:    *producer,
		underwriter: *underwriter,
	}, nil
}

func extractDriversAndVehicles(
	ctx context.Context,
	p *processors,
	segment *ibmodel.InsuranceBundleSegment,
) ([]iboapi.Driver, []iboapi.Vehicle) {
	driverChanges := p.driver.ExtractInitialDrivers(ctx, segment)
	vehicleChanges := p.vehicle.ExtractInitialVehicles(ctx, segment)

	drivers := slice_utils.Map(driverChanges, func(change endorsementappoapi.DriverChange) iboapi.Driver {
		return change.Driver
	})

	vehicles := slice_utils.Map(vehicleChanges, func(change endorsementappoapi.VehicleChange) iboapi.Vehicle {
		return change.Vehicle
	})

	return drivers, vehicles
}

func extractPackageInfo(
	ctx context.Context,
	deps deps.Deps,
	ib *ibmodel.InsuranceBundle,
) (*nirvana.PackageName, *float64, error) {
	bindableSubID, err := uuid.Parse(ib.GetMetadata().RootBindableSubmissionId)
	if err != nil {
		return nil, nil, common.NewNirvanaInternalServerWithReason(err, ErrParseSubmissionID.Error())
	}

	packageType, _, err := getPackageTypeAndTotalPremium(
		ctx,
		deps,
		bindableSubID,
		ib.ProgramType,
	)
	if err != nil {
		return nil, nil, common.NewNirvanaInternalServerWithReason(err, ErrGetPackageAndPremium.Error())
	}

	// NOTE:
	//
	// We are passing a nil rate basis value to get the total premium, because this
	// assumes all charges are amount based. In the future, we may need to pass other
	// kinds of rate basis values to get the total premium.
	//
	// We also supply the useChargeAdjustments as true to include charge adjustments in
	// the total. This makes sure correct total premium is calculated.
	chargeDistribution, err := ib.GetChargesWithDistribution(nil, true)
	if err != nil {
		return nil, nil, common.NewNirvanaInternalServerWithReason(err, ErrGetChargeDistribution.Error())
	}
	totalPremium, _ := chargeDistribution.TotalCharge.Float64()
	return &packageType, &totalPremium, nil
}

// Extract coverage information
func extractCoverageInfo(
	ctx context.Context,
	processor endorsement.CoverageProcessor,
	segment *ibmodel.InsuranceBundleSegment,
) ([]commonoapi.Coverage, []commonoapi.CoverageGroup) {
	subCoverageGroups := processor.ExtractInitialCoverages(ctx, segment)
	coverages := slice_utils.Map(subCoverageGroups, func(change endorsementappoapi.SubCoverageChangeGroup) commonoapi.Coverage {
		return change.Coverage
	})

	// Sort coverages in the order of AL, APD, MTC, GL
	slices.SortFunc(coverages, func(a, b commonoapi.Coverage) int {
		aPos := slices.Index(nonfleet.CoveragesOrder, a.Name)
		bPos := slices.Index(nonfleet.CoveragesOrder, b.Name)
		return aPos - bPos
	})

	var coverageGroups []commonoapi.CoverageGroup
	for _, subCoverageGroup := range subCoverageGroups {
		children := make([]commonoapi.SubCoverage, 0)
		for _, sc := range subCoverageGroup.SubCoverages {
			children = append(children, sc.SubCoverage)
		}
		coverageGroups = append(coverageGroups, commonoapi.CoverageGroup{
			Children: children,
			Parent:   subCoverageGroup.Coverage,
		})
	}

	// Sort coverage groups
	slices.SortFunc(coverageGroups, func(a, b commonoapi.CoverageGroup) int {
		aPos := slices.Index(nonfleet.CoveragesOrder, a.Parent.Name)
		bPos := slices.Index(nonfleet.CoveragesOrder, b.Parent.Name)
		return aPos - bPos
	})

	return coverages, coverageGroups
}

func parseApplicationID(appIDStr string) (uuid.UUID, error) {
	appID, err := uuid.Parse(appIDStr)
	if err != nil {
		return uuid.Nil, common.NewNirvanaInternalServerWithReason(err, application.ErrParseApplicationID.Error())
	}
	return appID, nil
}

func getProducerInfo(
	ctx context.Context,
	deps deps.Deps,
	applicationID uuid.UUID,
	programType insurancecoreproto.ProgramType,
) (*nirvana.ProducerInfo, error) {
	producer, _, err := getProducerAndMarketer(ctx, deps, applicationID, programType)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, "failed to get producer")
	}

	producerInfo, err := deps.AuthWrapper.FetchUserInfo(ctx, producer)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, "failed to get producer info")
	}

	retval := converters.UserInfoToProducerInfo(producerInfo)
	return &retval, nil
}

func getUnderwriterInfo(
	ctx context.Context,
	deps deps.Deps,
	applicationID uuid.UUID,
	programType insurancecoreproto.ProgramType,
) (*nirvana.UserInfo, error) {
	underwriterID, err := application.GetUnderwriterID(
		ctx,
		deps.ApplicationWrapper,
		deps.AdmittedAppWrapper,
		applicationID,
		programType,
	)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, ErrGetUnderwriterID.Error())
	}

	uwInfo, err := deps.AuthWrapper.FetchUserInfo(ctx, underwriterID)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, "failed to get underwriter info")
	}

	return &nirvana.UserInfo{
		Email:     pointer_utils.ToPointer(oapitypes.Email(uwInfo.Email)),
		FirstName: uwInfo.FirstName,
		LastName:  uwInfo.LastName,
	}, nil
}

func getAdditionalInsureds(
	p *processors,
	ib *ibmodel.InsuranceBundle,
) ([]nirvana.AdditionalInsured, error) {
	retval := make([]nirvana.AdditionalInsured, 0)
	ibLastSegment := ib.GetLastSegment()
	if ibLastSegment == nil {
		return nil, common.NewNirvanaInternalServerWithReason(nil, commonib.ErrGetLastSegment.Error())
	}

	// We use the auto liability policy to extract the non-primary insureds
	policy, err := p.policy.ExtractAutoLiabilityPolicy(ibLastSegment)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, ErrExtractAutoPolicy.Error())
	}

	for _, npi := range policy.NonPrimaryInsureds {
		retval = append(retval, converters.InsuredToAdditionalInsured(npi))
	}
	return retval, nil
}
