package business_auto

import (
	"context"

	openapitypes "github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/interceptors/business_auto/deps"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/log"
	ba_filters "nirvanatech.com/nirvana/db-api/db_wrappers/business_auto/application"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/constants"
	openapi "nirvanatech.com/nirvana/openapi-specs/components/business-auto"
)

// HandleListApplicationsAuthz handles authorization for the list applications endpoint.
// This operation checks if the user has read permission to view application lists.
func HandleListApplicationsAuthz(
	ctx context.Context,
	_ deps.Deps,
) common.HandlerAuthzResponse {
	return common.HandlerAuthzResponse{IsAuthorized: true}
}

func HandleListApplications(
	ctx context.Context,
	deps deps.Deps,
) ([]openapi.BusinessAutoApplicationListRecord, error) {
	ctx = log.ContextWithFields(ctx,
		log.HandlerName("HandleListApplications"))

	// Get filtered applications from the database
	apps, err := deps.AppWrapper.GetAll(ctx, generateQueryMods(ctx))
	if err != nil {
		log.Error(ctx, "Failed to list applications",
			log.Err(err))
		return nil, common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, "")
	}

	// Convert applications to response format
	response, err := convertToListResponse(apps)
	if err != nil {
		log.Error(ctx, "Failed to convert applications to response",
			log.Err(err))
		return nil, common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, "")
	}

	return response, nil
}

// generateQueryMods generates the query mods for the application filter.
// Currently, it we only have one mod which is the Kiwi QA filter, to exclude Kiwi QA applications.
func generateQueryMods(ctx context.Context) ba_filters.Filter {
	// Determine if user is QA
	user := authz.UserFromContext(ctx)

	isQA := false
	for _, r := range user.Roles {
		if r.AgencyID != nil && *r.AgencyID == constants.KiwiQAAgencyID {
			isQA = true
			break
		}
	}

	// Apply appropriate filter based on QA status
	var filter ba_filters.Filter
	if isQA {
		filter = ba_filters.OnlyKiwiQA()
	} else {
		filter = ba_filters.NotInKiwiQA()
	}
	return filter
}

func convertToListResponse(
	apps []model.BusinessAutoApp,
) ([]openapi.BusinessAutoApplicationListRecord, error) {
	if apps == nil {
		return []openapi.BusinessAutoApplicationListRecord{}, nil
	}

	records := make([]openapi.BusinessAutoApplicationListRecord, 0, len(apps))
	for _, app := range apps {
		// Convert UWState to OpenAPI AppState using the proper mapping
		state, err := convertUWStateToOpenAPIAppState(app.UWState)
		if err != nil {
			return nil, err
		}

		// Determine if this is an internal application
		isInternal := constants.InternalTestAgencies[app.AgencyID]

		// TODO: ADD TSP PROVIDER LOGIC ONCE WE CONFIRM HOW WOULD WE GET THIS
		record := openapi.BusinessAutoApplicationListRecord{
			Id:          app.ID,
			State:       state,
			ShortID:     string(app.ShortID),
			CompanyName: app.CompanyInfo.Name,
			EffectiveDate: openapitypes.Date{
				Time: app.EffectiveDurationStart,
			},
			IsInternal: &isInternal,
		}
		records = append(records, record)
	}

	return records, nil
}
