package insured

import (
	"context"
	"database/sql"
	"strconv"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	openapi_types "github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/interceptors/insured/deps"
	"nirvanatech.com/nirvana/claims/reporting/loss_runs/client"
	"nirvanatech.com/nirvana/claims/reporting/loss_runs/db"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/infra/authz"
	oapi "nirvanatech.com/nirvana/openapi-specs/components/insured"
)

const downloadLinkExpiration = time.Hour * 24

func HandleProvisionalLossRunsReportsAuthz(ctx context.Context) common.HandlerAuthzResponse {
	user := authz.UserFromContext(ctx)
	return common.HandlerAuthzResponse{IsAuthorized: user.HasUnderwriterAccess() || user.IsClaimsAdmin()}
}

func HandleGetAllProvisionalLossRunsReports(
	ctx context.Context,
	d deps.Deps,
) (oapiProvisionalLossRunsReports []oapi.ProvisionalLossRunsReport, err error) {
	reports, err := d.LossRunsRenewalsClient.GetLossRunsReports(ctx)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, "failed to get loss runs reports")
	}

	reportersIds := slice_utils.Map(reports, func(lrr db.LossRunsReport) uuid.UUID {
		return lrr.RequestedBy
	})
	users, err := d.AuthWrapper.FetchAuthzUsers(ctx, reportersIds)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, "failed to fetch reporters")
	}

	userIdToUserFullNameMap := make(map[uuid.UUID]string)
	for _, u := range users {
		userIdToUserFullNameMap[u.ID] = u.FullName()
	}

	oapiProvisionalLossRunsReports = make([]oapi.ProvisionalLossRunsReport, 0, len(reports))
	for _, lrr := range reports {
		policy, err := d.LossRunsRenewalsClient.GetPolicy(ctx, lrr.PolicyNumber)
		if err != nil {
			return nil, common.NewNirvanaInternalServerWithReason(err, "failed to get policy")
		}
		oapiReport := oapi.ProvisionalLossRunsReport{
			Id:          lrr.Id.String(),
			GeneratedAt: lrr.GeneratedAt,
			Policy: oapi.Policy{
				PolicyNumber:   policy.PolicyNumber,
				InsuredName:    policy.InsuredName,
				DotNumber:      strconv.FormatInt(policy.DotNumber, 10),
				EffectiveDate:  openapi_types.Date{Time: policy.EffectiveDate.ToTime()},
				ExpirationDate: openapi_types.Date{Time: policy.ExpirationDate.ToTime()},
			},
			RequestedBy: userIdToUserFullNameMap[lrr.RequestedBy],
		}

		// TODO: Modify endpoint to handle non-existent handle ids
		// e.g. make link nullable or simply handle on frontend
		if lrr.FileHandleId == nil {
			err := errors.New("no file handle id")
			return nil, common.NewNirvanaInternalServerWithReason(err, err.Error())
		}

		downloadLink, err := d.FileUploadManager.GenerateTemporaryDownloadLink(
			ctx, *lrr.FileHandleId, downloadLinkExpiration)
		if err != nil {
			return nil, common.NewNirvanaInternalServerWithReason(err, "failed to generate download link")
		}
		oapiReport.Link = downloadLink

		oapiProvisionalLossRunsReports = append(oapiProvisionalLossRunsReports, oapiReport)
	}

	return oapiProvisionalLossRunsReports, nil
}

func HandleGenerateProvisionalLossRunsReports(
	ctx context.Context, d deps.Deps, req oapi.GenerateProvisionalLossRunsReportRequest,
) (err error) {
	for _, pn := range req.PolicyNumbers {
		_, err := d.LossRunsRenewalsClient.GenerateProvisionalLossRunsReport(ctx, pn)
		if errors.Is(err, sql.ErrNoRows) {
			return common.NewNirvanaNotFoundErrorf(err, common.EntityPolicy, pn)
		}
		if err != nil {
			return errors.Wrapf(err, "unable to generate loss runs report for policy number %s", pn)
		}
	}
	return nil
}

func HandleGenerateProvisionalLossRunsReportsForDOT(
	ctx context.Context, d deps.Deps, req oapi.GenerateProvisionalLossRunsReportsForDotRequest,
) (res []oapi.LossRunsReportLinkForPolicy, err error) {
	lrrs, err := d.LossRunsRenewalsClient.GenerateProvisionalLossRunsReportsFromDOT(
		ctx,
		req.DotNumber,
	)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, common.NewNirvanaNotFoundErrorf(
			err,
			common.EntityFleet,
			strconv.Itoa(int(req.DotNumber)),
		)
	}
	if err != nil {
		return nil, err
	}

	res = slice_utils.Map(
		lrrs,
		func(lrr client.LossRunsReport) oapi.LossRunsReportLinkForPolicy {
			return oapi.LossRunsReportLinkForPolicy{
				PolicyNumber: lrr.PolicyNumber,
				Link:         lrr.DownloadLink,
			}
		},
	)
	return res, nil
}
