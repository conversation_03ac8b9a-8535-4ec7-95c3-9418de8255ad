// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package parsed_loss_runs

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Aggregation is an object representing the database table.
type Aggregation struct {
	ID                  string     `boil:"id" json:"id" toml:"id" yaml:"id"`
	ApplicationID       string     `boil:"application_id" json:"application_id" toml:"application_id" yaml:"application_id"`
	Status              string     `boil:"status" json:"status" toml:"status" yaml:"status"`
	ConfidenceInfo      types.JSON `boil:"confidence_info" json:"confidence_info" toml:"confidence_info" yaml:"confidence_info"`
	CreatedAt           time.Time  `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt           null.Time  `boil:"updated_at" json:"updated_at,omitempty" toml:"updated_at" yaml:"updated_at,omitempty"`
	SummaryJSON         null.JSON  `boil:"summary_json" json:"summary_json,omitempty" toml:"summary_json" yaml:"summary_json,omitempty"`
	ApplicationReviewID string     `boil:"application_review_id" json:"application_review_id" toml:"application_review_id" yaml:"application_review_id"`

	R *aggregationR `boil:"" json:"" toml:"" yaml:""`
	L aggregationL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var AggregationColumns = struct {
	ID                  string
	ApplicationID       string
	Status              string
	ConfidenceInfo      string
	CreatedAt           string
	UpdatedAt           string
	SummaryJSON         string
	ApplicationReviewID string
}{
	ID:                  "id",
	ApplicationID:       "application_id",
	Status:              "status",
	ConfidenceInfo:      "confidence_info",
	CreatedAt:           "created_at",
	UpdatedAt:           "updated_at",
	SummaryJSON:         "summary_json",
	ApplicationReviewID: "application_review_id",
}

var AggregationTableColumns = struct {
	ID                  string
	ApplicationID       string
	Status              string
	ConfidenceInfo      string
	CreatedAt           string
	UpdatedAt           string
	SummaryJSON         string
	ApplicationReviewID string
}{
	ID:                  "aggregation.id",
	ApplicationID:       "aggregation.application_id",
	Status:              "aggregation.status",
	ConfidenceInfo:      "aggregation.confidence_info",
	CreatedAt:           "aggregation.created_at",
	UpdatedAt:           "aggregation.updated_at",
	SummaryJSON:         "aggregation.summary_json",
	ApplicationReviewID: "aggregation.application_review_id",
}

// Generated where

type whereHelperstring struct{ field string }

func (w whereHelperstring) EQ(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.EQ, x) }
func (w whereHelperstring) NEQ(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.NEQ, x) }
func (w whereHelperstring) LT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.LT, x) }
func (w whereHelperstring) LTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.LTE, x) }
func (w whereHelperstring) GT(x string) qm.QueryMod  { return qmhelper.Where(w.field, qmhelper.GT, x) }
func (w whereHelperstring) GTE(x string) qm.QueryMod { return qmhelper.Where(w.field, qmhelper.GTE, x) }
func (w whereHelperstring) IN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereIn(fmt.Sprintf("%s IN ?", w.field), values...)
}
func (w whereHelperstring) NIN(slice []string) qm.QueryMod {
	values := make([]interface{}, 0, len(slice))
	for _, value := range slice {
		values = append(values, value)
	}
	return qm.WhereNotIn(fmt.Sprintf("%s NOT IN ?", w.field), values...)
}

type whereHelpertypes_JSON struct{ field string }

func (w whereHelpertypes_JSON) EQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_JSON) NEQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_JSON) LT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_JSON) LTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_JSON) GT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_JSON) GTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpertime_Time struct{ field string }

func (w whereHelpertime_Time) EQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertime_Time) NEQ(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertime_Time) LT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertime_Time) LTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertime_Time) GT(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertime_Time) GTE(x time.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpernull_Time struct{ field string }

func (w whereHelpernull_Time) EQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_Time) NEQ(x null.Time) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_Time) LT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_Time) LTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_Time) GT(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_Time) GTE(x null.Time) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_Time) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_Time) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

type whereHelpernull_JSON struct{ field string }

func (w whereHelpernull_JSON) EQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_JSON) NEQ(x null.JSON) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_JSON) LT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_JSON) LTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_JSON) GT(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_JSON) GTE(x null.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_JSON) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_JSON) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var AggregationWhere = struct {
	ID                  whereHelperstring
	ApplicationID       whereHelperstring
	Status              whereHelperstring
	ConfidenceInfo      whereHelpertypes_JSON
	CreatedAt           whereHelpertime_Time
	UpdatedAt           whereHelpernull_Time
	SummaryJSON         whereHelpernull_JSON
	ApplicationReviewID whereHelperstring
}{
	ID:                  whereHelperstring{field: "\"parsed_loss_runs\".\"aggregation\".\"id\""},
	ApplicationID:       whereHelperstring{field: "\"parsed_loss_runs\".\"aggregation\".\"application_id\""},
	Status:              whereHelperstring{field: "\"parsed_loss_runs\".\"aggregation\".\"status\""},
	ConfidenceInfo:      whereHelpertypes_JSON{field: "\"parsed_loss_runs\".\"aggregation\".\"confidence_info\""},
	CreatedAt:           whereHelpertime_Time{field: "\"parsed_loss_runs\".\"aggregation\".\"created_at\""},
	UpdatedAt:           whereHelpernull_Time{field: "\"parsed_loss_runs\".\"aggregation\".\"updated_at\""},
	SummaryJSON:         whereHelpernull_JSON{field: "\"parsed_loss_runs\".\"aggregation\".\"summary_json\""},
	ApplicationReviewID: whereHelperstring{field: "\"parsed_loss_runs\".\"aggregation\".\"application_review_id\""},
}

// AggregationRels is where relationship names are stored.
var AggregationRels = struct {
	ProcessedLosses string
}{
	ProcessedLosses: "ProcessedLosses",
}

// aggregationR is where relationships are stored.
type aggregationR struct {
	ProcessedLosses ProcessedLossSlice `boil:"ProcessedLosses" json:"ProcessedLosses" toml:"ProcessedLosses" yaml:"ProcessedLosses"`
}

// NewStruct creates a new relationship struct
func (*aggregationR) NewStruct() *aggregationR {
	return &aggregationR{}
}

// aggregationL is where Load methods for each relationship are stored.
type aggregationL struct{}

var (
	aggregationAllColumns            = []string{"id", "application_id", "status", "confidence_info", "created_at", "updated_at", "summary_json", "application_review_id"}
	aggregationColumnsWithoutDefault = []string{"id", "application_id", "status", "confidence_info", "created_at", "application_review_id"}
	aggregationColumnsWithDefault    = []string{"updated_at", "summary_json"}
	aggregationPrimaryKeyColumns     = []string{"id"}
	aggregationGeneratedColumns      = []string{}
)

type (
	// AggregationSlice is an alias for a slice of pointers to Aggregation.
	// This should almost always be used instead of []Aggregation.
	AggregationSlice []*Aggregation
	// AggregationHook is the signature for custom Aggregation hook methods
	AggregationHook func(context.Context, boil.ContextExecutor, *Aggregation) error

	aggregationQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	aggregationType                 = reflect.TypeOf(&Aggregation{})
	aggregationMapping              = queries.MakeStructMapping(aggregationType)
	aggregationPrimaryKeyMapping, _ = queries.BindMapping(aggregationType, aggregationMapping, aggregationPrimaryKeyColumns)
	aggregationInsertCacheMut       sync.RWMutex
	aggregationInsertCache          = make(map[string]insertCache)
	aggregationUpdateCacheMut       sync.RWMutex
	aggregationUpdateCache          = make(map[string]updateCache)
	aggregationUpsertCacheMut       sync.RWMutex
	aggregationUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var aggregationAfterSelectHooks []AggregationHook

var aggregationBeforeInsertHooks []AggregationHook
var aggregationAfterInsertHooks []AggregationHook

var aggregationBeforeUpdateHooks []AggregationHook
var aggregationAfterUpdateHooks []AggregationHook

var aggregationBeforeDeleteHooks []AggregationHook
var aggregationAfterDeleteHooks []AggregationHook

var aggregationBeforeUpsertHooks []AggregationHook
var aggregationAfterUpsertHooks []AggregationHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Aggregation) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aggregationAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Aggregation) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aggregationBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Aggregation) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aggregationAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Aggregation) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aggregationBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Aggregation) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aggregationAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Aggregation) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aggregationBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Aggregation) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aggregationAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Aggregation) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aggregationBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Aggregation) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range aggregationAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddAggregationHook registers your hook function for all future operations.
func AddAggregationHook(hookPoint boil.HookPoint, aggregationHook AggregationHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		aggregationAfterSelectHooks = append(aggregationAfterSelectHooks, aggregationHook)
	case boil.BeforeInsertHook:
		aggregationBeforeInsertHooks = append(aggregationBeforeInsertHooks, aggregationHook)
	case boil.AfterInsertHook:
		aggregationAfterInsertHooks = append(aggregationAfterInsertHooks, aggregationHook)
	case boil.BeforeUpdateHook:
		aggregationBeforeUpdateHooks = append(aggregationBeforeUpdateHooks, aggregationHook)
	case boil.AfterUpdateHook:
		aggregationAfterUpdateHooks = append(aggregationAfterUpdateHooks, aggregationHook)
	case boil.BeforeDeleteHook:
		aggregationBeforeDeleteHooks = append(aggregationBeforeDeleteHooks, aggregationHook)
	case boil.AfterDeleteHook:
		aggregationAfterDeleteHooks = append(aggregationAfterDeleteHooks, aggregationHook)
	case boil.BeforeUpsertHook:
		aggregationBeforeUpsertHooks = append(aggregationBeforeUpsertHooks, aggregationHook)
	case boil.AfterUpsertHook:
		aggregationAfterUpsertHooks = append(aggregationAfterUpsertHooks, aggregationHook)
	}
}

// One returns a single aggregation record from the query.
func (q aggregationQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Aggregation, error) {
	o := &Aggregation{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to execute a one query for aggregation")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Aggregation records from the query.
func (q aggregationQuery) All(ctx context.Context, exec boil.ContextExecutor) (AggregationSlice, error) {
	var o []*Aggregation

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "parsed_loss_runs: failed to assign all query results to Aggregation slice")
	}

	if len(aggregationAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Aggregation records in the query.
func (q aggregationQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to count aggregation rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q aggregationQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: failed to check if aggregation exists")
	}

	return count > 0, nil
}

// ProcessedLosses retrieves all the processed_loss's ProcessedLosses with an executor.
func (o *Aggregation) ProcessedLosses(mods ...qm.QueryMod) processedLossQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"parsed_loss_runs\".\"processed_loss\".\"aggregation_id\"=?", o.ID),
	)

	return ProcessedLosses(queryMods...)
}

// LoadProcessedLosses allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (aggregationL) LoadProcessedLosses(ctx context.Context, e boil.ContextExecutor, singular bool, maybeAggregation interface{}, mods queries.Applicator) error {
	var slice []*Aggregation
	var object *Aggregation

	if singular {
		object = maybeAggregation.(*Aggregation)
	} else {
		slice = *maybeAggregation.(*[]*Aggregation)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &aggregationR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &aggregationR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`parsed_loss_runs.processed_loss`),
		qm.WhereIn(`parsed_loss_runs.processed_loss.aggregation_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load processed_loss")
	}

	var resultSlice []*ProcessedLoss
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice processed_loss")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on processed_loss")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for processed_loss")
	}

	if len(processedLossAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ProcessedLosses = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &processedLossR{}
			}
			foreign.R.Aggregation = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.AggregationID {
				local.R.ProcessedLosses = append(local.R.ProcessedLosses, foreign)
				if foreign.R == nil {
					foreign.R = &processedLossR{}
				}
				foreign.R.Aggregation = local
				break
			}
		}
	}

	return nil
}

// AddProcessedLosses adds the given related objects to the existing relationships
// of the aggregation, optionally inserting them as new records.
// Appends related to o.R.ProcessedLosses.
// Sets related.R.Aggregation appropriately.
func (o *Aggregation) AddProcessedLosses(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*ProcessedLoss) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.AggregationID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"parsed_loss_runs\".\"processed_loss\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"aggregation_id"}),
				strmangle.WhereClause("\"", "\"", 2, processedLossPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.AggregationID = o.ID
		}
	}

	if o.R == nil {
		o.R = &aggregationR{
			ProcessedLosses: related,
		}
	} else {
		o.R.ProcessedLosses = append(o.R.ProcessedLosses, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &processedLossR{
				Aggregation: o,
			}
		} else {
			rel.R.Aggregation = o
		}
	}
	return nil
}

// Aggregations retrieves all the records using an executor.
func Aggregations(mods ...qm.QueryMod) aggregationQuery {
	mods = append(mods, qm.From("\"parsed_loss_runs\".\"aggregation\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"parsed_loss_runs\".\"aggregation\".*"})
	}

	return aggregationQuery{q}
}

// FindAggregation retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindAggregation(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Aggregation, error) {
	aggregationObj := &Aggregation{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"parsed_loss_runs\".\"aggregation\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, aggregationObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "parsed_loss_runs: unable to select from aggregation")
	}

	if err = aggregationObj.doAfterSelectHooks(ctx, exec); err != nil {
		return aggregationObj, err
	}

	return aggregationObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Aggregation) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no aggregation provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if queries.MustTime(o.UpdatedAt).IsZero() {
			queries.SetScanner(&o.UpdatedAt, currTime)
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(aggregationColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	aggregationInsertCacheMut.RLock()
	cache, cached := aggregationInsertCache[key]
	aggregationInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			aggregationAllColumns,
			aggregationColumnsWithDefault,
			aggregationColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(aggregationType, aggregationMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(aggregationType, aggregationMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"parsed_loss_runs\".\"aggregation\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"parsed_loss_runs\".\"aggregation\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to insert into aggregation")
	}

	if !cached {
		aggregationInsertCacheMut.Lock()
		aggregationInsertCache[key] = cache
		aggregationInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Aggregation.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Aggregation) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	aggregationUpdateCacheMut.RLock()
	cache, cached := aggregationUpdateCache[key]
	aggregationUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			aggregationAllColumns,
			aggregationPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("parsed_loss_runs: unable to update aggregation, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"aggregation\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, aggregationPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(aggregationType, aggregationMapping, append(wl, aggregationPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update aggregation row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by update for aggregation")
	}

	if !cached {
		aggregationUpdateCacheMut.Lock()
		aggregationUpdateCache[key] = cache
		aggregationUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q aggregationQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all for aggregation")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected for aggregation")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o AggregationSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("parsed_loss_runs: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), aggregationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"parsed_loss_runs\".\"aggregation\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, aggregationPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to update all in aggregation slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to retrieve rows affected all in update all aggregation")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Aggregation) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("parsed_loss_runs: no aggregation provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		queries.SetScanner(&o.UpdatedAt, currTime)
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(aggregationColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	aggregationUpsertCacheMut.RLock()
	cache, cached := aggregationUpsertCache[key]
	aggregationUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			aggregationAllColumns,
			aggregationColumnsWithDefault,
			aggregationColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			aggregationAllColumns,
			aggregationPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("parsed_loss_runs: unable to upsert aggregation, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(aggregationPrimaryKeyColumns))
			copy(conflict, aggregationPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"parsed_loss_runs\".\"aggregation\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(aggregationType, aggregationMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(aggregationType, aggregationMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to upsert aggregation")
	}

	if !cached {
		aggregationUpsertCacheMut.Lock()
		aggregationUpsertCache[key] = cache
		aggregationUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Aggregation record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Aggregation) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("parsed_loss_runs: no Aggregation provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), aggregationPrimaryKeyMapping)
	sql := "DELETE FROM \"parsed_loss_runs\".\"aggregation\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete from aggregation")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by delete for aggregation")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q aggregationQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("parsed_loss_runs: no aggregationQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from aggregation")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for aggregation")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o AggregationSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(aggregationBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), aggregationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"parsed_loss_runs\".\"aggregation\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, aggregationPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: unable to delete all from aggregation slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "parsed_loss_runs: failed to get rows affected by deleteall for aggregation")
	}

	if len(aggregationAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Aggregation) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindAggregation(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *AggregationSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := AggregationSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), aggregationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"parsed_loss_runs\".\"aggregation\".* FROM \"parsed_loss_runs\".\"aggregation\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, aggregationPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "parsed_loss_runs: unable to reload all in AggregationSlice")
	}

	*o = slice

	return nil
}

// AggregationExists checks if the Aggregation row exists.
func AggregationExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"parsed_loss_runs\".\"aggregation\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "parsed_loss_runs: unable to check if aggregation exists")
	}

	return exists, nil
}
