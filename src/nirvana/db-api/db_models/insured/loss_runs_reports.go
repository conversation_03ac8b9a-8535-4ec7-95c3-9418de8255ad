// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package insured

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// LossRunsReport is an object representing the database table.
type LossRunsReport struct {
	ID           string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	PolicyNumber string      `boil:"policy_number" json:"policy_number" toml:"policy_number" yaml:"policy_number"`
	GeneratedAt  time.Time   `boil:"generated_at" json:"generated_at" toml:"generated_at" yaml:"generated_at"`
	RequestedBy  string      `boil:"requested_by" json:"requested_by" toml:"requested_by" yaml:"requested_by"`
	FileHandleID null.String `boil:"file_handle_id" json:"file_handle_id,omitempty" toml:"file_handle_id" yaml:"file_handle_id,omitempty"`
	CreatedAt    time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	JobRunID     null.String `boil:"job_run_id" json:"job_run_id,omitempty" toml:"job_run_id" yaml:"job_run_id,omitempty"`

	R *lossRunsReportR `boil:"" json:"" toml:"" yaml:""`
	L lossRunsReportL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var LossRunsReportColumns = struct {
	ID           string
	PolicyNumber string
	GeneratedAt  string
	RequestedBy  string
	FileHandleID string
	CreatedAt    string
	JobRunID     string
}{
	ID:           "id",
	PolicyNumber: "policy_number",
	GeneratedAt:  "generated_at",
	RequestedBy:  "requested_by",
	FileHandleID: "file_handle_id",
	CreatedAt:    "created_at",
	JobRunID:     "job_run_id",
}

var LossRunsReportTableColumns = struct {
	ID           string
	PolicyNumber string
	GeneratedAt  string
	RequestedBy  string
	FileHandleID string
	CreatedAt    string
	JobRunID     string
}{
	ID:           "loss_runs_reports.id",
	PolicyNumber: "loss_runs_reports.policy_number",
	GeneratedAt:  "loss_runs_reports.generated_at",
	RequestedBy:  "loss_runs_reports.requested_by",
	FileHandleID: "loss_runs_reports.file_handle_id",
	CreatedAt:    "loss_runs_reports.created_at",
	JobRunID:     "loss_runs_reports.job_run_id",
}

// Generated where

var LossRunsReportWhere = struct {
	ID           whereHelperstring
	PolicyNumber whereHelperstring
	GeneratedAt  whereHelpertime_Time
	RequestedBy  whereHelperstring
	FileHandleID whereHelpernull_String
	CreatedAt    whereHelpertime_Time
	JobRunID     whereHelpernull_String
}{
	ID:           whereHelperstring{field: "\"insured\".\"loss_runs_reports\".\"id\""},
	PolicyNumber: whereHelperstring{field: "\"insured\".\"loss_runs_reports\".\"policy_number\""},
	GeneratedAt:  whereHelpertime_Time{field: "\"insured\".\"loss_runs_reports\".\"generated_at\""},
	RequestedBy:  whereHelperstring{field: "\"insured\".\"loss_runs_reports\".\"requested_by\""},
	FileHandleID: whereHelpernull_String{field: "\"insured\".\"loss_runs_reports\".\"file_handle_id\""},
	CreatedAt:    whereHelpertime_Time{field: "\"insured\".\"loss_runs_reports\".\"created_at\""},
	JobRunID:     whereHelpernull_String{field: "\"insured\".\"loss_runs_reports\".\"job_run_id\""},
}

// LossRunsReportRels is where relationship names are stored.
var LossRunsReportRels = struct {
}{}

// lossRunsReportR is where relationships are stored.
type lossRunsReportR struct {
}

// NewStruct creates a new relationship struct
func (*lossRunsReportR) NewStruct() *lossRunsReportR {
	return &lossRunsReportR{}
}

// lossRunsReportL is where Load methods for each relationship are stored.
type lossRunsReportL struct{}

var (
	lossRunsReportAllColumns            = []string{"id", "policy_number", "generated_at", "requested_by", "file_handle_id", "created_at", "job_run_id"}
	lossRunsReportColumnsWithoutDefault = []string{"id", "policy_number", "generated_at", "requested_by"}
	lossRunsReportColumnsWithDefault    = []string{"file_handle_id", "created_at", "job_run_id"}
	lossRunsReportPrimaryKeyColumns     = []string{"id"}
	lossRunsReportGeneratedColumns      = []string{}
)

type (
	// LossRunsReportSlice is an alias for a slice of pointers to LossRunsReport.
	// This should almost always be used instead of []LossRunsReport.
	LossRunsReportSlice []*LossRunsReport
	// LossRunsReportHook is the signature for custom LossRunsReport hook methods
	LossRunsReportHook func(context.Context, boil.ContextExecutor, *LossRunsReport) error

	lossRunsReportQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	lossRunsReportType                 = reflect.TypeOf(&LossRunsReport{})
	lossRunsReportMapping              = queries.MakeStructMapping(lossRunsReportType)
	lossRunsReportPrimaryKeyMapping, _ = queries.BindMapping(lossRunsReportType, lossRunsReportMapping, lossRunsReportPrimaryKeyColumns)
	lossRunsReportInsertCacheMut       sync.RWMutex
	lossRunsReportInsertCache          = make(map[string]insertCache)
	lossRunsReportUpdateCacheMut       sync.RWMutex
	lossRunsReportUpdateCache          = make(map[string]updateCache)
	lossRunsReportUpsertCacheMut       sync.RWMutex
	lossRunsReportUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var lossRunsReportAfterSelectHooks []LossRunsReportHook

var lossRunsReportBeforeInsertHooks []LossRunsReportHook
var lossRunsReportAfterInsertHooks []LossRunsReportHook

var lossRunsReportBeforeUpdateHooks []LossRunsReportHook
var lossRunsReportAfterUpdateHooks []LossRunsReportHook

var lossRunsReportBeforeDeleteHooks []LossRunsReportHook
var lossRunsReportAfterDeleteHooks []LossRunsReportHook

var lossRunsReportBeforeUpsertHooks []LossRunsReportHook
var lossRunsReportAfterUpsertHooks []LossRunsReportHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *LossRunsReport) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossRunsReportAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *LossRunsReport) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossRunsReportBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *LossRunsReport) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossRunsReportAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *LossRunsReport) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossRunsReportBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *LossRunsReport) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossRunsReportAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *LossRunsReport) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossRunsReportBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *LossRunsReport) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossRunsReportAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *LossRunsReport) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossRunsReportBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *LossRunsReport) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range lossRunsReportAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddLossRunsReportHook registers your hook function for all future operations.
func AddLossRunsReportHook(hookPoint boil.HookPoint, lossRunsReportHook LossRunsReportHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		lossRunsReportAfterSelectHooks = append(lossRunsReportAfterSelectHooks, lossRunsReportHook)
	case boil.BeforeInsertHook:
		lossRunsReportBeforeInsertHooks = append(lossRunsReportBeforeInsertHooks, lossRunsReportHook)
	case boil.AfterInsertHook:
		lossRunsReportAfterInsertHooks = append(lossRunsReportAfterInsertHooks, lossRunsReportHook)
	case boil.BeforeUpdateHook:
		lossRunsReportBeforeUpdateHooks = append(lossRunsReportBeforeUpdateHooks, lossRunsReportHook)
	case boil.AfterUpdateHook:
		lossRunsReportAfterUpdateHooks = append(lossRunsReportAfterUpdateHooks, lossRunsReportHook)
	case boil.BeforeDeleteHook:
		lossRunsReportBeforeDeleteHooks = append(lossRunsReportBeforeDeleteHooks, lossRunsReportHook)
	case boil.AfterDeleteHook:
		lossRunsReportAfterDeleteHooks = append(lossRunsReportAfterDeleteHooks, lossRunsReportHook)
	case boil.BeforeUpsertHook:
		lossRunsReportBeforeUpsertHooks = append(lossRunsReportBeforeUpsertHooks, lossRunsReportHook)
	case boil.AfterUpsertHook:
		lossRunsReportAfterUpsertHooks = append(lossRunsReportAfterUpsertHooks, lossRunsReportHook)
	}
}

// One returns a single lossRunsReport record from the query.
func (q lossRunsReportQuery) One(ctx context.Context, exec boil.ContextExecutor) (*LossRunsReport, error) {
	o := &LossRunsReport{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insured: failed to execute a one query for loss_runs_reports")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all LossRunsReport records from the query.
func (q lossRunsReportQuery) All(ctx context.Context, exec boil.ContextExecutor) (LossRunsReportSlice, error) {
	var o []*LossRunsReport

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "insured: failed to assign all query results to LossRunsReport slice")
	}

	if len(lossRunsReportAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all LossRunsReport records in the query.
func (q lossRunsReportQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "insured: failed to count loss_runs_reports rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q lossRunsReportQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "insured: failed to check if loss_runs_reports exists")
	}

	return count > 0, nil
}

// LossRunsReports retrieves all the records using an executor.
func LossRunsReports(mods ...qm.QueryMod) lossRunsReportQuery {
	mods = append(mods, qm.From("\"insured\".\"loss_runs_reports\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"insured\".\"loss_runs_reports\".*"})
	}

	return lossRunsReportQuery{q}
}

// FindLossRunsReport retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindLossRunsReport(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*LossRunsReport, error) {
	lossRunsReportObj := &LossRunsReport{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"insured\".\"loss_runs_reports\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, lossRunsReportObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "insured: unable to select from loss_runs_reports")
	}

	if err = lossRunsReportObj.doAfterSelectHooks(ctx, exec); err != nil {
		return lossRunsReportObj, err
	}

	return lossRunsReportObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *LossRunsReport) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("insured: no loss_runs_reports provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(lossRunsReportColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	lossRunsReportInsertCacheMut.RLock()
	cache, cached := lossRunsReportInsertCache[key]
	lossRunsReportInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			lossRunsReportAllColumns,
			lossRunsReportColumnsWithDefault,
			lossRunsReportColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(lossRunsReportType, lossRunsReportMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(lossRunsReportType, lossRunsReportMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"insured\".\"loss_runs_reports\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"insured\".\"loss_runs_reports\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "insured: unable to insert into loss_runs_reports")
	}

	if !cached {
		lossRunsReportInsertCacheMut.Lock()
		lossRunsReportInsertCache[key] = cache
		lossRunsReportInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the LossRunsReport.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *LossRunsReport) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	lossRunsReportUpdateCacheMut.RLock()
	cache, cached := lossRunsReportUpdateCache[key]
	lossRunsReportUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			lossRunsReportAllColumns,
			lossRunsReportPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("insured: unable to update loss_runs_reports, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"insured\".\"loss_runs_reports\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, lossRunsReportPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(lossRunsReportType, lossRunsReportMapping, append(wl, lossRunsReportPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "insured: unable to update loss_runs_reports row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insured: failed to get rows affected by update for loss_runs_reports")
	}

	if !cached {
		lossRunsReportUpdateCacheMut.Lock()
		lossRunsReportUpdateCache[key] = cache
		lossRunsReportUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q lossRunsReportQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insured: unable to update all for loss_runs_reports")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insured: unable to retrieve rows affected for loss_runs_reports")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o LossRunsReportSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("insured: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), lossRunsReportPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"insured\".\"loss_runs_reports\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, lossRunsReportPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insured: unable to update all in lossRunsReport slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insured: unable to retrieve rows affected all in update all lossRunsReport")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *LossRunsReport) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("insured: no loss_runs_reports provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(lossRunsReportColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	lossRunsReportUpsertCacheMut.RLock()
	cache, cached := lossRunsReportUpsertCache[key]
	lossRunsReportUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			lossRunsReportAllColumns,
			lossRunsReportColumnsWithDefault,
			lossRunsReportColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			lossRunsReportAllColumns,
			lossRunsReportPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("insured: unable to upsert loss_runs_reports, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(lossRunsReportPrimaryKeyColumns))
			copy(conflict, lossRunsReportPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"insured\".\"loss_runs_reports\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(lossRunsReportType, lossRunsReportMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(lossRunsReportType, lossRunsReportMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "insured: unable to upsert loss_runs_reports")
	}

	if !cached {
		lossRunsReportUpsertCacheMut.Lock()
		lossRunsReportUpsertCache[key] = cache
		lossRunsReportUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single LossRunsReport record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *LossRunsReport) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("insured: no LossRunsReport provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), lossRunsReportPrimaryKeyMapping)
	sql := "DELETE FROM \"insured\".\"loss_runs_reports\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insured: unable to delete from loss_runs_reports")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insured: failed to get rows affected by delete for loss_runs_reports")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q lossRunsReportQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("insured: no lossRunsReportQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "insured: unable to delete all from loss_runs_reports")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insured: failed to get rows affected by deleteall for loss_runs_reports")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o LossRunsReportSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(lossRunsReportBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), lossRunsReportPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"insured\".\"loss_runs_reports\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, lossRunsReportPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "insured: unable to delete all from lossRunsReport slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "insured: failed to get rows affected by deleteall for loss_runs_reports")
	}

	if len(lossRunsReportAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *LossRunsReport) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindLossRunsReport(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *LossRunsReportSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := LossRunsReportSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), lossRunsReportPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"insured\".\"loss_runs_reports\".* FROM \"insured\".\"loss_runs_reports\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, lossRunsReportPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "insured: unable to reload all in LossRunsReportSlice")
	}

	*o = slice

	return nil
}

// LossRunsReportExists checks if the LossRunsReport row exists.
func LossRunsReportExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"insured\".\"loss_runs_reports\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "insured: unable to check if loss_runs_reports exists")
	}

	return exists, nil
}
