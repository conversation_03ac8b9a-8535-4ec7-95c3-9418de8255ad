package schedulechange

import (
	"context"
	"slices"

	underwriting "nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"

	"github.com/cockroachdb/errors"
	openapitypes "github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/change/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted"
	oapiend "nirvanatech.com/nirvana/openapi-specs/components/endorsement"
	endorsementuwoapi "nirvanatech.com/nirvana/openapi-specs/components/endorsementuw"
	oapinonfleet "nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
)

func ConvertScheduleChangeFromOAPI(
	ctx context.Context, data oapiend.NonFleetAdmittedScheduleChange, policy *policy.Policy,
) (*ScheduleChange, error) {
	programData, err := policy.GetAdmittedProgramData(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get admitted program data")
	}

	companyUSState := programData.CompanyInfo.USState
	driverChanges, vehicleChanges, err := convertFromOAPI(data, companyUSState)
	if err != nil {
		return nil, errors.Wrap(err, "failed to convert from oapi")
	}

	originalDriversList := programData.DriverInfo.Drivers
	originalVehiclesList := programData.EquipmentInfo.Vehicles

	if err = validate(
		ctx, driverChanges, vehicleChanges, originalDriversList, originalVehiclesList,
	); err != nil {
		return nil, errors.Wrap(err, "failed to validate")
	}

	updatedDriversList := slices.Clone(originalDriversList)
	updatedDriverViolations := programData.UnderwriterInfo.DriverViolations
	if driverChanges != nil {
		updatedDriversList = updateSchedule(
			updatedDriversList, driverChanges.added, driverChanges.removed, driverChanges.updated,
			LicenseNumberExtractor,
		)
		updatedDriverViolations = updateDriverViolations(updatedDriversList, updatedDriverViolations)
	}

	updatedVehiclesList := slices.Clone(originalVehiclesList)
	if vehicleChanges != nil {
		updatedVehiclesList = updateSchedule(
			updatedVehiclesList, vehicleChanges.added, vehicleChanges.removed, vehicleChanges.updated,
			VinExtractor,
		)
	}

	scheduleChangeType := enums.ScheduleChangeTypeInvalid
	if driverChanges != nil && vehicleChanges == nil {
		scheduleChangeType = enums.ScheduleChangeTypeDriver
	}
	if driverChanges == nil && vehicleChanges != nil {
		scheduleChangeType = enums.ScheduleChangeTypeVehicle
	}
	if driverChanges != nil && vehicleChanges != nil {
		scheduleChangeType = enums.ScheduleChangeTypeBoth
	}

	if scheduleChangeType == enums.ScheduleChangeTypeInvalid || scheduleChangeType == enums.ScheduleChangeTypeBoth {
		return nil, errors.New("invalid schedule change type")
	}

	return &ScheduleChange{
		Drivers:            updatedDriversList,
		Vehicles:           updatedVehiclesList,
		DriverViolations:   updatedDriverViolations,
		ScheduleChangeType: scheduleChangeType,
	}, nil
}

func convertFromOAPI(
	data oapiend.NonFleetAdmittedScheduleChange, companyUSState us_states.USState,
) (*driverListChanges, *vehicleListChanges, error) {
	convertedDriverListChanges, err := convertDriversListChanges(data.DriversListChanges, companyUSState)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to convert drivers list changes")
	}

	convertedVehicleListChanges, err := convertVehiclesListChanges(data.VehiclesListChanges)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to convert vehicles list changes")
	}

	return convertedDriverListChanges, convertedVehicleListChanges, nil
}

func ConstructValueExistsMap[T schedule](slice []T, valueExtractor func(T) string) map[string]bool {
	valueExistsMap := make(map[string]bool, len(slice))
	for _, elem := range slice {
		valueExistsMap[valueExtractor(elem)] = true
	}
	return valueExistsMap
}

func updateSchedule[T schedule](
	originalList, addedList, removedList, updatedList []T, valueExtractor func(T) string,
) []T {
	originalList = append(originalList, addedList...)
	if removedList != nil {
		originalList = removeFromList(originalList, removedList, valueExtractor)
	}
	if updatedList != nil {
		originalList = updateInList(originalList, updatedList, valueExtractor)
	}

	return originalList
}

func removeFromList[T schedule](originalList, removedList []T, valueExtractor func(T) string) []T {
	toRemoveKeysMap := ConstructValueExistsMap(removedList, valueExtractor)
	var listToReturn []T
	for _, elem := range originalList {
		if _, ok := toRemoveKeysMap[valueExtractor(elem)]; !ok {
			listToReturn = append(listToReturn, elem)
		}
	}
	return listToReturn
}

// updateDriverViolations updates the driver violations for the updated drivers list. For newly added drivers,
// violations would get added during the rating job.
func updateDriverViolations(
	updatedDriversList []admitted.DriverDetails, originalDriverViolations []admitted_app.DriverViolation,
) []admitted_app.DriverViolation {
	originalDriverViolationsMap := ConstructIndexMap(originalDriverViolations, LicenseNumberFromViolationExtractor)

	var updatedDriverViolations []admitted_app.DriverViolation
	for _, updatedDriver := range updatedDriversList {
		driverViolation := admitted_app.DriverViolation{
			LicenseNumber: updatedDriver.LicenseNumber,
		}
		if originalDriverViolationIdx, ok := originalDriverViolationsMap[updatedDriver.LicenseNumber]; ok {
			driverViolation = originalDriverViolations[originalDriverViolationIdx]
		}
		updatedDriverViolations = append(updatedDriverViolations, driverViolation)
	}

	return updatedDriverViolations
}

func updateInList[T schedule](originalList, updatedList []T, valueExtractor func(T) string) []T {
	keyToIndexMap := ConstructIndexMap(originalList, valueExtractor)

	for _, elem := range updatedList {
		index, ok := keyToIndexMap[valueExtractor(elem)]
		if ok {
			originalList[index] = elem
		}
	}

	return originalList
}

func ConstructIndexMap[T schedule](slice []T, valueExtractor func(T) string) map[string]int {
	keyToIndexMap := make(map[string]int)
	for i, elem := range slice {
		keyToIndexMap[valueExtractor(elem)] = i
	}
	return keyToIndexMap
}

func ConvertScheduleChangeToOAPI(
	ctx context.Context, scheduleChange *ScheduleChange, policy *policy.Policy,
) (oapiend.Change_Data, error) {
	programData, err := policy.GetAdmittedProgramData(ctx)
	if err != nil {
		return oapiend.Change_Data{}, errors.Wrap(err, "failed to get admitted program data")
	}

	originalDriversList := programData.DriverInfo.Drivers
	newDriversList := scheduleChange.Drivers
	// Diff and return added, updated and removed drivers b/w original and updated drivers list
	driverChanges, err := GetDriverUpdates(originalDriversList, newDriversList, scheduleChange.PullMVR, scheduleChange.MVRDetailsMap)
	if err != nil {
		return oapiend.Change_Data{}, errors.Wrap(err, "failed to get driver updates")
	}

	originalVehiclesList := programData.EquipmentInfo.Vehicles
	newVehiclesList := scheduleChange.Vehicles
	// Diff and return added, updated and removed vehicles b/w original and updated vehicles list
	vehicleChanges, err := GetVehicleUpdates(originalVehiclesList, newVehiclesList)
	if err != nil {
		return oapiend.Change_Data{}, errors.Wrap(err, "failed to get vehicle updates")
	}

	convertedScheduleChange := oapiend.NonFleetAdmittedScheduleChange{
		DriversListChanges:  driverChanges,
		VehiclesListChanges: vehicleChanges,
	}
	var changeData oapiend.Change_Data
	err = changeData.FromNonFleetAdmittedScheduleChange(convertedScheduleChange)
	if err != nil {
		return oapiend.Change_Data{}, errors.Wrap(
			err, "failed to convert NonFleetAdmittedScheduleChange to Change_Data",
		)
	}
	return changeData, nil
}

func GetDriverUpdates(
	originalDriversList, newDriversList []admitted.DriverDetails,
	pullMVR bool,
	mvrDetailsMap map[string]underwriting.MVRDetails,
) (*oapiend.DriversListChanges, error) {
	var (
		updatedDrivers             []oapiend.AdmittedDriverDataDiff
		addedDriversBasicDetails   []oapinonfleet.AdmittedAppDriverDetails
		removedDriversBasicDetails []oapinonfleet.AdmittedAppDriverDetails
	)
	driverChangeList := ConvertToDriverChangeLists(originalDriversList, newDriversList)

	for _, addedDriver := range driverChangeList.Added {
		addedDriversBasicDetails = append(addedDriversBasicDetails, *convertDriverToOAPI(addedDriver))
	}
	for _, removedDriver := range driverChangeList.Removed {
		removedDriversBasicDetails = append(removedDriversBasicDetails, *convertDriverToOAPI(removedDriver))
	}

	for _, updatedDriver := range driverChangeList.Updated {
		updatedDrivers = append(updatedDrivers, *convertDriverToOAPIDataDiff(updatedDriver.Original, updatedDriver.New))
	}

	addedDrivers, err := getDriverDetails(addedDriversBasicDetails, pullMVR, mvrDetailsMap)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't get addedDrivers details")
	}

	removedDrivers, err := getDriverDetails(removedDriversBasicDetails, false, mvrDetailsMap)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't get removedDrivers details")
	}

	if pullMVR {
		updatedDrivers, err = populateMVRDetailsToUpdatedDriver(updatedDrivers, mvrDetailsMap)
		if err != nil {
			return nil, errors.Wrap(err, "couldn't populate updatedDrivers with MVR details")
		}
	}

	return atLeastOneDriverChangeOrNil(addedDrivers, removedDrivers, updatedDrivers, pullMVR), nil
}

func atLeastOneDriverChangeOrNil(
	added, removed []oapiend.DriverDetails, updated []oapiend.AdmittedDriverDataDiff, pullMVR bool,
) *oapiend.DriversListChanges {
	addedPtr := nonZeroOrNil(added)
	removedPtr := nonZeroOrNil(removed)
	updatedPtr := nonZeroOrNil(updated)
	if addedPtr == nil && removedPtr == nil && updatedPtr == nil {
		return nil
	}

	return &oapiend.DriversListChanges{
		Added:       addedPtr,
		Removed:     removedPtr,
		Updated:     updatedPtr,
		IsMVRPulled: pullMVR,
	}
}

func GetVehicleUpdates(
	originalVehiclesList, newVehiclesList []admitted.VehicleDetails,
) (*oapiend.VehiclesListChanges, error) {
	var (
		addedVehicles, removedVehicles []oapinonfleet.AdmittedAppVehicleDetails
		updatedVehicles                []oapiend.AdmittedVehicleDataDiff
	)
	originalVehiclesVinToIndexMap := ConstructIndexMap(originalVehiclesList, VinExtractor)
	newVehiclesVinExistsMap := ConstructValueExistsMap(newVehiclesList, VinExtractor)

	for _, newVehicle := range newVehiclesList {
		idx, exists := originalVehiclesVinToIndexMap[newVehicle.VIN]
		if !exists {
			addedVehicles = append(addedVehicles, *convertVehicleToOAPI(newVehicle))
			continue
		}
		originalVehicle := originalVehiclesList[idx]
		if !newVehicle.Equal(&originalVehicle) {
			updatedVehicles = append(updatedVehicles, *convertVehicleToOAPIDataDiff(originalVehicle, newVehicle))
		}
	}

	for _, originalVehicle := range originalVehiclesList {
		if _, ok := newVehiclesVinExistsMap[originalVehicle.VIN]; !ok {
			removedVehicles = append(removedVehicles, *convertVehicleToOAPI(originalVehicle))
		}
	}

	// Stated Value is either present in all vehicles or none. This is essentially determined by if APD is
	// a part of the policy set or not.
	statedValuePresent := isStatedValuePresent(newVehiclesList)
	return atLeastOneVehiclesChangeOrNil(addedVehicles, removedVehicles, updatedVehicles, statedValuePresent), nil
}

func atLeastOneVehiclesChangeOrNil(
	added, removed []oapinonfleet.AdmittedAppVehicleDetails, updated []oapiend.AdmittedVehicleDataDiff,
	statedValuePresent bool,
) *oapiend.VehiclesListChanges {
	addedPtr := nonZeroOrNil(added)
	removedPtr := nonZeroOrNil(removed)
	updatedPtr := nonZeroOrNil(updated)
	if addedPtr == nil && removedPtr == nil && updatedPtr == nil {
		return nil
	}

	return &oapiend.VehiclesListChanges{
		Added:                addedPtr,
		Removed:              removedPtr,
		Updated:              updatedPtr,
		IsStatedValuePresent: pointer_utils.ToPointer(statedValuePresent),
	}
}

func isStatedValuePresent(
	vehiclesList []admitted.VehicleDetails,
) bool {
	for _, vehicle := range vehiclesList {
		if vehicle.StatedValue != nil {
			return true
		}
	}
	return false
}

func getDriverDetails(
	driverBasicDetails []oapinonfleet.AdmittedAppDriverDetails,
	pullMVR bool,
	mvrDetailsMap map[string]underwriting.MVRDetails,
) ([]oapiend.DriverDetails, error) {
	var driverDetails []oapiend.DriverDetails
	for _, driverBasicDetail := range driverBasicDetails {
		driver := oapiend.DriverDetails{
			BasicDetails: driverBasicDetail,
		}
		driverDetails = append(driverDetails, driver)
	}
	if pullMVR {
		if mvrDetailsMap == nil {
			return nil, errors.New("couldn't get mvr details map")
		}
		for idx := range driverDetails {
			if mvrDetail, ok := mvrDetailsMap[driverDetails[idx].BasicDetails.LicenseNumber]; ok {
				driverDetails[idx].MvrDetails = &endorsementuwoapi.DriverMVRDetails{
					CdlClass:        mvrDetail.CdlClass,
					CdlNumber:       mvrDetail.CdlNumber,
					CdlStatus:       mvrDetail.CdlStatus,
					ExpirationDate:  mvrDetail.ExpirationDate,
					IssueDate:       mvrDetail.IssueDate,
					MvrPullError:    mvrDetail.MvrPullError,
					MvrPullStatus:   mvrDetail.MvrPullStatus,
					Violations:      mvrDetail.Violations,
					ViolationPoints: pointer_utils.Int(mvrDetail.ViolationPoints),
					ViolationCount:  pointer_utils.Int(mvrDetail.ViolationCount),
					YearIssued:      getYearIssued(mvrDetail.IssueDate),
				}
			}
		}
	}
	return driverDetails, nil
}

func populateMVRDetailsToUpdatedDriver(
	driverList []oapiend.AdmittedDriverDataDiff,
	mvrDetailsMap map[string]underwriting.MVRDetails,
) ([]oapiend.AdmittedDriverDataDiff, error) {
	if mvrDetailsMap == nil {
		return nil, errors.New("couldn't get mvr details map")
	}
	for idx := range driverList {
		if mvrDetail, ok := mvrDetailsMap[driverList[idx].BasicDriverDetailsDiff.LicenseNumber.Updated]; ok {
			driverList[idx].MvrDetails = &endorsementuwoapi.DriverMVRDetails{
				CdlClass:        mvrDetail.CdlClass,
				CdlNumber:       mvrDetail.CdlNumber,
				CdlStatus:       mvrDetail.CdlStatus,
				ExpirationDate:  mvrDetail.ExpirationDate,
				IssueDate:       mvrDetail.IssueDate,
				MvrPullError:    mvrDetail.MvrPullError,
				MvrPullStatus:   mvrDetail.MvrPullStatus,
				Violations:      mvrDetail.Violations,
				ViolationPoints: pointer_utils.Int(mvrDetail.ViolationPoints),
				ViolationCount:  pointer_utils.Int(mvrDetail.ViolationCount),
				YearIssued:      getYearIssued(mvrDetail.IssueDate),
			}
		}
	}
	return driverList, nil
}

func getYearIssued(issueDate *openapitypes.Date) *int {
	if issueDate == nil {
		return nil
	}
	year := issueDate.Year()
	return &year
}
