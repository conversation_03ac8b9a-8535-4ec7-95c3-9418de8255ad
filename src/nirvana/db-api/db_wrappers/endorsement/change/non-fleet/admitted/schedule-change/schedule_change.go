package schedulechange

import (
	"context"
	"strings"

	underwriting "nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	app_enum "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/change/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/change/shared"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted"
	oapiend "nirvanatech.com/nirvana/openapi-specs/components/endorsement"
)

var (
	LicenseNumberExtractor = func(driver admitted.DriverDetails) string {
		return driver.LicenseNumber
	}
	VinExtractor = func(vehicle admitted.VehicleDetails) string {
		return vehicle.VIN
	}
	LicenseNumberFromViolationExtractor = func(violation admitted_app.DriverViolation) string {
		return violation.LicenseNumber
	}
)

type (
	ScheduleChange struct {
		Drivers  []admitted.DriverDetails
		Vehicles []admitted.VehicleDetails
		PullMVR  bool
		// MVRDetailsMap stores MVRDetails in a map with LicenseNumber being the key of the map
		MVRDetailsMap map[string]underwriting.MVRDetails
		// DriverViolations is a list of driver violations
		DriverViolations   []admitted_app.DriverViolation
		ScheduleChangeType enums.ScheduleChangeType

		shared.FormsListNoOp
		shared.InvalidForFleetMethodsChange
	}

	driverListChanges struct {
		added, removed, updated []admitted.DriverDetails
	}

	vehicleListChanges struct {
		added, removed, updated []admitted.VehicleDetails
	}

	schedule interface {
		admitted.DriverDetails | admitted.VehicleDetails | admitted_app.DriverViolation
	}
)

func (s *ScheduleChange) Get() any {
	return s
}

func (s *ScheduleChange) GetType() enums.ChangeType {
	return enums.ChangeTypeNonFleetAdmittedScheduleChange
}

func (s *ScheduleChange) Validate(ctx context.Context, policy *policy.Policy) error {
	if len(s.Drivers) == 0 && len(s.Vehicles) == 0 {
		return errors.New("no changes to the schedule")
	}

	if err := checkForDuplicates(s.Drivers, LicenseNumberExtractor); err != nil {
		return errors.Wrap(err, "duplicate drivers present")
	}
	if err := checkForDuplicates(s.Vehicles, VinExtractor); err != nil {
		return errors.Wrap(err, "duplicate vehicles present")
	}

	programData, err := policy.GetAdmittedProgramData(ctx)
	if err != nil {
		return errors.Wrap(err, "failed to get admitted program data")
	}

	if programData.CoverageInfo.CoverageExists(app_enum.CoverageAutoPhysicalDamage) {
		aggregatedStatedValue := aggregateStatedValue(s.Vehicles)
		if aggregatedStatedValue == 0 {
			return errors.New("TIV can't be 0 for APD coverage")
		}
	}
	return nil
}

func (s *ScheduleChange) GetEndorsementType() oapiend.EndorsementType {
	return oapiend.EndorsementTypeUpdateScheduleRequest
}

func (s *ScheduleChange) GetPricingPreference() enums.PricingPreference {
	return enums.PricingPreferenceAutomatedPricing
}

func checkForDuplicates[T schedule](schedule []T, valueExtractor func(T) string) error {
	values := slice_utils.Map(schedule, valueExtractor)
	hasDuplicates, duplicateVal := slice_utils.HasDuplicates(values)
	if hasDuplicates {
		return errors.Newf("duplicate value present: %s", duplicateVal)
	}
	return nil
}

func (s *ScheduleChange) GetPolicyModifier() policy.UpdateFn {
	return func(ctx context.Context, policy *policy.Policy) (*policy.Policy, error) {
		if err := s.Validate(ctx, policy); err != nil {
			return nil, errors.Wrap(err, "invalid endorsement Change Data")
		}

		switch policy.ProgramType { //nolint:exhaustive
		case policyenums.ProgramTypeNonFleetAdmitted:
			programData, err := policy.GetAdmittedProgramData(ctx)
			if err != nil {
				return nil, errors.Wrap(err, "failed to get admitted program data")
			}
			s.changeAdmittedProgramData(programData)
			if err = policy.SetProgramData(*programData); err != nil {
				return nil, errors.Wrap(err, "failed to set admitted program data")
			}
			return policy, nil

		default:
			return nil, errors.Newf(
				"%s GetPolicyModifier() has not been implemented for programType %", s.GetType(), policy.ProgramType,
			)
		}
	}
}

func (s *ScheduleChange) changeAdmittedProgramData(programData *admitted.ProgramData) {
	if s.Drivers != nil {
		programData.DriverInfo.Drivers = s.Drivers
		programData.UnderwriterInfo.DriverViolations = s.DriverViolations
	}
	if s.Vehicles != nil {
		programData.EquipmentInfo.Vehicles = s.Vehicles
	}
}

func (s *ScheduleChange) GetAdmittedSubmissionModifier() (
	application.SubmissionUpdateFn[*admitted_app.AdmittedApp], error,
) {
	return func(
		submission application.Submission[*admitted_app.AdmittedApp],
	) (*application.Submission[*admitted_app.AdmittedApp], error) {
		// If the schedule change is for drivers or both, only update the driver info in the submission
		if s.isDriverModificationAllowedOnSubmission() {
			submission.Info.DriverInfo.Drivers = convertDriversToAdmittedApp(s.Drivers)
			submission.Info.UnderwriterInfo.DriverViolations = s.getFilteredDriverViolations()
		}
		// If the schedule change is for vehicles or both, only update the vehicle info in the submission
		if s.isVehicleModificationAllowedOnSubmission() {
			submission.Info.EquipmentInfo.Vehicles = convertVehiclesToAdmittedApp(s.Vehicles)
		}

		return &submission, nil
	}, nil
}

func (s *ScheduleChange) isVehicleModificationAllowedOnSubmission() bool {
	return s.Vehicles != nil &&
		(s.ScheduleChangeType == enums.ScheduleChangeTypeVehicle || s.ScheduleChangeType == enums.ScheduleChangeTypeBoth)
}

func (s *ScheduleChange) isDriverModificationAllowedOnSubmission() bool {
	return s.Drivers != nil &&
		(s.ScheduleChangeType == enums.ScheduleChangeTypeDriver || s.ScheduleChangeType == enums.ScheduleChangeTypeBoth)
}

// getFilteredDriverViolations is of O(n^2) complexity but the number of drivers is expected to be small (10-20)
// so it should be fine. But if the number of drivers increases, we can use a map to store the included drivers
// to reduce the complexity to O(n)
func (s *ScheduleChange) getFilteredDriverViolations() []admitted_app.DriverViolation {
	return slice_utils.Filter(s.DriverViolations, func(violation admitted_app.DriverViolation) bool {
		for _, d := range s.Drivers {
			if strings.EqualFold(d.LicenseNumber, violation.LicenseNumber) {
				return d.IsIncluded
			}
		}
		return false
	})
}

func convertDriversToAdmittedApp(drivers []admitted.DriverDetails) []admitted_app.DriverDetails {
	var admittedAppDrivers []admitted_app.DriverDetails
	for _, driver := range drivers {
		admittedAppDrivers = append(admittedAppDrivers, *driver.ToAdmittedAppDriver())
	}
	return admittedAppDrivers
}

func convertVehiclesToAdmittedApp(vehicles []admitted.VehicleDetails) []admitted_app.VehicleDetails {
	var admittedAppVehicles []admitted_app.VehicleDetails
	for _, vehicle := range vehicles {
		admittedAppVehicles = append(admittedAppVehicles, *vehicle.ToAdmittedAppVehicle())
	}
	return admittedAppVehicles
}

func aggregateStatedValue(
	vehiclesList []admitted.VehicleDetails,
) int {
	var statedValue int
	for _, vehicle := range vehiclesList {
		if vehicle.StatedValue != nil {
			statedValue += *vehicle.StatedValue
		}
	}
	return statedValue
}
