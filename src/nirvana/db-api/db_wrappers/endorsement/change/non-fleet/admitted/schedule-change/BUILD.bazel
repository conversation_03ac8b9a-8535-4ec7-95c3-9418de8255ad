load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "schedule-change",
    srcs = [
        "driver_oapi_converters.go",
        "oapi.go",
        "object_def.go",
        "schedule_change.go",
        "test_utils.go",
        "utils.go",
        "validations.go",
        "vehicle_oapi_converters.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/endorsement/change/non-fleet/admitted/schedule-change",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/endorsement/change/enums",
        "//nirvana/db-api/db_wrappers/endorsement/change/shared",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted",
        "//nirvana/db-api/db_wrappers/policy/program_data/shared",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/endorsement",
        "//nirvana/openapi-specs/components/endorsementuw",
        "//nirvana/openapi-specs/components/nonfleet",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_oapi_codegen_runtime//types",
    ],
)

go_test(
    name = "schedule-change_test",
    srcs = ["schedule_change_test.go"],
    embed = [":schedule-change"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted",
        "@com_github_stretchr_testify//require",
    ],
)
