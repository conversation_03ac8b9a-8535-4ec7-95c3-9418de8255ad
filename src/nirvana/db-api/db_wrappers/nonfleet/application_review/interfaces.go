package application_review

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	file_upload_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/db-api/db_models/non_fleet"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/common"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
)

type Filter interface {
	queryMods() []qm.QueryMod
}

type AppReviewUpdateFn func(review ApplicationReview) (ApplicationReview, error)

type Wrapper interface {
	// InsertApplicationReview persists a new application review. If another
	// application review with the same id already exists, this will throw an
	// error
	InsertApplicationReview(ctx context.Context, appReviewObj ApplicationReview) error

	// GetAppReviewByID fetches the application review identified by appReviewId.
	GetAppReviewByID(ctx context.Context, appReviewId string) (ApplicationReview, error)

	// GetAllApplicationReviews fetches all application reviews.
	GetAllApplicationReviews(ctx context.Context, filters ...Filter) ([]ApplicationReview, error)

	// GetAllReviewsForApp fetches all application reviews for the application identified by appId.
	GetAllReviewsForApp(ctx context.Context, appId string) ([]ApplicationReview, error)

	// GetPaginatedReviews fetches all the ApplicationReview objects with pagination
	GetPaginatedReviews(
		ctx context.Context,
		options Options,
		cursor *Cursor,
	) ([]ApplicationReview, error)

	// UpdateAppReview updates the app review identified by appReviewId. updateFn should be
	// idempotent, since it can be retried internally if we fail to apply the
	// update due to concurrent updates.
	//
	// UpdateApp offers serializable transaction guarantees.
	UpdateAppReview(ctx context.Context, appReviewId string, updateFn AppReviewUpdateFn) error

	// GetAppReviewNotes fetches all notes for the application review identified by appReviewId.
	GetAppReviewNotes(ctx context.Context, appReviewId string) (*ApplicationReviewNote, error)

	// UpdateAppReviewNotes updates the notes for the application review identified by appReviewId.
	UpdateAppReviewNotes(ctx context.Context, appReviewId, notes string) error

	// GetAppReviewDocuments fetches all documents for the application review identified by appReviewId.
	GetAppReviewDocuments(ctx context.Context, appReviewId string) (ApplicationReviewDocuments, error)

	// InsertAppReviewDocuments appends a new document in the additionalDocuments column of the application review table
	InsertAppReviewDocument(ctx context.Context, appReviewId, fileName string, fileHandle *uuid.UUID, fileType *file_upload_enums.FileType) error

	// GetAppReviewEvents fetches all Events for the application review identified by appReviewId.
	GetAppReviewEvents(ctx context.Context, appReviewId string) ([]ApplicationReviewEvent, error)

	// InsertAppReviewEvent persists a new application review Event. If another
	// application review Event with the same id already exists, this will throw an
	// error
	InsertAppReviewEvent(ctx context.Context, appReviewEventObj *ApplicationReviewEvent) error

	// GetAppReviewByAppID fetches the latest app review using the supplied
	// appID
	GetAppReviewByAppID(ctx context.Context, appID uuid.UUID) (ApplicationReview, error)

	// GetAppReviewIDByAppIDAndState fetches the app review id using the supplied
	// appID and app review state
	GetAppReviewIDByAppIDAndState(ctx context.Context, appID uuid.UUID, state AppReviewState) (uuid.UUID, error)
}

type ApplicationReview interface {
	GetID() uuid.UUID
	GetProgram() policy_enums.ProgramType
	GetAppState() AppReviewState
	UpdateAppState(AppReviewState)
	GetApplicationID() uuid.UUID
	GetSubmissionID() uuid.UUID
	SetSubmissionID(uuid.UUID)
	GetUnderwriterID() uuid.UUID
	SetUnderwriterID(uuid.UUID)
	IsMVRPulled() bool
	SetMVRPulled(bool)
	GetReviewNotes() *ApplicationReviewNote
	UpdateReviewNotes(string)
	GetReviewDocuments() ApplicationReviewDocuments
	InsertReviewDocument(string, *uuid.UUID, *file_upload_enums.FileType)
	IsPanelReviewed(PanelType) bool
	SetPanelReview(PanelType, bool) error
	SetCoverageOverride() error
	GetOverrides() Overrides
	UpdateOverrides(func(*Overrides) (Overrides, error)) error
	GetCreatedAt() time.Time
	GetUpdatedAt() time.Time
	AppReviewToDB() (*non_fleet.ApplicationReview, error)
	ResourcePaths() []string
	SetPreBindInfo(preBindInfo PreBindInfo)
	SetEndStateReasons(reasons EndStateReasons)
	GetEndStateReasons() EndStateReasons
	GetEffectiveDate() time.Time
	GetEffectiveDateTo() time.Time
	SetScraperInfo(scraperInfo common.ScraperInfo)
	GetScraperInfo() *common.ScraperInfo
	GetDriversInfo() *DriversInfo
	UpdateDriversInfo(driversInfo *DriversInfo)
	GetNFAuthorityInfo() *NFAuthorityInfo
	UpdateNFAuthorityInfo(nfAuthorityInfo *NFAuthorityInfo)
}
