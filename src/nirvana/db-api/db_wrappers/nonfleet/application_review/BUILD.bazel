load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "application_review",
    srcs = [
        "admitted_app_review.go",
        "appreviewstate_enumer.go",
        "client_mock.go",
        "doc.go",
        "enums.go",
        "eventtype_enumer.go",
        "filters.go",
        "fx.go",
        "helper.go",
        "interfaces.go",
        "object_defs.go",
        "paneltype_enumer.go",
        "serde_utils.go",
        "utils.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/retry",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/type_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models",
        "//nirvana/db-api/db_models/non_fleet",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/common",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/common",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/infra/authz",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/authorities/fact",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/underwriting",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "application_review_test",
    srcs = ["serde_utils_test.go"],
    embed = [":application_review"],
    deps = [
        "//nirvana/db-api/db_models/non_fleet",
        "//nirvana/db-api/db_wrappers/nonfleet/common",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/nonfleet/authorities/fact",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_volatiletech_null_v8//:null",
    ],
)
