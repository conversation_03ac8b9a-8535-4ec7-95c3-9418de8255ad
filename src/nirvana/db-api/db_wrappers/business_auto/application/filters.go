package application

import (
	"fmt"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	root_models "nirvanatech.com/nirvana/db-api/db_models"
	db_models "nirvanatech.com/nirvana/db-api/db_models/business_auto"
	"nirvanatech.com/nirvana/infra/constants"
)

type singleQMFilter struct {
	queryMod qm.QueryMod
}

func (s singleQMFilter) queryMods() []qm.QueryMod {
	return []qm.QueryMod{s.queryMod}
}

type multiQMFilter struct {
	qMods []qm.QueryMod
}

func (m *multiQMFilter) queryMods() []qm.QueryMod {
	return m.qMods
}

// OnlyTestAgencies returns a filter that includes only applications whose agency is marked as a test agency
func OnlyTestAgencies() Filter {
	agencyJoinClause := qm.InnerJoin(fmt.Sprintf(
		"%s ON %s = %s.%s",
		root_models.TableNames.Agency, db_models.ApplicationTableColumns.AgencyID,
		root_models.TableNames.Agency, root_models.AgencyColumns.ID,
	))
	agencyTestAgencyFilterClause := qm.Where(fmt.Sprintf(
		"%s.%s = true", root_models.TableNames.Agency, root_models.AgencyColumns.IsTestAgency,
	))

	return &multiQMFilter{
		qMods: []qm.QueryMod{agencyJoinClause, agencyTestAgencyFilterClause},
	}
}

// SkipTestAgencies returns a filter that excludes applications whose agency is marked as a test agency
func SkipTestAgencies() Filter {
	agencyJoinClause := qm.InnerJoin(fmt.Sprintf(
		"%s ON %s = %s.%s",
		root_models.TableNames.Agency, db_models.ApplicationTableColumns.AgencyID,
		root_models.TableNames.Agency, root_models.AgencyColumns.ID,
	))
	agencyTestAgencyFilterClause := qm.Where(fmt.Sprintf(
		"%s.%s = false", root_models.TableNames.Agency, root_models.AgencyColumns.IsTestAgency,
	))

	return &multiQMFilter{
		qMods: []qm.QueryMod{agencyJoinClause, agencyTestAgencyFilterClause},
	}
}

// getKiwiQAAgencyIds returns the list of KiwiQA agency IDs as interfaces for SQL queries
// Includes both KiwiQA agency and clearance testing agencies
func getKiwiQAAgencyIds() []interface{} {
	var kiwiQAIds []interface{}
	kiwiQAIds = append(kiwiQAIds, constants.KiwiQAAgencyID.String())
	kiwiQAIds = append(kiwiQAIds, constants.ClearanceTestingAgencyID1.String())
	kiwiQAIds = append(kiwiQAIds, constants.ClearanceTestingAgencyID2.String())
	kiwiQAIds = append(kiwiQAIds, constants.ClearanceTestingAgencyID3.String())
	return kiwiQAIds
}

// OnlyKiwiQA applies the "WHERE agency_id IN ()" clause for KiwiQA specific agencies
func OnlyKiwiQA() Filter {
	kiwiQAIds := getKiwiQAAgencyIds()
	return &singleQMFilter{
		queryMod: qm.WhereIn(
			fmt.Sprintf("%s IN ?", db_models.ApplicationTableColumns.AgencyID),
			kiwiQAIds...,
		),
	}
}

// NotInKiwiQA applies the "WHERE agency_id NOT IN ()" clause to exclude KiwiQA specific agencies
func NotInKiwiQA() Filter {
	kiwiQAIds := getKiwiQAAgencyIds()
	return &singleQMFilter{
		queryMod: qm.WhereNotIn(
			fmt.Sprintf("%s NOT IN ?", db_models.ApplicationTableColumns.AgencyID),
			kiwiQAIds...,
		),
	}
}
