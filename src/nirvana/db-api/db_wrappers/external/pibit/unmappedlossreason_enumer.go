// Code generated by "enumer -type=UnmappedLossReason -json"; DO NOT EDIT.

package pibit

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _UnmappedLossReasonName = "UnmappedLossReasonMissingCoverageUnmappedLossReasonMissingDateOfLossUnmappedLossReasonOutsideAggregationPeriodsUnmappedLossReasonDuplicateLossUnmappedLossReasonUnsupportedCoverage"

var _UnmappedLossReasonIndex = [...]uint8{0, 33, 68, 111, 142, 179}

const _UnmappedLossReasonLowerName = "unmappedlossreasonmissingcoverageunmappedlossreasonmissingdateoflossunmappedlossreasonoutsideaggregationperiodsunmappedlossreasonduplicatelossunmappedlossreasonunsupportedcoverage"

func (i UnmappedLossReason) String() string {
	if i < 0 || i >= UnmappedLossReason(len(_UnmappedLossReasonIndex)-1) {
		return fmt.Sprintf("UnmappedLossReason(%d)", i)
	}
	return _UnmappedLossReasonName[_UnmappedLossReasonIndex[i]:_UnmappedLossReasonIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _UnmappedLossReasonNoOp() {
	var x [1]struct{}
	_ = x[UnmappedLossReasonMissingCoverage-(0)]
	_ = x[UnmappedLossReasonMissingDateOfLoss-(1)]
	_ = x[UnmappedLossReasonOutsideAggregationPeriods-(2)]
	_ = x[UnmappedLossReasonDuplicateLoss-(3)]
	_ = x[UnmappedLossReasonUnsupportedCoverage-(4)]
}

var _UnmappedLossReasonValues = []UnmappedLossReason{UnmappedLossReasonMissingCoverage, UnmappedLossReasonMissingDateOfLoss, UnmappedLossReasonOutsideAggregationPeriods, UnmappedLossReasonDuplicateLoss, UnmappedLossReasonUnsupportedCoverage}

var _UnmappedLossReasonNameToValueMap = map[string]UnmappedLossReason{
	_UnmappedLossReasonName[0:33]:         UnmappedLossReasonMissingCoverage,
	_UnmappedLossReasonLowerName[0:33]:    UnmappedLossReasonMissingCoverage,
	_UnmappedLossReasonName[33:68]:        UnmappedLossReasonMissingDateOfLoss,
	_UnmappedLossReasonLowerName[33:68]:   UnmappedLossReasonMissingDateOfLoss,
	_UnmappedLossReasonName[68:111]:       UnmappedLossReasonOutsideAggregationPeriods,
	_UnmappedLossReasonLowerName[68:111]:  UnmappedLossReasonOutsideAggregationPeriods,
	_UnmappedLossReasonName[111:142]:      UnmappedLossReasonDuplicateLoss,
	_UnmappedLossReasonLowerName[111:142]: UnmappedLossReasonDuplicateLoss,
	_UnmappedLossReasonName[142:179]:      UnmappedLossReasonUnsupportedCoverage,
	_UnmappedLossReasonLowerName[142:179]: UnmappedLossReasonUnsupportedCoverage,
}

var _UnmappedLossReasonNames = []string{
	_UnmappedLossReasonName[0:33],
	_UnmappedLossReasonName[33:68],
	_UnmappedLossReasonName[68:111],
	_UnmappedLossReasonName[111:142],
	_UnmappedLossReasonName[142:179],
}

// UnmappedLossReasonString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func UnmappedLossReasonString(s string) (UnmappedLossReason, error) {
	if val, ok := _UnmappedLossReasonNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _UnmappedLossReasonNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to UnmappedLossReason values", s)
}

// UnmappedLossReasonValues returns all values of the enum
func UnmappedLossReasonValues() []UnmappedLossReason {
	return _UnmappedLossReasonValues
}

// UnmappedLossReasonStrings returns a slice of all String values of the enum
func UnmappedLossReasonStrings() []string {
	strs := make([]string, len(_UnmappedLossReasonNames))
	copy(strs, _UnmappedLossReasonNames)
	return strs
}

// IsAUnmappedLossReason returns "true" if the value is listed in the enum definition. "false" otherwise
func (i UnmappedLossReason) IsAUnmappedLossReason() bool {
	for _, v := range _UnmappedLossReasonValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for UnmappedLossReason
func (i UnmappedLossReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for UnmappedLossReason
func (i *UnmappedLossReason) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("UnmappedLossReason should be a string, got %s", data)
	}

	var err error
	*i, err = UnmappedLossReasonString(s)
	return err
}
