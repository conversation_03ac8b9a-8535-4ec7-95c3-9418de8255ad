package pibit

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/volatiletech/sqlboiler/v4/types"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_models/parsed_loss_runs"
)

func documentToDb(document *Document) (*parsed_loss_runs.Document, error) {
	currTime := time.Now()

	documentDb := &parsed_loss_runs.Document{}
	documentDb.ID = document.ID
	documentDb.Status = document.Status.String()
	documentDb.CreatedAt = document.CreatedAt
	documentDb.UpdatedAt = null.TimeFrom(currTime)
	documentDb.SubmissionID = null.StringFromPtr(document.SubmissionId)
	documentDb.ErrorInfo = null.NewJSON(nil, false)
	documentDb.RequestID = null.StringFrom(document.RequestId)
	if document.ErrorInfo != nil {
		errorInfoBytes, err := json.Marshal(document.ErrorInfo)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to marshal ErrorInfo into bytes")
		}
		documentDb.ErrorInfo = null.JSONFrom(errorInfoBytes)
	}
	if document.FirstUpdatedAt != nil {
		documentDb.FirstUpdatedAt = null.TimeFromPtr(document.FirstUpdatedAt)
	} else {
		// Ensures that FirstUpdatedAt is assigned a time value only once in the DB row lifetime
		//
		// FirstUpdatedAt will be assigned a value for the first time when,
		// the status being written is Received or Errored (not Sent)
		if document.Status == DocumentStatusReceived || document.Status == DocumentStatusErrored || document.Status == DocumentStatusNoLossRuns {
			documentDb.FirstUpdatedAt = null.TimeFrom(currTime)
		}
	}
	documentDb.SummaryFileURL = null.StringFromPtr(document.SummaryFileUrl)
	return documentDb, nil
}

func dbToDocument(documentDb *parsed_loss_runs.Document) (*Document, error) {
	document := &Document{}
	document.ID = documentDb.ID
	document.Status = DocumentStatusMap[documentDb.Status]
	document.CreatedAt = documentDb.CreatedAt
	if documentDb.UpdatedAt.Valid {
		document.UpdatedAt = pointer_utils.Time(documentDb.UpdatedAt.Time)
	}
	if documentDb.SubmissionID.Valid {
		document.SubmissionId = pointer_utils.String(documentDb.SubmissionID.String)
	}
	if documentDb.ErrorInfo.Valid {
		var errorInfo DocumentErrorInfo
		err := json.Unmarshal(documentDb.ErrorInfo.JSON, &errorInfo)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to unmarshal json bytes into ErrorInfo")
		}
		document.ErrorInfo = &errorInfo
	}
	if documentDb.RequestID.Valid {
		document.RequestId = documentDb.RequestID.String
	}
	if documentDb.FirstUpdatedAt.Valid {
		document.FirstUpdatedAt = pointer_utils.Time(documentDb.FirstUpdatedAt.Time)
	}
	document.SummaryFileUrl = documentDb.SummaryFileURL.Ptr()
	return document, nil
}

func policyToDb(policy *PolicyData) *parsed_loss_runs.Policy {
	policyDb := &parsed_loss_runs.Policy{
		ID:                   uuid.New().String(),
		DocumentID:           *policy.DocumentId,
		PolicySN:             int(*policy.PolicySn),
		PolicyNo:             generateNullPkgString(policy.PolicyNo),
		Lob:                  generateNullPkgString(policy.Lob),
		Insurer:              generateNullPkgString(policy.Insurer),
		Insured:              generateNullPkgString(policy.Insured),
		Agent:                generateNullPkgString(policy.Agent),
		EffDate:              null.TimeFromPtr(policy.EffDate),
		ExpDate:              null.TimeFromPtr(policy.ExpDate),
		ReportGenerationDate: null.TimeFromPtr(policy.ReportGenerationDate),
		CancelDate:           null.TimeFromPtr(policy.CancelDate),
		NoLoss:               int(*policy.NoLoss),
		TotalPaid:            null.Float64FromPtr(policy.TotalPaid),
		TotalReserve:         null.Float64FromPtr(policy.TotalReserve),
		TotalRecovered:       null.Float64FromPtr(policy.TotalRecovered),
		TotalIncurred:        *policy.TotalIncurred,
	}
	return policyDb
}

func dbToPolicy(policyDb *parsed_loss_runs.Policy) *PolicyData {
	policy := &PolicyData{}
	policy.DocumentId = &policyDb.DocumentID
	policy.PolicySn = pointer_utils.ToPointer(int32(policyDb.PolicySN))
	if policyDb.Insurer.Valid {
		policy.Insurer = pointer_utils.ToPointer(policyDb.Insurer.String)
	}
	if policyDb.Lob.Valid {
		policy.Lob = pointer_utils.ToPointer(policyDb.Lob.String)
	}
	if policyDb.Insured.Valid {
		policy.Insured = pointer_utils.ToPointer(policyDb.Insured.String)
	}
	if policyDb.Agent.Valid {
		policy.Agent = pointer_utils.ToPointer(policyDb.Agent.String)
	}
	if policyDb.PolicyNo.Valid {
		policy.PolicyNo = pointer_utils.ToPointer(policyDb.PolicyNo.String)
	}
	if policyDb.EffDate.Valid {
		policy.EffDate = pointer_utils.ToPointer(policyDb.EffDate.Time)
	}
	if policyDb.ExpDate.Valid {
		policy.ExpDate = pointer_utils.ToPointer(policyDb.ExpDate.Time)
	}
	if policyDb.ReportGenerationDate.Valid {
		policy.ReportGenerationDate = pointer_utils.ToPointer(policyDb.ReportGenerationDate.Time)
	}
	if policyDb.CancelDate.Valid {
		policy.CancelDate = pointer_utils.ToPointer(policyDb.CancelDate.Time)
	}
	policy.NoLoss = pointer_utils.ToPointer(int32(policyDb.NoLoss))
	if policyDb.TotalPaid.Valid {
		policy.TotalPaid = pointer_utils.ToPointer(policyDb.TotalPaid.Float64)
	}
	if policyDb.TotalReserve.Valid {
		policy.TotalReserve = pointer_utils.ToPointer(policyDb.TotalReserve.Float64)
	}
	if policyDb.TotalRecovered.Valid {
		policy.TotalRecovered = pointer_utils.ToPointer(policyDb.TotalRecovered.Float64)
	}
	policy.TotalIncurred = &policyDb.TotalIncurred
	return policy
}

func claimToDb(claim *ClaimData) *parsed_loss_runs.Claim {
	claimDb := &parsed_loss_runs.Claim{
		ID:                     uuid.New().String(),
		DocumentID:             *claim.DocumentId,
		ClaimSN:                int(*claim.ClaimSn),
		PolicySN:               int(*claim.PolicySn),
		ClaimID:                generateNullPkgString(claim.ClaimID),
		OccurrenceID:           claim.OccurrenceID,
		DateOfLoss:             null.TimeFromPtr(claim.DateOfLoss),
		DateReported:           null.TimeFromPtr(claim.DateReported),
		TimeOfLoss:             null.TimeFromPtr(claim.TimeOfLoss),
		ClosedDate:             null.TimeFromPtr(claim.ClosedDate),
		CauseOfLossSummary:     generateNullPkgString(claim.CauseOfLossSummary),
		CauseOfLossDescription: generateNullPkgString(claim.CauseOfLossDescription),
		Vin:                    generateNullPkgString(claim.VIN),
		Type:                   generateNullPkgString(claim.Type),
		DriverID:               generateNullPkgString(claim.DriverID),
		Name:                   generateNullPkgString(claim.Name),
		Age:                    null.NewInt(0, false),
		Gender:                 generateNullPkgString(claim.Gender),
		Dob:                    null.TimeFromPtr(claim.DOB),
		HiringDate:             null.TimeFromPtr(claim.HiringDate),
		Street:                 generateNullPkgString(claim.Street),
		CityCounty:             generateNullPkgString(claim.CityCounty),
		State:                  generateNullPkgString(claim.State),
		Zip:                    generateNullPkgString(claim.Zip),
		LossLocation:           generateNullPkgString(claim.LossLocation),
		ClaimStatus:            generateNullPkgString(claim.ClaimStatus),
	}

	if claim.Age != nil {
		claimDb.Age = null.IntFrom(int(*claim.Age))
	}
	return claimDb
}

func dbToClaim(claimDb *parsed_loss_runs.Claim) *ClaimData {
	claim := &ClaimData{}
	claim.DocumentId = &claimDb.DocumentID
	claim.ClaimSn = pointer_utils.ToPointer(int32(claimDb.ClaimSN))
	claim.PolicySn = pointer_utils.ToPointer(int32(claimDb.PolicySN))
	claim.OccurrenceID = claimDb.OccurrenceID
	if claimDb.ClaimID.Valid {
		claim.ClaimID = pointer_utils.ToPointer(claimDb.ClaimID.String)
	}
	if claimDb.DateOfLoss.Valid {
		claim.DateOfLoss = pointer_utils.ToPointer(claimDb.DateOfLoss.Time)
	}
	if claimDb.DateReported.Valid {
		claim.DateReported = pointer_utils.ToPointer(claimDb.DateReported.Time)
	}
	if claimDb.TimeOfLoss.Valid {
		claim.TimeOfLoss = pointer_utils.ToPointer(claimDb.TimeOfLoss.Time)
	}
	if claimDb.ClosedDate.Valid {
		claim.ClosedDate = pointer_utils.ToPointer(claimDb.ClosedDate.Time)
	}
	if claimDb.CauseOfLossSummary.Valid {
		claim.CauseOfLossSummary = pointer_utils.ToPointer(claimDb.CauseOfLossSummary.String)
	}
	if claimDb.CauseOfLossDescription.Valid {
		claim.CauseOfLossDescription = pointer_utils.ToPointer(claimDb.CauseOfLossDescription.String)
	}
	if claimDb.Vin.Valid {
		claim.VIN = pointer_utils.ToPointer(claimDb.Vin.String)
	}
	if claimDb.Type.Valid {
		claim.Type = pointer_utils.ToPointer(claimDb.Type.String)
	}
	if claimDb.DriverID.Valid {
		claim.DriverID = pointer_utils.ToPointer(claimDb.DriverID.String)
	}
	if claimDb.Name.Valid {
		claim.Name = pointer_utils.ToPointer(claimDb.Name.String)
	}
	if claimDb.Age.Valid {
		claim.Age = pointer_utils.ToPointer(int32(claimDb.Age.Int))
	}
	if claimDb.Gender.Valid {
		claim.Gender = pointer_utils.ToPointer(claimDb.Gender.String)
	}
	if claimDb.Dob.Valid {
		claim.DOB = pointer_utils.ToPointer(claimDb.Dob.Time)
	}
	if claimDb.HiringDate.Valid {
		claim.HiringDate = pointer_utils.ToPointer(claimDb.HiringDate.Time)
	}
	if claimDb.Street.Valid {
		claim.Street = pointer_utils.ToPointer(claimDb.Street.String)
	}
	if claimDb.CityCounty.Valid {
		claim.CityCounty = pointer_utils.ToPointer(claimDb.CityCounty.String)
	}
	if claimDb.State.Valid {
		claim.State = pointer_utils.ToPointer(claimDb.State.String)
	}
	if claimDb.Zip.Valid {
		claim.Zip = pointer_utils.ToPointer(claimDb.Zip.String)
	}
	if claimDb.LossLocation.Valid {
		claim.LossLocation = pointer_utils.ToPointer(claimDb.LossLocation.String)
	}
	if claimDb.ClaimStatus.Valid {
		claim.ClaimStatus = pointer_utils.ToPointer(claimDb.ClaimStatus.String)
	}
	return claim
}

func lossToDb(loss *LossData) *parsed_loss_runs.Loss {
	var coverageInferred null.String
	if loss.CoverageInferred != nil {
		coverageInferred.Valid = true
		coverageInferred.String = loss.CoverageInferred.String()
	}
	lossDb := &parsed_loss_runs.Loss{
		ID:                    uuid.New().String(),
		DocumentID:            *loss.DocumentId,
		PolicySN:              int(*loss.PolicySn),
		ClaimSN:               int(*loss.ClaimSn),
		LossSeqID:             *loss.LossSeqID,
		Claimant:              generateNullPkgString(loss.Claimant),
		CoverageInferred:      coverageInferred,
		ClaimLossType:         generateNullPkgString(loss.ClaimLossType),
		ClaimLossTypeInferred: generateNullPkgString(loss.ClaimLossTypeInferred),
		Examiner:              generateNullPkgString(loss.Examiner),
		LossReserved:          null.Float64FromPtr(loss.LossReserved),
		LossPaid:              null.Float64FromPtr(loss.LossPaid),
		ExpenseReserve:        null.Float64FromPtr(loss.ExpenseReserve),
		ExpensePaid:           null.Float64FromPtr(loss.ExpensePaid),
		TotalReserve:          null.Float64FromPtr(loss.TotalReserve),
		TotalPaid:             null.Float64FromPtr(loss.TotalPaid),
		TotalRecovered:        null.Float64FromPtr(loss.TotalRecovered),
		TotalIncurred:         null.Float64FromPtr(loss.TotalIncurred),
		DeductibleAmount:      null.Float64FromPtr(loss.DeductibleAmount),
		SubrogationAmount:     null.Float64FromPtr(loss.SubrogationAmount),
		SalvageAmount:         null.Float64FromPtr(loss.SalvageAmount),
		OtherRecovery:         null.Float64FromPtr(loss.OtherRecovery),
		LegalReserve:          null.Float64FromPtr(loss.LegalReserve),
		LegalPaid:             null.Float64FromPtr(loss.LegalPaid),
		LegalIncurred:         null.Float64FromPtr(loss.LegalIncurred),
		AlaeExpenseReserve:    null.Float64FromPtr(loss.AlaeExpenseReserve),
		AlaePaid:              null.Float64FromPtr(loss.AlaePaid),
		AlaeIncurred:          null.Float64FromPtr(loss.AlaeIncurred),
		OtherExpenseReserve:   null.Float64FromPtr(loss.OtherExpenseReserve),
		OtherExpensePaid:      null.Float64FromPtr(loss.OtherExpensePaid),
		OtherExpenseIncurred:  null.Float64FromPtr(loss.OtherExpenseIncurred),
	}

	return lossDb
}

func dbToLoss(lossDb *parsed_loss_runs.Loss) (*LossData, error) {
	loss := &LossData{}
	loss.DocumentId = &lossDb.DocumentID
	loss.ClaimSn = pointer_utils.ToPointer(int32(lossDb.ClaimSN))
	loss.PolicySn = pointer_utils.ToPointer(int32(lossDb.PolicySN))
	loss.LossSeqID = &lossDb.LossSeqID
	if lossDb.Claimant.Valid {
		loss.Claimant = pointer_utils.ToPointer(lossDb.Claimant.String)
	}
	if lossDb.CoverageInferred.Valid {
		coverageInferred, err := ParseCoverageInferred(lossDb.CoverageInferred.String)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to parse coverage inferred")
		}
		loss.CoverageInferred = pointer_utils.ToPointer(coverageInferred)
	}
	if lossDb.ClaimLossType.Valid {
		loss.ClaimLossType = pointer_utils.ToPointer(lossDb.ClaimLossType.String)
	}
	if lossDb.ClaimLossTypeInferred.Valid {
		loss.ClaimLossTypeInferred = pointer_utils.ToPointer(lossDb.ClaimLossTypeInferred.String)
	}
	if lossDb.Examiner.Valid {
		loss.Examiner = pointer_utils.ToPointer(lossDb.Examiner.String)
	}
	if lossDb.LossReserved.Valid {
		loss.LossReserved = pointer_utils.ToPointer(lossDb.LossReserved.Float64)
	}
	if lossDb.LossPaid.Valid {
		loss.LossPaid = pointer_utils.ToPointer(lossDb.LossPaid.Float64)
	}
	if lossDb.ExpenseReserve.Valid {
		loss.ExpenseReserve = pointer_utils.ToPointer(lossDb.ExpenseReserve.Float64)
	}
	if lossDb.ExpensePaid.Valid {
		loss.ExpensePaid = pointer_utils.ToPointer(lossDb.ExpensePaid.Float64)
	}
	if lossDb.TotalReserve.Valid {
		loss.TotalReserve = pointer_utils.ToPointer(lossDb.TotalReserve.Float64)
	}
	if lossDb.TotalPaid.Valid {
		loss.TotalPaid = pointer_utils.ToPointer(lossDb.TotalPaid.Float64)
	}
	if lossDb.TotalRecovered.Valid {
		loss.TotalRecovered = pointer_utils.ToPointer(lossDb.TotalRecovered.Float64)
	}
	if lossDb.TotalIncurred.Valid {
		loss.TotalIncurred = pointer_utils.ToPointer(lossDb.TotalIncurred.Float64)
	}
	if lossDb.DeductibleAmount.Valid {
		loss.DeductibleAmount = pointer_utils.ToPointer(lossDb.DeductibleAmount.Float64)
	}
	if lossDb.SubrogationAmount.Valid {
		loss.SubrogationAmount = pointer_utils.ToPointer(lossDb.SubrogationAmount.Float64)
	}
	if lossDb.SalvageAmount.Valid {
		loss.SalvageAmount = pointer_utils.ToPointer(lossDb.SalvageAmount.Float64)
	}
	if lossDb.OtherRecovery.Valid {
		loss.OtherRecovery = pointer_utils.ToPointer(lossDb.OtherRecovery.Float64)
	}
	if lossDb.LegalReserve.Valid {
		loss.LegalReserve = pointer_utils.ToPointer(lossDb.LegalReserve.Float64)
	}
	if lossDb.LegalPaid.Valid {
		loss.LegalPaid = pointer_utils.ToPointer(lossDb.LegalPaid.Float64)
	}
	if lossDb.LegalIncurred.Valid {
		loss.LegalIncurred = pointer_utils.ToPointer(lossDb.LegalIncurred.Float64)
	}
	if lossDb.AlaeExpenseReserve.Valid {
		loss.AlaeExpenseReserve = pointer_utils.ToPointer(lossDb.AlaeExpenseReserve.Float64)
	}
	if lossDb.AlaePaid.Valid {
		loss.AlaePaid = pointer_utils.ToPointer(lossDb.AlaePaid.Float64)
	}
	if lossDb.AlaeIncurred.Valid {
		loss.AlaeIncurred = pointer_utils.ToPointer(lossDb.AlaeIncurred.Float64)
	}
	if lossDb.OtherExpenseReserve.Valid {
		loss.OtherExpenseReserve = pointer_utils.ToPointer(lossDb.OtherExpenseReserve.Float64)
	}
	if lossDb.OtherExpensePaid.Valid {
		loss.OtherExpensePaid = pointer_utils.ToPointer(lossDb.OtherExpensePaid.Float64)
	}
	if lossDb.OtherExpenseIncurred.Valid {
		loss.OtherExpenseIncurred = pointer_utils.ToPointer(lossDb.OtherExpenseIncurred.Float64)
	}
	return loss, nil
}

func docStateTransitionToDb(transition DocumentStateTransition) *parsed_loss_runs.DocumentStateTransition {
	transitionDb := &parsed_loss_runs.DocumentStateTransition{
		ID:             transition.ID,
		DocumentID:     transition.DocumentID,
		FromStatus:     transition.FromStatus.String(),
		ToStatus:       transition.ToStatus.String(),
		TransitionTime: transition.TransitionTime,
		TransitionedBy: transition.TransitionedBy,
		RequestID:      null.StringFrom(transition.RequestID),
	}
	return transitionDb
}

func aggregationToDb(aggregation Aggregation) (*parsed_loss_runs.Aggregation, error) {
	confidenceInfo, err := json.Marshal(aggregation.ConfidenceInfo)
	if err != nil {
		return nil, err
	}

	var summary null.JSON
	if aggregation.Summary != nil {
		marshalled, err := json.Marshal(aggregation.Summary)
		if err != nil {
			return nil, err
		}
		summary = null.JSONFrom(marshalled)
	}

	aggregationDb := &parsed_loss_runs.Aggregation{
		ID:                  aggregation.ID,
		ApplicationID:       aggregation.ApplicationID,
		ApplicationReviewID: aggregation.ApplicationReviewID,
		Status:              aggregation.Status.String(),
		ConfidenceInfo:      confidenceInfo,
		CreatedAt:           aggregation.CreatedAt,
		UpdatedAt:           null.TimeFromPtr(aggregation.UpdatedAt),
		SummaryJSON:         summary,
	}
	return aggregationDb, nil
}

func dbToAggregation(aggregationDb parsed_loss_runs.Aggregation) (*Aggregation, error) {
	status, err := AggregationStatusString(aggregationDb.Status)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to convert string %s to AggregationStatus", aggregationDb.Status)
	}

	var confidenceInfo AggregationConfidenceInfo
	err = json.Unmarshal(aggregationDb.ConfidenceInfo, &confidenceInfo)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to unmarshal confidence info")
	}

	var updatedAt *time.Time
	if aggregationDb.UpdatedAt.Valid {
		updatedAt = pointer_utils.ToPointer(aggregationDb.UpdatedAt.Time)
	}

	var aggregationSummary *AggregationSummary
	if aggregationDb.SummaryJSON.Valid {
		err = json.Unmarshal(aggregationDb.SummaryJSON.JSON, &aggregationSummary)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal summary")
		}
	}

	aggregation := &Aggregation{
		ID:                  aggregationDb.ID,
		ApplicationID:       aggregationDb.ApplicationID,
		ApplicationReviewID: aggregationDb.ApplicationReviewID,
		Status:              status,
		ConfidenceInfo:      confidenceInfo,
		CreatedAt:           aggregationDb.CreatedAt,
		UpdatedAt:           updatedAt,
		Summary:             aggregationSummary,
	}
	return aggregation, nil
}

func processedLossToDb(processedLoss ProcessedLoss) (*parsed_loss_runs.ProcessedLoss, error) {
	confidenceInfo, err := json.Marshal(processedLoss.ConfidenceInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal confidence info: %w", err)
	}

	unmappedReasons := make(types.StringArray, len(processedLoss.UnmappedReasons))
	for i, reason := range processedLoss.UnmappedReasons {
		unmappedReasons[i] = reason.String()
	}

	var coverageStringPtr *string
	if processedLoss.Coverage != nil {
		coverageStringPtr = pointer_utils.ToPointer(processedLoss.Coverage.String())
	}

	var coverageInferredStringPtr *string
	if processedLoss.CoverageInferred != nil {
		coverageInferredStringPtr = pointer_utils.ToPointer(processedLoss.CoverageInferred.String())
	}

	return &parsed_loss_runs.ProcessedLoss{
		ID:                     processedLoss.ID,
		AggregationID:          processedLoss.AggregationID,
		PeriodStartDate:        null.TimeFromPtr(processedLoss.PeriodStartDate),
		PeriodEndDate:          null.TimeFromPtr(processedLoss.PeriodEndDate),
		Coverage:               null.StringFromPtr(coverageStringPtr),
		PolicyNo:               null.StringFromPtr(processedLoss.PolicyNo),
		Lob:                    null.StringFromPtr(processedLoss.Lob),
		Insurer:                null.StringFromPtr(processedLoss.Insurer),
		Insured:                null.StringFromPtr(processedLoss.Insured),
		Agent:                  null.StringFromPtr(processedLoss.Agent),
		EffDate:                null.TimeFromPtr(processedLoss.EffDate),
		ExpDate:                null.TimeFromPtr(processedLoss.ExpDate),
		ReportGenerationDate:   null.TimeFromPtr(processedLoss.ReportGenerationDate),
		CancelDate:             null.TimeFromPtr(processedLoss.CancelDate),
		NoLoss:                 processedLoss.NoLoss,
		PolicyTotalPaid:        null.Float64FromPtr(processedLoss.PolicyTotalPaid),
		PolicyTotalReserve:     null.Float64FromPtr(processedLoss.PolicyTotalReserve),
		PolicyTotalRecovered:   null.Float64FromPtr(processedLoss.PolicyTotalRecovered),
		PolicyTotalIncurred:    processedLoss.PolicyTotalIncurred,
		ClaimID:                null.StringFromPtr(processedLoss.ClaimID),
		OccurrenceID:           processedLoss.OccurrenceID,
		DateOfLoss:             null.TimeFromPtr(processedLoss.DateOfLoss),
		DateReported:           null.TimeFromPtr(processedLoss.DateReported),
		TimeOfLoss:             null.TimeFromPtr(processedLoss.TimeOfLoss),
		ClosedDate:             null.TimeFromPtr(processedLoss.ClosedDate),
		CauseOfLossSummary:     null.StringFromPtr(processedLoss.CauseOfLossSummary),
		CauseOfLossDescription: null.StringFromPtr(processedLoss.CauseOfLossDesc),
		Vin:                    null.StringFromPtr(processedLoss.VIN),
		Type:                   null.StringFromPtr(processedLoss.Type),
		DriverID:               null.StringFromPtr(processedLoss.DriverID),
		Name:                   null.StringFromPtr(processedLoss.Name),
		Age:                    null.IntFromPtr(processedLoss.Age),
		Gender:                 null.StringFromPtr(processedLoss.Gender),
		Dob:                    null.TimeFromPtr(processedLoss.DOB),
		HiringDate:             null.TimeFromPtr(processedLoss.HiringDate),
		Street:                 null.StringFromPtr(processedLoss.Street),
		CityCounty:             null.StringFromPtr(processedLoss.CityCounty),
		State:                  null.StringFromPtr(processedLoss.State),
		Zip:                    null.StringFromPtr(processedLoss.Zip),
		LossLocation:           null.StringFromPtr(processedLoss.LossLocation),
		ClaimStatus:            null.StringFromPtr(processedLoss.ClaimStatus),
		Claimant:               null.StringFromPtr(processedLoss.Claimant),
		CoverageInferred:       null.StringFromPtr(coverageInferredStringPtr),
		ClaimLossType:          null.StringFromPtr(processedLoss.ClaimLossType),
		ClaimLossTypeInferred:  null.StringFromPtr(processedLoss.ClaimLossTypeInferred),
		Examiner:               null.StringFromPtr(processedLoss.Examiner),
		LossReserved:           null.Float64FromPtr(processedLoss.LossReserved),
		LossPaid:               null.Float64FromPtr(processedLoss.LossPaid),
		ExpenseReserve:         null.Float64FromPtr(processedLoss.ExpenseReserve),
		ExpensePaid:            null.Float64FromPtr(processedLoss.ExpensePaid),
		LossTotalReserve:       null.Float64FromPtr(processedLoss.LossTotalReserve),
		LossTotalPaid:          null.Float64FromPtr(processedLoss.LossTotalPaid),
		LossTotalRecovered:     null.Float64FromPtr(processedLoss.LossTotalRecovered),
		LossTotalIncurred:      processedLoss.LossTotalIncurred,
		DeductibleAmount:       null.Float64FromPtr(processedLoss.DeductibleAmount),
		SubrogationAmount:      null.Float64FromPtr(processedLoss.SubrogationAmount),
		SalvageAmount:          null.Float64FromPtr(processedLoss.SalvageAmount),
		OtherRecovery:          null.Float64FromPtr(processedLoss.OtherRecovery),
		LegalReserve:           null.Float64FromPtr(processedLoss.LegalReserve),
		LegalPaid:              null.Float64FromPtr(processedLoss.LegalPaid),
		LegalIncurred:          null.Float64FromPtr(processedLoss.LegalIncurred),
		AlaeExpenseReserve:     null.Float64FromPtr(processedLoss.AlaeExpenseReserve),
		AlaePaid:               null.Float64FromPtr(processedLoss.AlaePaid),
		AlaeIncurred:           null.Float64FromPtr(processedLoss.AlaeIncurred),
		OtherExpenseReserve:    null.Float64FromPtr(processedLoss.OtherExpenseReserve),
		OtherExpensePaid:       null.Float64FromPtr(processedLoss.OtherExpensePaid),
		OtherExpenseIncurred:   null.Float64FromPtr(processedLoss.OtherExpenseIncurred),
		ConfidenceInfo:         confidenceInfo,
		CreatedAt:              processedLoss.CreatedAt,
		Unmapped:               null.BoolFromPtr(processedLoss.Unmapped),
		UnmappedReasons:        unmappedReasons,
	}, nil
}

func generateNullPkgString(s *string) null.String {
	if s == nil {
		return null.NewString("", false)
	}
	return null.StringFrom(*s)
}
