load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "pibit",
    srcs = [
        "aggregationstatus_enumer.go",
        "confidencelevel_enumer.go",
        "fx.go",
        "helper.go",
        "interface.go",
        "lowconfidenceaggregationreason_enumer.go",
        "lowconfidencelossreason_enumer.go",
        "object_defs.go",
        "serde_utils.go",
        "unmappedlossreason_enumer.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/parsed_loss_runs",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@com_github_volatiletech_sqlboiler_v4//types",
        "@org_uber_go_fx//:fx",
    ],
)
