package pibit

import (
	"fmt"
	"time"

	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"

	"github.com/google/uuid"
)

type ParsedLossRun struct {
	ID uuid.UUID

	DocumentID uuid.UUID
	PibitData  ParsedLossRunResponse

	CreatedAt time.Time
}

// DocumentStatus denotes the status of parsing of a loss run document
type DocumentStatus int

const (
	// DocumentStatusSent indicates that the document has been sent to Pibit.Ai
	DocumentStatusSent DocumentStatus = iota
	// DocumentStatusReceived indicates that the parsed output has been received & persisted from Pibit.Ai
	DocumentStatusReceived
	// DocumentStatusErrored indicates that the errored output has been received & persisted from Pibit.Ai
	DocumentStatusErrored
	// DocumentStatusNoLossRuns indicates that Pibit.AI did not find any loss runs in the document.
	DocumentStatusNoLossRuns = 3
)

// DocumentStatusMap stores the string representations and their corresponding integer values
var DocumentStatusMap = map[string]DocumentStatus{
	"Sent":       DocumentStatusSent,
	"Received":   DocumentStatusReceived,
	"Errored":    DocumentStatusErrored,
	"NoLossRuns": DocumentStatusNoLossRuns,
}

// Overriding String() function for DocumentStatus enum type
func (d DocumentStatus) String() string {
	switch d {
	case DocumentStatusSent:
		return "Sent"
	case DocumentStatusReceived:
		return "Received"
	case DocumentStatusErrored:
		return "Errored"
	case DocumentStatusNoLossRuns:
		return "NoLossRuns"
	default:
		return fmt.Sprintf("%d", int(d))
	}
}

// DocumentErrorCode denotes the error code for a document output received by Pibit for a parsing error case
type DocumentErrorCode int

const (
	// ErrorInvalidFile indicates that the file was invalid (could be corrupt, password protected, etc)
	ErrorInvalidFile DocumentErrorCode = iota
	// ErrorUnparsableFile indicates that Pibit.Ai's OCR extraction is not able to work on loss run file
	ErrorUnparsableFile
	// ErrorNoRowsPresent indicates that the file can be read by Pibit.Ai, but no data is present
	ErrorNoRowsPresent
	// ErrorIncompleteFile indicates that loss summary was present in the loss run file
	// (and hence, policy data can be extracted), but no claim/loss rows was present
	ErrorIncompleteFile
)

// Overriding String() function for DocumentErrorCode enum type
func (d DocumentErrorCode) String() string {
	switch d {
	case ErrorInvalidFile:
		return "ERROR_INVALID_FILE"
	case ErrorUnparsableFile:
		return "ERROR_UNPARSABLE_FILE"
	case ErrorNoRowsPresent:
		return "ERROR_NO_ROWS_PRESENT"
	case ErrorIncompleteFile:
		return "ERROR_INCOMPLETE_FILE"
	default:
		return fmt.Sprintf("%d", int(d))
	}
}

// DocumentErrorCodeMap stores the string representations and their corresponding integer values
var DocumentErrorCodeMap = map[string]DocumentErrorCode{
	"ERROR_INVALID_FILE":    ErrorInvalidFile,
	"ERROR_UNPARSABLE_FILE": ErrorUnparsableFile,
	"ERROR_NO_ROWS_PRESENT": ErrorNoRowsPresent,
	"ERROR_INCOMPLETE_FILE": ErrorIncompleteFile,
}

// DocumentErrorInfo represents the error state of a loss run document (received from Pibit.Ai)
type DocumentErrorInfo struct {
	ErrorCode    *DocumentErrorCode
	ErrorMessage string
}

// Document represents state of a loss run file
type Document struct {
	ID             string
	Status         DocumentStatus
	CreatedAt      time.Time
	UpdatedAt      *time.Time
	SubmissionId   *string
	ErrorInfo      *DocumentErrorInfo
	FirstUpdatedAt *time.Time
	SummaryFileUrl *string
	RequestId      string
	// Add metadata field containing Indicators and SummaryFileType
}

// PolicyData represents (one of many) policy level data  in a loss run file
// 1 Policy is related to N Claim
type PolicyData struct {
	DocumentId           *string
	PolicySn             *int32
	Lob                  *string
	Insurer              *string
	Insured              *string
	Agent                *string
	PolicyNo             *string
	EffDate              *time.Time
	ExpDate              *time.Time
	ReportGenerationDate *time.Time
	CancelDate           *time.Time
	NoLoss               *int32
	TotalPaid            *float64
	TotalReserve         *float64
	TotalRecovered       *float64
	TotalIncurred        *float64
}

// ClaimData represents (one of many) claim level data in a loss run file
// 1 Claim is related to N Loss
type ClaimData struct {
	DocumentId             *string
	ClaimSn                *int32
	PolicySn               *int32
	ClaimID                *string
	OccurrenceID           string
	DateOfLoss             *time.Time
	DateReported           *time.Time
	TimeOfLoss             *time.Time
	ClosedDate             *time.Time
	CauseOfLossSummary     *string
	CauseOfLossDescription *string
	VIN                    *string
	Type                   *string
	DriverID               *string
	Name                   *string
	Age                    *int32
	Gender                 *string
	DOB                    *time.Time
	HiringDate             *time.Time
	Street                 *string
	CityCounty             *string
	State                  *string
	Zip                    *string
	LossLocation           *string
	ClaimStatus            *string
}

type CoverageInferred string

const (
	CoverageInferredAutoPhysicalDamage CoverageInferred = "Auto Physical Damage"
	CoverageInferredAutoLiability      CoverageInferred = "Auto Liability"
	CoverageInferredES                 CoverageInferred = "E&S"
	CoverageInferredInlandMarine       CoverageInferred = "Inland Marine"
	CoverageInferredOther              CoverageInferred = "Other"
	CoverageInferredMotorTruckCargo    CoverageInferred = "Motor Truck Cargo"
	CoverageInferredWorkerCompensation CoverageInferred = "Worker Compensation"
	CoverageInferredOtherCoverages     CoverageInferred = "Other Coverages"
	CoverageInferredGeneralLiability   CoverageInferred = "General Liability"
	CoverageInferredCommercialProperty CoverageInferred = "Commercial Property"
)

// LossData represents (one of many) loss level data in a loss run file
type LossData struct {
	DocumentId            *string
	ClaimSn               *int32
	PolicySn              *int32
	LossSeqID             *string
	Claimant              *string
	CoverageInferred      *CoverageInferred
	ClaimLossType         *string
	ClaimLossTypeInferred *string
	Examiner              *string
	LossReserved          *float64
	LossPaid              *float64
	ExpenseReserve        *float64
	ExpensePaid           *float64
	TotalReserve          *float64
	TotalPaid             *float64
	TotalRecovered        *float64
	TotalIncurred         *float64
	DeductibleAmount      *float64
	SubrogationAmount     *float64
	SalvageAmount         *float64
	OtherRecovery         *float64
	LegalReserve          *float64
	LegalPaid             *float64
	LegalIncurred         *float64
	AlaeExpenseReserve    *float64
	AlaePaid              *float64
	AlaeIncurred          *float64
	OtherExpenseReserve   *float64
	OtherExpensePaid      *float64
	OtherExpenseIncurred  *float64
}

type DocumentStateTransition struct {
	ID             string
	DocumentID     string
	FromStatus     DocumentStatus
	ToStatus       DocumentStatus
	TransitionTime time.Time
	TransitionedBy string
	RequestID      string
}

type ParsedLossRunResponse struct {
	Status                string
	RequestId             string
	DocumentId            string
	Data                  *ParsedLossRunData
	ExcelFileBase64String *string
	RequestMetadata       ParsedLossRunRequestMetadata
}

type ParsedLossRunData struct {
	Documents []ParsedLossRunDataDocument
}

type ParsedLossRunDataDocument struct {
	Filename string
	Tables   map[string]*ParsedLossRunDataDocumentTable
}

type ParsedLossRunDataDocumentTable struct {
	TableName string
	Headers   []string
	Rows      [][]string
}

type ParsedLossRunRequestMetadata struct {
	RequestId    string
	ErrorMessage string
	StatusCode   string
	Version      string
	ClientID     string
	Timestamp    string
}

// MappedDBObjects holds all mapped database objects.
type MappedDBObjects struct {
	Policies []PolicyData
	Claims   []ClaimData
	Losses   []LossData
}

type Aggregation struct {
	ID                  string
	ApplicationID       string
	ApplicationReviewID string
	Status              AggregationStatus
	ConfidenceInfo      AggregationConfidenceInfo
	CreatedAt           time.Time
	UpdatedAt           *time.Time
	Summary             *AggregationSummary
}

type AggregationSummary struct {
	PeriodSummary []AggregationCoveragePeriodSummary
}

type AggregationCoveragePeriodSummary struct {
	Coverage      app_enums.Coverage
	PeriodSummary []PeriodSummary
}

type PeriodSummary struct {
	PeriodStartDate   time.Time
	PeriodEndDate     time.Time
	MissingSubPeriods []MissingSubPeriod
	ClaimCount        int
	GrossLoss         float64
	NumberOfPUs       int32
}

type Period struct {
	FromDate time.Time
	ToDate   time.Time
}

type PeriodWithPUCount struct {
	Period             Period
	NumberOfPowerUnits int32
}

type MissingSubPeriod struct {
	Period              Period
	LargerThanThreshold bool
}

type ProcessedLoss struct {
	ID            string
	AggregationID string

	// Aggregation grouping
	PeriodStartDate *time.Time
	PeriodEndDate   *time.Time
	Coverage        *app_enums.Coverage

	// Policy fields
	PolicyNo             *string
	Lob                  *string
	Insurer              *string
	Insured              *string
	Agent                *string
	EffDate              *time.Time
	ExpDate              *time.Time
	ReportGenerationDate *time.Time
	CancelDate           *time.Time
	NoLoss               int
	PolicyTotalPaid      *float64
	PolicyTotalReserve   *float64
	PolicyTotalRecovered *float64
	PolicyTotalIncurred  float64

	// Claim fields
	ClaimID            *string
	OccurrenceID       string
	DateOfLoss         *time.Time
	DateReported       *time.Time
	TimeOfLoss         *time.Time
	ClosedDate         *time.Time
	CauseOfLossSummary *string
	CauseOfLossDesc    *string
	VIN                *string
	Type               *string
	DriverID           *string
	Name               *string
	Age                *int
	Gender             *string
	DOB                *time.Time
	HiringDate         *time.Time
	Street             *string
	CityCounty         *string
	State              *string
	Zip                *string
	LossLocation       *string
	ClaimStatus        *string

	// Loss fields
	Claimant              *string
	CoverageInferred      *CoverageInferred
	ClaimLossType         *string
	ClaimLossTypeInferred *string
	Examiner              *string
	LossReserved          *float64
	LossPaid              *float64
	ExpenseReserve        *float64
	ExpensePaid           *float64
	LossTotalReserve      *float64
	LossTotalPaid         *float64
	LossTotalRecovered    *float64
	LossTotalIncurred     float64
	DeductibleAmount      *float64
	SubrogationAmount     *float64
	SalvageAmount         *float64
	OtherRecovery         *float64
	LegalReserve          *float64
	LegalPaid             *float64
	LegalIncurred         *float64
	AlaeExpenseReserve    *float64
	AlaePaid              *float64
	AlaeIncurred          *float64
	OtherExpenseReserve   *float64
	OtherExpensePaid      *float64
	OtherExpenseIncurred  *float64

	// metadata
	ConfidenceInfo  LossConfidenceInfo
	CreatedAt       time.Time
	Unmapped        *bool
	UnmappedReasons []UnmappedLossReason
}

type LossConfidenceInfo struct {
	Level                ConfidenceLevel
	LowConfidenceReasons []LowConfidenceLossReason
}

type AggregationConfidenceInfo struct {
	Level                          ConfidenceLevel
	LowConfidenceAggregationReason []LowConfidenceAggregationReason
}

//go:generate go run github.com/dmarkham/enumer -type=ConfidenceLevel -json
type ConfidenceLevel int

const (
	ConfidenceLevelHigh ConfidenceLevel = iota
	ConfidenceLevelLow
)

//go:generate go run github.com/dmarkham/enumer -type=AggregationStatus -json
type AggregationStatus int

const (
	AggregationStatusActive AggregationStatus = iota
	AggregationStatusStale
)

//go:generate go run github.com/dmarkham/enumer -type=LowConfidenceLossReason -json
type LowConfidenceLossReason int

const (
	LowConfidenceLossReasonMissingDateOfLoss LowConfidenceLossReason = iota
	LowConfidenceLossReasonMissingPolicyNo
	LowConfidenceLossReasonMissingClaimId
	LowConfidenceLossReasonMissingClaimLossType
)

//go:generate go run github.com/dmarkham/enumer -type=LowConfidenceAggregationReason -json
type LowConfidenceAggregationReason int

const (
	LowConfidenceAggregationReasonMissingPolicyPeriods LowConfidenceAggregationReason = iota
	LowConfidenceAggregationReasonLowConfidenceLosses
	LowConfidenceAggregationReasonUnaggregatedNonDuplicateLosses
)

//go:generate go run github.com/dmarkham/enumer -type=UnmappedLossReason -json
type UnmappedLossReason int

const (
	UnmappedLossReasonMissingCoverage UnmappedLossReason = iota
	UnmappedLossReasonMissingDateOfLoss
	UnmappedLossReasonOutsideAggregationPeriods
	UnmappedLossReasonDuplicateLoss
	UnmappedLossReasonUnsupportedCoverage
)

func (c ParsedLossRun) ResourcePaths() []string {
	return []string{}
}
