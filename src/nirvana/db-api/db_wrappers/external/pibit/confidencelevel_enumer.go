// Code generated by "enumer -type=ConfidenceLevel -json"; DO NOT EDIT.

package pibit

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _ConfidenceLevelName = "ConfidenceLevelHighConfidenceLevelLow"

var _ConfidenceLevelIndex = [...]uint8{0, 19, 37}

const _ConfidenceLevelLowerName = "confidencelevelhighconfidencelevellow"

func (i ConfidenceLevel) String() string {
	if i < 0 || i >= ConfidenceLevel(len(_ConfidenceLevelIndex)-1) {
		return fmt.Sprintf("ConfidenceLevel(%d)", i)
	}
	return _ConfidenceLevelName[_ConfidenceLevelIndex[i]:_ConfidenceLevelIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _ConfidenceLevelNoOp() {
	var x [1]struct{}
	_ = x[ConfidenceLevelHigh-(0)]
	_ = x[ConfidenceLevelLow-(1)]
}

var _ConfidenceLevelValues = []ConfidenceLevel{ConfidenceLevelHigh, ConfidenceLevelLow}

var _ConfidenceLevelNameToValueMap = map[string]ConfidenceLevel{
	_ConfidenceLevelName[0:19]:       ConfidenceLevelHigh,
	_ConfidenceLevelLowerName[0:19]:  ConfidenceLevelHigh,
	_ConfidenceLevelName[19:37]:      ConfidenceLevelLow,
	_ConfidenceLevelLowerName[19:37]: ConfidenceLevelLow,
}

var _ConfidenceLevelNames = []string{
	_ConfidenceLevelName[0:19],
	_ConfidenceLevelName[19:37],
}

// ConfidenceLevelString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func ConfidenceLevelString(s string) (ConfidenceLevel, error) {
	if val, ok := _ConfidenceLevelNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _ConfidenceLevelNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to ConfidenceLevel values", s)
}

// ConfidenceLevelValues returns all values of the enum
func ConfidenceLevelValues() []ConfidenceLevel {
	return _ConfidenceLevelValues
}

// ConfidenceLevelStrings returns a slice of all String values of the enum
func ConfidenceLevelStrings() []string {
	strs := make([]string, len(_ConfidenceLevelNames))
	copy(strs, _ConfidenceLevelNames)
	return strs
}

// IsAConfidenceLevel returns "true" if the value is listed in the enum definition. "false" otherwise
func (i ConfidenceLevel) IsAConfidenceLevel() bool {
	for _, v := range _ConfidenceLevelValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for ConfidenceLevel
func (i ConfidenceLevel) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for ConfidenceLevel
func (i *ConfidenceLevel) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("ConfidenceLevel should be a string, got %s", data)
	}

	var err error
	*i, err = ConfidenceLevelString(s)
	return err
}
