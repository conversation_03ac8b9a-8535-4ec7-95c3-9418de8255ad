// Code generated by "enumer -type=LowConfidenceAggregationReason -json"; DO NOT EDIT.

package pibit

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _LowConfidenceAggregationReasonName = "LowConfidenceAggregationReasonMissingPolicyPeriodsLowConfidenceAggregationReasonLowConfidenceLossesLowConfidenceAggregationReasonUnaggregatedNonDuplicateLosses"

var _LowConfidenceAggregationReasonIndex = [...]uint8{0, 50, 99, 159}

const _LowConfidenceAggregationReasonLowerName = "lowconfidenceaggregationreasonmissingpolicyperiodslowconfidenceaggregationreasonlowconfidencelosseslowconfidenceaggregationreasonunaggregatednonduplicatelosses"

func (i LowConfidenceAggregationReason) String() string {
	if i < 0 || i >= LowConfidenceAggregationReason(len(_LowConfidenceAggregationReasonIndex)-1) {
		return fmt.Sprintf("LowConfidenceAggregationReason(%d)", i)
	}
	return _LowConfidenceAggregationReasonName[_LowConfidenceAggregationReasonIndex[i]:_LowConfidenceAggregationReasonIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _LowConfidenceAggregationReasonNoOp() {
	var x [1]struct{}
	_ = x[LowConfidenceAggregationReasonMissingPolicyPeriods-(0)]
	_ = x[LowConfidenceAggregationReasonLowConfidenceLosses-(1)]
	_ = x[LowConfidenceAggregationReasonUnaggregatedNonDuplicateLosses-(2)]
}

var _LowConfidenceAggregationReasonValues = []LowConfidenceAggregationReason{LowConfidenceAggregationReasonMissingPolicyPeriods, LowConfidenceAggregationReasonLowConfidenceLosses, LowConfidenceAggregationReasonUnaggregatedNonDuplicateLosses}

var _LowConfidenceAggregationReasonNameToValueMap = map[string]LowConfidenceAggregationReason{
	_LowConfidenceAggregationReasonName[0:50]:        LowConfidenceAggregationReasonMissingPolicyPeriods,
	_LowConfidenceAggregationReasonLowerName[0:50]:   LowConfidenceAggregationReasonMissingPolicyPeriods,
	_LowConfidenceAggregationReasonName[50:99]:       LowConfidenceAggregationReasonLowConfidenceLosses,
	_LowConfidenceAggregationReasonLowerName[50:99]:  LowConfidenceAggregationReasonLowConfidenceLosses,
	_LowConfidenceAggregationReasonName[99:159]:      LowConfidenceAggregationReasonUnaggregatedNonDuplicateLosses,
	_LowConfidenceAggregationReasonLowerName[99:159]: LowConfidenceAggregationReasonUnaggregatedNonDuplicateLosses,
}

var _LowConfidenceAggregationReasonNames = []string{
	_LowConfidenceAggregationReasonName[0:50],
	_LowConfidenceAggregationReasonName[50:99],
	_LowConfidenceAggregationReasonName[99:159],
}

// LowConfidenceAggregationReasonString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func LowConfidenceAggregationReasonString(s string) (LowConfidenceAggregationReason, error) {
	if val, ok := _LowConfidenceAggregationReasonNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _LowConfidenceAggregationReasonNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to LowConfidenceAggregationReason values", s)
}

// LowConfidenceAggregationReasonValues returns all values of the enum
func LowConfidenceAggregationReasonValues() []LowConfidenceAggregationReason {
	return _LowConfidenceAggregationReasonValues
}

// LowConfidenceAggregationReasonStrings returns a slice of all String values of the enum
func LowConfidenceAggregationReasonStrings() []string {
	strs := make([]string, len(_LowConfidenceAggregationReasonNames))
	copy(strs, _LowConfidenceAggregationReasonNames)
	return strs
}

// IsALowConfidenceAggregationReason returns "true" if the value is listed in the enum definition. "false" otherwise
func (i LowConfidenceAggregationReason) IsALowConfidenceAggregationReason() bool {
	for _, v := range _LowConfidenceAggregationReasonValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for LowConfidenceAggregationReason
func (i LowConfidenceAggregationReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for LowConfidenceAggregationReason
func (i *LowConfidenceAggregationReason) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("LowConfidenceAggregationReason should be a string, got %s", data)
	}

	var err error
	*i, err = LowConfidenceAggregationReasonString(s)
	return err
}
