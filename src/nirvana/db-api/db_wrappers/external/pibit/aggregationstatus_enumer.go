// Code generated by "enumer -type=AggregationStatus -json"; DO NOT EDIT.

package pibit

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _AggregationStatusName = "AggregationStatusActiveAggregationStatusStale"

var _AggregationStatusIndex = [...]uint8{0, 23, 45}

const _AggregationStatusLowerName = "aggregationstatusactiveaggregationstatusstale"

func (i AggregationStatus) String() string {
	if i < 0 || i >= AggregationStatus(len(_AggregationStatusIndex)-1) {
		return fmt.Sprintf("AggregationStatus(%d)", i)
	}
	return _AggregationStatusName[_AggregationStatusIndex[i]:_AggregationStatusIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _AggregationStatusNoOp() {
	var x [1]struct{}
	_ = x[AggregationStatusActive-(0)]
	_ = x[AggregationStatusStale-(1)]
}

var _AggregationStatusValues = []AggregationStatus{AggregationStatusActive, AggregationStatusStale}

var _AggregationStatusNameToValueMap = map[string]AggregationStatus{
	_AggregationStatusName[0:23]:       AggregationStatusActive,
	_AggregationStatusLowerName[0:23]:  AggregationStatusActive,
	_AggregationStatusName[23:45]:      AggregationStatusStale,
	_AggregationStatusLowerName[23:45]: AggregationStatusStale,
}

var _AggregationStatusNames = []string{
	_AggregationStatusName[0:23],
	_AggregationStatusName[23:45],
}

// AggregationStatusString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func AggregationStatusString(s string) (AggregationStatus, error) {
	if val, ok := _AggregationStatusNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _AggregationStatusNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to AggregationStatus values", s)
}

// AggregationStatusValues returns all values of the enum
func AggregationStatusValues() []AggregationStatus {
	return _AggregationStatusValues
}

// AggregationStatusStrings returns a slice of all String values of the enum
func AggregationStatusStrings() []string {
	strs := make([]string, len(_AggregationStatusNames))
	copy(strs, _AggregationStatusNames)
	return strs
}

// IsAAggregationStatus returns "true" if the value is listed in the enum definition. "false" otherwise
func (i AggregationStatus) IsAAggregationStatus() bool {
	for _, v := range _AggregationStatusValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for AggregationStatus
func (i AggregationStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for AggregationStatus
func (i *AggregationStatus) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("AggregationStatus should be a string, got %s", data)
	}

	var err error
	*i, err = AggregationStatusString(s)
	return err
}
