// Code generated by "enumer -type=LowConfidenceLossReason -json"; DO NOT EDIT.

package pibit

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _LowConfidenceLossReasonName = "LowConfidenceLossReasonMissingDateOfLossLowConfidenceLossReasonMissingPolicyNoLowConfidenceLossReasonMissingClaimIdLowConfidenceLossReasonMissingClaimLossType"

var _LowConfidenceLossReasonIndex = [...]uint8{0, 40, 78, 115, 158}

const _LowConfidenceLossReasonLowerName = "lowconfidencelossreasonmissingdateoflosslowconfidencelossreasonmissingpolicynolowconfidencelossreasonmissingclaimidlowconfidencelossreasonmissingclaimlosstype"

func (i LowConfidenceLossReason) String() string {
	if i < 0 || i >= LowConfidenceLossReason(len(_LowConfidenceLossReasonIndex)-1) {
		return fmt.Sprintf("LowConfidenceLossReason(%d)", i)
	}
	return _LowConfidenceLossReasonName[_LowConfidenceLossReasonIndex[i]:_LowConfidenceLossReasonIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _LowConfidenceLossReasonNoOp() {
	var x [1]struct{}
	_ = x[LowConfidenceLossReasonMissingDateOfLoss-(0)]
	_ = x[LowConfidenceLossReasonMissingPolicyNo-(1)]
	_ = x[LowConfidenceLossReasonMissingClaimId-(2)]
	_ = x[LowConfidenceLossReasonMissingClaimLossType-(3)]
}

var _LowConfidenceLossReasonValues = []LowConfidenceLossReason{LowConfidenceLossReasonMissingDateOfLoss, LowConfidenceLossReasonMissingPolicyNo, LowConfidenceLossReasonMissingClaimId, LowConfidenceLossReasonMissingClaimLossType}

var _LowConfidenceLossReasonNameToValueMap = map[string]LowConfidenceLossReason{
	_LowConfidenceLossReasonName[0:40]:         LowConfidenceLossReasonMissingDateOfLoss,
	_LowConfidenceLossReasonLowerName[0:40]:    LowConfidenceLossReasonMissingDateOfLoss,
	_LowConfidenceLossReasonName[40:78]:        LowConfidenceLossReasonMissingPolicyNo,
	_LowConfidenceLossReasonLowerName[40:78]:   LowConfidenceLossReasonMissingPolicyNo,
	_LowConfidenceLossReasonName[78:115]:       LowConfidenceLossReasonMissingClaimId,
	_LowConfidenceLossReasonLowerName[78:115]:  LowConfidenceLossReasonMissingClaimId,
	_LowConfidenceLossReasonName[115:158]:      LowConfidenceLossReasonMissingClaimLossType,
	_LowConfidenceLossReasonLowerName[115:158]: LowConfidenceLossReasonMissingClaimLossType,
}

var _LowConfidenceLossReasonNames = []string{
	_LowConfidenceLossReasonName[0:40],
	_LowConfidenceLossReasonName[40:78],
	_LowConfidenceLossReasonName[78:115],
	_LowConfidenceLossReasonName[115:158],
}

// LowConfidenceLossReasonString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func LowConfidenceLossReasonString(s string) (LowConfidenceLossReason, error) {
	if val, ok := _LowConfidenceLossReasonNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _LowConfidenceLossReasonNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to LowConfidenceLossReason values", s)
}

// LowConfidenceLossReasonValues returns all values of the enum
func LowConfidenceLossReasonValues() []LowConfidenceLossReason {
	return _LowConfidenceLossReasonValues
}

// LowConfidenceLossReasonStrings returns a slice of all String values of the enum
func LowConfidenceLossReasonStrings() []string {
	strs := make([]string, len(_LowConfidenceLossReasonNames))
	copy(strs, _LowConfidenceLossReasonNames)
	return strs
}

// IsALowConfidenceLossReason returns "true" if the value is listed in the enum definition. "false" otherwise
func (i LowConfidenceLossReason) IsALowConfidenceLossReason() bool {
	for _, v := range _LowConfidenceLossReasonValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for LowConfidenceLossReason
func (i LowConfidenceLossReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for LowConfidenceLossReason
func (i *LowConfidenceLossReason) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("LowConfidenceLossReason should be a string, got %s", data)
	}

	var err error
	*i, err = LowConfidenceLossReasonString(s)
	return err
}
