package pibit

import (
	"fmt"
)

func ParseCoverageInferred(s string) (CoverageInferred, error) {
	switch s {
	case string(CoverageInferredAutoPhysicalDamage),
		string(CoverageInferredAutoLiability),
		string(CoverageInferredES),
		string(CoverageInferredInlandMarine),
		string(CoverageInferredOther),
		string(CoverageInferredMotorTruckCargo),
		string(CoverageInferredWorkerCompensation),
		string(CoverageInferredOtherCoverages),
		string(CoverageInferredGeneralLiability),
		string(CoverageInferredCommercialProperty):
		return CoverageInferred(s), nil
	default:
		return "", fmt.Errorf("invalid CoverageInferred: %s", s)
	}
}

func (c CoverageInferred) String() string {
	return string(c)
}
