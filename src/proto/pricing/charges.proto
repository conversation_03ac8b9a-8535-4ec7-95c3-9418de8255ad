syntax = "proto3";
package pricing;

option go_package = "nirvanatech.com/nirvana/rating/pricing/api/ptypes";

import "google/protobuf/timestamp.proto";
import "common/time.proto";
import "pricing/sub_coverages.proto";

// Charges hold the information necessary to determine how much
// to charge for a given rate basis value (which in turn depends
// on the Charge's date or period). See the doc for the Calculate method.
//
// A charge has 3 main dimensions: billing details, type and charged
// item. Each dimension can have multiple values (see proto below for the
// possible values of each dimension).
//
// In theory any combination of values is possible. However, in practice,
// we currently only have the following combinations:
//	- For NF:
//	    - AmountBasedBillingDetails + BaseCharge (with no extra info) + ChargeableSubCoverageGroup
//	    - AmountBasedBillingDetails + BaseCharge (with BlanketAdditionalInsuredInfo) + + ChargeablePolicy
//	    - AmountBasedBillingDetails + BaseCharge (with BlanketWaiverOfSubrogationInfo) + ChargeablePolicy
//	    - AmountBasedBillingDetails + BaseCharge (with SpecifiedAdditionalInsuredInfo) + ChargeablePolicy
//	    - AmountBasedBillingDetails + BaseCharge (with SpecifiedThirdPartyWithWaiverOfSubrogationInfo) + ChargeablePolicy
//	    - AmountBasedBillingDetails + Surcharge + ChargeablePolicy
//  - For Fleet: not implemented yet.
//
// Note that each value for these dimensions is a message. Each message
// has all the fields relevant to achieve the ultimate goal which is to
// calculate the final premium to be charged (e.g. charges with rate-based
// billing details have a rate, while charges with amount-based billing
// details have a premium). They can also hold data relevant for attribution.
//
// Other relevant principles:
//	1. Pricing does not know how sub-coverages are grouped into a coverage
//   	 (this is left to upper layers, which are closer to the end user).
//     Pricing does know though how to map sub-coverages to policies and
//     vice versa (through the request). Pricing doesn't own this
//     mapping logic, it just uses it.
//	2. Charges can be refundable or not, but that information is not stored inside the charge.
//	   We have a separate utility component that for a given charge offers that information
//	3. Charges can also have distributions, which gives a deeper understanding to consumers
//     about where does the charge come from (e.g. at the vehicle level).
//	4. What we called "flat charge" in the past is now a "base charge with amount-based billing
//		 details" with "fully earned charges details" inside the base charge.
message Charge {
	reserved 1;

	repeated Distribution distributions = 2;

	// In the future, we can have other "billing details" for charges.
	// "BillingDetails" defines the way the amount to be charged is calculated.
	oneof billingDetails {
		AmountBasedBillingDetails amountBasedBillingDetails = 3;
		RateBasedBillingDetails rateBasedBillingDetails = 4;
	}

	// In the future, we can have other "types" of charges.
	//
	// Base charges are all charges that Nirvana decides to charge
	// for itself. For instance, due to a sub-coverage or a fully
	// earned charge associated to an additional insured.
	//
	// Surcharges are all charges that Nirvana has to charge due to
	// regulatory reasons (e.g. taxes). Nirvana doesn't control this,
	// but is forced to charge them. The DOI of each state sets these
	// surcharges and the logic behind them.
	//
	// Manual endorsement charges are charges that are neither base charges
	// nor surcharges, but represent a combination of both due to lack of
	// granularity. These are policy-level charges that are applied  during
	// endorsements as charges with amount-based billing details.
	oneof type {
		BaseCharge baseCharge = 5;
		Surcharge surcharge = 6;
		ManualEndorsementCharge manualEndorsementCharge = 9;
	}

	// In the future we might need to add other types of chargeable items.
	// For instance, a cross-policy chargeable item (i.e. a charge that
	// applies to a group of policies, a.k.a. bundle-level charge).
	oneof chargedItem {
		ChargeableSubCoverageGroup chargedSubCoverageGroup = 7;
		ChargeablePolicy chargedPolicy = 8;
	}

	message Distribution {
		DistributionType type = 1;
		repeated DistributionItem items = 2;
	}

	enum DistributionType {
		DistributionType_Unspecified = 0;
		DistributionType_Vehicle = 1;
	}

	message DistributionItem {
		string id = 1;

		// Needs to be a decimal between 0 and 1.
		// The last item' fraction will not be considered,
		// and instead, the remaining percentage amount that wasn't
		// allocated to previous items will be used.
		// The fraction is returned with a precision defined by a
		// a constant in the code (originally it was 4 decimal places).
		string fraction = 2;
	}
}

message AmountBasedBillingDetails {
	// This field indicates on which date should the charge be charged.
	google.protobuf.Timestamp date = 1;

	// We store the dollar amount as a string in order to avoid losing
	// precision. This will be returned as a decimal.Decimal to the
	// consumer when calculating the final amount that it needs to charge.
	string amount = 2;

	// This is optional as only certain models, of certain programs,
	// include this information. E.g. newer models in Fleet.
	optional string unmodifiedAmount = 3;
}

message RateBasedBillingDetails {
	// Dates is the interval for which Billing calculates the rate
	// basis value to use with the charge's rate.
	common.Interval dates = 1;

	// Rate is the dollar amount to charge per unit of rate basis.
	// We store it as a string in order to avoid losing precision.
	// Calculation are done with decimal.Decimal.
	string rate = 2;

	// This is optional as only certain models, of certain programs,
	// include this information. E.g. newer models in Fleet.
	optional string unmodifiedRate = 3;

	RateBasis rateBasis = 4;
}

// Values in this enum should map 1-1 to the possible messages
// in the RateBasisValue `oneof` field.
// Note: for AmountBasedBillingDetails this field does not exist,
// because they don't use a rate to calculate the amount
// to be charged (see doc for the Calculate method).
enum RateBasis {
	RateBasis_Unspecified = 0;
	RateBasis_Miles = 1;
	RateBasis_TIV_Days = 2; // TIV x days
	RateBasis_Premium = 3; // Dollar amount
}

message RateBasisValue {
	oneof value {
		int64 miles = 1;
		int64 tivDays = 2; // TIV x days
		int64 premium = 3; // Dollar amount
	}
}

message BaseCharge {
	oneof extraInfo {
		BlanketRegularAdditionalInsuredInfo blanketRegularAdditionalInsuredInfo = 1;
		BlanketPrimaryAndNonContributoryAdditionalInsuredInfo blanketPrimaryAndNonContributoryAdditionalInsuredInfo = 2;
		BlanketWaiverOfSubrogationInfo blanketWaiverOfSubrogationInfo = 3;

		SpecifiedRegularAdditionalInsuredInfo specifiedRegularAdditionalInsuredInfo = 4;
		SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo specifiedPrimaryAndNonContributoryAdditionalInsuredInfo = 5;
		SpecifiedThirdPartyWithWaiverOfSubrogationInfo specifiedThirdPartyWithWaiverOfSubrogationInfo = 6;
	}
}

/*
[ABOUT BLANKET/SPECIFIED AI AND WOS]

First, let's start by explaining the concepts of AI and WOS:
	1. AI (Additional Insured): say we have a trucking company called A that delegates some work to company B.
	Company A can buy one of our policies and state that it wants B to be covered in the same way that A would.
	This only applies while B is performing activities for A. In this example B would be the "additional insured".
	There's two types of AI: "Regular" and "Primary Non-Contributory". The former means that we cover B after
	other insurances that B might have have reached their limits, while the other means that we are the first
	insurance to cover B. This is why PNC is more expensive than Regular AI.

	2. WOS (Waiver of Subrogation): this is an agreement that forbids us (the insurer) to pursue compensation
	from a third party. Following the example shared in (1), say that B loses some cargo while operating. We would
	have to cover for that, because B was added as additional insured in A's policy. Nonetheless, we could also go
	after B and make them pay for their mistake. This would not be possible for us if A added a WOS for B.

Note though that a WOS could be added for any third party, not just an AI. For instance, let's say that A has a
partnership with a company C that does the trucks maintenance. It doesn't make sense to add C as an additional
insured, but it might make sense to add a WOS for C. That WOS would mean that Nirvana can't pursue compensation
from C even if we determine that the accident experienced by A was due to an improper maintenance performed by C.
Why would this make sense for A? Say that C gives A a special price for maintenance, but only if A promises that
neither A nor A's insurance will sue them for any mistakes they can make. Also, from Nirvana's perspective, it
might also make sense because we are being payed an extra for the WOS piece. This will depend on how much more are
we getting from it, versus how reputable is C and how often do they make mistakes.

Second, let's explain what "blanket" and "specified" mean.
	1. "Blanket" essentially means "cover for anyone or everyone".
	2. "Specified" means "cover for a specific entity that needs to appear in the policy".

Note that these terms are orthogonal to AI and WOS. In other words, you can have a blanket AI, a specified AI, a
blanket WOS, and a specified WOS.

In the previous example, A could have added B as a specified AI or third party with WOS or it could have purchased
from us a blanket AI or WOS. The difference is that the blanket AI/WOS would apply on any other companies, not just B.

When does it make sense for the customer to buy blanket vs specified? Currently we charge $20 for a specified AI, 
$25 for a specified WOS,and $75 for a blanket AI/WOS. If the customer is going to add more than 3 entities, it's
better for them to buy the blanket.
 */

message BlanketRegularAdditionalInsuredInfo {}

message BlanketPrimaryAndNonContributoryAdditionalInsuredInfo {}

message BlanketWaiverOfSubrogationInfo {}

message SpecifiedRegularAdditionalInsuredInfo {
	string additionalInsuredId = 1;
}

message SpecifiedPrimaryAndNonContributoryAdditionalInsuredInfo {
	string additionalInsuredId = 1;
}

message SpecifiedThirdPartyWithWaiverOfSubrogationInfo {
	string thirdPartyId = 2;
}

message Surcharge {
	Type type = 1;

	/*
	TODO:
		consider refactoring this into a oneof with a separate messages, such
		that the Stamping Fee and Surplus Tax messages can have an extra field
		to indicate whether they come from a regular charge or a fee charge).

		Note: we will now start writing into Business Auto tables, which means
		that any refactor also needs to include migrating/backfilling these tables
		as well (on top of NF's tables).
	*/
	enum Type {
		Type_Unspecified = 0;
		Type_NCRF = 1; // North Carolina Reinsurance Facility (NC state only)
		Type_MCCA = 2; // Michigan Catastrophic Claims Association (MI state only)

		/*
		Better names for these ones could be:
			- Type_SURPLUS_LINES_TAX_FROM_REFUNDABLE_PREMIUM
			- Type_SURPLUS_LINES_TAX_FROM_NON_REFUNDABLE_PREMIUM
			- Type_SURPLUS_STAMPING_FEE_FROM_REFUNDABLE_PREMIUM
			- Type_SURPLUS_STAMPING_FEE_FROM_NON_REFUNDABLE_PREMIUM

		Or we could also modify this by a boolean field inside the Surcharge object.
		E.g. "fromRefundablePremium". We still haven't decided how to model this yet.
		*/
		Type_STAMPING_FEE = 3; // Only relevant for Non-admitted filings (multiple states)
		Type_SURPLUS_TAX = 4; // Only relevant for Non-admitted filings (multiple states)
		Type_FEE_CHARGE_STAMPING_FEE = 5; // Only relevant for Non-admitted filings (multiple states). Note that the term "Fee Charge" is outdates. Should be named "Fully Earned".
		Type_FEE_CHARGE_SURPLUS_TAX = 6; // Only relevant for Non-admitted filings (multiple states). Note that the term "Fee Charge" is outdates. Should be named "Fully Earned".
	}
}

// ManualEndorsementCharge represents charges that are applied during endorsements
// and are neither pure base charges nor pure surcharges, but a combination of both
// due to lack of granularity. These charges are typically applied at the policy level
// as amount-based charges.
// This is a temporary charge type which has been introduced to enable manual pricing
// of fleet endorsements. Once the new fleet pricing API is integrated and fleet
// endorsement pricing is automated, this charge type will be removed.
message ManualEndorsementCharge {}

message ChargeableSubCoverageGroup {
	pricing.SubCoverageGroup group = 1;
}

message ChargeablePolicy {
	string policyNumber = 1;
}
