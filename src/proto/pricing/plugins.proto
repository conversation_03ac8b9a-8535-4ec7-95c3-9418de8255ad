syntax = "proto3";
package pricing;

option go_package = "nirvanatech.com/nirvana/rating/pricing/api/ptypes";

// When storing plugins, store them by ID, not number.
enum PluginID {
    PluginID_Unspecified = 0;
    PluginID_LossHistoryExperiment_V1 = 1;
    PluginID_DriverEndorsementRule_V1 = 2;
    PluginID_RateMLArtifactUpload_V1 = 3;
    PluginID_MetricsReporting_V1 = 4;
    PluginID_FullyEarnedChargesDedupRule_V1 = 5;
    PluginID_MaxFullyEarnedChargesRule_V1 = 6;
    PluginID_MCCASurchargeDedupRule_V1 = 7;
    PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1 = 8;
    PluginID_CollRequiresCompValidation_V1 = 9;
    PluginID_SamePDCombinationForAllVehiclesValidation_V1 = 10;
    PluginID_UMPDRestrictedToPPTVehiclesValidation_V1 = 11;
    PluginID_CollAndUMPDMutualExclusivityValidation_V1 = 12;
    PluginID_TowingRestrictedToCertainVehiclesValidation_V1 = 13;
    PluginID_NoSpecifiedXModifierValidation_V1 = 14;
    PluginID_HiredAutoPDRequiresCompValidation_V1 = 15;
    PluginID_UMBIIsRequiredIfLiabIsPresentValidation_V1 = 16;
    PluginID_TowingRequiresPDValidation_V1 = 17;
    PluginID_RentalRequiresPDValidation_V1 = 18;
    PluginID_MedPayRequiresLiabValidation_V1 = 19;
    PluginID_UMBIRequiresLiabValidation_V1 = 20;
    PluginID_UMPDRequiresLiabValidation_V1 = 21;
    PluginID_UIMBIRequiresLiabValidation_V1 = 22;
    PluginID_HiredAutoLiabRequiresLiabValidation_V1 = 23;
    PluginID_HiredAutoPDRequiresLiabValidation_V1 = 24;
    PluginID_NonOwnedVehicleRequiresLiabValidation_V1 = 25;
    PluginID_BIAndPDRequireJointSelectionValidation_V1 = 26;
    PluginID_OnlyBAPolicyIsSupportedValidation_V1 = 27;
    PluginID_UMPDRequiresUMBIValidation_V1 = 28;
    PluginID_SamePerOccurrenceLimitForUMBIAndUIMBIValidation_V1 = 29;
    PluginID_ChargesProration_V1 = 30;
    PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1 = 31;
    PluginID_MedicalExpenseBenefitsRequiresLiabValidation_V1 = 32;
    PluginID_PIPRequiresLiabValidation_V1 = 33;
    PluginID_UMUIMRequiresLiabValidation_V1 = 34;
    PluginID_MedPayAndPIPMutualExclusivityValidation_V1 = 35;

    PluginID_MockPlugin_V1 = 1000;
    PluginID_MockPlugin_V2 = 1001;
    PluginID_MockPlugin_V3 = 1002;
    PluginID_MockPlugin_V4 = 1003;
    PluginID_MockPlugin_V5 = 1004;
}

message PluginsMetadata {
    // This field will only be present for policies that have the corresponding
    // plugin present.
    //
    // Furthermore, within the policy, it will only apply to chunks with a change
    // in the drivers list, as the rule is only evaluated in this scenario.
    optional DriverEndorsementRuleV1Metadata driverEndorsementRuleV1Metadata = 1;
}

message DriverEndorsementRuleV1Metadata {
    // The percentage which triggers the rule.
    // Shared as a decimal between 0 and 1 (e.g. 20% is represented as 0.2).
    double threshold = 1;

    // The base premium is the premium obtained from rating the chunk
    // with the last rated drivers.
    double baseSubTotalPremium = 2;

    // The proposed premium is the premium obtained from rating the chunk with
    // its current drivers.
    double proposedSubTotalPremium = 3;
}
