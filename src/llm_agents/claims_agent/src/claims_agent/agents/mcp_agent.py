"""MCP Agent implementation.

This module provides the core implementation of the MCPAgent, an AI agent that
interacts with MCP (Model-Context-Protocol) servers to perform tasks. It uses a
dependency injection pattern to receive its necessary components, such as the
MCP client manager, tool service, and agent executor factory.
"""

import contextlib
import time
from collections.abc import AsyncIterator
from datetime import datetime
from typing import Any

import httpx
from langchain.agents import (
    AgentExecutor,
)
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from loguru import logger

from claims_agent.agents.agent_executor_factory import AgentExecutorFactory
from claims_agent.agents.agent_logging_utils import (
    log_intermediate_steps,
    trace_tools_execution,
)
from claims_agent.interfaces.agents import (
    AgentProtocol,
    ProcessPromptResponse,
)
from claims_agent.agents.mcp_client_manager import (
    MCPClientConfigurationError,
    MCPClientManager,
    ToolFetchingError,
)
from claims_agent.agents.mcp_tool_service import MCPToolService
from claims_agent.agents.prompts import (
    create_agent_prompt_template,
    create_refinement_agent_prompt_template,
    get_coverage_determination_prompt,
    get_language_consistency_refinement_prompt,
)
from claims_agent.agents.response_parser import (
    ResponseParsingError,
    parse_coverage_determination_response,
)
from claims_agent.api.errors import AuthenticationError, DataExtractionError
from claims_agent.instrumentation.helpers import (
    trace_async_function,
    trace_span_async,
)
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
)


class MCPAgent(AgentProtocol):
    """MCP-capable AI Agent implementation.

    This class orchestrates the agent's operations. It is responsible for:
    - Managing the lifecycle of MCP clients via `MCPClientManager`.
    - Dynamically fetching MCP tools for the agent to use via `MCPToolService`.
    - Creating a LangChain `AgentExecutor` using `AgentExecutorFactory`.
    - Invoking the agent with a formatted prompt to perform tasks.
    - Parsing the structured response from the agent.

    This class is designed to be instantiated and managed by a dependency
    injection container, which provides its dependencies.
    """

    def __init__(
        self,
        mcp_client_manager: MCPClientManager,
        mcp_tool_service: MCPToolService,
        agent_executor_factory: AgentExecutorFactory,
    ) -> None:
        """Initialize the MCP Agent.

        Args:
            mcp_client_manager: Pre-configured MCPClientManager instance.
            mcp_tool_service: Pre-configured MCPToolService instance.
            agent_executor_factory: Pre-configured factory for creating agent executors.
        """
        self.mcp_client_manager = mcp_client_manager
        logger.info("MCPAgent using provided MCPClientManager instance.")

        self.agent_executor_factory = agent_executor_factory
        logger.info("MCPAgent using provided AgentExecutorFactory instance.")

        self.mcp_tool_service = mcp_tool_service
        logger.info("MCPAgent using provided MCPToolService instance.")

    @trace_async_function(name="MCPAgent._create_agent_executor_with_active_client")
    async def _create_agent_executor_with_active_client(
        self, mcp_client: Any, tools: list[Any]
    ) -> AgentExecutor:
        """Create the LangChain agent executor, assuming tools are already fetched with an active client.

        Args:
            mcp_client: The active MCP client (primarily for logging or future use if needed by factory).
            tools: List of MCP tools already fetched.

        Returns:
            An AgentExecutor configured with MCP tools.

        Raises:
            ValueError: If the agent runnable or executor cannot be created by the factory.
        """
        if not tools:
            logger.error(
                "Critical internal error: _create_agent_executor_with_active_client called with empty tools list."
            )
            raise ToolFetchingError(
                "Tool list is empty when trying to create executor."
            )

        async with trace_span_async("create_agent_prompt_template"):
            prompt_template = create_agent_prompt_template()

        logger.info(
            "Using AgentExecutorFactory to create executor with pre-fetched tools..."
        )
        try:
            agent_executor = self.agent_executor_factory.create_executor(
                tools=tools,
                prompt=prompt_template,
            )
            logger.info("AgentExecutor created successfully by factory.")
            return agent_executor
        except ValueError as e:
            logger.error(f"AgentExecutorFactory failed to create executor: {e}")
            raise

    async def _initialize_resources(self) -> None:
        """Initialize async resources for the agent.

        This method performs any necessary initialization for the agent.
        It should be called during application startup before any requests are handled.
        """
        logger.info("Initializing MCP Agent resources...")
        try:
            logger.info("MCP Agent resources initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize MCP Agent resources: {str(e)}")
            logger.exception(e)
            raise

    @contextlib.asynccontextmanager
    async def _get_configured_agent_executor(
        self, authorization: str | None = None
    ) -> AsyncIterator[AgentExecutor]:
        """Context manager to get a configured AgentExecutor with an active MCP client and tools.

        Handles MCP client acquisition, tool fetching, and executor creation.

        Args:
            authorization: Optional authorization header for MCP client.

        Yields:
            A configured AgentExecutor instance.

        Raises:
            MCPClientConfigurationError: If agent client configuration fails.
            ToolFetchingError: If tools cannot be fetched.
            ValueError: If the agent executor cannot be created.
        """
        async with trace_span_async(
            "MCPAgent._get_configured_agent_executor",
            attributes={"has_auth": authorization is not None},
        ):
            try:
                async with self.mcp_client_manager.get_client(
                    authorization=authorization
                ) as mcp_client:
                    logger.debug(
                        "MCP client obtained for agent executor configuration."
                    )

                    async with trace_span_async(
                        "fetch_mcp_tools_within_executor_config",
                        attributes={"has_auth": authorization is not None},
                    ):
                        tools = await self.mcp_tool_service.get_mcp_tools(mcp_client)
                        logger.info(f"Fetched {len(tools)} tools for agent executor.")
                        if not tools:
                            logger.error(
                                "No tools fetched, cannot create agent executor."
                            )
                            raise ToolFetchingError(
                                "Failed to fetch any tools for agent execution."
                            )

                    async with trace_span_async(
                        "create_executor_with_active_client_within_config"
                    ):
                        agent_executor = (
                            await self._create_agent_executor_with_active_client(
                                mcp_client=mcp_client, tools=tools
                            )
                        )

                    trace_tools_execution(agent_executor)

                    yield agent_executor
            except MCPClientConfigurationError as e:
                logger.error(f"MCP Client Config Error during executor setup: {e}")
                raise
            except ToolFetchingError as e:
                logger.error(f"Tool Fetching Error during executor setup: {e}")
                raise
            except ValueError as e:
                logger.error(
                    f"Configuration or factory error during executor setup: {e}"
                )
                raise

    async def _format_chat_history(
        self, chat_history: list[dict[str, Any]] | None = None
    ) -> list[HumanMessage | AIMessage]:
        """Format chat history for agent input.

        Args:
            chat_history: List of previous chat messages.

        Returns:
            Formatted chat history for agent consumption.
        """
        if chat_history is None:
            chat_history = []

        formatted_history: list[HumanMessage | AIMessage] = []
        for message in chat_history:
            if message["role"] == "user":
                formatted_history.append(HumanMessage(content=message["content"]))
            elif message["role"] == "assistant":
                formatted_history.append(AIMessage(content=message["content"]))

        return formatted_history

    async def _process_agent_result(self, result: dict[str, Any]) -> str:
        """Process the agent result and create the final response.

        Args:
            result: The result from agent execution.

        Returns:
            The final formatted output string.
        """
        log_intermediate_steps(result)

        steps = result.get("intermediate_steps", [])

        output_value = result.get("output")
        final_output_str: str
        if isinstance(output_value, str):
            final_output_str = output_value
        else:
            if output_value is not None:
                logger.warning(
                    f"Agent result 'output' was not a string (type: {type(output_value)}). "
                    f"Using empty string instead. Value: {str(output_value)[:200]}"
                )
            final_output_str = ""

        logger.info(f"Processing complete with {len(steps)} steps")
        return final_output_str

    @trace_async_function(name="MCPAgent.process_prompt")
    async def process_prompt(
        self,
        prompt: str,
        chat_history: list[dict[str, Any]] | None = None,
        authorization: str | None = None,
        **kwargs: Any,
    ) -> ProcessPromptResponse:
        """Process a user prompt through the MCP agent.

        This method runs the agent executor which will automatically handle multiple
        iterations of tool calling to complete complex tasks that require multiple
        API calls in sequence.

        Args:
            prompt: The user input prompt.
            chat_history: Optional list of previous chat messages.
            authorization: Optional authorization header for MCP client.
            **kwargs: Additional keyword arguments.

        Returns:
            The agent's complete response as a string.

        Raises:
            MCPClientConfigurationError: If agent client configuration fails.
            ToolFetchingError: If tools cannot be fetched.
            Exception: For other unexpected errors during agent invocation.
        """
        logger.info(
            f"Processing prompt: {prompt[:200]}... with auth: {authorization is not None}"
        )

        trace_attributes = {
            "prompt_length": len(prompt),
            "has_chat_history": chat_history is not None and len(chat_history) > 0,
            "has_auth": authorization is not None,
        }

        async with trace_span_async("format_chat_history"):
            formatted_history = await self._format_chat_history(chat_history)

        try:
            async with self._get_configured_agent_executor(
                authorization=authorization
            ) as agent_executor:
                agent_input = {
                    "input": prompt,
                    "chat_history": formatted_history,
                }

                logger.debug(f"Core agent invocation with input: {agent_input}")
                async with trace_span_async(
                    "agent_executor.ainvoke", attributes=trace_attributes
                ):
                    result = await agent_executor.ainvoke(agent_input)
                    logger.debug(f"Core agent invocation successful, result: {result}")

        except (MCPClientConfigurationError, ToolFetchingError, ValueError):
            raise
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 401:
                logger.warning(
                    f"Authentication failed during prompt processing: {e.response.status_code}"
                )
                raise AuthenticationError(
                    "Invalid or expired authentication token"
                ) from e
            else:
                logger.exception(f"HTTP error during prompt processing: {e}")
                raise
        except ExceptionGroup as eg:
            # Handle ExceptionGroup that may contain httpx.HTTPStatusError
            for exc in eg.exceptions:
                if (
                    isinstance(exc, httpx.HTTPStatusError)
                    and exc.response.status_code == 401
                ):
                    logger.warning(
                        f"Authentication failed during prompt processing: {exc.response.status_code}"
                    )
                    raise AuthenticationError(
                        "Invalid or expired authentication token"
                    ) from exc
            # If no 401 errors found, re-raise the original ExceptionGroup
            logger.exception(f"Exception group during prompt processing: {eg}")
            raise
        except Exception as e:
            logger.exception(f"Unexpected error during process_prompt: {e}")
            raise

        async with trace_span_async("process_agent_result"):
            final_output_str = await self._process_agent_result(result)

        return ProcessPromptResponse(content=final_output_str)

    @trace_async_function(name="MCPAgent.determine_coverage")
    async def determine_coverage(
        self,
        claim_id: str,
        authorization: str | None = None,
        as_of_date: datetime | None = None,
        refined_notes: str | None = None,
    ) -> LegacyCoverageDeterminationResponse:
        """Runs the coverage determination process for a given claim.

        This is the primary business logic method of the agent. It orchestrates
        the entire process of analyzing a claim's coverage by:
        1.  Generating a detailed, task-specific prompt using the claim ID and date.
        2.  Acquiring a properly configured `AgentExecutor` with dynamic tools
            fetched from MCP servers using the provided authorization.
        3.  Invoking the agent executor with the prompt.
        4.  Receiving the raw string output from the LLM.
        5.  Parsing the raw output into a structured `LegacyCoverageDeterminationResponse`
            object using the robust parsing utilities.

        Args:
            claim_id: The ID of the claim to analyze.
            authorization: The authorization token required to access MCP services.
            as_of_date: The date for which the coverage determination should be
                performed. Defaults to now if not provided.
            refined_notes: Optional previous refined notes for language consistency.

        Returns:
            A structured `LegacyCoverageDeterminationResponse` object containing the
            verification results.

        Raises:
            MCPClientConfigurationError: If agent client configuration fails.
            ToolFetchingError: If tools cannot be fetched.
            ResponseParsingError: If the agent's response cannot be parsed.
            Exception: For other unexpected errors during agent invocation.
        """
        current_date = (as_of_date if as_of_date else datetime.now()).strftime(
            "%Y-%m-%d"
        )
        logger.info(
            f"Determining coverage for claim_id: {claim_id}, as_of_date: {current_date}, "
            f"with auth: {authorization is not None}"
        )

        trace_attributes = {
            "claim_id": claim_id,
            "as_of_date": current_date,
            "has_auth": authorization is not None,
        }

        async with trace_span_async(
            "get_coverage_determination_prompt", attributes=trace_attributes
        ):
            prompt = get_coverage_determination_prompt(
                claim_id=claim_id, as_of_date=current_date
            )
            logger.debug(
                f"Coverage determination prompt length: {len(prompt)} characters"
            )

        try:
            async with self._get_configured_agent_executor(
                authorization=authorization
            ) as agent_executor:
                agent_input = {
                    "input": prompt,
                    "chat_history": [],
                }

                logger.debug(f"Core agent invocation with input: {agent_input}")
                logger.debug(
                    f"Agent executor tools: {[tool.name if hasattr(tool, 'name') else str(tool) for tool in agent_executor.tools]}"
                )
                async with trace_span_async(
                    "agent_executor.ainvoke", attributes=trace_attributes
                ):
                    config = RunnableConfig(
                        metadata={**trace_attributes, "step_name": "determine_coverage"}
                    )
                    result = await agent_executor.ainvoke(agent_input, config=config)
                    logger.debug(
                        f"Core agent invocation successful, result keys: {list(result.keys())}"
                    )
                    logger.debug(
                        f"Core agent invocation result output: '{result.get('output', 'NO_OUTPUT_KEY')}'"
                    )
                    logger.debug(
                        f"Core agent invocation result intermediate_steps count: {len(result.get('intermediate_steps', []))}"
                    )

        except (MCPClientConfigurationError, ToolFetchingError, ValueError):
            raise
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 401:
                logger.warning(
                    f"Authentication failed for claim {claim_id}: {e.response.status_code}"
                )
                raise AuthenticationError(
                    "Invalid or expired authentication token"
                ) from e
            else:
                logger.exception(
                    f"HTTP error during coverage determination for claim {claim_id}: {e}"
                )
                raise
        except ExceptionGroup as eg:
            # Handle ExceptionGroup that may contain httpx.HTTPStatusError
            for exc in eg.exceptions:
                if (
                    isinstance(exc, httpx.HTTPStatusError)
                    and exc.response.status_code == 401
                ):
                    logger.warning(
                        f"Authentication failed for claim {claim_id}: {exc.response.status_code}"
                    )
                    raise AuthenticationError(
                        "Invalid or expired authentication token"
                    ) from exc
            # If no 401 errors found, re-raise the original ExceptionGroup
            logger.exception(
                f"Exception group during coverage determination for claim {claim_id}: {eg}"
            )
            raise
        except Exception as e:
            logger.exception(
                f"Error during coverage determination for claim {claim_id}: {e}"
            )
            raise

        async with trace_span_async("log_intermediate_steps"):
            log_intermediate_steps(result)

        if "output" not in result:
            logger.error("Agent response missing 'output' field")
            raise ValueError("Agent response missing 'output' field")

        llm_output = result["output"]

        logger.debug(f"Extracted LLM output: {llm_output}")

        # Fail early if LLM produced no output
        if not llm_output or not llm_output.strip():
            logger.error("LLM produced empty output for coverage determination")
            raise DataExtractionError(
                "LLM failed to generate coverage determination output"
            )

        # Apply language consistency refinement if refined_notes are provided
        if refined_notes:
            try:
                logger.info(
                    "Applying language consistency refinement with provided notes"
                )

                # Generate the refinement prompt using the raw LLM output
                async with trace_span_async("generate_refinement_prompt"):
                    refinement_prompt = get_language_consistency_refinement_prompt(
                        initial_coverage_notes=llm_output,
                        previous_refined_notes=refined_notes,
                    )

                # Apply the refinement
                refined_output = await self.apply_language_consistency_refinement(
                    refinement_prompt=refinement_prompt,
                    trace_attributes=trace_attributes,
                )

                if refined_output and refined_output.strip():
                    logger.info(
                        "Language consistency refinement completed successfully, using refined output"
                    )
                    llm_output = refined_output
                else:
                    logger.warning(
                        "Refinement produced empty output, using original response"
                    )

            except Exception as e:
                logger.warning(
                    f"Language consistency refinement failed: {e}, using original response"
                )

        try:
            async with trace_span_async("parse_coverage_determination_response"):
                parsed_response = parse_coverage_determination_response(llm_output)
        except ResponseParsingError as e:
            logger.error(f"Failed to parse coverage determination response: {e}")
            raise DataExtractionError(f"Could not parse LLM response: {e}") from e

        logger.info("Successfully determined coverage and parsed results")
        return parsed_response

    @trace_async_function(name="MCPAgent.apply_language_consistency_refinement")
    async def apply_language_consistency_refinement(
        self, refinement_prompt: str, trace_attributes: dict[str, Any] | None = None
    ) -> str:
        """Apply language consistency refinement using the provided prompt.

        Args:
            refinement_prompt: The complete refinement prompt to send to the agent.
            trace_attributes: Optional dictionary of trace attributes to include in spans.

        Returns:
            The refined LLM output, or empty string if refinement fails.
        """
        if trace_attributes is None:
            trace_attributes = {}

        start_time = time.time()
        try:
            logger.info("Starting language consistency refinement process")

            async with trace_span_async("execute_refinement"):
                # Create a stripped down agent executor with no tools for refinement
                setup_start = time.time()
                async with trace_span_async("create_refinement_prompt_template"):
                    prompt_template = create_refinement_agent_prompt_template()

                logger.info(
                    "Creating stripped down agent executor for refinement (no tools, no MCP client)"
                )
                agent_executor = self.agent_executor_factory.create_executor(
                    tools=[],  # No tools needed for refinement
                    prompt=prompt_template,
                )
                setup_time = time.time() - setup_start
                logger.info(f"Agent executor setup completed in {setup_time:.2f}s")

                agent_input = {
                    "input": refinement_prompt,
                    "chat_history": [],
                }

                execution_start = time.time()
                config = RunnableConfig(
                    metadata={**trace_attributes, "step_name": "refinement"}
                )
                async with trace_span_async("agent_executor.ainvoke"):
                    result = await agent_executor.ainvoke(agent_input, config=config)
                execution_time = time.time() - execution_start
                logger.info(f"Refinement execution completed in {execution_time:.2f}s")

                refined_output = str(result.get("output", ""))

            total_time = time.time() - start_time
            logger.info(
                f"Language consistency refinement completed successfully in {total_time:.2f}s"
            )
            return refined_output

        except Exception as e:
            total_time = time.time() - start_time
            logger.warning(
                f"Language consistency refinement failed after {total_time:.2f}s: {e}, returning empty string"
            )
            return ""

    async def _cleanup_resources(self) -> None:
        """Clean up any resources used by the agent.

        This method is provided to satisfy the AgentProtocol.
        MCPAgent does not hold long-lived resources that require explicit cleanup.
        """
        logger.debug("MCPAgent._cleanup_resources called.")
        pass
