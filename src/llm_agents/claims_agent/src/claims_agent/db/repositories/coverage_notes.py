"""Repository for coverage notes database operations."""

from datetime import datetime, timezone
from typing import Optional
from uuid import UUID

from loguru import logger
from sqlalchemy import select, update, delete, desc
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker

from claims_agent.db.orm.generated import CoverageNotes
from claims_agent.interfaces.legacy_models import VerificationItem
from claims_agent.models.feedback import NoteFeedback


class CoverageNotesRepository:
    """Repository for managing coverage notes in the database."""

    def __init__(self, session_maker: async_sessionmaker[AsyncSession]):
        """Initialize the repository with a SQLAlchemy session maker.

        Args:
            session_maker: A pre-configured SQLAlchemy async_sessionmaker.
        """
        self._session_maker = session_maker
        logger.info("CoverageNotesRepository initialized.")

    async def create_notes_for_run(
        self,
        run_id: UUID,
        claim_id: str,
        verification_items: list[VerificationItem],
    ) -> list[UUID]:
        """Create coverage notes for a coverage run with proper priority ordering.

        Args:
            run_id: The ID of the coverage run.
            claim_id: The claim ID being analyzed.
            verification_items: List of verification items to store as notes.

        Returns:
            List of note IDs that were created.
        """
        async with self._session_maker() as session:
            note_ids = []

            for i, verification_item in enumerate(verification_items):
                priority = i * 10  # 0, 10, 20, 30, etc.
                note = CoverageNotes(
                    run_id=run_id,
                    claim_id=claim_id,
                    original_content=verification_item.model_dump(mode="json"),
                    priority=priority,
                )
                session.add(note)
                await session.flush()  # Flush to get the generated ID
                note_ids.append(note.note_id)

            await session.commit()
            logger.info(f"Created {len(note_ids)} coverage notes for run {run_id}")
            return note_ids

    async def upsert_feedback(
        self,
        feedback_list: list[NoteFeedback],
        updated_by: str,
    ) -> list[UUID]:
        """Upsert feedback for multiple coverage notes.

        Args:
            feedback_list: List of feedback for individual notes.
            updated_by: Email of the user providing feedback.

        Returns:
            List of note IDs that were updated.
        """
        async with self._session_maker() as session:
            updated_note_ids = []

            for feedback in feedback_list:
                # Update the note with the modified content
                stmt = (
                    update(CoverageNotes)
                    .where(
                        CoverageNotes.note_id == feedback.note_id,
                    )
                    .values(
                        modified_content=feedback.modified_content.model_dump(
                            mode="json"
                        ),
                        updated_at=datetime.now(timezone.utc),
                        updated_by=updated_by,
                    )
                )
                result = await session.execute(stmt)

                if result.rowcount > 0:
                    updated_note_ids.append(feedback.note_id)
                    logger.info(f"Updated note {feedback.note_id} with feedback")
                else:
                    logger.warning(f"Note {feedback.note_id} not found")

            await session.commit()
            logger.info(f"Updated {len(updated_note_ids)} notes with feedback")
            return updated_note_ids

    async def get_notes_for_run(self, run_id: UUID) -> list[CoverageNotes]:
        """Get all coverage notes for a specific run, ordered by priority.

        Args:
            run_id: The ID of the coverage run.

        Returns:
            List of coverage notes for the run, ordered by priority ascending.
        """
        async with self._session_maker() as session:
            stmt = (
                select(CoverageNotes)
                .where(CoverageNotes.run_id == run_id)
                .order_by(CoverageNotes.priority.asc())
            )
            result = await session.execute(stmt)
            notes = result.scalars().all()

            logger.info(f"Retrieved {len(notes)} notes for run {run_id}")
            return list(notes)

    async def get_note_by_id(self, note_id: UUID) -> Optional[CoverageNotes]:
        """Get a specific coverage note by its ID.

        Args:
            note_id: The ID of the note to retrieve.

        Returns:
            The coverage note, or None if not found.
        """
        async with self._session_maker() as session:
            stmt = select(CoverageNotes).where(CoverageNotes.note_id == note_id)
            result = await session.execute(stmt)
            note = result.scalar_one_or_none()

            if note:
                logger.info(f"Retrieved note {note_id}")
            else:
                logger.warning(f"Note {note_id} not found")

            return note

    async def delete_notes_for_run(self, run_id: UUID) -> int:
        """Delete all coverage notes for a specific run.

        Args:
            run_id: The ID of the coverage run.

        Returns:
            Number of notes deleted.
        """
        async with self._session_maker() as session:
            # Use bulk delete instead of fetching and deleting individual records
            stmt = delete(CoverageNotes).where(CoverageNotes.run_id == run_id)
            result = await session.execute(stmt)
            await session.commit()

            deleted_count = result.rowcount
            logger.info(f"Deleted {deleted_count} notes for run {run_id}")
            return deleted_count

    async def get_recent_refined_coverage_notes(
        self, claim_id: str, limit: int = 20, exclude_run_id: Optional[UUID] = None
    ) -> list[CoverageNotes]:
        """Get recent coverage notes with human-refined content for language consistency analysis.

        Only returns notes that have been refined through human feedback (modified_content is not null)
        for the specific claim.

        Args:
            claim_id: The claim ID to filter notes for.
            limit: Maximum number of notes to retrieve.
            exclude_run_id: Optional run ID to exclude from results (e.g., current run).

        Returns:
            List of recent refined coverage notes for the claim, ordered by most recent first.
        """
        async with self._session_maker() as session:
            stmt = (
                select(CoverageNotes)
                .where(
                    CoverageNotes.modified_content.is_not(None),
                    CoverageNotes.claim_id == claim_id,
                )
                .order_by(desc(CoverageNotes.updated_at))
            )

            if exclude_run_id is not None:
                stmt = stmt.where(CoverageNotes.run_id != exclude_run_id)

            stmt = stmt.limit(limit)

            result = await session.execute(stmt)
            notes = result.scalars().all()

            logger.info(
                f"Retrieved {len(notes)} recent refined coverage notes for claim {claim_id} for language consistency"
            )
            return list(notes)
