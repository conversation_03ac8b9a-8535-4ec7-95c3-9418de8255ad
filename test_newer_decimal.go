package main

import (
	"fmt"
	"os/exec"
)

func main() {
	// Check what version we're actually using
	cmd := exec.Command("go", "list", "-m", "github.com/shopspring/decimal")
	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("Error checking version: %v\n", err)
	} else {
		fmt.Printf("Current version: %s\n", string(output))
	}

	// Let's also check the release notes or changelog
	fmt.Println("This appears to be a bug in shopspring/decimal v1.2.0")
	fmt.Println("The Float64() method is incorrectly returning exact=false for simple decimal values")
}
