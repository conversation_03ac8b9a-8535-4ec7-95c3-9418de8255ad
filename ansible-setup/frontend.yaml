---
- hosts: localhost
  connection: local
  gather_facts: true
  become: false
  vars:
    - node_version: v22.15.0
  tasks:
  - name: Setup Node
    block:
    # Not checking if nvm is installed as the following steps are idempotent.
    # But we can check the installation of nvm to improve the script
      - name: Install nvm
        ansible.builtin.shell: >
          curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
        args:
          executable: /bin/bash
          chdir: "$HOME"
          creates: "$HOME/.nvm/nvm.sh"


      - name: Install node
        ansible.builtin.shell: |
          source ~/.zshrc
          nvm install {{item}}
          nvm use {{item}}
          source ~/.zshrc
        args:
          executable: /bin/bash
          chdir: "$HOME"
          creates: "$HOME/.nvm/versions/node/v{{item}}"
        loop:
          - '{{ node_version }}'

  - name: Discover npm prefix directory
    ansible.builtin.command:
      cmd: >
        bash -c "source $HOME/.nvm/nvm.sh &&
        nvm exec --silent {{ node_version }} npm config get prefix"
    register: npm_prefix_result
    changed_when: false

  - name: Set npm binaries directory
    ansible.builtin.set_fact:
      npm_binaries_dir: "{{ npm_prefix_result.stdout }}/bin"

  - name: Install yarn
    ansible.builtin.shell: |
      source $HOME/.nvm/nvm.sh
      nvm exec {{ node_version }} npm install -g yarn
      export PATH=~/usr/local/bin/bin:$PATH
      source ~/.zshrc
    args:
      creates: "{{ npm_binaries_dir }}/yarn"