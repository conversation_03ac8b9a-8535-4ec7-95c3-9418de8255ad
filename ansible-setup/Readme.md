

Start the dev setup. This includes all the tasks needed to be run as part of the dev setup

```shell
./dev-bootstrap.sh
```

dev-bootstrap.sh does not install apps like chrome and goland. This is intentional as we do not want
to be opinionated on the tools used. Please refer to apps.yaml for a quick install of common apps.

```shell
ansible-playbook apps.yaml
```

`backend-go.yaml` Installs golang and postgres. Sets up the user, password and creates required dbs. 

`ds.yaml` Installs pyenv, poetry and sets up python environment

`frontend.yaml` Installs Nodejs, yarn, and installs the node modules
