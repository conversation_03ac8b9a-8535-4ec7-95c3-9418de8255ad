---
# This playbook setus up command line tools on macos.
# In our local Dev setups xcode-select path is always set
# So running this playbook will result in no-op in most cases.
# We have this playbook as it is necessary to run this in EC2 Macos machine we do the playbook testing
- hosts: localhost
  connection: local
  gather_facts: true
  become: false
  tasks:
    - name: Am I running on Mac OS X?
      fail:
        msg: Target host is not running Mac OS X
      when: ansible_distribution != 'MacOSX'

    - name: Is xcode-select path set?
      command: xcode-select -p
      register: xcode_select
      check_mode: no
      ignore_errors: true
      changed_when: false

    - name: Check the Command Line Tools package metadata
      command: pkgutil --pkg-info=com.apple.pkg.CLTools_Executables
      register: pkg_info
      check_mode: no
      ignore_errors: true
      changed_when: false
      
    - name: Prepare to install Command Line Tools
      file:
        path: /tmp/.com.apple.dt.CommandLineTools.installondemand.in-progress
        state: touch
      when: pkg_info.rc != 0 or xcode_select.rc != 0

    - name: Create zshrc
      ansible.builtin.shell: |
          touch ~/.zshrc
