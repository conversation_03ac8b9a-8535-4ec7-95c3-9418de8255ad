---
- hosts: localhost
  connection: local
  gather_facts: true
  become: false
  tasks:
    - name: Setup go
      homebrew: name={{ item }}
      loop:
        - go@1.24
        - postgresql@16
        - libpq
        - openjdk

    - name: Start postgresql and link go@1.24
      shell: |
        brew unlink go
        brew link --force go@1.24
        brew link --force postgresql@16
        brew services start postgresql@16
        brew link --force --overwrite libpq

    - name: Install psycopg2
      pip:
        name: psycopg2-binary

    - name: Create postgres user and password
      postgresql_user:
        login_user: "{{ ansible_user_id }}"
        db: postgres
        name: postgres
        password: postgres
        role_attr_flags: SUPERUSER

    - name: Create a new database with name nirvana
      postgresql_db:
        name: nirvana

    - name: Create a new database with name fmcsa
      postgresql_db:
        name: fmcsa
