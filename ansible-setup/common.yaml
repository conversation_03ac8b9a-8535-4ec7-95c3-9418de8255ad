---
- hosts: localhost
  connection: local
  gather_facts: true
  become: false
  tasks:
    - name: install brew packages
      homebrew: name={{ item.name }}
      loop:
        - name: git
        - name: cask
        - name: awscli
        - name: bazelisk
        - name: gh
        - name: go-task/tap/go-task
        - name: fzf
        - name: jq
        - name: openjdk
    - name: Check the list of installed apps
      stat: path=/Applications/{{item.path}}
      loop:
        - path: Docker.app
          name: docker
      register: apps_install_status
    - name: Install Docker If docker is not installed
      homebrew_cask: name={{ item.item.name }}
      loop: "{{ apps_install_status.results }}"
      when: not item.stat.exists
