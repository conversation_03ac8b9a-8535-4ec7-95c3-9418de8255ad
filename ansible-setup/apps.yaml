---
- hosts: localhost
  connection: local
  gather_facts: true
  become: false
  tasks:
    - name: homebrew-cask ensure taps are present
      homebrew_tap: tap={{ item }}
      loop:
        - homebrew/cask

    # We can install the common apps quickly using the below task
    # we do not want to be opinionated on the apps we would use
    # we can also install each of the following apps manually using brew
    # brew cask install {{name}}. Fill name from the list below

    - name: Check the list of installed apps
      stat: path=/Applications/{{item.path}}
      loop:
        - path: GoLand.app
          name: goland
        - path: pgAdmin 4.app
          name: pgadmin4
        - path: Google Chrome.app
          name: google-chrome
        - path: Postman.app
          name: postman
        - path: Slack.app
          name: slack
        - path: Visual Studio Code.app
          name: visual-studio-code
      register: apps_install_status
    - name: Install Apps not in the applications folder
      homebrew_cask: name={{ item.item.name }}
      loop: "{{ apps_install_status.results }}"
      when: not item.stat.exists