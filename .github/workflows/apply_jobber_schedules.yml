name: Apply Jobber Schedules

permissions:
  contents: read
  id-token: write

on:
  workflow_dispatch:
    inputs:
      jobber_cluster:
        type: choice
        description: Jobber Cluster whose schedules needs to be applied
        options:
          - quoting
          - standard
          - safety
          - data-infra
          - event

defaults:
  run:
    shell: bash
    working-directory: src/nirvana
env:
  TFTOOLS: terraform_7bca0ddf6c

jobs:
  apply_schedules:
    runs-on:
      group: 'Default Larger Runners'
      labels: 'ubuntu-latest-16-cores-new'
    if: ${{ github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'}}
    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials

      - name: Setup terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.3.6
          terraform_wrapper: false # ! Removing this will break our scripts which run jq on terraform output

      - name: Setup tftools
        run: |
          aws s3 cp s3://cloud.nirvanatech.com/tools/"$TFTOOLS" $HOME/.local/bin/"$TFTOOLS" 
          chmod +x $HOME/.local/bin/"$TFTOOLS"

      - name: Acquire remote lock
        id: lock
        run: $TFTOOLS lock gha_apply_schedules_${{ github.run_id }}_${{ github.run_attempt }}

      - name: Get current timestamp
        id: timestamp
        run: echo "::set-output name=timestamp::$(date +'%Y-%m-%dT%H:%M:%S')"

      - name: Apply Standard Jobber Schedule
        id: apply-schedules-standard
        env:
          ENV: prod
        if: ${{ inputs.jobber_cluster == 'standard' }}
        run: |
          FILE_PATH=jobber/standard_jobber/schedules/state.json
          OBJECT_NAME=standard/${{ steps.timestamp.outputs.timestamp }}_${{ github.sha }}.json
          BUCKET_NAME=jobber-schedules
          aws s3 cp $FILE_PATH s3://$BUCKET_NAME/$OBJECT_NAME
          echo "$OBJECT_NAME uploaded to s3 bucket : $BUCKET_NAME"
          go run cmd/jobber/main.go apply_schedule standard --s3bucket=$BUCKET_NAME --s3objectKey=$OBJECT_NAME --force
          unset FILE_PATH OBJECT_NAME BUCKET_NAME

      - name: Apply Safety Jobber Schedule
        id: apply-schedules-safety
        env:
          ENV: prod
        if: ${{ inputs.jobber_cluster == 'safety' }}
        run: |
          FILE_PATH=safety_jobber/schedules/state.json
          OBJECT_NAME=safety/${{ steps.timestamp.outputs.timestamp }}_${{ github.sha }}.json
          BUCKET_NAME=jobber-schedules
          aws s3 cp $FILE_PATH s3://$BUCKET_NAME/$OBJECT_NAME
          echo "$OBJECT_NAME uploaded to s3 bucket : $BUCKET_NAME"
          go run cmd/jobber/main.go apply_schedule safety --s3bucket=$BUCKET_NAME --s3objectKey=$OBJECT_NAME --force
          unset FILE_PATH OBJECT_NAME BUCKET_NAME

      - name: Apply Data-Infra Jobber Schedule
        id: apply-schedules-data-infra
        env:
          ENV: prod
        if: ${{ inputs.jobber_cluster == 'data-infra' }}
        run: |
          FILE_PATH=data_infra_jobber/schedules/state.json
          OBJECT_NAME=data-infra/${{ steps.timestamp.outputs.timestamp }}_${{ github.sha }}.json
          BUCKET_NAME=jobber-schedules
          aws s3 cp $FILE_PATH s3://$BUCKET_NAME/$OBJECT_NAME
          echo "$OBJECT_NAME uploaded to s3 bucket : $BUCKET_NAME"
          go run cmd/jobber/main.go apply_schedule data-infra --s3bucket=$BUCKET_NAME --s3objectKey=$OBJECT_NAME --force
          unset FILE_PATH OBJECT_NAME BUCKET_NAME

      - name: Apply Event Jobber Schedule
        id: apply-schedules-event
        env:
          ENV: prod
        if: ${{ inputs.jobber_cluster == 'event' }}
        run: |
          FILE_PATH=jobber/event/schedules/state.json
          OBJECT_NAME=event/${{ steps.timestamp.outputs.timestamp }}_${{ github.sha }}.json
          BUCKET_NAME=jobber-schedules
          aws s3 cp $FILE_PATH s3://$BUCKET_NAME/$OBJECT_NAME
          echo "$OBJECT_NAME uploaded to s3 bucket : $BUCKET_NAME"
          go run cmd/jobber/main.go apply_schedule event --s3bucket=$BUCKET_NAME --s3objectKey=$OBJECT_NAME --force
          unset FILE_PATH OBJECT_NAME BUCKET_NAME

      - name: Apply Quoting Jobber Schedule
        id: apply-schedules-quoting
        env:
          ENV: prod
        if: ${{ inputs.jobber_cluster == 'quoting' }}
        run: |
          FILE_PATH=api-server/quoting_jobber/schedules/state.json
          OBJECT_NAME=quoting/${{ steps.timestamp.outputs.timestamp }}_${{ github.sha }}.json
          BUCKET_NAME=jobber-schedules
          aws s3 cp $FILE_PATH s3://$BUCKET_NAME/$OBJECT_NAME
          echo "$OBJECT_NAME uploaded to s3 bucket : $BUCKET_NAME"
          go run cmd/jobber/main.go apply_schedule quoting --s3bucket=$BUCKET_NAME --s3objectKey=$OBJECT_NAME --force
          unset FILE_PATH OBJECT_NAME BUCKET_NAME
#         Also add support for other clusters

      - name: Release remote lock
        if: ${{ always()  && steps.lock.conclusion == 'success' }}
        run: $TFTOOLS unlock gha_apply_schedules_${{ github.run_id }}_${{ github.run_attempt }}
