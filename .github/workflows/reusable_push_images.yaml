name: Push images to ECR

on:
  workflow_call:
    inputs:
      service:
        type: string
        description: |
          JSON array of service names to deploy
          Example: '["service1", "service2"]'
        required: true
      account_id:
        type: string
        description: AWS account ID to use. Defaults to production account (************).
        default: "************"
    # https://docs.github.com/en/actions/sharing-automations/reusing-workflows#using-outputs-from-a-reusable-workflow
    outputs:
      image_tag:
        description: Image tag set on the images pushed by this workflow
        value: ${{ jobs.build-and-push.outputs.image_tag }}

defaults:
  run:
    shell: bash
    working-directory: src

jobs:
  build-and-push:
    runs-on:
      group: "Default Larger Runners"
      labels: "ubuntu-latest-16-cores-new"
    outputs:
      image_tag: ${{ steps.get-tag.outputs.tag }}

    steps:
      - uses: actions/checkout@v4

      #### SETUP GOLANG ENVIRONMENT
      - name: Mount bazel cache
        id: mount-bazel-cache
        uses: ./.github/actions/mount-cache
        with:
          prefix: bazel
          skipSave: "true"
          path: |
            ~/.cache/bazel
            ~/.cache/bazelisk
      - name: Install go-task
        uses: arduino/setup-task@v2
        with:
          version: 3.43.2
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Set up Bazel using version from src/.bazelversion
        uses: bazelbuild/setup-bazelisk@v3
      - name: Install docker-credential-ecr-login
        run: go install github.com/awslabs/amazon-ecr-credential-helper/ecr-login/cli/docker-credential-ecr-login@latest

      #### SETUP AWS CREDENTIALS AND DOCKER CONFIG
      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials
        with:
          aws-account-id: ${{ inputs.account_id }}
      - name: Create docker config
        shell: bash
        run: |
          mkdir -p ~/.docker
          cat <<EOT > ~/.docker/config.json
          {
              "credHelpers": {
                  "${{ inputs.account_id }}.dkr.ecr.us-east-2.amazonaws.com": "ecr-login"
              }
          }
          EOT
      - name: Export ECR url
        id: get-ecr-url
        run: |
          echo "ecr_url=${{ inputs.account_id }}.dkr.ecr.us-east-2.amazonaws.com" >> $GITHUB_OUTPUT

      #### Set image tag
      - name: Set git commit hash as image tag
        id: get-tag
        run: |
          echo "Image tag is $(git rev-parse --short HEAD)"
          echo "tag=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Print image tag to be used
        run: |
          echo "Image tag is ${{ steps.get-tag.outputs.tag }}"

      #### PUSH IMAGES
      - name: Push mvr_cache_server image
        if: ${{ contains(fromJSON(inputs.service), 'mvr_cache_server') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/mvr_cache_server:push_mvr_cache_server \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=mvr-cache-server-repo \
          --define=account_id=${{ inputs.account_id }}

      - name: Push pdfgen_server image
        if: ${{ contains(fromJSON(inputs.service), 'pdfgen_server') }}
        working-directory: src/nirvana/pdfgen
        run: |
          bazel build //nirvana/pdfgen/cmd/pdfgen_server:pdfgen_server --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64
          cp ../../bazel-bin/nirvana/pdfgen/cmd/pdfgen_server/pdfgen_server_/pdfgen_server cmd/pdfgen_server
          docker build --platform=linux/amd64 -t ************.dkr.ecr.us-east-2.amazonaws.com/pdfgen-server-repo:${{ steps.get-tag.outputs.tag }} .
          rm cmd/pdfgen_server/pdfgen_server
          docker push ************.dkr.ecr.us-east-2.amazonaws.com/pdfgen-server-repo:${{ steps.get-tag.outputs.tag }}

      - name: Push api_server image
        if: ${{ contains(fromJSON(inputs.service), 'api_server') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/api_server:push_api_server \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=api-server-repo \
          --define=account_id=${{ inputs.account_id }}

      - name: Push graphql_server image
        if: ${{ contains(fromJSON(inputs.service), 'graphql_server') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/graphql_server:push_graphql_server \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=gql-api-server-repo \
          --define=account_id=${{ inputs.account_id }}

      - name: Push job_processor image
        if: ${{ contains(fromJSON(inputs.service), 'job_processor') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/job_processor:push_job_processor \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=job-processor \
          --define=account_id=${{ inputs.account_id }}

      - name: Push oauth_server image
        if: ${{ contains(fromJSON(inputs.service), 'oauth_server') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/oauth_server:push \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=oauth-server-repo \
          --define=account_id=${{ inputs.account_id }}

      - name: Push data_infra_job_processor image
        if: ${{ contains(fromJSON(inputs.service), 'data_infra_job_processor') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/data_infra_job_processor:push_image \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=data-infra-job-processor \
          --define=account_id=${{ inputs.account_id }}

      - name: Push telematics_grpc_server image
        if: ${{ contains(fromJSON(inputs.service), 'telematics_grpc_server') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/telematics_grpc_server:push_telematics_grpc_server \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=telematics-grpc-server \
          --define=account_id=${{ inputs.account_id }}

      - name: Push vehicles_grpc_server image
        if: ${{ contains(fromJSON(inputs.service), 'vehicles_grpc_server') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/vehicles_grpc_server:push_vehicles_grpc_server \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=vehicles-grpc-server \
          --define=account_id=${{ inputs.account_id }}

      - name: Push quote_scraper_grpc_server image
        if: ${{ contains(fromJSON(inputs.service), 'quote_scraper_grpc_server') }}
        working-directory: src/nirvana/servers/quote_scraper
        run: |
          bazel build //nirvana/servers/quote_scraper/cmd:quote_scraper --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64
          cp ../../../bazel-bin/nirvana/servers/quote_scraper/cmd/quote_scraper_/quote_scraper .
          docker build -t ************.dkr.ecr.us-east-2.amazonaws.com/quote-scraper-server-repo:${{ steps.get-tag.outputs.tag }} .
          rm quote_scraper
          docker push ************.dkr.ecr.us-east-2.amazonaws.com/quote-scraper-server-repo:${{ steps.get-tag.outputs.tag }}

      - name: Push feature_store_server image
        if: ${{ contains(fromJSON(inputs.service), 'feature_store_server') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/feature_store_server:push_feature_store \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=feature-store-server \
          --define=account_id=${{ inputs.account_id }}

      - name: Push quoting_job_processor image
        if: ${{ contains(fromJSON(inputs.service), 'quoting_job_processor') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/quoting_job_processor:push_image \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=quoting-job-processor \
          --define=account_id=${{ inputs.account_id }}

      - name: Push distsem_server image
        if: ${{ contains(fromJSON(inputs.service), 'distsem_server') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/distsem_server:push_distsem_server \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=distsem-server-repo \
          --define=account_id=${{ inputs.account_id }}

      # FIXME: This has moved to a Dockerfile based build.
      - name: Push fmcsa_scraper image
        if: ${{ contains(fromJSON(inputs.service), 'fmcsa_scraper') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/scrapers/fmcsa:push_fmcsa_scraper_image \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=fmcsa-scraper \
          --define=account_id=${{ inputs.account_id }}

      - name: Push nirvanamq lambda & ecs task image
        if: ${{ contains(fromJSON(inputs.service), 'nirvanamq') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/nirvanamq:push_lambda_image \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=nirvanamq-events-lambda \
          --define=account_id=${{ inputs.account_id }}

          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/nirvanamq:push_task_image \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=nirvanamq-events-task \
          --define=account_id=${{ inputs.account_id }}

      - name: Push jobber_monitor image
        if: ${{ contains(fromJSON(inputs.service), 'jobber_monitor') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/jobber_monitor:push_image \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=jobber-monitor \
          --define=account_id=${{ inputs.account_id }}

      - name: Push saferwatch_scraper image
        if: ${{ contains(fromJSON(inputs.service), 'saferwatch_scraper') }}
        working-directory: src/nirvana/servers/saferwatch_scraper
        run: |
          docker build -t ************.dkr.ecr.us-east-2.amazonaws.com/saferwatch-scraper:${{ steps.get-tag.outputs.tag }} .
          docker push ************.dkr.ecr.us-east-2.amazonaws.com/saferwatch-scraper:${{ steps.get-tag.outputs.tag }}

      - name: Push fmcsa_data_provider_grpc_server image
        if: ${{ contains(fromJSON(inputs.service), 'fmcsa_data_provider_grpc_server') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/fmcsa_data_provider_grpc_server:push_fmcsa_data_provider_grpc_server \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=fmcsa-data-provider-grpc-server \
          --define=account_id=${{ inputs.account_id }}

      - name: Push llmops_server image
        if: ${{ contains(fromJSON(inputs.service), 'llmops_server') }}
        working-directory: src/nirvana/llmops
        run: |
          img=${{ steps.get-ecr-url.outputs.ecr_url }}/llmops-service:${{ steps.get-tag.outputs.tag }}
          docker build -t "$img" .
          docker push "$img"

      - name: Push mcp_experiments_rest_server image
        if: ${{ contains(fromJSON(inputs.service), 'mcp_experiments_rest_server') }}
        run: |
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/mcp_experiments_rest_server:push_mcp_experiments_rest_server \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=mcp-experiments-rest-server \
          --define=account_id=${{ inputs.account_id }}

      # Python images in src/llm_agents (require uv)
      - name: Install uv
        if: |
          ${{ contains(fromJSON(inputs.service), 'mcp_servers') }} ||
          ${{ contains(fromJSON(inputs.service), 'llm_agents_claims') }}
        uses: astral-sh/setup-uv@v6
        with:
          version: "latest"

      - name: Push MCP servers
        if: ${{ contains(fromJSON(inputs.service), 'mcp_servers') }}
        working-directory: src/llm_agents
        run: |
          uv run poe build-and-push --tag ${{ steps.get-tag.outputs.tag }} --aws_account_id ${{ inputs.account_id }} --package mcp_servers --ecr_path init-ai/mcp-servers

      - name: Push LLM Agents (Claims)
        if: ${{ contains(fromJSON(inputs.service), 'llm_agents_claims') }}
        working-directory: src/llm_agents
        run: |
          uv run poe build-and-push --tag ${{ steps.get-tag.outputs.tag }} --aws_account_id ${{ inputs.account_id }} --package claims_agent --ecr_path init-ai/llm_agents/claims_agent
