name: Setup and run Cypress tests

on:
  workflow_call:
    inputs:
      app-name:
        description: "The name of the app to run Cypress tests for: safety, support, or quoting"
        required: true
        type: string

      port:
        description: "The port to run the tests on"
        required: true
        type: string

      run-e2e-tests:
        description: "Run Cypress e2e tests"
        required: false
        default: true
        type: boolean

      run-component-tests:
        description: "Run Cypress component tests"
        required: false
        default: false
        type: boolean

      skip:
        description: "Skip running the tests"
        required: true
        type: boolean

    secrets:
      gh-token:
        required: true
      turborepo-token:
        required: true
      clerk-secret-key:
        required: true

permissions: write-all

jobs:
  build:
    if: ${{ !inputs.skip }}
    runs-on: ubuntu-latest-4-cores
    outputs:
      test-files: ${{ steps.parse.outputs.testfiles }}
    env:
      TURBO_TEAM: ${{ vars.TURBOREPO_TEAM }}
      TURBO_TOKEN: ${{ secrets.turborepo-token }}
    steps:
      - uses: actions/checkout@v4

      - name: Install yarn packages
        uses: ./.github/actions/setup-frontend-apps-node

      - name: Build application
        if: ${{ inputs.run-e2e-tests }}
        working-directory: src/nirvana/client
        run: yarn turbo run build:test --filter=@nirvana/${{ inputs.app-name }}...

      - name: Upload a Build Artifact
        if: ${{ inputs.run-e2e-tests }}
        uses: actions/upload-artifact@v4
        with:
          name: build-artifact-${{ inputs.app-name }}-${{ github.sha }}
          path: src/nirvana/client/apps/${{ inputs.app-name }}/dist

      - name: Parse test files for parallelization
        id: parse
        if: ${{ inputs.run-e2e-tests }}
        run: |
          chmod +x ./.github/scripts/cypress-group-in-batches.sh 
          ./.github/scripts/cypress-group-in-batches.sh src/nirvana/client/apps/${{ inputs.app-name }}

  run-e2e:
    if: ${{ inputs.run-e2e-tests && !inputs.skip }}
    runs-on: ubuntu-latest-4-cores
    needs: build
    strategy:
      matrix:
        test-files: ${{ fromJSON(needs.build.outputs.test-files) }}
    steps:
      - uses: actions/checkout@v4

      - name: Install yarn packages
        id: install-yarn-packages
        uses: ./.github/actions/setup-frontend-apps-node

      - uses: actions/download-artifact@v4
        with:
          name: build-artifact-${{ inputs.app-name }}-${{ github.sha }}
          path: src/nirvana/client/apps/${{ inputs.app-name }}/dist

      - name: Cypress run e2e
        uses: cypress-io/github-action@v6.7.10
        timeout-minutes: 15
        env:
          GITHUB_TOKEN: ${{ secrets.gh-token }}
          CYPRESS_video: false
          TZ: America/Los_Angeles
          cache-key: ${{ steps.install-yarn-packages.outputs.yarn-cache-key }}
          spec: ${{ matrix.test-files }}
          CLERK_SECRET_KEY: ${{ secrets.clerk-secret-key }}
        with:
          working-directory: src/nirvana/client
          start: yarn serve:${{ inputs.app-name }} --port ${{ inputs.port }} --host
          wait-on: http://127.0.0.1:${{ inputs.port }}
          command: yarn cy:${{ inputs.app-name }}:run --spec ${{ matrix.test-files }}
          install: false

      - name: Upload screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          path: src/nirvana/client/apps/${{ inputs.app-name }}/cypress/screenshots
          name: screenshots-${{ inputs.app-name }}-e2e
  run-component:
    if: ${{ inputs.run-component-tests && !inputs.skip }}
    runs-on: ubuntu-latest-4-cores
    needs: build
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v4
      - name: Install yarn packages
        id: install-yarn-packages
        uses: ./.github/actions/setup-frontend-apps-node

      - name: Run Component Testing
        uses: cypress-io/github-action@v6
        with:
          working-directory: src/nirvana/client
          command: yarn cy:${{ inputs.app-name }}:run --component
          component: true
          cache-key: ${{ steps.install-yarn-packages.outputs.yarn-cache-key }}
          install: false
        env:
          TZ: America/Los_Angeles
          ENV: production

      - name: Upload screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          path: src/nirvana/client/apps/${{ inputs.app-name }}/cypress/screenshots
          name: screenshots-${{ inputs.app-name }}-component
