name: Python CI

on:
  merge_group:
    types: [checks_requested]
  pull_request:
    branches: [main]

permissions:
  contents: read
  id-token: write
  pull-requests: read

defaults:
  run:
    shell: bash
    working-directory: src/ds

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

jobs:
  # JOB to run change detection
  python-changes:
    runs-on: ubuntu-latest
    # Set job outputs to values from filter step
    outputs:
      ds: ${{ steps.filter.outputs.ds }}
      proto: ${{ steps.filter.outputs.proto }}
      llm-agents: ${{ steps.filter.outputs.llm-agents }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            ds:
              - 'src/ds/**'
            proto:
              - 'src/proto/**/*.proto'
            llm-agents:
              - 'src/llm_agents/**'

  # We don't have seperate jobs for build and test because jobs don't share
  # artifacts by default.
  python_build_and_test:
    needs: [python-changes]
    if: ${{ needs.python-changes.outputs.ds == 'true' || needs.python-changes.outputs.proto == 'true' }}
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up environment
        id: setup-environment
        uses: ./.github/actions/setup-ds-env
      - name: Configure AWS credentials
        uses: ./.github/actions/setup-aws-credentials
      - name: Fail if protogen not clean
        run: |
          ENV=prod task -v terraflow -- protogen
          task py-protogen
          git diff --stat --exit-code
      - name: Fail if oapi-gen not clean
        run: |
          task oapi-gen
          git diff --stat --exit-code
      - name: Run test-all task
        run: |
          task -v test-all

  test-llm-agents:
    needs: [python-changes]
    if: ${{ needs.python-changes.outputs.llm-agents == 'true' }}
    runs-on:
      - runs-on # https://runs-on.com/configuration/job-labels/#available-labels
      - family=m7g.xlarge+m8gd.xlarge+m7gd.xlarge # Determined based on spot failure rate, price and memory
      - image=ubuntu22-full-arm64
      - spot=${{ !startsWith(github.head_ref || github.ref_name, 'nmq-') && 'capacity-optimized' }} # disable spot instance for nmq's speculative checks
      - run-id=${{ github.run_id }}
    timeout-minutes: 30
    defaults:
      run:
        working-directory: src/llm_agents
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v4

      - name: Set up Python
        run: uv python install
      - name: Set up Docker Compose
        uses: docker/setup-compose-action@v1
        with:
          version: latest

      - name: Configure AWS credentials
        uses: ./.github/actions/setup-aws-credentials

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Install dependencies and workspace
        run: uv sync --all-packages --dev

      - name: Create .env files
        run: |
          cp supabase.example.env .env
          cp claims_agent/.env.example claims_agent/.env

      - name: Start DB & run migrations
        env:
          PULL_THROUGH_CACHE_BASE: ${{ steps.login-ecr.outputs.registry }}/public-docker-hub/
        run: |
          docker compose run --rm migrator up

      - name: Start temporal
        env:
          PULL_THROUGH_CACHE_BASE: ${{ steps.login-ecr.outputs.registry }}/public-docker-hub/
        run: |
          docker compose up temporal -d

      - name: Run ORM codegen for claims agent and fail if changes are detected
        run: |
          uv run poe generate-orm
          git diff --stat --exit-code   

      - name: Run codegen tasks and fail if changes are detected
        run: |
          # Disabling generate-api-client for now since we generate OpenAPI 3.1.0, which is not compatible with the 3.0.1 frontend code generator.
          # uv run poe generate-api-client
          git diff --stat --exit-code

      - name: Run checks and fail if formatting detects changes
        run: |
          uv run poe check
          git diff --stat --exit-code

      - name: Run tests
        env:
          LLAMA_CLOUD_API_KEY: sk-dummy
        run: |
          uv run poe test-all
