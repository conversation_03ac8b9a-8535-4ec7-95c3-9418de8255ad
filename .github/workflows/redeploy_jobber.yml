name: Re-deploy Jobber processor

permissions:
  contents: read
  id-token: write

on: 
  workflow_dispatch:
    inputs:
      jobber_cluster:
        type: choice
        description: Jobber Cluster whose job_processor needs to be updated
        options:
          - quoting
          - standard
          - safety
          - event
          # See PR #10399 for reason on why we disable data-infra
          # - data-infra

defaults:
  run:
    shell: bash
    working-directory: src

env:
  TFTOOLS: terraform_7bca0ddf6c
  TFLOCKKEY: gha_redeploy_jobber

concurrency:
  group: "terraform-singleton"
  cancel-in-progress: false

jobs:
  redeploy:
    runs-on:
        # FIXME: Reduce size
      group: 'Default Larger Runners'
      labels: 'ubuntu-latest-16-cores-new'
    steps:
    - uses: actions/checkout@v4

   # TODO: Create an action to perform following AWS+tftools setup+lock and unlock in post
    - name: Configure AWS Credentials
      uses: ./.github/actions/setup-aws-credentials

    - name: Setup terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: 1.7.5
        terraform_wrapper: false # ! Removing this will break our scripts which run jq on terraform output

    - name: Run terraform init
      working-directory: src/deployment/app
      run: |
        terraform init

    - name: Setup tftools
      run: |
        aws s3 cp s3://cloud.nirvanatech.com/tools/"$TFTOOLS" $HOME/.local/bin/"$TFTOOLS" 
        chmod +x $HOME/.local/bin/"$TFTOOLS"

    - name: Acquire remote lock
      id: lock
      run: $TFTOOLS lock ${TFLOCKKEY}_${{ github.run_id }}_${{ github.run_attempt }}

   # ecs-only check will succeed on no-op and as a fallback
   # job-processor-change-only will cover other changes to jobber task
   # definitions
    - name: Eagerly check terraform plan and fail early
      working-directory: src/deployment/app
      run: |
        planfile=$(mktemp)

        ../../scripts/DONOT_RUN_MANUALLY_terraform plan \
          -input=false -out=$planfile > /dev/null

        terraform show $planfile

        $TFTOOLS verify ecs-only <(terraform show -json $planfile) null || \
        $TFTOOLS verify job-processor-change-only <(terraform show -json $planfile) \
            ${{ github.event.inputs.jobber_cluster }}
        rm $planfile

    - name: Mount bazel cache
      id: mount-bazel-cache
      uses: ./.github/actions/mount-cache
      with:
        prefix: bazel
        skipSave: 'true'
        path: |
          ~/.cache/bazel
          ~/.cache/bazelisk

    - name: Set up Bazel using version from src/.bazelversion
      uses: bazelbuild/setup-bazelisk@v3

    - name: Install docker-credential-ecr-login
      run: go install github.com/awslabs/amazon-ecr-credential-helper/ecr-login/cli/docker-credential-ecr-login@latest

    - name: Create docker config
      shell: bash
      run: |
        mkdir -p ~/.docker
        cat <<EOT > ~/.docker/config.json
        {
            "credHelpers": {
                "************.dkr.ecr.us-east-2.amazonaws.com": "ecr-login"
            }
        }
        EOT
    
    - name: Set git commit hash as image tag
      id: get-tag
      run: |
        echo "Image tag is $(git rev-parse --short HEAD)"
        echo "tag=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

    - name: Print image tag to be used
      run: |
        echo "Image tag is ${{ steps.get-tag.outputs.tag }}"
  
    - name: Push job_processor image
      if: github.event.inputs.jobber_cluster == 'standard'
      run: |
        bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
        --@io_bazel_rules_go//go/config:pure \
        //nirvana/images/job_processor:push_job_processor \
        --define=image_tag=${{ steps.get-tag.outputs.tag }} \
        --define=repository=job-processor \
        --define=account_id=************
        echo "job_processor_tag = \"${{ steps.get-tag.outputs.tag }}\"" >> vars.tfvars
  
    - name: Push data_infra_job_processor image
      if: github.event.inputs.jobber_cluster == 'data-infra'
      run: |
        bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
        --@io_bazel_rules_go//go/config:pure \
        //nirvana/images/data_infra_job_processor:push_image \
        --define=image_tag=${{ steps.get-tag.outputs.tag }} \
        --define=repository=data-infra-job-processor \
        --define=account_id=************
        echo "data_infra_job_processor_tag = \"${{ steps.get-tag.outputs.tag }}\"" >> vars.tfvars
 
    - name: Push quoting_job_processor image
      if: github.event.inputs.jobber_cluster == 'quoting'
      run: |
        bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
        --@io_bazel_rules_go//go/config:pure \
        //nirvana/images/quoting_job_processor:push_image \
        --define=image_tag=${{ steps.get-tag.outputs.tag }} \
        --define=repository=quoting-job-processor \
        --define=account_id=************
        echo "quoting_job_processor_tag = \"${{ steps.get-tag.outputs.tag }}\"" >> vars.tfvars

    - name: Push safety_job_processor image
      if: github.event.inputs.jobber_cluster == 'safety'
      run: |
        bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
        --@io_bazel_rules_go//go/config:pure \
        //nirvana/images/safety_job_processor:push_image \
        --define=image_tag=${{ steps.get-tag.outputs.tag }} \
        --define=repository=safety-job-processor \
        --define=account_id=************
        echo "safety_job_processor_tag = \"${{ steps.get-tag.outputs.tag }}\"" >> vars.tfvars

    - name: Push event_job_processor image
      if: github.event.inputs.jobber_cluster == 'event'
      run: |
        bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
        --@io_bazel_rules_go//go/config:pure \
        //nirvana/images/event_job_processor:push_image \
        --define=image_tag=${{ steps.get-tag.outputs.tag }} \
        --define=repository=event-job-processor \
        --define=account_id=************
        echo "event_job_processor_tag = \"${{ steps.get-tag.outputs.tag }}\"" >> vars.tfvars

    - name: Prep cluster for draining deployment
      env:
        ENV: prod
      run: |
        bazel run //nirvana/jobber/cmd/jobber_clusterops \
        -- aws single_node_prep_for_draining_redeploy --cluster ${{ github.event.inputs.jobber_cluster }}
 
    - name: Plan & Apply infrastructure changes
      working-directory: src/deployment/app
      run: |
        planfile=$(mktemp)

        cat ../../vars.tfvars

        ../../scripts/DONOT_RUN_MANUALLY_terraform plan \
          -var-file=../../vars.tfvars \
          -input=false -out=$planfile \
          > /dev/null
        terraform show $planfile

        $TFTOOLS verify job-processor-change-only <(terraform show -json $planfile) \
           ${{ github.event.inputs.jobber_cluster }}

        terraform apply -auto-approve -input=false $planfile |\
          grep -v "Refreshing state...\|Reading...\|Read complete after"
        rm $planfile

    - name: Drain after redeploy and node is up
      id: wait-and-drain
      env:
        ENV: prod
      run: |
        bazel run //nirvana/jobber/cmd/jobber_clusterops \
        -- aws single_node_start_draining_after_redeploy --cluster ${{ github.event.inputs.jobber_cluster }}

    - name: Send pagerduty event on failure
      if: ${{ failure() && steps.wait-and-drain.conclusion == 'failure' }}
      uses: Entle/action-pagerduty-alert@1.0.2
      with:
        pagerduty-integration-key: '${{ secrets.PAGERDUTY_INFRA_INTEGRATION_KEY }}'
        pagerduty-dedup-key: ${{ github.run_id }}

    - name: Release remote lock
      if: ${{ always()  && steps.lock.conclusion == 'success' }}
      run: $TFTOOLS unlock ${TFLOCKKEY}_${{ github.run_id }}_${{ github.run_attempt }}
