name: Deploy a metaflow to dev/training

permissions:
  contents: read
  id-token: write

on:
  workflow_dispatch:
    inputs:
      flowFile:
        description: Path to python flow file (example - flows/uw_telematics_data_v1.py)
        required: true
        default: flows/uw_telematics_data_v1.py
        type: string

      deployFlow:
        description: Uncheck to not deploy flow
        type: boolean
        required: true
        default: true

      buildFreshDockerImage:
        description: Check to build a new docker image for dependencies for this branch, otherwise latest production one is used.
        required: true
        default: false
        type: boolean

      envType:
        description: Choose deployment type
        required: true
        default: dev
        type: choice
        options:
          - dev
          - training

defaults:
  run:
    shell: bash
    working-directory: src/ds

jobs:
  dev-deploy:
    name: Deploy flow to dev/training
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Check that flow file exists
        run: |
          [[ -f "${{ inputs.flowFile }}" ]]

      - name: Set up environment
        id: setup-environment
        uses: ./.github/actions/setup-ds-env

      - name: Configure AWS credentials
        uses: ./.github/actions/setup-aws-credentials

      - name: Set git commit hash as image tag
        id: get-tag
        run: |
          echo "Image tag is $(git rev-parse --short HEAD)"
          echo "tag=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Set image or latest
        id: get-image-to-use
        run: |
          [[ ${{ inputs.buildFreshDockerImage}} == "true" ]] && echo "image=${{ steps.get-tag.outputs.tag }}" >> $GITHUB_OUTPUT && exit 0
          echo "image=latest" >> $GITHUB_OUTPUT

      - name: Build datascience docker image
        id: build-image
        uses: ./.github/actions/push-python-image
        if: ${{ inputs.buildFreshDockerImage }}
        with:
          image_tag: ${{ steps.get-tag.outputs.tag }}
          # we purposefully avoid tagging the image as latest

      - name: Push flow to dev/training
        env:
          METAFLOW_USER: github
          ENV: ${{ inputs.envType }}
        if: ${{ inputs.deployFlow }}
        run: |
          task -v dev-deploy FLOW="${{ inputs.flowFile }}" IMAGE=${{ steps.get-image-to-use.outputs.image }}
