name: Close stale issues and PRs

on:
  schedule:
    - cron: '30 1 * * *'
  workflow_dispatch:

permissions:
  contents: read
  issues: write
  pull-requests: write

concurrency:
  group: "close-stale-prs-singleton"
  cancel-in-progress: false

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          exempt-pr-labels: 'need-help,WIP'
          stale-pr-message: |
            This PR has been automatically marked as stale because it has not had any activity in the last 15 days.
            If you think this is a mistake, either update the branch, or add the 'WIP' or 'need-help' label.
          days-before-stale: 15
          days-before-close: 7
          ascending: true
          operations-per-run: 100
