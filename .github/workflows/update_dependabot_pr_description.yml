name: Customize Dependabot PR Description

on:
  pull_request:
    types:
      - opened
      - synchronize
jobs:
  update-description:
    if: ${{ github.actor == 'dependabot[bot]' }}
    permissions:
      pull-requests: write
      contents: read
      issues: write
      repository-projects: write
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Login
        run: echo "${{ secrets.GITHUB_TOKEN }}" | gh auth login --with-token

      - name: Post current PR description as comment
        run: |
          PR_DESCRIPTION=$(gh pr view ${{ github.event.pull_request.number }} --json body --jq .body)
          gh pr comment ${{ github.event.pull_request.number }} --body "$PR_DESCRIPTION"

      - name: Update PR description w/ PR title
        run: |
          NEW_DESCRIPTION="${{ github.event.pull_request.title }}"
          gh pr edit ${{ github.event.pull_request.number }} --body "$NEW_DESCRIPTION"
