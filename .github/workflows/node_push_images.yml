name: Push NodeJS Images

permissions:
  contents: read
  id-token: write

on:
  workflow_dispatch:
    inputs:
      service:
        type: choice
        description: Service image to push
        options:
          - cms_server

defaults:
  run:
    shell: bash
    working-directory: src

jobs:
  push:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Print service to be pushed
        run: |
          echo "Service image to be pushed is ${{ github.event.inputs.service }}"

      - name: Set git commit hash as image tag
        id: get-tag
        run: |
          echo "Image tag is $(git rev-parse --short HEAD)"
          echo "tag=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Print image tag to be used
        run: |
          echo "Image tag is ${{ steps.get-tag.outputs.tag }}"

      - name: Push cms_server image
        if: github.event.inputs.service == 'cms_server'
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: cms-server
          IMAGE_TAG: ${{ steps.get-tag.outputs.tag }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG nirvana/blog/.
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
