name: Report merged PR metrics to Datadog
on:
  pull_request:
    types:
      - closed
permissions:
  contents: read

concurrency:
  group: "merged-pr-metrics-singleton"
  cancel-in-progress: false

jobs:
  report:
    if: github.event.pull_request.merged == true && !contains(fromJSON('["app/nirvanamq", "app/nirvanacodegenbot", "app/dependabot"]'), github.actor)
    name: Send merged PR metrics to Datadog
    runs-on: ubuntu-latest
    permissions:
      issues: read
      pull-requests: read
    steps:
      - name: Generate raw metrics using raw-metrics tool
        id: raw-metrics
        uses: github/issue-metrics@v3
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SEARCH_QUERY: "repo:${{ github.repository }} is:pr is:merged ${{ github.event.number }}"
          RATE_LIMIT_BYPASS: "true"

      - name: Convert raw metrics (timestamps strings) to formatted metrics (seconds integers) (e.g 02:23:43 => 8632)
        id: metrics
        run: |
          echo time_to_first_response=$(echo ${{ fromJson(steps.raw-metrics.outputs.metrics).issues[0].time_to_first_response }} | awk -F: '{ print ($1 * 3600) + ($2 * 60) + $3 }') >> $GITHUB_OUTPUT
          echo time_to_close=$(echo ${{ fromJson(steps.raw-metrics.outputs.metrics).issues[0].time_to_close }} | awk -F: '{ print ($1 * 3600) + ($2 * 60) + $3 }') >> $GITHUB_OUTPUT

      - name: Get PR size
        run: |
          LABELS='${{ toJson(github.event.pull_request.labels) }}'
          SIZE_LABEL=$(echo "$LABELS" | jq -r '.[] | select(.name | startswith("size/")) | .name')
          echo "pr_size=$SIZE_LABEL" >> $GITHUB_ENV

      - name: Get labels
        run: |
          labels=$(echo '"${{ join(github.event.pull_request.labels.*.name, '", "') }}"')
          echo "labels=[$labels]" >> $GITHUB_ENV

      - name: Get requested teams
        run: |
          requested_teams=$(echo '"${{ join(github.event.pull_request.requested_teams.*.name, '", "') }}"')
          echo "requested_teams=[$requested_teams]" >> $GITHUB_ENV

      - name: Get requested reviewers
        run: |
          requested_reviewers=$(echo '"${{ join(github.event.pull_request.requested_reviewers.*.name, '", "') }}"')
          echo "requested_reviewers=[$requested_reviewers]" >> $GITHUB_ENV

      - name: Send metrics to Datadog
        uses: masci/datadog@v1
        with:
          api-key: ${{ secrets.DATADOG_API_KEY }}
          logs: |
            - hostname: "github.com"
              source: "github-actions"
              service: "pr-merge-metrics"
              message: |
                {
                  "time_to_first_response": ${{ steps.metrics.outputs.time_to_first_response }},
                  "time_to_close": ${{ steps.metrics.outputs.time_to_close }},
                  "author": "${{ github.event.pull_request.user.login }}",
                  "labels": ${{ env.labels }},
                  "pr_size": "${{ env.pr_size }}",
                  "requested_teams": ${{ env.requested_teams }},
                  "requested_reviewers": ${{ env.requested_reviewers }},
                  "url": "${{ github.event.pull_request.url }}"
                }
