name: Deploy LLM Agents services

permissions:
  contents: read
  id-token: write

on:
  workflow_dispatch:
    inputs:
      service:
        type: choice
        description: The service to deploy
        options:
          - "mcp_servers"
          - "llm_agents_claims"

defaults:
  run:
    shell: bash
    working-directory: src

env:
  # Note: to release a new version run task release-executable:tftools
  TFTOOLS: terraform_7bca0ddf6c
  AWS_ACCOUNT_ID: ************

concurrency:
  group: "deploy-llm-agents-${{ inputs.service }}"
  cancel-in-progress: false

jobs:
  acquire-lock:
    name: Acquire remote lock
    runs-on: ubuntu-latest
    outputs:
      module_name: ${{ steps.set-module-name.outputs.module_name }}
      lock_key: ${{ steps.set-lock-key.outputs.lock_key }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set module name
        id: set-module-name
        run: |
          echo "module_name=src/infra/llm_agents/${{ inputs.service }}" >> $GITHUB_OUTPUT

      - name: Set lock key
        id: set-lock-key
        run: |
          echo "lock_key=gha:deploy_${{ github.run_id }}_${{ github.run_attempt }}" >> $GITHUB_OUTPUT

      - name: Setup Terraform environment
        uses: ./.github/actions/setup-tf-env
        with:
          tftools: $TFTOOLS
          account_id: ${{ env.AWS_ACCOUNT_ID }}

      - name: Acquire remote lock
        id: lock
        run: $TFTOOLS lock --tf-module ${{ steps.set-module-name.outputs.module_name }} ${{ steps.set-lock-key.outputs.lock_key }}

      - name: Release remote lock if this action fails
        if: ${{ always() && steps.lock.conclusion == 'success' && job.status != 'success' }}
        run: $TFTOOLS unlock --tf-module ${{ steps.set-module-name.outputs.module_name }} ${{ steps.set-lock-key.outputs.lock_key }}

  push-images:
    needs: acquire-lock
    uses: ./.github/workflows/reusable_push_images.yaml
    with:
      service: '["${{ inputs.service }}"]'

  release-lock-on-failure:
    needs: [acquire-lock, push-images]
    if: ${{ always() && needs.push-images.result != 'success' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Terraform environment
        uses: ./.github/actions/setup-tf-env
        with:
          tftools: $TFTOOLS
          account_id: ${{ env.AWS_ACCOUNT_ID }}

      - name: Release remote lock
        run: $TFTOOLS unlock --tf-module ${{ needs.acquire-lock.outputs.module_name }} ${{ needs.acquire-lock.outputs.lock_key }}

  deploy:
    needs: [acquire-lock, push-images]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Terraform environment
        uses: ./.github/actions/setup-tf-env
        with:
          tftools: $TFTOOLS
          account_id: ${{ env.AWS_ACCOUNT_ID }}

      - name: Ensure that we're still holding the remote lock
        id: lock
        run: $TFTOOLS lock --tf-module ${{ needs.acquire-lock.outputs.module_name }} ${{ needs.acquire-lock.outputs.lock_key }}

      - name: Print image tag to be used
        run: |
          echo "Image tag is ${{ needs.push-images.outputs.image_tag }}"

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          version: "latest"

      - name: Make pytfrelay available
        # TODO: Use GITHUB_PATH here.
        run: |
          cp infra/pytfrelay $HOME/.local/bin/pytfrelay

      - name: Deploy mcp servers using pytfrelay
        if: ${{ inputs.service == 'mcp_servers' }}
        run: |
          pytfrelay --auto-init apply default-root-coreinfra-mcp-servers-us-east-2 \
            -- \
            -auto-approve \
            -var='image_tag=${{ needs.push-images.outputs.image_tag }}'

      - name: Deploy claims agent using pytfrelay
        if: ${{ inputs.service == 'llm_agents_claims' }}
        run: |
          pytfrelay --auto-init apply default-root-coreinfra-llm-agent-web-servers-us-east-2 \
            -- \
            -auto-approve \
            -var='claims_agent_image_tag=${{ needs.push-images.outputs.image_tag }}'

      - name: Release remote lock
        if: ${{ always() && steps.lock.conclusion == 'success' }}
        run: $TFTOOLS unlock --tf-module ${{ needs.acquire-lock.outputs.module_name }} ${{ needs.acquire-lock.outputs.lock_key }}
