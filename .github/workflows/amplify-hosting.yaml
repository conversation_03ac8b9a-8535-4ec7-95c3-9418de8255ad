name: Deploy to Amplify template

on:
  workflow_call:
    inputs:
      app-id:
        description: Amplify app ID
        required: true
        type: string

      app-name:
        description: Nirvana app name (safety, support, quoting, underwriter)
        required: true
        type: string

      branch-name:
        required: true
        type: string

      commit-hash:
        required: true
        type: string

      pull-request-number:
        description: Pull request number
        required: true
        type: string

      skip:
        description: Skip workflow run
        required: true
        type: boolean

      mode:
        description: If set to production, the workflow will deploy to production
        required: false
        default: staging
        type: string

    secrets:
      sentry-auth-token:
        description: Token used to upload sourcemaps to Sentry
        required: false

      webhook-url:
        description: Slack webhook URL
        required: false
      turborepo-token:
        required: true

jobs:
  deploy:
    if: ${{ !inputs.skip }}
    runs-on: linux-arm-8-cores
    steps:
      - uses: actions/checkout@v4
        if: always()

      - name: Configure AWS Credentials
        if: always()
        uses: ./.github/actions/setup-aws-credentials

      - name: Install yarn packages
        uses: ./.github/actions/setup-frontend-apps-node

      - name: Build application
        working-directory: src/nirvana/client
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.sentry-auth-token }}
          TURBO_TEAM: ${{ vars.TURBOREPO_TEAM }}
          TURBO_TOKEN: ${{ secrets.turborepo-token }}
          NODE_ENV: production
        run: yarn turbo run build:${{inputs.mode}} --filter=@nirvana/${{ inputs.app-name }}...

      - name: ZIP build
        working-directory: src/nirvana/client/apps/${{ inputs.app-name }}/dist
        run: zip -x "*.map" -r dist.zip ./

      - name: Upload dist to S3
        working-directory: src/nirvana/client/apps/${{ inputs.app-name }}/dist
        run: aws s3 cp ./dist.zip s3://nirvana-frontend-builds/${{ inputs.app-name }}/${{ inputs.branch-name }}/${{ inputs.pull-request-number }}/dist.zip

      - name: Get Signed URL
        id: get-signed-url
        run: |
          signed_url=$(aws s3 presign s3://nirvana-frontend-builds/${{ inputs.app-name }}/${{ inputs.branch-name }}/${{ inputs.pull-request-number }}/dist.zip)
          echo "signed_url=$signed_url" >> $GITHUB_OUTPUT

      - name: Create branch if it doesn't exist and start job
        id: create-branch
        if: ${{ inputs.mode == 'staging' }}
        run: |
          branchQuery=$(aws amplify list-branches --app-id=${{ inputs.app-id }} --query 'branches[?branchName==`${{ inputs.branch-name }}`].branchName | [0]' --output json | tail -n 1 | tr -d '"')

          if [ "$branchQuery" = "null" ]; then
            aws amplify create-branch --no-enable-auto-build --stage="PULL_REQUEST" --app-id=${{ inputs.app-id }} --branch-name=${{ inputs.branch-name }} --display-name="pr-${{ inputs.pull-request-number }}" 1>/dev/null
          fi

          currentJobRunStatus=$(aws amplify list-jobs --app-id=${{ inputs.app-id }} --branch-name=${{ inputs.branch-name }} --query 'jobSummaries[*].status | [0]' --output json | tail -n 1 | tr -d '"')
          if [[ "$currentJobRunStatus" == "RUNNING" ]]; then
              echo "Job is already running"
              exit 0
          elif [[ "$currentJobRunStatus" == "PENDING" ]]; then
              echo "Job is pending"
              exit 0
          else
              jobId=$(aws amplify start-deployment \
                  --app-id=${{ inputs.app-id }} \
                  --branch-name=${{ inputs.branch-name }} \
                  --source-url="${{ steps.get-signed-url.outputs.signed_url }}" \
                  --query 'jobSummary.jobId' --output json | tail -n 1 | tr -d '"')
              echo "Job ID: $jobId"
              echo jobId=$jobId >> $GITHUB_OUTPUT
              sleep 5
          fi

      - name: Deploy to production
        if: ${{ inputs.mode == 'production' }}
        id: deploy-to-production
        run: |
          jobId=$(aws amplify start-deployment \
            --app-id=${{ inputs.app-id }} \
            --branch-name=main \
            --source-url="${{ steps.get-signed-url.outputs.signed_url }}" \
            --query 'jobSummary.jobId' --output json | tail -n 1 | tr -d '"')
            echo "Job ID: $jobId"
            echo jobId=$jobId >> $GITHUB_OUTPUT
            sleep 5

      - name: Wait for deployment to finish
        shell: bash
        run: |
          jobId=${{ steps.create-branch.outputs.jobId || steps.deploy-to-production.outputs.jobId}}
          currentStatus=$(aws amplify list-jobs --app-id=${{ inputs.app-id }} --branch-name=${{ inputs.branch-name }} --query 'jobSummaries[?jobId==`"'${jobId}'"`].status | [0]' --output json | tail -n 1 | tr -d '"' )
          echo "Current status: $currentStatus"
          count=0
          while [[ "$currentStatus" == "PENDING" || "$currentStatus" == "RUNNING" || "$count" -gt 900 ]]; do
            sleep 10
            
            currentStatus=$(aws amplify list-jobs --app-id=${{ inputs.app-id }} --branch-name=${{ inputs.branch-name }} --query 'jobSummaries[?jobId==`"'${jobId}'"`].status | [0]' --output json | tail -n 1 | tr -d '"' )
            echo "Current status: $currentStatus"
            count=$((count+10))
          done

          if [[ "$currentStatus" == "FAILED" ]]; then
            exit 1
          fi

      - name: Add Custom Subdomain on Amplify
        if: ${{ inputs.mode == 'staging' }}
        id: custom-subdomain
        uses: ./.github/actions/amplify-subdomain
        with:
          app-id: ${{ inputs.app-id }}
          app-name: ${{ inputs.app-name }}
          branch-name: ${{ inputs.branch-name }}
          pull-request-number: ${{ inputs.pull-request-number }}

      - name: Find Comment
        uses: peter-evans/find-comment@v3
        if: ${{ inputs.mode == 'staging' }}
        id: fc
        with:
          issue-number: ${{ github.event.pull_request.number }}
          comment-author: "github-actions[bot]"
          body-includes: preview-result-${{inputs.app-name}}

      - name: On success comment
        if: ${{ success() && inputs.mode == 'staging' }}
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ steps.fc.outputs.comment-id }}
          issue-number: ${{ github.event.pull_request.number }}
          body: |
            **${{ inputs.app-name }}**
            Preview URL available at: ${{ steps.custom-subdomain.outputs.subdomain || format('https://pr-{0}.{1}.amplifyapp.com', inputs.pull-request-number, inputs.app-id) }}
            Commit used: ${{ inputs.commit-hash }} 
            preview-result-${{inputs.app-name}}
          edit-mode: replace

      - name: Comment PR with error message
        if: ${{ failure() && inputs.mode == 'staging' }}
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ steps.fc.outputs.comment-id }}
          issue-number: ${{ github.event.pull_request.number }}
          body: |
            Failed to deploy PR preview for **${{ inputs.app-name }}** on ${{ inputs.commit-hash }}
            preview-result-${{inputs.app-name}}
          edit-mode: replace

      - name: Send Slack notification
        if: ${{ always() && inputs.mode == 'production' }}
        uses: slackapi/slack-github-action@v2.1.0
        with:
          webhook: ${{ secrets.webhook-url }}
          webhook-type: webhook-trigger
          payload: |
            status: "${{ job.status == 'success' && '✅ Success' || job.status == 'failure' && '❌ Failure' || '⚠️ Unknown' }}"
            app_name: "${{ inputs.app-name }}"
            commit_hash: "${{ inputs.commit-hash }}"
            branch_name: "${{ inputs.branch-name }}"
