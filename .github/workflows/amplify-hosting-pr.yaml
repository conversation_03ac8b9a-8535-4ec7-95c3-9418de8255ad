name: Amplify PR Preview

on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - synchronize
      - reopened
      - closed

permissions: write-all

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}

jobs:
  changes:
    if: github.event.action != 'closed'
    runs-on: ubuntu-latest
    permissions:
      pull-requests: read
    outputs:
      safety: ${{ steps.filter.outputs.safety }}
      ui-kit: ${{ steps.filter.outputs.ui-kit }}
      support: ${{ steps.filter.outputs.support }}
      quoting: ${{ steps.filter.outputs.quoting }}
      underwriter: ${{ steps.filter.outputs.underwriter }}
      graphiql: ${{ steps.filter.outputs.graphiql }}
      storybook: ${{ steps.filter.outputs.storybook }}
    steps:
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            safety:
              - "src/nirvana/client/apps/safety/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - "src/nirvana/client/turbo.json"
              - ".github/workflows/amplify-hosting.yaml"
              - ".github/workflows/amplify-hosting-pr.yaml"
            ui-kit:
              - "src/nirvana/client/packages/ui-kit/**"
              - "src/nirvana/client/packages/ui/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - "src/nirvana/client/turbo.json"
              - ".github/workflows/amplify-hosting.yaml"
              - ".github/workflows/amplify-hosting-pr.yaml"
            storybook:
              - "src/nirvana/client/apps/storybook/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - "src/nirvana/client/turbo.json"
              - ".github/workflows/amplify-hosting.yaml"
              - ".github/workflows/amplify-hosting-pr.yaml"
            support:
              - "src/nirvana/client/apps/support/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - "src/nirvana/client/turbo.json"
              - ".github/workflows/amplify-hosting.yaml"
              - ".github/workflows/amplify-hosting-pr.yaml"
            quoting:
              - "src/nirvana/client/apps/quoting/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - "src/nirvana/client/turbo.json"
              - ".github/workflows/amplify-hosting.yaml"
              - ".github/workflows/amplify-hosting-pr.yaml"
            underwriter:
              - "src/nirvana/client/apps/underwriter/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - "src/nirvana/client/turbo.json"
              - ".github/workflows/amplify-hosting.yaml"
              - ".github/workflows/amplify-hosting-pr.yaml"
            graphiql:
              - "src/nirvana/client/apps/graphiql/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - "src/nirvana/client/turbo.json"
              - ".github/workflows/amplify-hosting.yaml"
              - ".github/workflows/amplify-hosting-pr.yaml"
  build-and-preview-safety:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: dmmns8s1lobsz
      app-name: safety
      branch-name: ${{ github.head_ref }}
      commit-hash: ${{ github.sha }}
      pull-request-number: ${{ github.event.number }}
      skip: ${{ startsWith(github.head_ref || github.ref_name, 'nmq-') || (needs.changes.outputs.safety == 'false' && needs.changes.outputs.ui-kit == 'false') }}
    secrets:
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}
      sentry-auth-token: ${{ secrets.SAFETY_SENTRY_AUTH_TOKEN }}

  build-and-preview-support:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: d2rhwh5sa9jmhi
      app-name: support
      branch-name: ${{ github.head_ref }}
      commit-hash: ${{ github.sha }}
      pull-request-number: ${{ github.event.number }}
      skip: ${{ startsWith(github.head_ref || github.ref_name, 'nmq-') || (needs.changes.outputs.support == 'false' && needs.changes.outputs.ui-kit == 'false') }}
    secrets:
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}
      sentry-auth-token: ${{ secrets.SUPPORT_SENTRY_AUTH_TOKEN }}

  build-and-preview-quoting:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: d1vlfpei1tktik
      app-name: quoting
      branch-name: ${{ github.head_ref }}
      commit-hash: ${{ github.sha }}
      pull-request-number: ${{ github.event.number }}
      skip: ${{ startsWith(github.head_ref || github.ref_name, 'nmq-') || (needs.changes.outputs.quoting == 'false' && needs.changes.outputs.ui-kit == 'false') }}
    secrets:
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}
      sentry-auth-token: ${{ secrets.QUOTING_SENTRY_AUTH_TOKEN }}

  build-and-preview-underwriter:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: d23z2wyvd07g2o
      app-name: underwriter
      branch-name: ${{ github.head_ref }}
      commit-hash: ${{ github.sha }}
      pull-request-number: ${{ github.event.number }}
      skip: ${{ startsWith(github.head_ref || github.ref_name, 'nmq-') || (needs.changes.outputs.underwriter == 'false' && needs.changes.outputs.ui-kit == 'false') }}
    secrets:
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}
      sentry-auth-token: ${{ secrets.UNDERWRITER_SENTRY_AUTH_TOKEN }}

  build-and-preview-graphiql:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: d3sp3eglwcay59
      app-name: graphiql
      branch-name: ${{ github.head_ref }}
      commit-hash: ${{ github.sha }}
      pull-request-number: ${{ github.event.number }}
      skip: ${{ startsWith(github.head_ref || github.ref_name, 'nmq-') || (needs.changes.outputs.graphiql == 'false' && needs.changes.outputs.ui-kit == 'false') }}
    secrets:
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}

  build-and-preview-storybook:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: d2gjvnfrie6ud4
      app-name: storybook
      branch-name: ${{ github.head_ref }}
      commit-hash: ${{ github.sha }}
      pull-request-number: ${{ github.event.number }}
      skip: ${{ startsWith(github.head_ref || github.ref_name, 'nmq-') || (needs.changes.outputs.storybook == 'false' && needs.changes.outputs.ui-kit == 'false') }}
    secrets:
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}

  frontend-hosting-checks:
    if: always()
    needs:
      [
        build-and-preview-safety,
        build-and-preview-support,
        build-and-preview-quoting,
        build-and-preview-underwriter,
        build-and-preview-graphiql,
        build-and-preview-storybook,
      ]
    runs-on: ubuntu-latest
    steps:
      - shell: bash
        run: |
          failed_jobs=$(echo '${{ toJSON(needs) }}' | jq -r 'to_entries[] | select(.value.result == "failure") | .key')
          if [ -n "$failed_jobs" ]; then
            echo "Failed jobs: $failed_jobs"
            exit 1
          fi

  cleanup:
    if: github.event.action == 'closed'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials

      - name: Delete branches
        run: |
          app_ids=(
            dmmns8s1lobsz
            d2rhwh5sa9jmhi
            d1vlfpei1tktik
            d23z2wyvd07g2o
            d3sp3eglwcay59
            d2gjvnfrie6ud4
          )

          for app_id in "${app_ids[@]}"; do
            if ! aws amplify delete-branch --app-id="$app_id" --branch-name=${{ github.head_ref }} 2>&1 >/dev/null; then
              echo ""
            fi
          done

      - name: Delete S3 file
        run: |
          app_names=(
            safety
            support
            quoting
            underwriter
            graphiql
            storybook
          )

          for app_name in "${app_names[@]}"; do
            if ! aws s3 rm s3://nirvana-frontend-builds/$app_name/${{ github.head_ref }}/${{ github.event.number }}/dist.zip 2>&1 >/dev/null; then
              echo ""
            fi
          done
