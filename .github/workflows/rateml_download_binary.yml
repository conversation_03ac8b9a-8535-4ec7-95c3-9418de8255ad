name: Generate RateML binaries

on:
  workflow_dispatch:
    inputs:
      os:
        type: choice
        description: OS
        options:
        - windows
        - macos
      cli:
        type: choice
        description: CLI
        options:
        - rateml

defaults:
  run:
    shell: bash
    working-directory: src

permissions:
  contents: read
  id-token: write

jobs:
  push:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Configure AWS Credentials
      uses: ./.github/actions/setup-aws-credentials

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 1.24.1

    - name: Set up Bazel using version from src/.bazelversion
      uses: bazelbuild/setup-bazelisk@v3

    - name: Mount bazel cache
      id: mount-bazel-cache
      uses: ./.github/actions/mount-cache
      with:
        prefix: bazel
        skipSave: 'true'
        path: |
          ~/.cache/bazel
          ~/.cache/bazelisk

    - name: Export target platform
      id: export-target-platform
      run: |
        PLATFORM=${{ github.event.inputs.os == 'windows' && 'windows_amd64' || 'darwin_arm64' }}
        echo "PLATFORM=${PLATFORM}" >> "$GITHUB_OUTPUT"

    - name: Export expected output file path
      id: export-output-path
      run: |
        CLI_NAME=${{ github.event.inputs.cli }}
        EXTENSION=${{ github.event.inputs.os == 'windows' && '.exe' || '' }}
        OUTPUT_PATH=src/bazel-bin/nirvana/rating/cmd/${CLI_NAME}/${CLI_NAME}_/${CLI_NAME}${EXTENSION}
        echo "OUTPUT_PATH=${OUTPUT_PATH}" >> "$GITHUB_OUTPUT"

    - name: Export uploaded file name
      id: export-uploaded-file-name
      run: |
        OS=${{ github.event.inputs.os }}
        CLI_NAME=${{ github.event.inputs.cli }}
        UPLOADED_FILE_NAME=${CLI_NAME}_${OS}
        echo "UPLOADED_FILE_NAME=${UPLOADED_FILE_NAME}" >> "$GITHUB_OUTPUT"

    - name: Generate binary
      run: >
        bazel build //nirvana/rating/cmd/${{ github.event.inputs.cli }} --platforms=@io_bazel_rules_go//go/toolchain:${{ steps.export-target-platform.outputs.PLATFORM }}

    - name: Upload binary
      uses: actions/upload-artifact@v4
      with:
        name: ${{ steps.export-uploaded-file-name.outputs.UPLOADED_FILE_NAME }}
        path: ${{ steps.export-output-path.outputs.OUTPUT_PATH }}
