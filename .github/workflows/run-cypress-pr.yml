name: Run Cypress tests

on:
  merge_group:
    types: [checks_requested]
  pull_request:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions: write-all

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      safety: ${{ steps.filter.outputs.safety }}
      support: ${{ steps.filter.outputs.support }}
      quoting: ${{ steps.filter.outputs.quoting }}
      underwriter: ${{ steps.filter.outputs.underwriter }}
      ui-kit: ${{ steps.filter.outputs.ui-kit }}
    steps:
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            safety:
              - "src/nirvana/client/apps/safety/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - ".github/workflows/run-cypress.yaml"
              - ".github/workflows/run-cypress-pr.yaml"              
            support:
              - "src/nirvana/client/apps/support/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - ".github/workflows/run-cypress.yaml"
              - ".github/workflows/run-cypress-pr.yaml"              
            quoting:
              - "src/nirvana/client/apps/quoting/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - ".github/workflows/run-cypress.yaml"
              - ".github/workflows/run-cypress-pr.yaml"
            underwriter:
              - "src/nirvana/client/apps/underwriter/**"
              - "src/nirvana/client/apps/underwriter/package.json"
              - "src/nirvana/client/yarn.lock"
              - ".github/workflows/run-cypress.yaml"
              - ".github/workflows/run-cypress-pr.yaml"
            ui-kit:
              - "src/nirvana/client/packages/ui-kit/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - ".github/workflows/run-cypress.yaml"
              - ".github/workflows/run-cypress-pr.yaml"

  run-safety:
    needs: [changes]
    uses: ./.github/workflows/run-cypress.yaml
    with:
      app-name: safety
      port: 3000
      run-component-tests: true
      skip: ${{ needs.changes.outputs.safety == 'false' }}
    secrets:
      gh-token: ${{ secrets.GITHUB_TOKEN }}
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN }}
      clerk-secret-key: ${{ secrets.CLERK_SECRET_KEY }}

  run-support:
    needs: [changes]
    uses: ./.github/workflows/run-cypress.yaml
    with:
      app-name: support
      port: 1120
      skip: ${{ needs.changes.outputs.support == 'false' }}
    secrets:
      gh-token: ${{ secrets.GITHUB_TOKEN }}
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN }}
      clerk-secret-key: ${{ secrets.CLERK_SECRET_KEY }}

  run-quoting:
    needs: [changes]
    uses: ./.github/workflows/run-cypress.yaml
    with:
      app-name: quoting
      port: 1110
      run-component-tests: false
      skip: ${{ needs.changes.outputs.quoting == 'false' }}
    secrets:
      gh-token: ${{ secrets.GITHUB_TOKEN }}
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN }}
      clerk-secret-key: ${{ secrets.CLERK_SECRET_KEY }}


  run-underwriter:
    needs: [changes]
    uses: ./.github/workflows/run-cypress.yaml
    with:
      app-name: underwriter
      port: 3000
      skip: ${{ needs.changes.outputs.underwriter == 'false' }}
    secrets:
      gh-token: ${{ secrets.GITHUB_TOKEN }}
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN }}
      clerk-secret-key: ${{ secrets.CLERK_SECRET_KEY }}


  run-core:
    needs: [changes]
    uses: ./.github/workflows/run-cypress.yaml
    with:
      app-name: core
      port: 3000
      run-e2e-tests: false
      run-component-tests: true
      skip: ${{ needs.changes.outputs.ui-kit == 'false' }}
    secrets:
      gh-token: ${{ secrets.GITHUB_TOKEN }}
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN }}
      clerk-secret-key: ${{ secrets.CLERK_SECRET_KEY }}


  frontend-tests-checks:
    if: always()
    needs: [run-safety, run-support, run-quoting, run-underwriter, run-core]
    runs-on: ubuntu-latest
    steps:
      - shell: bash
        run: |
          failed_jobs=$(echo '${{ toJSON(needs) }}' | jq -r 'to_entries[] | select(.value.result == "failure") | .key')
          if [ -n "$failed_jobs" ]; then
            echo "Failed jobs: $failed_jobs"
            exit 1
          fi
