name: <PERSON><PERSON> Diff Check for Impacted Services

on:
  pull_request:
    branches: [ main ]
    types:
      # the first three are defaults
      # https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#pull_request
      - opened
      - synchronize
      - reopened

permissions: write-all

defaults:
  run:
    working-directory: src/nirvana

concurrency:
  group:  ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  bazel-diff:
    name: bazel-diff
    if: ${{ github.event.pull_request.draft == false }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 100
          ref: ${{github.event.pull_request.head.sha}}

      - name: Merge (if needed) latest commits from base branch
        shell: bash
        run: |
          # This is needed to be able to merge the latest commits from the base branch
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git config user.name "github-actions[bot]"
          git fetch origin ${{github.event.pull_request.base.ref}}
          git merge origin/${{github.event.pull_request.base.ref}} $(git log --pretty=format:'%h' -n 1)

      - name: Setup Bazel
        uses: bazelbuild/setup-bazelisk@v3

      - name: Setup Java 11
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '11'
        id: java

      - name: Install Task
        uses: arduino/setup-task@v2
        with:
          version: 3.19.1

      - name: Run Bazel Diff
        id: run_bazel_diff
        shell: bash
        run: |
          # Using EOF since this output is multi line
          echo "impacted-images<<EOF" >> $GITHUB_OUTPUT
          task bazel:list-impacted-images --silent >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Evaluate labels to add & remove
        id: evaluate-labels
        shell: bash
        run: |
          have_image_labels=$( echo "${{join(github.event.pull_request.labels.*.name, ',')}}" | tr ',' '\n' | grep "^Δ" || true )
          want_image_labels=$( echo "${{steps.run_bazel_diff.outputs.impacted-images}}" | sed 's/^/Δ/' )
          labels_to_add=$(comm -13 <(echo "$have_image_labels" | sort) <(echo "$want_image_labels" | sort) | paste -sd ',' -)
          labels_to_remove=$(comm -23 <(echo "$have_image_labels" | sort) <(echo "$want_image_labels" | sort) | paste -sd ',' -)
          if [[ $labels_to_add != 'Δ' ]]
          then
            echo "labels-to-add=$(echo $labels_to_add)" >> $GITHUB_OUTPUT
          fi
          if [[ $labels_to_remove != 'Δ' ]]
          then
            echo "labels-to-remove=$(echo $labels_to_remove)" >> $GITHUB_OUTPUT
          fi

      - name: Remove labels
        uses: buildsville/add-remove-label@v2.0.0
        if: steps.evaluate-labels.outputs.labels-to-remove != ''
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          labels: ${{ steps.evaluate-labels.outputs.labels-to-remove }}
          type: remove

      - name: Add labels
        uses: buildsville/add-remove-label@v2.0.0
        if: steps.evaluate-labels.outputs.labels-to-add != ''
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          labels: ${{ steps.evaluate-labels.outputs.labels-to-add }}
          type: add

      - name: Print dependencies for changes
        id: dependencies_for_changes
        shell: bash
        env:
          BAZEL_DIFF_BASE_SHA: ${{github.event.pull_request.base.sha}}
        run: |
          task bazel:list-deps-impacted-images --silent