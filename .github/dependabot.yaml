version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      time: "09:00"
      timezone: "America/Santiago"
    open-pull-requests-limit: 10
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-patch"] # only update major and minor versions
    commit-message:
      prefix: "[DEPENDABOT - Github Actions]"

  - package-ecosystem: "npm"
    directories:
      - "**/client"
      - "**/client/apps/safety"
      - "**/client/apps/support"
    schedule:
      interval: "weekly"
      time: "08:00"
      timezone: "America/Santiago"
    open-pull-requests-limit: 10
    commit-message:
      prefix: "[DEPENDABOT - NPM]"

  - package-ecosystem: "gomod"
    directories:
      - "**/src/nirvana"
    schedule:
      interval: "weekly"
      time: "08:00"
      timezone: "America/Santiago"
    open-pull-requests-limit: 10
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-minor", "version-update:semver-major"] # only update patch versions
    commit-message:
      prefix: "[DEPENDABOT - Go]"

  - package-ecosystem: "uv"
    directories:
      - "**/src/nirvana/llmops"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "08:00"
      timezone: "America/Santiago"
    open-pull-requests-limit: 10
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"] # only update minor and patch versions
    commit-message:
      prefix: "[DEPENDABOT - LLMOps]"
