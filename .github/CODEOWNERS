# GENERATED FILE, DO NOT EDIT!
# Generated using nirvanatech.com/nirvana:codeowners
#
# NOTE: No need to generate this locally when raising your pull request.
# LET THE SCHEDULED CI JOB GENERATE THIS FILE FOR YOU.
#
# This file is generated from the granular, per-directory CODEOWNERS files
# in the repository. Although we have a go-task command to generate this file
# locally (task git:codeowners), to avoid conflicts we recommend not generating
# this file locally when raising pull requests.
#
# Note that we only allow two kinds of owners: A nirvanatech GitHub team, or a
# nirvanatech email address. The latter should also be configured as one of the
# email addresses in the user's GitHub account.
#
# See GitHub docs to know more about the syntax of this file:
# https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners#codeowners-syntax

# @nirvanatech/dev-infra owns this file
.github/CODEOWNERS @nirvanatech/dev-infra

# Source: /.github/actions
/.github/actions @nirvanatech/infra @nirvanatech/insured-eng

# Source: /.github/workflows
/.github/workflows/llm_ops*.yaml @nirvanatech/insured-eng

# Source: /src/deployment/snapsheet
/src/deployment/snapsheet @nirvanatech/insured-eng

# Source: /src/infra
/src/infra @nirvanatech/cloud-infra <EMAIL>

# Source: /src/infra/platform/src/airbyte
/src/infra/platform/src/airbyte @nirvanatech/cloud-infra

# Source: /src/infra/platform/src/llm-agents
/src/infra/platform/src/llm-agents @nirvanatech/ai-infra

# Source: /src/infra/platform/src/metaflow
/src/infra/platform/src/metaflow @nirvanatech/data-infra <EMAIL>

# Source: /src/infra/platform/src/ml
/src/infra/platform/src/ml <EMAIL>

# Source: /src/llm_agents
/src/llm_agents @nirvanatech/ai-infra

# Source: /src/nirvana
/src/nirvana/Taskfile.yml @nirvanatech/taskfile-reviewers
/src/nirvana/go.mod @nirvanatech/infra

# Source: /src/nirvana/api-server
/src/nirvana/api-server/README.md @nirvanatech/infra
/src/nirvana/api-server/api_server_main.go @nirvanatech/infra

# Source: /src/nirvana/api-server/cmd
/src/nirvana/api-server/cmd @nirvanatech/infra

# Source: /src/nirvana/api-server/common
/src/nirvana/api-server/common @nirvanatech/infra

# Source: /src/nirvana/api-server/handlers/application
/src/nirvana/api-server/handlers/application @nirvanatech/agent-experience

# Source: /src/nirvana/api-server/handlers/auth
/src/nirvana/api-server/handlers/auth @nirvanatech/security-eng
/src/nirvana/api-server/handlers/auth/post_google_auth_*.go <EMAIL>

# Source: /src/nirvana/api-server/handlers/billing
/src/nirvana/api-server/handlers/billing @nirvanatech/insured-eng

# Source: /src/nirvana/api-server/handlers/claim
/src/nirvana/api-server/handlers/claim @nirvanatech/insured-eng

# Source: /src/nirvana/api-server/handlers/data_platform
/src/nirvana/api-server/handlers/data_platform @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/api-server/handlers/endorsement
/src/nirvana/api-server/handlers/endorsement @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/api-server/handlers/external/nars
/src/nirvana/api-server/handlers/external/nars @nirvanatech/insured-eng

# Source: /src/nirvana/api-server/handlers/external/pibit
/src/nirvana/api-server/handlers/external/pibit @nirvanatech/underwriting-experience <EMAIL>

# Source: /src/nirvana/api-server/handlers/forms
/src/nirvana/api-server/handlers/forms @nirvanatech/core-insurance-platform <EMAIL>

# Source: /src/nirvana/api-server/handlers/insured
/src/nirvana/api-server/handlers/insured @nirvanatech/insured-eng

# Source: /src/nirvana/api-server/handlers/jobber
/src/nirvana/api-server/handlers/jobber @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/api-server/handlers/nonfleet
/src/nirvana/api-server/handlers/nonfleet @nirvanatech/agent-experience

# Source: /src/nirvana/api-server/handlers/nonfleet_underwriting
/src/nirvana/api-server/handlers/nonfleet_underwriting @nirvanatech/nonfleet-eng

# Source: /src/nirvana/api-server/handlers/policy
/src/nirvana/api-server/handlers/policy @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/api-server/handlers/tsp
/src/nirvana/api-server/handlers/tsp <EMAIL>

# Source: /src/nirvana/api-server/handlers/underwriting
/src/nirvana/api-server/handlers/underwriting @nirvanatech/underwriting-experience

# Source: /src/nirvana/api-server/interceptors
/src/nirvana/api-server/interceptors/nonfleet* @nirvanatech/nonfleet-eng

# Source: /src/nirvana/api-server/interceptors/billing
/src/nirvana/api-server/interceptors/billing @nirvanatech/insured-eng

# Source: /src/nirvana/api-server/interceptors/claim
/src/nirvana/api-server/interceptors/claim @nirvanatech/insured-eng

# Source: /src/nirvana/api-server/interceptors/data_platform
/src/nirvana/api-server/interceptors/data_platform @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/api-server/interceptors/endorsement
/src/nirvana/api-server/interceptors/endorsement @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/api-server/interceptors/external
/src/nirvana/api-server/interceptors/external @nirvanatech/insured-eng

# Source: /src/nirvana/api-server/interceptors/forms
/src/nirvana/api-server/interceptors/forms @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/api-server/interceptors/insured
/src/nirvana/api-server/interceptors/insured @nirvanatech/insured-eng

# Source: /src/nirvana/api-server/interceptors/nonfleet_underwriting
/src/nirvana/api-server/interceptors/nonfleet_underwriting @nirvanatech/underwriting-experience

# Source: /src/nirvana/api-server/interceptors/policy
/src/nirvana/api-server/interceptors/policy @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/api-server/middlewares
/src/nirvana/api-server/middlewares @nirvanatech/infra

# Source: /src/nirvana/api-server/quoting_jobber
/src/nirvana/api-server/quoting_jobber/client.go @nirvanatech/infra
/src/nirvana/api-server/quoting_jobber/fx.go @nirvanatech/infra

# Source: /src/nirvana/api-server/quoting_jobber/jobs
/src/nirvana/api-server/quoting_jobber/jobs @nirvanatech/infra

# Source: /src/nirvana/api-server/quoting_jobber/schedules
/src/nirvana/api-server/quoting_jobber/schedules/claims.go @nirvanatech/insured-eng
/src/nirvana/api-server/quoting_jobber/schedules/endorsement.go @nirvanatech/core-insurance-platform
/src/nirvana/api-server/quoting_jobber/schedules/impl.go @nirvanatech/infra
/src/nirvana/api-server/quoting_jobber/schedules/quoting.go @nirvanatech/agent-experience
/src/nirvana/api-server/quoting_jobber/schedules/reporting.go @nirvanatech/agent-experience
/src/nirvana/api-server/quoting_jobber/schedules/risk_metrics.go <EMAIL>
/src/nirvana/api-server/quoting_jobber/schedules/underwriting.go @nirvanatech/underwriting-experience

# Source: /src/nirvana/api-server/rule-engine
/src/nirvana/api-server/rule-engine @nirvanatech/underwriting-experience

# Source: /src/nirvana/api-server/tests
/src/nirvana/api-server/tests/api_server_main_test.go @nirvanatech/infra

# Source: /src/nirvana/api-server/tests/billing_apis
/src/nirvana/api-server/tests/billing_apis @nirvanatech/insured-eng

# Source: /src/nirvana/api-server/tests/claim_apis
/src/nirvana/api-server/tests/claim_apis @nirvanatech/insured-eng

# Source: /src/nirvana/api-server/tests/endorsementapp_apis
/src/nirvana/api-server/tests/endorsementapp_apis @nirvanatech/agent-experience

# Source: /src/nirvana/api-server/tests/forms_apis
/src/nirvana/api-server/tests/forms_apis @nirvanatech/core-insurance-platform <EMAIL>

# Source: /src/nirvana/api-server/tests/policy_apis
/src/nirvana/api-server/tests/policy_apis @nirvanatech/core-insurance-platform

# Source: /src/nirvana/api-server/tests/uw_apis
/src/nirvana/api-server/tests/uw_apis @nirvanatech/underwriting-experience

# Source: /src/nirvana/application/endorsementapp
/src/nirvana/application/endorsementapp @nirvanatech/agent-experience <EMAIL>

# Source: /src/nirvana/application/normalizer
/src/nirvana/application/normalizer @nirvanatech/agent-experience @nirvanatech/insured-eng

# Source: /src/nirvana/billing
/src/nirvana/billing @nirvanatech/insured-eng

# Source: /src/nirvana/billing/payments
/src/nirvana/billing/payments @nirvanatech/insured-eng

# Source: /src/nirvana/claims
/src/nirvana/claims @nirvanatech/insured-eng

# Source: /src/nirvana/claims/lambdas/draft-fnol-intake
/src/nirvana/claims/lambdas/draft-fnol-intake @nirvanatech/insured-eng

# Source: /src/nirvana/client
/src/nirvana/client @nirvanatech/frontend

# Source: /src/nirvana/client/apps/quoting
/src/nirvana/client/apps/quoting @nirvanatech/agent-experience

# Source: /src/nirvana/client/apps/quoting/cypress/e2e/quoting/claims
/src/nirvana/client/apps/quoting/cypress/e2e/quoting/claims @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/quoting/src/features/claims
/src/nirvana/client/apps/quoting/src/features/claims @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/quoting/src/types
/src/nirvana/client/apps/quoting/src/types/gqlgen-types.ts @nirvanatech/insured-eng
/src/nirvana/client/apps/quoting/src/types/graphql-types.ts @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/safety
/src/nirvana/client/apps/safety @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/safety/src/pages/claims
/src/nirvana/client/apps/safety/src/pages/claims @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/safety/src/types
/src/nirvana/client/apps/safety/src/types/gqlgen-types.ts @nirvanatech/insured-eng
/src/nirvana/client/apps/safety/src/types/graphql-types.ts @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/support
/src/nirvana/client/apps/support @nirvanatech/frontend

# Source: /src/nirvana/client/apps/support/src/pages/billing
/src/nirvana/client/apps/support/src/pages/billing @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/support/src/pages/billing-info
/src/nirvana/client/apps/support/src/pages/billing-info @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/support/src/pages/billing-pipeline
/src/nirvana/client/apps/support/src/pages/billing-pipeline @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/support/src/pages/billing-pipeline-debugger
/src/nirvana/client/apps/support/src/pages/billing-pipeline-debugger @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/support/src/pages/claims
/src/nirvana/client/apps/support/src/pages/claims @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/support/src/pages/formSignaturePacket/create-payment-link
/src/nirvana/client/apps/support/src/pages/formSignaturePacket/create-payment-link @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/support/src/pages/loss-runs-reports
/src/nirvana/client/apps/support/src/pages/loss-runs-reports @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/support/src/pages/policy
/src/nirvana/client/apps/support/src/pages/policy @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/support/src/pages/policy/components/tracked-vehicles
/src/nirvana/client/apps/support/src/pages/policy/components/tracked-vehicles @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/support/src/types
/src/nirvana/client/apps/support/src/types/gqlgen-types.ts @nirvanatech/insured-eng
/src/nirvana/client/apps/support/src/types/graphql-types.ts @nirvanatech/insured-eng

# Source: /src/nirvana/client/apps/underwriter
/src/nirvana/client/apps/underwriter <EMAIL>

# Source: /src/nirvana/client/packages/ui
/src/nirvana/client/packages/ui <EMAIL>

# Source: /src/nirvana/cmd/db
/src/nirvana/cmd/db @nirvanatech/infra <EMAIL>

# Source: /src/nirvana/cmd/fmcsa_data_provider
/src/nirvana/cmd/fmcsa_data_provider @nirvanatech/insured-eng

# Source: /src/nirvana/cmd/forms
/src/nirvana/cmd/forms @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/cmd/insurance-bundle
/src/nirvana/cmd/insurance-bundle @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/cmd/jobber
/src/nirvana/cmd/jobber @nirvanatech/infra

# Source: /src/nirvana/cmd/jobber/cmd/jobs
/src/nirvana/cmd/jobber/cmd/jobs/safety.go @nirvanatech/insured-eng

# Source: /src/nirvana/cmd/jobber/cmd/jobs/fmcsa
/src/nirvana/cmd/jobber/cmd/jobs/fmcsa @nirvanatech/insured-eng

# Source: /src/nirvana/cmd/jobber/cmd/jobs/safety
/src/nirvana/cmd/jobber/cmd/jobs/safety @nirvanatech/insured-eng

# Source: /src/nirvana/cmd/jobber/cmd/jobs/telematics
/src/nirvana/cmd/jobber/cmd/jobs/telematics @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/cmd/nonfleet
/src/nirvana/cmd/nonfleet @nirvanatech/nonfleet-eng

# Source: /src/nirvana/cmd/quoting
/src/nirvana/cmd/quoting @nirvanatech/agent-experience

# Source: /src/nirvana/cmd/safety
/src/nirvana/cmd/safety @nirvanatech/insured-eng

# Source: /src/nirvana/cmd/support
/src/nirvana/cmd/support @nirvanatech/agent-experience <EMAIL>

# Source: /src/nirvana/cmd/telematics
/src/nirvana/cmd/telematics @nirvanatech/data-infra

# Source: /src/nirvana/cmd/telematicsv2
/src/nirvana/cmd/telematicsv2 @nirvanatech/data-infra

# Source: /src/nirvana/cmd/terraform
/src/nirvana/cmd/terraform @nirvanatech/infra <EMAIL>

# Source: /src/nirvana/cmd/underwriting
/src/nirvana/cmd/underwriting @nirvanatech/underwriting-experience

# Source: /src/nirvana/common-go/application-util
/src/nirvana/common-go/application-util @nirvanatech/underwriting-experience

# Source: /src/nirvana/common-go/log
/src/nirvana/common-go/log @nirvanatech/infra

# Source: /src/nirvana/common-go/migration_utils
/src/nirvana/common-go/migration_utils @nirvanatech/core-insurance-platform

# Source: /src/nirvana/common-go/pgtskv
/src/nirvana/common-go/pgtskv @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/common-go/querybuilder_lib
/src/nirvana/common-go/querybuilder_lib <EMAIL> <EMAIL>

# Source: /src/nirvana/common-go/risk_score_utils
/src/nirvana/common-go/risk_score_utils @nirvanatech/underwriting-experience

# Source: /src/nirvana/common-go/test_utils/builders/datagov
/src/nirvana/common-go/test_utils/builders/datagov @nirvanatech/insured-eng

# Source: /src/nirvana/common-go/test_utils/builders/endorsementapp
/src/nirvana/common-go/test_utils/builders/endorsementapp @nirvanatech/agent-experience <EMAIL>

# Source: /src/nirvana/data_infra_jobber
/src/nirvana/data_infra_jobber @nirvanatech/data-infra

# Source: /src/nirvana/db-api/db_wrappers/agency
/src/nirvana/db-api/db_wrappers/agency @nirvanatech/agent-experience

# Source: /src/nirvana/db-api/db_wrappers/agent_license
/src/nirvana/db-api/db_wrappers/agent_license @nirvanatech/agent-experience

# Source: /src/nirvana/db-api/db_wrappers/agents_dashboard
/src/nirvana/db-api/db_wrappers/agents_dashboard @nirvanatech/agent-experience

# Source: /src/nirvana/db-api/db_wrappers/app_review_widget
/src/nirvana/db-api/db_wrappers/app_review_widget @nirvanatech/underwriting-experience

# Source: /src/nirvana/db-api/db_wrappers/application
/src/nirvana/db-api/db_wrappers/application @nirvanatech/agent-experience

# Source: /src/nirvana/db-api/db_wrappers/billing
/src/nirvana/db-api/db_wrappers/billing @nirvanatech/insured-eng

# Source: /src/nirvana/db-api/db_wrappers/billing/mileage
/src/nirvana/db-api/db_wrappers/billing/mileage @nirvanatech/insured-eng

# Source: /src/nirvana/db-api/db_wrappers/datagov
/src/nirvana/db-api/db_wrappers/datagov @nirvanatech/insured-eng

# Source: /src/nirvana/db-api/db_wrappers/emails
/src/nirvana/db-api/db_wrappers/emails @nirvanatech/underwriting-experience <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/emails/application
/src/nirvana/db-api/db_wrappers/emails/application <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/endorsement
/src/nirvana/db-api/db_wrappers/endorsement @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/endorsementapp
/src/nirvana/db-api/db_wrappers/endorsementapp @nirvanatech/agent-experience <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/entity_license
/src/nirvana/db-api/db_wrappers/entity_license @nirvanatech/agent-experience

# Source: /src/nirvana/db-api/db_wrappers/external/pibit
/src/nirvana/db-api/db_wrappers/external/pibit @nirvanatech/underwriting-experience <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/fleet
/src/nirvana/db-api/db_wrappers/fleet @nirvanatech/insured-eng

# Source: /src/nirvana/db-api/db_wrappers/fmcsa
/src/nirvana/db-api/db_wrappers/fmcsa @nirvanatech/insured-eng

# Source: /src/nirvana/db-api/db_wrappers/followup_questions
/src/nirvana/db-api/db_wrappers/followup_questions @nirvanatech/underwriting-experience <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/forms
/src/nirvana/db-api/db_wrappers/forms @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/forms/formschedule
/src/nirvana/db-api/db_wrappers/forms/formschedule @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/insurance-bundle
/src/nirvana/db-api/db_wrappers/insurance-bundle @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/insurance-bundle/insurance-bundle
/src/nirvana/db-api/db_wrappers/insurance-bundle/insurance-bundle @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/insured
/src/nirvana/db-api/db_wrappers/insured @nirvanatech/insured-eng

# Source: /src/nirvana/db-api/db_wrappers/nonfleet
/src/nirvana/db-api/db_wrappers/nonfleet @nirvanatech/nonfleet-eng

# Source: /src/nirvana/db-api/db_wrappers/nonfleet/application_review
/src/nirvana/db-api/db_wrappers/nonfleet/application_review @nirvanatech/nonfleet-eng @nirvanatech/underwriting-experience

# Source: /src/nirvana/db-api/db_wrappers/policy
/src/nirvana/db-api/db_wrappers/policy @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/policy_set
/src/nirvana/db-api/db_wrappers/policy_set @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/prog_type_identifier
/src/nirvana/db-api/db_wrappers/prog_type_identifier @nirvanatech/core-insurance-platform

# Source: /src/nirvana/db-api/db_wrappers/safety
/src/nirvana/db-api/db_wrappers/safety @nirvanatech/insured-eng

# Source: /src/nirvana/db-api/db_wrappers/smartdrive
/src/nirvana/db-api/db_wrappers/smartdrive @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/db-api/db_wrappers/telematics
/src/nirvana/db-api/db_wrappers/telematics @nirvanatech/data-infra

# Source: /src/nirvana/db-api/db_wrappers/uw
/src/nirvana/db-api/db_wrappers/uw @nirvanatech/underwriting-experience

# Source: /src/nirvana/db-api/postups
/src/nirvana/db-api/postups @nirvanatech/infra

# Source: /src/nirvana/distsem
/src/nirvana/distsem @nirvanatech/infra

# Source: /src/nirvana/emailer
/src/nirvana/emailer <EMAIL>

# Source: /src/nirvana/endorsement
/src/nirvana/endorsement @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/events/email-event
/src/nirvana/events/email-event <EMAIL>

# Source: /src/nirvana/events/external_integration_events
/src/nirvana/events/external_integration_events @nirvanatech/insured-eng

# Source: /src/nirvana/events/forms_event
/src/nirvana/events/forms_event @nirvanatech/core-insurance-platform <EMAIL>

# Source: /src/nirvana/events/nonfleet_events
/src/nirvana/events/nonfleet_events @nirvanatech/nonfleet-eng

# Source: /src/nirvana/events/pagerduty
/src/nirvana/events/pagerduty @nirvanatech/infra <EMAIL>

# Source: /src/nirvana/events/policy-event
/src/nirvana/events/policy-event @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/events/quoting_events
/src/nirvana/events/quoting_events @nirvanatech/agent-experience

# Source: /src/nirvana/events/underwriter_events
/src/nirvana/events/underwriter_events @nirvanatech/underwriting-experience

# Source: /src/nirvana/experiments
/src/nirvana/experiments <EMAIL>

# Source: /src/nirvana/external_client/salesforce
/src/nirvana/external_client/salesforce @nirvanatech/agent-experience <EMAIL>

# Source: /src/nirvana/external_data_management
/src/nirvana/external_data_management @nirvanatech/pricing-eng

# Source: /src/nirvana/external_data_management/mvr
/src/nirvana/external_data_management/mvr @nirvanatech/infra

# Source: /src/nirvana/feature_store
/src/nirvana/feature_store @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/features
/src/nirvana/features/definitions.go @nirvanatech/data-infra <EMAIL>
/src/nirvana/features/proto.go @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/features/daily_vin_mileage
/src/nirvana/features/daily_vin_mileage @nirvanatech/underwriting-experience

# Source: /src/nirvana/features/hazard_zones
/src/nirvana/features/hazard_zones @nirvanatech/underwriting-experience

# Source: /src/nirvana/features/radius_operation_telematics
/src/nirvana/features/radius_operation_telematics @nirvanatech/underwriting-experience

# Source: /src/nirvana/fmcsa
/src/nirvana/fmcsa @nirvanatech/insured-eng

# Source: /src/nirvana/fmcsa/datagov
/src/nirvana/fmcsa/datagov @nirvanatech/insured-eng

# Source: /src/nirvana/forms
/src/nirvana/forms @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/golden_dataset
/src/nirvana/golden_dataset <EMAIL> <EMAIL> <EMAIL>

# Source: /src/nirvana/gqlschema/agencies
/src/nirvana/gqlschema/agencies @nirvanatech/insured-eng

# Source: /src/nirvana/gqlschema/authz
/src/nirvana/gqlschema/authz @nirvanatech/security-eng

# Source: /src/nirvana/gqlschema/cameras
/src/nirvana/gqlschema/cameras @nirvanatech/insured-eng

# Source: /src/nirvana/gqlschema/fleet_safety_reports
/src/nirvana/gqlschema/fleet_safety_reports @nirvanatech/insured-eng

# Source: /src/nirvana/gqlschema/fleets
/src/nirvana/gqlschema/fleets @nirvanatech/insured-eng

# Source: /src/nirvana/gqlschema/generated
/src/nirvana/gqlschema/generated @nirvanatech/insured-eng

# Source: /src/nirvana/gqlschema/models
/src/nirvana/gqlschema/models @nirvanatech/insured-eng

# Source: /src/nirvana/gqlschema/policies
/src/nirvana/gqlschema/policies @nirvanatech/insured-eng

# Source: /src/nirvana/gqlschema/registry
/src/nirvana/gqlschema/registry @nirvanatech/insured-eng

# Source: /src/nirvana/gqlschema/resolver
/src/nirvana/gqlschema/resolver @nirvanatech/insured-eng

# Source: /src/nirvana/graphql-server
/src/nirvana/graphql-server @nirvanatech/insured-eng <EMAIL>

# Source: /src/nirvana/infra
/src/nirvana/infra @nirvanatech/infra

# Source: /src/nirvana/infra/auth
/src/nirvana/infra/auth @nirvanatech/insured-eng @nirvanatech/security-eng

# Source: /src/nirvana/infra/auth/workramp
/src/nirvana/infra/auth/workramp @nirvanatech/infra @nirvanatech/security-eng

# Source: /src/nirvana/infra/auth_session
/src/nirvana/infra/auth_session @nirvanatech/insured-eng <EMAIL>

# Source: /src/nirvana/infra/authz
/src/nirvana/infra/authz @nirvanatech/security-eng

# Source: /src/nirvana/infra/config
/src/nirvana/infra/config
/src/nirvana/infra/config/fx.go @nirvanatech/infra
/src/nirvana/infra/config/loader.go @nirvanatech/infra

# Source: /src/nirvana/infra/config/files
/src/nirvana/infra/config/files/config.staging.yaml @nirvanatech/cloud-infra

# Source: /src/nirvana/infra/constants
/src/nirvana/infra/constants

# Source: /src/nirvana/infra/fx
/src/nirvana/infra/fx @nirvanatech/infra

# Source: /src/nirvana/infra/fx/testfixtures/carfax_fixture
/src/nirvana/infra/fx/testfixtures/carfax_fixture @nirvanatech/data-infra

# Source: /src/nirvana/infra/fx/testfixtures/heremaps_fixture
/src/nirvana/infra/fx/testfixtures/heremaps_fixture @nirvanatech/data-infra

# Source: /src/nirvana/infra/fx/testfixtures/keeptruckin_fixture
/src/nirvana/infra/fx/testfixtures/keeptruckin_fixture @nirvanatech/data-infra

# Source: /src/nirvana/infra/fx/testfixtures/lni_fixture
/src/nirvana/infra/fx/testfixtures/lni_fixture @nirvanatech/insured-eng <EMAIL>

# Source: /src/nirvana/infra/fx/testfixtures/oauth_fixture
/src/nirvana/infra/fx/testfixtures/oauth_fixture @nirvanatech/data-infra

# Source: /src/nirvana/infra/fx/testfixtures/openmeteo_fixture
/src/nirvana/infra/fx/testfixtures/openmeteo_fixture @nirvanatech/data-infra

# Source: /src/nirvana/infra/fx/testfixtures/samsara_fixture
/src/nirvana/infra/fx/testfixtures/samsara_fixture @nirvanatech/data-infra

# Source: /src/nirvana/infra/fx/testfixtures/smartdrive_fixture
/src/nirvana/infra/fx/testfixtures/smartdrive_fixture @nirvanatech/data-infra

# Source: /src/nirvana/infra/fx/testfixtures/terminal_fixture
/src/nirvana/infra/fx/testfixtures/terminal_fixture @nirvanatech/data-infra

# Source: /src/nirvana/insurance-bundle
/src/nirvana/insurance-bundle @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/insurance-core
/src/nirvana/insurance-core @nirvanatech/core-insurance-platform <EMAIL> <EMAIL> <EMAIL>

# Source: /src/nirvana/insured
/src/nirvana/insured @nirvanatech/insured-eng

# Source: /src/nirvana/jobber
/src/nirvana/jobber @nirvanatech/infra

# Source: /src/nirvana/jobber/event/registry/data_platform
/src/nirvana/jobber/event/registry/data_platform @nirvanatech/data-infra

# Source: /src/nirvana/jobber/job_utils
/src/nirvana/jobber/job_utils @nirvanatech/infra

# Source: /src/nirvana/llmops
/src/nirvana/llmops @nirvanatech/insured-eng

# Source: /src/nirvana/metaflow
/src/nirvana/metaflow @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/nirvanamq
/src/nirvana/nirvanamq @nirvanatech/infra <EMAIL> <EMAIL>

# Source: /src/nirvana/nonfleet
/src/nirvana/nonfleet @nirvanatech/nonfleet-eng

# Source: /src/nirvana/nonfleet/authorities
/src/nirvana/nonfleet/authorities @nirvanatech/underwriting-experience

# Source: /src/nirvana/nonfleet/quoting-jobs
/src/nirvana/nonfleet/quoting-jobs @nirvanatech/nonfleet-eng @nirvanatech/underwriting-experience

# Source: /src/nirvana/nonfleet/rule_engine
/src/nirvana/nonfleet/rule_engine @nirvanatech/nonfleet-eng <EMAIL>

# Source: /src/nirvana/nonfleet/underwriting_panels
/src/nirvana/nonfleet/underwriting_panels @nirvanatech/underwriting-experience

# Source: /src/nirvana/oauth
/src/nirvana/oauth @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/openapi-specs/api_server_app
/src/nirvana/openapi-specs/api_server_app @nirvanatech/agent-experience

# Source: /src/nirvana/openapi-specs/api_server_app/appetite_lite
/src/nirvana/openapi-specs/api_server_app/appetite_lite <EMAIL> <EMAIL>

# Source: /src/nirvana/openapi-specs/api_server_app/nonfleet_app
/src/nirvana/openapi-specs/api_server_app/nonfleet_app @nirvanatech/agent-experience

# Source: /src/nirvana/openapi-specs/api_server_billing
/src/nirvana/openapi-specs/api_server_billing @nirvanatech/insured-eng

# Source: /src/nirvana/openapi-specs/api_server_claims
/src/nirvana/openapi-specs/api_server_claims @nirvanatech/insured-eng

# Source: /src/nirvana/openapi-specs/api_server_dp
/src/nirvana/openapi-specs/api_server_dp @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/openapi-specs/api_server_emails
/src/nirvana/openapi-specs/api_server_emails @nirvanatech/underwriting-experience <EMAIL>

# Source: /src/nirvana/openapi-specs/api_server_endorsement
/src/nirvana/openapi-specs/api_server_endorsement @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/openapi-specs/api_server_external
/src/nirvana/openapi-specs/api_server_external @nirvanatech/insured-eng

# Source: /src/nirvana/openapi-specs/api_server_forms
/src/nirvana/openapi-specs/api_server_forms @nirvanatech/core-insurance-platform <EMAIL>

# Source: /src/nirvana/openapi-specs/api_server_insured
/src/nirvana/openapi-specs/api_server_insured @nirvanatech/insured-eng

# Source: /src/nirvana/openapi-specs/api_server_jobber
/src/nirvana/openapi-specs/api_server_jobber @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/openapi-specs/api_server_policy
/src/nirvana/openapi-specs/api_server_policy @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/openapi-specs/api_server_reports
/src/nirvana/openapi-specs/api_server_reports @nirvanatech/agent-experience

# Source: /src/nirvana/openapi-specs/api_server_uw
/src/nirvana/openapi-specs/api_server_uw @nirvanatech/underwriting-experience

# Source: /src/nirvana/openapi-specs/api_server_uw/nonfleet_uw
/src/nirvana/openapi-specs/api_server_uw/nonfleet_uw @nirvanatech/nonfleet-eng

# Source: /src/nirvana/openapi-specs/components/appetite_lite
/src/nirvana/openapi-specs/components/appetite_lite <EMAIL> <EMAIL>

# Source: /src/nirvana/openapi-specs/components/application
/src/nirvana/openapi-specs/components/application @nirvanatech/agent-experience

# Source: /src/nirvana/openapi-specs/components/billing
/src/nirvana/openapi-specs/components/billing @nirvanatech/insured-eng

# Source: /src/nirvana/openapi-specs/components/claims
/src/nirvana/openapi-specs/components/claims @nirvanatech/insured-eng

# Source: /src/nirvana/openapi-specs/components/emails
/src/nirvana/openapi-specs/components/emails @nirvanatech/underwriting-experience <EMAIL>

# Source: /src/nirvana/openapi-specs/components/endorsement
/src/nirvana/openapi-specs/components/endorsement @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/openapi-specs/components/endorsementapp/intake
/src/nirvana/openapi-specs/components/endorsementapp/intake @nirvanatech/agent-experience

# Source: /src/nirvana/openapi-specs/components/external
/src/nirvana/openapi-specs/components/external @nirvanatech/insured-eng

# Source: /src/nirvana/openapi-specs/components/forms
/src/nirvana/openapi-specs/components/forms @nirvanatech/core-insurance-platform <EMAIL>

# Source: /src/nirvana/openapi-specs/components/insured
/src/nirvana/openapi-specs/components/insured @nirvanatech/insured-eng

# Source: /src/nirvana/openapi-specs/components/nonfleet
/src/nirvana/openapi-specs/components/nonfleet @nirvanatech/agent-experience

# Source: /src/nirvana/openapi-specs/components/nonfleet_underwriting
/src/nirvana/openapi-specs/components/nonfleet_underwriting @nirvanatech/nonfleet-eng

# Source: /src/nirvana/openapi-specs/components/policy
/src/nirvana/openapi-specs/components/policy @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/openapi-specs/components/reports
/src/nirvana/openapi-specs/components/reports @nirvanatech/agent-experience

# Source: /src/nirvana/openapi-specs/components/underwriting
/src/nirvana/openapi-specs/components/underwriting @nirvanatech/underwriting-experience

# Source: /src/nirvana/parsed_loss_runs
/src/nirvana/parsed_loss_runs @nirvanatech/underwriting-experience <EMAIL>

# Source: /src/nirvana/pdffill
/src/nirvana/pdffill @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/pdfgen
/src/nirvana/pdfgen @nirvanatech/core-insurance-platform <EMAIL> <EMAIL> <EMAIL>

# Source: /src/nirvana/pdfgen/pdf-create
/src/nirvana/pdfgen/pdf-create <EMAIL> <EMAIL>

# Source: /src/nirvana/pdfgen/pdf-create/src/templates/safety
/src/nirvana/pdfgen/pdf-create/src/templates/safety @nirvanatech/insured-eng

# Source: /src/nirvana/policy
/src/nirvana/policy @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/nirvana/policy/non_fleet
/src/nirvana/policy/non_fleet @nirvanatech/nonfleet-eng <EMAIL>

# Source: /src/nirvana/policy_common
/src/nirvana/policy_common @nirvanatech/core-insurance-platform

# Source: /src/nirvana/policy_common/forms_generator
/src/nirvana/policy_common/forms_generator @nirvanatech/core-insurance-platform <EMAIL>

# Source: /src/nirvana/pricing
/src/nirvana/pricing @nirvanatech/pricing-eng

# Source: /src/nirvana/quoting
/src/nirvana/quoting @nirvanatech/agent-experience

# Source: /src/nirvana/quoting/ancillary_coverages
/src/nirvana/quoting/ancillary_coverages @nirvanatech/underwriting-experience

# Source: /src/nirvana/quoting/emailer
/src/nirvana/quoting/emailer @nirvanatech/agent-experience <EMAIL>

# Source: /src/nirvana/quoting/experiments
/src/nirvana/quoting/experiments @nirvanatech/pricing-eng

# Source: /src/nirvana/quoting/pullers/ancillary_coverages
/src/nirvana/quoting/pullers/ancillary_coverages @nirvanatech/underwriting-experience <EMAIL>

# Source: /src/nirvana/quoting/pullers/package_definitions
/src/nirvana/quoting/pullers/package_definitions @nirvanatech/underwriting-experience

# Source: /src/nirvana/rating
/src/nirvana/rating @nirvanatech/pricing-eng

# Source: /src/nirvana/rating/adaptors/fleet_adaptor
/src/nirvana/rating/adaptors/fleet_adaptor @nirvanatech/pricing-eng
/src/nirvana/rating/adaptors/fleet_adaptor/sentry* @nirvanatech/agent-experience <EMAIL> <EMAIL>

# Source: /src/nirvana/rating/adaptors/fleet_adaptor/common
/src/nirvana/rating/adaptors/fleet_adaptor/common @nirvanatech/pricing-eng

# Source: /src/nirvana/rating/adaptors/nonfleet_adaptor
/src/nirvana/rating/adaptors/nonfleet_adaptor @nirvanatech/pricing-eng

# Source: /src/nirvana/rating/data_fetching
/src/nirvana/rating/data_fetching @nirvanatech/agent-experience @nirvanatech/underwriting-experience

# Source: /src/nirvana/rating/data_processing
/src/nirvana/rating/data_processing @nirvanatech/agent-experience @nirvanatech/underwriting-experience

# Source: /src/nirvana/rating/models
/src/nirvana/rating/models @nirvanatech/pricing-eng

# Source: /src/nirvana/rating/models/models_release
/src/nirvana/rating/models/models_release @nirvanatech/agent-experience @nirvanatech/pricing-eng

# Source: /src/nirvana/rating/mvr
/src/nirvana/rating/mvr @nirvanatech/underwriting-experience <EMAIL> <EMAIL>

# Source: /src/nirvana/rating/pricing
/src/nirvana/rating/pricing @nirvanatech/pricing-eng

# Source: /src/nirvana/reporting
/src/nirvana/reporting @nirvanatech/agent-experience

# Source: /src/nirvana/risk_metrics
/src/nirvana/risk_metrics @nirvanatech/pricing-eng

# Source: /src/nirvana/safety
/src/nirvana/safety @nirvanatech/insured-eng

# Source: /src/nirvana/safety_jobber
/src/nirvana/safety_jobber @nirvanatech/insured-eng

# Source: /src/nirvana/salesforce
/src/nirvana/salesforce @nirvanatech/agent-experience

# Source: /src/nirvana/servers/fmcsa_data_provider
/src/nirvana/servers/fmcsa_data_provider @nirvanatech/insured-eng

# Source: /src/nirvana/servers/quote_scraper/scrapers/pgr-scrapper
/src/nirvana/servers/quote_scraper/scrapers/pgr-scrapper <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>

# Source: /src/nirvana/servers/saferwatch_scraper
/src/nirvana/servers/saferwatch_scraper <EMAIL>

# Source: /src/nirvana/servers/telematicsv2
/src/nirvana/servers/telematicsv2 @nirvanatech/data-infra

# Source: /src/nirvana/sharing
/src/nirvana/sharing @nirvanatech/insured-eng

# Source: /src/nirvana/taskfiles
/src/nirvana/taskfiles @nirvanatech/taskfile-reviewers

# Source: /src/nirvana/telematics
/src/nirvana/telematics @nirvanatech/data-infra

# Source: /src/nirvana/telematics/connection_selector
/src/nirvana/telematics/connection_selector @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/telematics/connections/jobs/health_check
/src/nirvana/telematics/connections/jobs/health_check @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/telematics/data_platform/maps/heremaps
/src/nirvana/telematics/data_platform/maps/heremaps @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/telematics/data_platform/maps/openmeteo
/src/nirvana/telematics/data_platform/maps/openmeteo @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/telematics/data_platform/providers/smartdrive
/src/nirvana/telematics/data_platform/providers/smartdrive @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/telematics/integrations/smartdrive
/src/nirvana/telematics/integrations/smartdrive @nirvanatech/data-infra <EMAIL>

# Source: /src/nirvana/telematics/jobs/impl
/src/nirvana/telematics/jobs/impl/verify_tsp_connection.go <EMAIL>

# Source: /src/nirvana/underwriting
/src/nirvana/underwriting @nirvanatech/underwriting-experience

# Source: /src/proto/fmcsa_data_provider
/src/proto/fmcsa_data_provider @nirvanatech/insured-eng

# Source: /src/proto/insurance_bundle
/src/proto/insurance_bundle @nirvanatech/core-insurance-platform <EMAIL> <EMAIL>

# Source: /src/proto/insurance_core
/src/proto/insurance_core @nirvanatech/core-insurance-platform <EMAIL> <EMAIL> <EMAIL>

# Source: /src/proto/insured
/src/proto/insured @nirvanatech/insured-eng

# Source: /src/proto/metaflow
/src/proto/metaflow @nirvanatech/data-infra <EMAIL>

# Source: /src/proto/oauth_manager
/src/proto/oauth_manager @nirvanatech/data-infra

# Source: /src/proto/telematicsv2
/src/proto/telematicsv2 @nirvanatech/data-infra

# Source: /src/scripts
/src/scripts @nirvanatech/infra
