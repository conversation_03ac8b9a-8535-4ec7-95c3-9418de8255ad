package main

import (
	"fmt"
	"github.com/shopspring/decimal"
)

func main() {
	// Test various ways of creating 95.35
	fmt.Println("=== Testing decimal 95.35 ===")

	// Method 1: From string
	d1, err := decimal.NewFromString("95.35")
	if err != nil {
		fmt.Printf("Error creating from string: %v\n", err)
	} else {
		testDecimal("NewFromString('95.35')", d1)
	}

	// Method 2: From float
	d2 := decimal.NewFromFloat(95.35)
	testDecimal("NewFromFloat(95.35)", d2)

	// Method 3: Manual construction
	d3 := decimal.New(9535, -2)
	testDecimal("New(9535, -2)", d3)

	// Method 4: From calculation
	d4 := decimal.NewFromFloat(95.0).Add(decimal.NewFromFloat(0.35))
	testDecimal("95.0 + 0.35", d4)

	// Method 5: From int with division
	d5 := decimal.NewFromInt(9535).Div(decimal.NewFromInt(100))
	testDecimal("9535 / 100", d5)
}

func testDecimal(name string, d decimal.Decimal) {
	fmt.Printf("\n--- %s ---\n", name)
	fmt.Printf("String(): %s\n", d.String())
	fmt.Printf("Coefficient(): %s\n", d.Coefficient().String())
	fmt.Printf("Exponent(): %d\n", d.Exponent())

	f, ok := d.Float64()
	fmt.Printf("Float64(): %f, ok: %t\n", f, ok)

	if !ok {
		fmt.Printf("*** Float64 conversion FAILED for %s ***\n", d.String())
	}
}
